{"name": "qinxianghua-app", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@vant/area-data": "^1.2.3", "@vueuse/core": "^12.4.0", "@vueuse/router": "^13.5.0", "alova": "^3.3.4", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.21", "axios": "^0.26.1", "colorthief": "^2.4.0", "countup.js": "^2.8.0", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "dexie": "^4.0.11", "docx-preview": "^0.1.11", "fastclick": "^1.0.6", "format-number": "^3.0.0", "js-cookie": "^3.0.1", "jszip": "^3.10.0", "localforage": "^1.10.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mathjs": "^12.4.2", "motion-v": "^1.4.0", "odometer_countup": "^1.0.4", "path-browserify": "^1.0.1", "pdfjs-dist": "^2.12.313", "pinia": "^3.0.1", "qrcode": "^1.5.0", "qs": "^6.14.0", "swiper": "^11.2.8", "ua-parser-js": "^2.0.2", "uid": "^2.0.2", "uuid": "^11.1.0", "vant": "^4.9.19", "vconsole": "^3.14.6", "vue": "^3.5.14", "vue-cropper": "^1.0.3", "vue-dompurify-html": "^5.0.1", "vue-router": "^4.0.14", "vue3-clipboard": "^1.0.0", "vue3-count-to": "^1.1.2", "vue3-marquee": "^4.2.2", "vuex": "^4.0.2", "weixin-js-sdk": "^1.6.0", "xss": "^1.0.15"}, "devDependencies": {"@types/node": "^17.0.23", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.0.0", "sass-embedded": "^1.89.1", "sharp": "^0.34.1", "svgo": "^3.3.2", "unplugin-auto-import": "^0.6.9", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.0.0", "vite-plugin-vue-setup-extend": "^0.4.0"}}