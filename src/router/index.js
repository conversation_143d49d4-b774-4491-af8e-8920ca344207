import { createWebHistory, createRouter } from 'vue-router'
import myRouter from './constant/myRouter'
import goodsRouter from './constant/goodsRouter'
import cashierRoutes from './constant/cashier'
import iframeRouter from './constant/iframeRouter'
import cashLoan from './constant/cashLoanRouter'
import memberRouter from './constant/memberRouter'
import marketing from './constant/marketing'
import integralRoutes from './constant/integral'
import downloadRoutes from './constant/downloadRouter'
import treasureHuntRoutes from './constant/treasureHuntRouter'
import appJs from '@/utils/appJS'
// 公共路由
export const basicRoutes = [
  {
    path: '/',
    name: 'Main',
    meta: {
      index: 0,
      keepAlive: 1,
    },
    component: () => import('@/views/Main.vue'),
  },
  {
    path: '/base-search',
    name: 'BaseSearch',
    meta: {
      index: 1,
    },
    component: () => import('@/views/base/Search.vue'),
  },
  {
    path: '/my',
    name: 'My',
    meta: {
      index: 0,
    },
    component: () => import('@/views/my/Index.vue'),
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
  },
  {
    path: '/register',
    name: 'Register',
    meta: {
      index: 1,
    },
    async beforeEnter(to, from) {
      const { h5 } = await appJs.getEnv()
      if (!h5) {
        appJs.appLogin()
        return '/'
      }
    },
    component: () => import('@/views/Register.vue'),
  },
  {
    path: '/credit-facilities',
    name: 'CreditFacilities',
    meta: {
      index: 20,
    },
    // component: () => import('@/views/creditFacilities/Index.vue')
    component: () => import('@/views/creditFacilities2/index.vue'),
  },
  {
    path: '/face-recognition-result',
    name: 'FaceRecognitionResult',
    meta: {
      index: 21,
    },
    component: () => import('@/views/creditFacilities/FaceRecognitionResult.vue'),
  },
  {
    path: '/cust-info-result',
    name: 'CustInfoResult',
    meta: {
      index: 30,
    },
    component: () => import('@/views/custInfo/Result.vue'),
  },
  {
    path: '/cust-info',
    name: 'CustInfo',
    meta: {
      index: 31,
    },
    component: () => import('@/views/custInfo/Index.vue'),
  },
  {
    path: '/active-loading',
    name: 'ActiveLoading',
    meta: {
      index: 32,
    },
    component: () => import('@/views/active/Loading.vue'),
  },
  {
    path: '/active-finish',
    name: 'ActiveFinish',
    meta: {
      index: 33,
    },
    component: () => import('@/views/active/Finish.vue'),
  },
  {
    path: '/installment',
    name: 'Installment',
    meta: {
      index: 36,
    },
    component: () => import('@/views/installment/Index.vue'),
  },

  {
    path: '/message-notify',
    name: 'messageNotify',
    meta: {
      index: 10,
    },
    component: () => import('@/views/messageNofity/Index.vue'),
  },
  {
    path: '/integral',
    name: 'Integral',
    meta: {
      index: 1,
    },
    component: () => import('@/views/integral/Index.vue'),
  },
  {
    path: '/save-money',
    name: 'SaveMoney',
    meta: {
      index: 30,
    },
    component: () => import('@/views/saveMoney/Index.vue'),
  },
  {
    path: '/safe-area',
    name: 'safeArea',
    meta: {
      index: 1,
    },
    component: () => import('@/views/safe-area'),
  },
  {
    // 分期授信申请提交成功
    path: '/consume-apply-result',
    name: 'ConsumeApplyResult',
    component: () => import('@/views/ConsumeApplyResult/index.vue'),
  },
  {
    // 我的额度记录
    path: '/credit-quota-record/:sceneCode',
    name: 'CreditQuotaRecord',
    component: () => import('@/views/CreditQuotaRecord/index.vue'),
  },
]
const constantRoutes = basicRoutes.concat(
  myRouter,
  goodsRouter,
  cashierRoutes,
  iframeRouter,
  cashLoan,
  marketing,
  memberRouter,
  integralRoutes,
  downloadRoutes,
  treasureHuntRoutes
)
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

export default router
