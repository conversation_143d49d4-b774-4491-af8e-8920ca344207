const cashLoanRoutes = [
  {
    path: '/cash-loan-pre',
    name: 'cashLoanPre',
    meta: {
      index: 30
    },
    component: () => import('@/views/cashLoan/PreEntry')
  },
  {
    path: '/cash-loan-prebank',
    name: 'cashLoanPreBank',
    meta: {
      index: 32
    },
    component: () => import('@/views/cashLoan/pre/BindBank')
  },
  {
    path: '/cash-loan-matching',
    name: 'cashLoanMatching',
    meta: {
      index: 34
    },
    component: () => import('@/views/cashLoan/pre/ProductMatching')
  },
  {
    path: '/cash-loan-quota',
    name: 'cashLoanQuota',
    meta: {
      index: 36
    },
    component: () => import('@/views/cashLoan/pre/ExpectQuota')
  },
  {
    path: '/cash-loan',
    name: 'cashLoan',
    meta: {
      index: 40
    },
    component: () => import('@/views/cashLoan/Index.vue')
  },
  {
    path: '/cash-loan-order',
    name: 'cashLoanOrder',
    meta: {
      index: 41
    },
    component: () => import('@/views/cashLoan/order/Index.vue')
  },
  {
    path: '/cash-loan-apply',
    name: 'cashLoanApply',
    meta: {
      index: 41,
    },
    component: () => import('@/views/cashLoan/apply/Index')
  },
  {
    path: '/cash-loan-confirm',
    name: 'cashLoanConfirm',
    meta: {
      index: 42,
    },
    component: () => import('@/views/cashLoan/confirm/Index')
  },
  {
    path: '/cash-loan-finish',
    name: 'cashLoanFinish',
    meta: {
      index: 43
    },
    component: () => import('@/views/cashLoan/Finish.vue')
  }
]
export default cashLoanRoutes