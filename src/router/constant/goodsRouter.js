const goodsRoutes = [
  
  {
    path: '/goods-category',
    name: 'GoodsCategory',
    meta: {
      index: 2
    },
    component: () => import('@/views/goods/goodsCategory/Index.vue')
  },
  {
    path: '/goods-list',
    name: 'GoodsList',
    meta: {
      index: 3
    },
    component: () => import('@/views/goods/goodsList/Index.vue')
  },
  {
    path: '/mall',
    name: 'Mall',
    meta: {
      index: 3
    },
    component: () => import('@/views/mall/Index.vue')
  },
  {
    path: '/goods-detail',
    name: 'GoodsDetail',
    meta: {
      index: 38
    },
    component: () => import('@/views/goods/goodsDetail/Index.vue')
  },
  {
    path: '/shopping-cart',
    name: 'ShoppingCart',
    meta: {
      index: 40
    },
    component: () => import('@/views/my/shoppingCart/Index.vue')
  },
  {
    path: '/order-confirm',
    name: 'OrderConfirm',
    meta: {
      index: 50
    },
    component: () => import('@/views/goods/orderConfirm/Index.vue')
  },
  {
    path: '/order-confirm-seckill',
    name: 'OrderConfirmSeckill',
    meta: {
      index: 51
    },
    component: () => import('@/views/goods/orderConfirm/IndexSeckill.vue')
  },
  {
    path: '/goods-coupon',
    name: 'GoodsCoupon',
    meta: {
      index: 38
    },
    component: () => import('@/views/goods/goodsDetail/CouponBenefit')
  },
  {
    path: '/goods-tele',
    name: 'GoodsDetailTele',
    meta: {
      index: 35
    },
    component: () => import('@/views/goods/goodsDetail/TelePhone')
  },
  {
    path: '/time-limit',
    name: 'TimeLimit',
    meta: {
      index: 60
    },
    component: () => import('@/views/timeLimit/index')
  },
  {
    path: '/goods-detail-seckill',
    name: 'GoodsDetailSeckill',
    meta: {
      index: 39
    },
    component: () => import('@/views/goods/goodsDetail/IndexSeckill.vue')
  },
]
export default goodsRoutes