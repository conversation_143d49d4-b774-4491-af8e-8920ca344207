const memberRoutes = [
  {
    path: '/member',
    name: 'Member',
    meta: {
      index: 10
    },
    component: () => import('@/views/member/Index.vue')
  },
  {
    path: '/member/equity-order',
    name: 'MemberQuityOrder',
    meta: {
      index: 110
    },
    component: () => import('@/views/member/EquityOrder.vue')
  },
  {
    path: '/member/equity-order-detail',
    name: 'MemberQuityOrderDetail',
    meta: {
      index: 111
    },
    component: () => import('@/views/member/EquityOrderDetail')
  },
  {
    path: '/member/order-detail',
    name: 'MemberOrderDetail',
    meta: {
      index: 111
    },
    component: () => import('@/views/member/MemberOrderDetail')
  },
  {
    path: '/member/order-return',
    name: 'MemberOrderReturn',
    meta: {
      index: 112
    },
    component: () => import('@/views/member/OrderReturn.vue')
  },
]
export default memberRoutes