const integralRoutes = [
  // 外部回收
  {
    path: '/integral/sign',
    name: 'IntegralSign',
    meta: {
      index: 20,
    },
    component: () => import('@/views/integral/Sign.vue')
  },
  {
    path: '/integral/new-welfare',
    name: 'IntegralNewWefare',
    meta: {
      index: 25,
    },
    component: () => import('@/views/integral/NewWelfare.vue')
  },
  {
    path: '/integral/mall',
    name: 'IntegralMall',
    meta: {
      index: 25,
    },
    component: () => import('@/views/integral/Mall.vue')
  },
  {
    path: '/integral/detail',
    name: 'IntegralDetail',
    meta: {
      index: 3
    },
    component: () => import('@/views/integral/Detail.vue')
  },
  {
    path: '/integral/draw',
    name: 'IntegralDraw',
    meta: {
      index: 25,
    },
    component: () => import('@/views/integral/Draw.vue')
  },
  {
    path: '/integral/draw/log',
    name: 'IntegralDrawLog',
    meta: {
      index: 28,
    },
    component: () => import('@/views/integral/DrawLog.vue')
  },
  {
    path: '/integral/draw/rules',
    name: 'IntegralDrawRules',
    meta: {
      index: 28,
    },
    component: () => import('@/views/integral/DrawRules.vue')
  }
]

export default integralRoutes