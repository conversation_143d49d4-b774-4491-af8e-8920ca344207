const treasureHuntRoutes = [
  {
    path: '/treasure-hunt',
    name: 'TreasureHunt',
    meta: {
      index: 40
    },
    component: () => import('@/views/treasureHunt/Index')
  },
  {
    path: '/treasure-hunt-rules',
    name: 'TreasureHuntRules',
    meta: {
      index: 42
    },
    component: () => import('@/views/treasureHunt/Rules')
  },
  {
    path: '/treasure-hunt-detail',
    name: 'TreasureHuntDetail',
    meta: {
      index: 42
    },
    component: () => import('@/views/treasureHunt/Detail')
  },
  {
    path: '/treasure-hunt-compute',
    name: 'TreasureHuntCompute',
    meta: {
      index: 44
    },
    component: () => import('@/views/treasureHunt/ComputeFn')
  },
]
export default treasureHuntRoutes