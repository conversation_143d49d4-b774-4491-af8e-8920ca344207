const downloadRoutes = [
  {
    path: '/download/product',
    name: 'downloadProduct',
    meta: {
      index: 60
    },
    component: () => import('@/views/download/Product.vue')
  },
  {
    path: '/download/register',
    name: 'downloadRegister',
    meta: {
      index: 50
    },
    component: () => import('@/views/download/Register.vue')
  },
  {
    path: '/download2/product',
    name: 'download2Product',
    meta: {
      index: 60
    },
    component: () => import('@/views/download2/Product.vue')
  },
  {
    path: '/download2/register',
    name: 'download2Register',
    meta: {
      index: 50
    },
    component: () => import('@/views/download2/Register.vue')
  },
  {
    path: '/download3/product',
    name: 'download3Product',
    meta: {
      index: 60
    },
    component: () => import('@/views/download3/Product.vue')
  },
  {
    path: '/download3/register',
    name: 'download3Register',
    meta: {
      index: 50
    },
    component: () => import('@/views/download3/Register.vue')
  },
  {
    path: '/download4/product',
    name: 'download4Product',
    meta: {
      index: 60
    },
    component: () => import('@/views/download4/Product.vue')
  },
  {
    path: '/download5/register',
    name: 'download5Register',
    meta: {
      index: 50
    },
    component: () => import('@/views/download5/Register.vue')
  },
  {
    path: '/download5/product',
    name: 'download5Product',
    meta: {
      index: 60
    },
    component: () => import('@/views/download5/Product.vue')
  },
  {
    path: '/download6/register',
    name: 'download6Register',
    meta: {
      index: 50
    },
    component: () => import('@/views/download6/Register.vue')
  },
  {
    path: '/download6/product',
    name: 'download6Product',
    meta: {
      index: 60
    },
    component: () => import('@/views/download6/Product.vue')
  },
]
export default downloadRoutes