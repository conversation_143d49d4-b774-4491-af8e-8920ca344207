const orderRoutes = [
  {
    path: '/system-setting',
    name: 'SystemSetting',
    meta: {
      index: 1,
    },
    component: () => import('@/views/my/Setting.vue'),
  },
  {
    path: '/my/app',
    name: 'MyApp',
    meta: {
      index: 2,
    },
    component: () => import('@/views/my/App.vue'),
  },
  {
    path: '/my/account',
    name: 'MyAccount',
    meta: {
      index: 2,
    },
    component: () => import('@/views/my/account/Index.vue'),
  },
  {
    path: '/my/account/cancel',
    name: 'MyAccountCancel',
    meta: {
      index: 3,
    },
    component: () => import('@/views/my/account/Cancel.vue'),
  },
  {
    path: '/my/privacy/center',
    name: 'MyPrivacyCenter',
    meta: {
      index: 2,
    },
    component: () => import('@/views/my/privacyCenter/Index.vue'),
  },
  {
    path: '/my/privacy/policy',
    name: 'MyPrivacyPolicy',
    meta: {
      index: 3,
    },
    component: () => import('@/views/my/privacyCenter/Policy.vue'),
  },
  {
    path: '/my/privacy/register',
    name: 'MyPrivacyRegister',
    meta: {
      index: 3,
    },
    component: () => import('@/views/my/privacyCenter/Register.vue'),
  },
  {
    path: '/my/privacy/third-party',
    name: 'MyPrivacyThirdParty',
    meta: {
      index: 3,
    },
    component: () => import('@/views/my/privacyCenter/ThirdParty.vue'),
  },
  {
    path: '/my/privacy/auth',
    name: 'MyPrivacyAuth',
    meta: {
      index: 3,
    },
    component: () => import('@/views/my/privacyCenter/Authorization.vue'),
  },
  {
    path: '/order-list',
    name: 'OrderList',
    meta: {
      index: 10,
    },
    component: () => import('@/views/my/order/List.vue'),
  },
  {
    path: '/my/quota',
    name: 'MyQuota',
    // 重定向到分期额度页
    redirect: '/my/consume-quota',
    meta: {
      index: 35,
    },
    component: () => import('@/views/my/quota/Index.vue'),
  },
  {
    path: '/my/quota-detail',
    name: 'MyQuotaDetail',
    meta: {
      index: 58,
    },
    component: () => import('@/views/my/quota/Detail.vue'),
  },
  {
    path: '/my/bankcard',
    name: 'MyBankcard',
    meta: {
      index: 42,
    },
    component: () => import('@/views/my/bankCard/Index.vue'),
  },
  {
    path: '/my/bankcard/add',
    name: 'MyBankcardAdd',
    meta: {
      index: 43,
    },
    component: () => import('@/views/my/bankCard/Add.vue'),
  },
  {
    path: '/my/bill',
    name: 'MyBill',
    meta: {
      index: 60,
    },
    component: () => import('@/views/my/bill/Index.vue'),
  },
  {
    path: '/my/consume-bill',
    name: 'ConsumeBill',
    alias: [
      // @deprecated 废弃path,暂时保留访问
      '/my/bill1',
    ],
    meta: {
      index: 60,
    },
    component: () => import('@/views/my/UnpaidBills/Index.vue'),
  },
  {
    path: '/my/consume-quota',
    name: 'ConsumeQuota',
    meta: {
      index: 66,
    },
    component: () => import('@/views/my/ConsumeQuota/Index.vue'),
  },
  {
    path: '/my/bill/repayment',
    name: 'MyBillRepayment',
    meta: {
      index: 120,
    },
    component: () => import('@/views/my/bill/repayment.vue'),
  },
  {
    path: '/my/bill/pay',
    name: 'MyBillPay',
    meta: {
      index: 122,
    },
    component: () => import('@/views/my/bill/pay.vue'),
  },
  {
    path: '/my/bill/pay-result',
    name: 'MyBillPayResult',
    meta: {
      index: 123,
    },
    component: () => import('@/views/my/bill/payResult.vue'),
  },
  {
    path: '/my/otayonii',
    name: 'MyOtayonii',
    meta: {
      index: 50,
    },
    component: () => import('@/views/my/otayonii/Index.vue'),
  },
  {
    path: '/my/otayonii/withdrawal',
    name: 'MyOtayoniiWithdrawal',
    meta: {
      index: 51,
    },
    component: () => import('@/views/my/otayonii/Withdrawal.vue'),
  },
  {
    path: '/my/address',
    name: 'MyAddress',
    meta: {
      index: 50,
    },
    component: () => import('@/views/my/address/Index.vue'),
  },
  {
    path: '/my/address-form',
    name: 'MyAddressForm',
    meta: {
      index: 55,
    },
    component: () => import('@/views/my/address/Form.vue'),
  },
  {
    path: '/my/goods-order',
    name: 'MyGoodsOrder',
    meta: {
      index: 60,
    },
    component: () => import('@/views/my/goodsOrder/Index.vue'),
  },
  {
    path: '/my/goods-order-detail',
    name: 'MyGoodsOrderDetail',
    meta: {
      index: 65,
    },
    component: () => import('@/views/my/goodsOrder/Detail.vue'),
  },
  {
    path: '/my/order-logistics',
    name: 'MyOrderLogistics',
    meta: {
      index: 68,
    },
    component: () => import('@/views/my/goodsOrder/Logistics'),
  },
  {
    path: '/my/after-sales',
    name: 'MyAfterSales',
    meta: {
      index: 70,
    },
    component: () => import('@/views/my/afterSales/Index.vue'),
  },
  {
    path: '/my/after-sales-form',
    name: 'MyAfterSalesFrom',
    meta: {
      index: 75,
      title: '申请退款',
    },
    component: () => import('@/views/my/afterSales/Form.vue'),
  },
  {
    path: '/my/after-sales-detail',
    name: 'MyAfterSalesDetail',
    meta: {
      index: 80,
      title: '服务单详情',
    },
    component: () => import('@/views/my/afterSales/Detail.vue'),
  },
  {
    path: '/my/voucher',
    name: 'MyVoucher',
    meta: {
      index: 34,
      title: '我的券票',
    },
    component: () => import('@/views/my/coupon/voucher/Index'),
  },
]
export default orderRoutes
