@use './vant-ui.scss';
/**
 * 通用css样式布局处理
 */
$theme: #4671eb;
$red: #ff2323;
$orange: #f37b1d;
$yellow: #fbbd08;
$olive: #8dc63f;
$green: #39b54a;
$cyan: #1cbbb4;
$blue: #0081ff;
$purple: #6739b6;
$mauve: #9c26b0;
$pink: #e03997;
$brown: #a5673f;
$grey: #8799a3;
$black: #333333;
$darkGray: #666666;
$gray: #aaaaaa;
$ghostWhite: #f1f1f1;
$white: #ffffff;
$market: #f57984;
/* 浅色 */
$redLight: #fadbd9;
$orangeLight: #fde6d2;
$yellowLight: #fef2ce;
$oliveLight: #e8f4d9;
$greenLight: #d7f0db;
$cyanLight: #d2f1f0;
$blueLight: #cce6ff;
$purpleLight: #e1d7f0;
$mauveLight: #ebd4ef;
$pinkLight: #f9d7ea;
$brownLight: #ede1d9;
$greyLight: #e7ebed;
/* 渐变色 */
$gradualRed: linear-gradient(45deg, #f43f3b, #ec008c);
$gradualOrange: linear-gradient(45deg, #ff9700, #ed1c24);
$gradualGreen: linear-gradient(45deg, #39b54a, #8dc63f);
$gradualPurple: linear-gradient(45deg, #9000ff, #5e00ff);
$gradualPink: linear-gradient(45deg, #ec008c, #6739b6);
$gradualBlue: linear-gradient(45deg, #0081ff, #1cbbb4);
/* 阴影透明色 */
$ShadowSize: 6px 6px 8px;
$redShadow: rgba(204, 69, 59, 0.2);
$orangeShadow: rgba(217, 109, 26, 0.2);
$yellowShadow: rgba(224, 170, 7, 0.2);
$oliveShadow: rgba(124, 173, 55, 0.2);
$greenShadow: rgba(48, 156, 63, 0.2);
$cyanShadow: rgba(28, 187, 180, 0.2);
$blueShadow: rgba(0, 102, 204, 0.2);
$purpleShadow: rgba(88, 48, 156, 0.2);
$mauveShadow: rgba(133, 33, 150, 0.2);
$pinkShadow: rgba(199, 50, 134, 0.2);
$brownShadow: rgba(140, 88, 53, 0.2);
$greyShadow: rgba(114, 130, 138, 0.2);
$grayShadow: rgba(114, 130, 138, 0.2);
$blackShadow: rgba(26, 26, 26, 0.2);

$background-color: $ghostWhite;
$color: $black;

:root:root {
  --primary-color: #4671eb;
  --primary-linear-color-1: #3f58d6;
  --primary-linear-color-2: #a7bbfd;
  --primary-linear-to-bottom: linear-gradient(
    180deg,
    var(--primary-linear-color-1) 0%,
    var(--primary-linear-color-2) 100%
  );
  --safe-area-inset-top: max(35px, env(safe-area-inset-top, 0px));
  // max(0px) 有问题 会被转换为max(0) 导致失效
  --safe-area-inset-bottom: max(0vw, env(safe-area-inset-bottom, 0px));
  // --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --van-primary-color: var(--primary-color);
}
html {
  font-size: 10vw !important;
}
body {
  font-size: 14px;
}
/** 基础通用 **/
/* ==================
          布局
 ==================== */
/*  -- flex弹性布局 -- */
.theme-text {
  color: var(--primary-color) !important;
}
.theme-bg {
  background: var(--primary-color) !important;
}
.theme-border {
  border: 1px solid var(--primary-color) !important;
}
.theme-border-bg {
  background: #eff1ff !important;
}
.theme-linear-gradient {
  background: var(--primary-linear-to-bottom);
  border-radius: 24px;
}
.auxiliary-text {
  color: #ff4019 !important;
}
.auxiliary-bg {
  background: #ff4019 !important;
}
.auxiliary-border {
  border: 1px solid #ff4019 !important;
}
.flex {
  display: flex;
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.flex-sub {
  flex: 1;
}

.flex-twice {
  flex: 2;
}

.flex-treble {
  flex: 3;
}

.flex-direction {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}
.flex-shrink {
  flex-shrink: 0;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: flex-center;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}
/*  -- 内外边距 -- */

.margin-0 {
  margin: 0;
}
.margin-xs {
  margin: 5px;
}

.margin-sm {
  margin: 10px;
}

.margin {
  margin: 15px;
}

.margin-lg {
  margin: 20px;
}

.margin-xl {
  margin: 30px;
}
.margin-top-2 {
  margin-top: 2px;
}
.margin-top-xs {
  margin-top: 5px;
}

.margin-top-sm {
  margin-top: 10px;
}

.margin-top {
  margin-top: 15px;
}

.margin-top-lg {
  margin-top: 20px;
}

.margin-top-xl {
  margin-top: 30px;
}

.margin-right-xs {
  margin-right: 5px;
}

.margin-right-sm {
  margin-right: 10px;
}

.margin-right {
  margin-right: 15px;
}

.margin-right-lg {
  margin-right: 20px;
}

.margin-right-xl {
  margin-right: 30px;
}

.margin-bottom-xs {
  margin-bottom: 5px;
}

.margin-bottom-sm {
  margin-bottom: 10px;
}

.margin-bottom {
  margin-bottom: 15px;
}

.margin-bottom-lg {
  margin-bottom: 20px;
}

.margin-bottom-xl {
  margin-bottom: 30px;
}
.margin-left-2 {
  margin-left: 2px;
}
.margin-left-xs {
  margin-left: 5px;
}

.margin-left-sm {
  margin-left: 10px;
}

.margin-left {
  margin-left: 15px;
}

.margin-left-lg {
  margin-left: 20px;
}

.margin-left-xl {
  margin-left: 30px;
}

.margin-lr-xs {
  margin-left: 5px;
  margin-right: 5px;
}

.margin-lr-sm {
  margin-left: 10px;
  margin-right: 10px;
}

.margin-lr {
  margin-left: 15px;
  margin-right: 15px;
}

.margin-lr-lg {
  margin-left: 20px;
  margin-right: 20px;
}

.margin-lr-xl {
  margin-left: 30px;
  margin-right: 30px;
}

.margin-tb-xs {
  margin-top: 5px;
  margin-bottom: 5px;
}

.margin-tb-sm {
  margin-top: 10px;
  margin-bottom: 10px;
}

.margin-tb {
  margin-top: 15px;
  margin-bottom: 15px;
}

.margin-tb-lg {
  margin-top: 20px;
  margin-bottom: 20px;
}

.margin-tb-xl {
  margin-top: 30px;
  margin-bottom: 30px;
}
.margin-top-0 {
  margin-top: 0;
}
.margin-left-0 {
  margin-left: 0;
}
.margin-right-0 {
  margin-right: 0;
}
.margin-bottom-0 {
  margin-bottom: 0;
}
.padding-0 {
  padding: 0;
}

.padding-xs {
  padding: 5px;
}

.padding-sm {
  padding: 10px;
}

.padding {
  padding: 15px;
}

.padding-lg {
  padding: 20px;
}

.padding-xl {
  padding: 30px;
}

.padding-top-xs {
  padding-top: 5px;
}

.padding-top-sm {
  padding-top: 10px;
}

.padding-top {
  padding-top: 15px;
}

.padding-top-lg {
  padding-top: 20px;
}

.padding-top-xl {
  padding-top: 30px;
}

.padding-right-xs {
  padding-right: 5px;
}

.padding-right-sm {
  padding-right: 10px;
}

.padding-right {
  padding-right: 15px;
}

.padding-right-lg {
  padding-right: 20px;
}

.padding-right-xl {
  padding-right: 30px;
}

.padding-bottom-xs {
  padding-bottom: 5px;
}

.padding-bottom-sm {
  padding-bottom: 10px;
}

.padding-bottom {
  padding-bottom: 15px;
}

.padding-bottom-lg {
  padding-bottom: 20px;
}

.padding-bottom-xl {
  padding-bottom: 30px;
}

.padding-left-xs {
  padding-left: 5px;
}

.padding-left-sm {
  padding-left: 10px;
}

.padding-left {
  padding-left: 15px;
}

.padding-left-lg {
  padding-left: 20px;
}

.padding-left-xl {
  padding-left: 30px;
}

.padding-lr-xs {
  padding-left: 5px;
  padding-right: 5px;
}

.padding-lr-sm {
  padding-left: 10px;
  padding-right: 10px;
}

.padding-lr {
  padding-left: 15px;
  padding-right: 15px;
}

.padding-lr-lg {
  padding-left: 20px;
  padding-right: 20px;
}

.padding-lr-xl {
  padding-left: 30px;
  padding-right: 30px;
}

.padding-tb-xs {
  padding-top: 5px;
  padding-bottom: 5px;
}

.padding-tb-sm {
  padding-top: 10px;
  padding-bottom: 10px;
}

.padding-tb {
  padding-top: 15px;
  padding-bottom: 15px;
}

.padding-tb-lg {
  padding-top: 20px;
  padding-bottom: 20px;
}

.padding-tb-xl {
  padding-top: 30px;
  padding-bottom: 30px;
}
.padding-top-0 {
  padding-top: 0;
}
.padding-left-0 {
  padding-left: 0;
}
.padding-right-0 {
  padding-right: 0;
}
.padding-bottom-0 {
  padding-bottom: 0;
}
/* -- 浮动 --  */

.cf::after,
.cf::before {
  content: ' ';
  display: table;
}

.cf::after {
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}
/* ==================
          边框
 ==================== */

/* -- 实线 -- */

.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
  position: relative;
}

.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}

.solid::after {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-top::after {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-right::after {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-bottom::after {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-left::after {
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.solids::after {
  border: 8px solid #eee;
}

.solids-top::after {
  border-top: 8px solid #eee;
}

.solids-right::after {
  border-right: 8px solid #eee;
}

.solids-bottom::after {
  border-bottom: 8px solid #eee;
}

.solids-left::after {
  border-left: 8px solid #eee;
}

/* -- 虚线 -- */

.dashed::after {
  border: 1px dashed #ddd;
}

.dashed-top::after {
  border-top: 1px dashed #ddd;
}

.dashed-right::after {
  border-right: 1px dashed #ddd;
}

.dashed-bottom::after {
  border-bottom: 1px dashed #ddd;
}

.dashed-left::after {
  border-left: 1px dashed #ddd;
}

/* -- 阴影 -- */

.shadow[class*='white'] {
  --ShadowSize: 0 1px 6px;
}

.shadow-lg {
  --ShadowSize: 0px 0.4rem 100px 0px;
}

.shadow-warp {
  position: relative;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.shadow-warp:before,
.shadow-warp:after {
  position: absolute;
  content: '';
  top: 20px;
  bottom: 30px;
  left: 20px;
  width: 50%;
  box-shadow: 0 0.3rem 0.2rem rgba(0, 0, 0, 0.2);
  transform: rotate(-3deg);
  z-index: -1;
}

.shadow-warp:after {
  right: 20px;
  left: auto;
  transform: rotate(3deg);
}

.shadow-blur {
  position: relative;
}

.shadow-blur::before {
  content: '';
  display: block;
  background: inherit;
  filter: blur(10px);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10px;
  left: 10px;
  z-index: -1;
  opacity: 0.4;
  transform-origin: 0 0;
  border-radius: inherit;
  transform: scale(1, 1);
}
/* ==================
          文本
 ==================== */

.text-xs {
  font-size: 10px;
}

.text-sm {
  font-size: 12px;
}

.text-df {
  font-size: 14px;
}

.text-lg {
  font-size: 16px;
}

.text-xl {
  font-size: 18px;
}
.text-xxl {
  font-size: 20px;
}
.text-xxxl {
  font-size: 22px;
}

.text-sl {
  font-size: 24px;
}

.text-xsl {
  font-size: 26px;
}
.zoom-text-11 {
  font-size: 12px;
  zoom: 0.93;
}
.zoom-text-10 {
  font-size: 12px;
  zoom: 0.83;
}
.zoom-text-9 {
  font-size: 12px;
  zoom: 0.75;
}
.text-Abc {
  text-transform: Capitalize;
}

.text-ABC {
  text-transform: Uppercase;
}

.text-abc {
  text-transform: Lowercase;
}
.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-content {
  line-height: 1.6;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}
.text-red,
.line-red,
.lines-red {
  color: $red;
}

.text-orange,
.line-orange,
.lines-orange {
  color: $orange;
}

.text-yellow,
.line-yellow,
.lines-yellow {
  color: $yellow;
}

.text-olive,
.line-olive,
.lines-olive {
  color: $olive;
}

.text-green,
.line-green,
.lines-green {
  color: $green;
}

.text-cyan,
.line-cyan,
.lines-cyan {
  color: $cyan;
}

.text-blue,
.line-blue,
.lines-blue {
  color: $blue;
}

.text-purple,
.line-purple,
.lines-purple {
  color: $purple;
}

.text-mauve,
.line-mauve,
.lines-mauve {
  color: $mauve;
}

.text-pink,
.line-pink,
.lines-pink {
  color: $pink;
}

.text-brown,
.line-brown,
.lines-brown {
  color: $brown;
}

.text-grey,
.line-grey,
.lines-grey {
  color: $grey;
}

.text-gray,
.line-gray,
.lines-gray {
  color: $gray;
}

.text-black,
.line-black,
.lines-black {
  color: $black;
}

.text-white,
.line-white,
.lines-white {
  color: $white;
}
.bg-red {
  background-color: $red;
}

.bg-orange {
  background-color: $orange;
}

.bg-yellow {
  background-color: $yellow;
}

.bg-olive {
  background-color: $olive;
}

.bg-green {
  background-color: $green;
}

.bg-cyan {
  background-color: $cyan;
}

.bg-blue {
  background-color: $blue;
}

.bg-purple {
  background-color: $purple;
}

.bg-mauve {
  background-color: $mauve;
}

.bg-pink {
  background-color: $pink;
}

.bg-brown {
  background-color: $brown;
}

.bg-grey {
  background-color: $grey;
}

.bg-gray {
  background-color: #f0f0f0;
}

.bg-black {
  background-color: $black;
}

.bg-white {
  background-color: $white;
}
.bg-market {
  background-color: $market;
}

.bg-shadeTop {
  background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
  color: $white;
}

.bg-shadeBottom {
  background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
  color: $white;
}

.bg-red.light {
  color: $red;
  background-color: $redLight;
}

.bg-orange.light {
  color: $orange;
  background-color: $orangeLight;
}

.bg-yellow.light {
  color: $yellow;
  background-color: $yellowLight;
}

.bg-olive.light {
  color: $olive;
  background-color: $oliveLight;
}

.bg-green.light {
  color: $green;
  background-color: $greenLight;
}

.bg-cyan.light {
  color: $cyan;
  background-color: $cyanLight;
}

.bg-blue.light {
  color: $blue;
  background-color: $blueLight;
}

.bg-purple.light {
  color: $purple;
  background-color: $purpleLight;
}

.bg-mauve.light {
  color: $mauve;
  background-color: $mauveLight;
}

.bg-pink.light {
  color: $pink;
  background-color: $pinkLight;
}

.bg-brown.light {
  color: $brown;
  background-color: $brownLight;
}

.bg-grey.light {
  color: $grey;
  background-color: $greyLight;
}

.bg-gradual-red {
  background-image: $gradualRed;
  color: $white;
}

.bg-gradual-orange {
  background-image: $gradualOrange;
  color: $white;
}

.bg-gradual-green {
  background-image: $gradualGreen;
  color: $white;
}

.bg-gradual-purple {
  background-image: $gradualPurple;
  color: $white;
}

.bg-gradual-pink {
  background-image: $gradualPink;
  color: $white;
}

.bg-gradual-blue {
  background-image: $gradualBlue;
  color: $white;
}
.co-2C6FEC {
  color: #2c6fec;
}
.co-A8A8A8 {
  color: #a8a8a8;
}
.co-A54E21 {
  color: #a54e21;
}
.arrows-right,
.arrows-bottom,
.arrows-top {
  position: relative;
}
.arrows-right:after,
.arrows-bottom:after,
.arrows-top:after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  margin-top: -5px;
  width: 10px;
  height: 10px;
  border-top: 1px solid #4c4c4c;
  border-right: 1px solid #4c4c4c;
  opacity: 0.6;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.arrows-bottom:after {
  transform: rotate(135deg);
  margin-top: -5px;
}
.round-16 {
  border-radius: 8px;
}
.round {
  border-radius: 5000px;
}
.opacity {
  opacity: 0.5;
}
.overflow-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.overflow-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.nav-footer {
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 10px;
  background: #ffffff;
  z-index: 100;
}
/// iconfont
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.popup-title {
  height: 55px;
  line-height: 55px;
  text-align: center;
  color: #363636;
  font-size: 18px;
  font-weight: 500;
}
.my-cell {
  display: flex;
  align-items: center;
  padding-left: 13px;
  .cell-icon {
    width: 26px;
    height: 26px;
    margin-right: 8px;
  }
  .cell-content {
    flex: 1;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    padding-right: 15px;
    line-height: 20px;
    .cell-title {
      color: #333333;
    }
    .cell-value {
      color: #999999;
      display: flex;
      align-items: center;
      .text {
        margin-right: 7px;
      }
    }
  }
}
.fun-wrapper {
  margin: 10px;
  background: #ffffff;
  border-radius: 8px;
}
.vhtml-content {
  font-size: 14px;
  img {
    max-width: 100% !important;
    height: auto !important;
  }
}
.img-check-icon {
  width: 18px;
  height: 18px;
}
.external-links {
  width: 100%;
  height: 100%;
  position: relative;
}
.back-img {
  width: 32px;
  height: 32px;
}
.flex-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
}
img.radio {
  width: 16px;
  height: 16px;
}
.text-del {
  text-decoration: line-through;
}
.my-van-tabs .van-tab__text {
  font-size: 16px;
}
.my-van-tabs .van-tabs__wrap {
  height: 50px !important;
}
.my-van-tabs .van-tab {
  line-height: 50px;
}
.iphonex-top {
  padding-top: var(--safe-area-inset-top);
}
.iphonex-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}
.rich-text {
  font-size: 14px;
  p {
    margin: 10px 0;
    line-height: 1.3;
  }
  ul {
    list-style-type: disc;
    margin-left: 40px;
    li {
      line-height: 1.3;
    }
  }
}
.display-inline {
  display: inline-block;
}
.h100 {
  height: 100px;
}
.h200 {
  height: 200px;
}
.h300 {
  height: 300px;
}
.popup-container {
  width: 195px;
  padding: 20px;
  .title {
    font-size: 17px;
    font-weight: bold;
    color: #363636;
    text-align: center;
    margin-top: 3px;
  }
  .content {
    margin-top: 11px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
  }
}
.footer-btn {
  width: 306px;
  height: 48px;
  margin: 0 auto;
  text-align: center;
  line-height: 48px;
  font-size: 15px;
  color: #ffffff;
  font-weight: 500;
}
// 弧形背景
// .aaa::after {
// 	content: '';
// 	width: 120%;
// 	height: 252px;
// 	position: absolute;
// 	z-index: 0;
// 	left: -10%;     //椭圆左边隐藏10%，右边隐藏10%
// 	top: 0;
// 	border-radius: 0 0 50% 50%;  //左上角，右上角，右下角，左下角
// 	background: #1496f1;
// }
