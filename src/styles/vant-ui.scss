.van-field__label {
  font-size: 15px;
  color: #363636;
}
.van-field__value {
  font-size: 15px;
}
.van-cell__title,
.van-cell__value {
  font-size: 15px;
}
.mini-text .van-cell__title,
.mini-text .van-cell__value {
  font-size: 13px;
}
.van-notify {
  padding-top: 28px !important;
}
.van-popup.ad-popup {
  overflow-y: initial;
  background: initial;
}
.van-popup.overflow-inherit {
  overflow-y: initial;
}
.van-popup.popup-transparent {
  background: transparent !important;
}
.custom-check .van-checkbox__icon {
  height: inherit;
  font-size: 0 !important;
}
.van-search__action {
  line-height: inherit !important;
}
.van-field__left-icon {
  display: flex;
  align-items: center;
  img {
    width: 24px;
    height: 24px;
  }
}
.custom-grid-item .van-grid-item__content {
  padding: 0;
}
.van-search {
  padding: 0 10px !important;
}
.nav-bar .center form {
  display: flex;
  align-items: center;
  flex: 1;
}
.text-white .van-pull-refresh__head {
  color: #ffffff;
  .van-loading .van-loading__text {
    color: #ffffff;
  }
}
.theme-text .van-pull-refresh__head {
  color: var(--primary-color);
  .van-loading .van-loading__text {
    color: var(--primary-color);
  }
}
.footer-total .van-checkbox__label {
  font-size: 14px;
}
.van-swipe-cell__right {
  right: -1px !important;
}
.ad-popup.van-popup--center {
  top: 50% !important;
  left: 0 !important;
  right: 0 !important;
  width: fit-content !important;
  max-width: calc(100vw - 16px * 2);
  margin: 0 auto !important;
  transform: translateY(-50%) !important;
}
.full-screen.van-popup--center {
  max-width: none;
}
.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #4671eb !important;
}
.van-floating-bubble {
  background: none !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
}
.van-empty__bottom {
  font-size: 16px;
  color: #999999;
}
.van-popup.full-screen {
  display: flex;
  flex-direction: column;
  .nav-bar {
    flex: none;
  }
  .external-links {
    flex: 1;
  }
}
