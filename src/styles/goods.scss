.goods-row-style {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 10px;
  &-item {
    width: 172px;
    margin-bottom: 10px;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 5px;
    box-sizing: border-box;
    .goods-img {
      width: 100%;
      display: block;
      border-radius: 12px;
    }
    .goods-info {
      flex: 1;
      padding: 5px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .goods-title {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-weight: 400;
      }
      .goods-spec {
        margin-top: 6px;
        font-size: 12px;
        transform: scale(0.9);
        transform-origin: left top;
        color: #999999;
        height: 13px;
      }
      .goods-buy {
        margin-top: 6px;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .cart-img {
          width: 23px;
          height: 23px;
        }
      }
    }
  }
}
.goods-list-style {
  &-item {
    display: flex;
    padding: 7px;
    background: #ffffff;
    margin: 0px 10px 12px 10px;
    border-radius: 16px;
    .goods-img {
      width: 105px;
      height: 105px;
      border-radius: 4px;
    }
    .goods-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 8px;
      flex: 1;
      .goods-info-top {
        .goods-title {
          font-size: 14px;
          color: #333;
          font-weight: 400;
          line-height: 20px;
        }
        .goods-spec {
          margin-top: 1px;
          font-size: 12px;
          transform: scale(0.9);
          transform-origin: left top;
          color: #999999;
          line-height: 16px;
        }
        .goods-label {
          display: flex;
          .scale {
            border-radius: 2px;
            border: 1px solid #e9362e;
            font-size: 12px;
            transform: scale(0.8);
            transform-origin: left top;
            padding: 1px 3px;
            color: #e9362e;
            margin-top: 4px;
          }
        }
      }
      .goods-info-bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .cart-img {
          width: 23px;
          height: 23px;
        }
      }
    }
  }
}
.goods-list-style-item.round {
  border-radius: 8px;
}
.goods-info {
  .goods-price {
    width: 100%;
    position: relative;
    .goods-sales-price {
      font-weight: 600;
      font-size: 18px;
      color: #333;
      line-height: 25px;
    }
    .goods-sales-price::first-letter {
      font-size: 70%;
    }
    .sale-count {
      position: absolute;
      right: 5px;
      bottom: 4px;
      font-size: 12px;
      color: #bbbbbb;
      font-weight: normal;
      line-height: 17px;
    }
    .goods-market-price {
      font-size: 13px;
      color: #999999;
      line-height: 18px;
      display: flex;
      align-items: flex-end;
    }
    .goods-member-price {
      margin-top: 5px;
      font-size: 12px;
      height: 15px;
      color: #5d3f2d;
      background: #fbdec4;
      border-radius: 2px;
      padding-right: 5px;
      display: flex;
      align-items: center;
      img {
        width: 26px;
        height: 15px;
      }
    }
  }
}
// 购物车徽标
.action-cart .van-badge--top-right {
  top: 5px;
  right: 0;
}
// 收银台
.pay-product-header {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 15px;
  padding-right: 16px;
  .pay-info {
    display: flex;
    align-items: center;
    img {
      width: 24px;
      height: 24px;
    }
    .name {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      margin-left: 10px;
    }
  }
}
.goods-tags {
  display: flex;
  margin-top: 5px;
  overflow-x: auto;
  .tag-item {
    word-break: keep-all;
    border-radius: 4px;
    border: 1px solid #fe671a;
    margin-right: 5px;
    padding: 0 5px;
    .text {
      font-size: 12px;
      line-height: 17px;
      // transform: scale(0.8);
      transform-origin: center center;
      color: #fe671a;
    }
  }
}
// 银行卡
.bank.gongshang {
  background: url('@/assets/images/bankcard/gongshang.png');
  background-size: 100% 100%;
}
.bank.beijing {
  background: url('@/assets/images/bankcard/beijing.png');
  background-size: 100% 100%;
}
.bank.gongshang {
  background: url('@/assets/images/bankcard/gongshang.png');
  background-size: 100% 100%;
}
.bank.guangda {
  background: url('@/assets/images/bankcard/guangda.png');
  background-size: 100% 100%;
}
.bank.guangfa {
  background: url('@/assets/images/bankcard/guangfa.png');
  background-size: 100% 100%;
}
.bank.huaxia {
  background: url('@/assets/images/bankcard/huaxia.png');
  background-size: 100% 100%;
}
.bank.jianshe {
  background: url('@/assets/images/bankcard/jianshe.png');
  background-size: 100% 100%;
}
.bank.jiaotong {
  background: url('@/assets/images/bankcard/jiaotong.png');
  background-size: 100% 100%;
}
.bank.minsheng {
  background: url('@/assets/images/bankcard/minsheng.png');
  background-size: 100% 100%;
}
.bank.nongye {
  background: url('@/assets/images/bankcard/nongye.png');
  background-size: 100% 100%;
}
.bank.pufa {
  background: url('@/assets/images/bankcard/pufa.png');
  background-size: 100% 100%;
}
.bank.shanghai {
  background: url('@/assets/images/bankcard/shanghai.png');
  background-size: 100% 100%;
}
.bank.pingan {
  background: url('@/assets/images/bankcard/pingan.png');
  background-size: 100% 100%;
}
.bank.xingye {
  background: url('@/assets/images/bankcard/xingye.png');
  background-size: 100% 100%;
}
.bank.youchu {
  background: url('@/assets/images/bankcard/youchu.png');
  background-size: 100% 100%;
}
.bank.zhaoshang {
  background: url('@/assets/images/bankcard/zhaoshang.png');
  background-size: 100% 100%;
}
.bank.zhongguo {
  background: url('@/assets/images/bankcard/zhongguo.png');
  background-size: 100% 100%;
}
.bank.zhongxin {
  background: url('@/assets/images/bankcard/zhongxin.png');
  background-size: 100% 100%;
}

// 银行卡logo
.bank-logo.gongshang {
  background: url('@/assets/images/bankcard/logo/gongshang.png');
  background-size: 100% 100%;
}
.bank-logo.beijing {
  background: url('@/assets/images/bankcard/logo/beijing.png');
  background-size: 100% 100%;
}
.bank-logo.gongshang {
  background: url('@/assets/images/bankcard/logo/gongshang.png');
  background-size: 100% 100%;
}
.bank-logo.guangda {
  background: url('@/assets/images/bankcard/logo/guangda.png');
  background-size: 100% 100%;
}
.bank-logo.guangfa {
  background: url('@/assets/images/bankcard/logo/guangfa.png');
  background-size: 100% 100%;
}
.bank-logo.huaxia {
  background: url('@/assets/images/bankcard/logo/huaxia.png');
  background-size: 100% 100%;
}
.bank-logo.jianshe {
  background: url('@/assets/images/bankcard/logo/jianshe.png');
  background-size: 100% 100%;
}
.bank-logo.jiaotong {
  background: url('@/assets/images/bankcard/logo/jiaotong.png');
  background-size: 100% 100%;
}
.bank-logo.minsheng {
  background: url('@/assets/images/bankcard/logo/minsheng.png');
  background-size: 100% 100%;
}
.bank-logo.nongye {
  background: url('@/assets/images/bankcard/logo/nongye.png');
  background-size: 100% 100%;
}
.bank-logo.pufa {
  background: url('@/assets/images/bankcard/logo/pufa.png');
  background-size: 100% 100%;
}
.bank-logo.shanghai {
  background: url('@/assets/images/bankcard/logo/shanghai.png');
  background-size: 100% 100%;
}
.bank-logo.pingan {
  background: url('@/assets/images/bankcard/logo/pingan.png');
  background-size: 100% 100%;
}
.bank-logo.xingye {
  background: url('@/assets/images/bankcard/logo/xingye.png');
  background-size: 100% 100%;
}
.bank-logo.youchu {
  background: url('@/assets/images/bankcard/logo/youchu.png');
  background-size: 100% 100%;
}
.bank-logo.zhaoshang {
  background: url('@/assets/images/bankcard/logo/zhaoshang.png');
  background-size: 100% 100%;
}
.bank-logo.zhongguo {
  background: url('@/assets/images/bankcard/logo/zhongguo.png');
  background-size: 100% 100%;
}
.bank-logo.zhongxin {
  background: url('@/assets/images/bankcard/logo/zhongxin.png');
  background-size: 100% 100%;
}
.member-popup {
  background: inherit !important;
  &-context {
    position: relative;
    width: 291px;
    height: 350px;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
    }
    .btn-wrapper {
      position: absolute;
      top: 244px;
      z-index: 100;
      width: calc(100% - 48px);
      margin: 0 24px;
      .open-btn {
        height: 49px;
        background: #fa1414;
        box-shadow: 0px 3px 5px 0px rgba(250, 20, 20, 0.35);
        border-radius: 24px;
        line-height: 49px;
        text-align: center;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
      }
      .close-btn {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #717171;
        line-height: 18px;
        text-align: center;
        margin-top: 13px;
      }
    }
  }
}
// 短信
.sms-popup {
  width: 300px;
  padding: 10px 13px;
  .van-cell {
    padding: 8px 20px !important;
    border: none !important;
    background-color: #f9f9f9;
    margin-top: 10px;
  }
  .sms-popup-tip {
    margin: 15px auto;
    width: 280px;
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    border-radius: 15px;
    background: linear-gradient(180deg, rgba(244, 55, 39, 0.2) 0%, rgba(244, 55, 39, 0.0001) 100%);
    .sms-popup-tip-image {
      width: 44px;
      height: 44px;
      margin-left: 14px;
      transform: translateY(-5px);
    }
    .sms-popup-tip-text {
      font-size: 17px;
      font-weight: 400;
      color: #4671eb;
    }
  }
  .phone-text {
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #000;
    line-height: 25px;
    text-align: center;
    margin-top: 10px;
  }
  .tips {
    font-size: 14px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #747474;
    line-height: 20px;
    margin: 11px 20px 0 20px;
    text-align: center;
  }
  .phone-hide {
    margin-top: 3px;
    margin-bottom: 20px;
    font-size: 22px;
    font-weight: 500;
    color: #747474;
    line-height: 30px;
    text-align: center;
  }
  .sms-btn {
    border: none;
    color: #4671eb;
    background: none;
    font-size: 17px;
  }
  .btn-wrapper {
    display: flex;
    justify-content: space-between;
    margin: 17px 12px 19px 15px;
    .btn {
      text-align: center;
      width: 120px;
      height: 40px;
      line-height: 40px;
      border-radius: 8px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #a8a8a8;
    }
    .btn.cancel-btn {
      border: 1px solid #cccccc;
    }
    .btn.confirm-btn {
      color: #ffffff;
      // background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
      border-radius: 8px;
      background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
    }
  }
}
