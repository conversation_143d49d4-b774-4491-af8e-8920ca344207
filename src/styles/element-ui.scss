.element-group{
  position: relative;
}
.element-title{
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #363636;
  line-height: 25px;
  padding-bottom: 17px;
  padding-top: 20px;
}
.element-list{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 15px;
  .element-item{
    font-size: 16px;
    border-radius: 6px;
    background: #F7F7F7;
    border: 1px solid #F7F7F7;
    height: 42px;
    line-height: 42px;
    margin-bottom: 12px;
    text-align: center;
    color: #A8A8A8;
  }
  .element-item.active{
    color: #0864FE;
    background: #E1ECFE;
    border: 1px solid #5694FC;
  }
  .element-item.even{
    width: 150px;
  }
  .element-item.odd{
    width: 96px;
  }
  .element-item.white{
    color: #ffffff;
    background: #ffffff;
    border: 1px solid #ffffff;
  }
}
.page-number{
  position: absolute;
  right: 10px;
  top: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #A8A8A8;
  line-height: 20px;
}