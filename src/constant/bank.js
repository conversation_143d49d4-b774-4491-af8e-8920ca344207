export const bankList = [
  { code: 'zhaoshang', bankAliasName: '招商银行', bankName: '招商银行'},
  { code: 'jiaotong', bankAliasName: '交通银行', bankName: '交通银行'},
  { code: 'nongye', bankAliasName: '农业银行', bankName: '农业银行'},
  { code: 'gongshang', bankAliasName: '工商银行', bankName: '工商银行'},
  { code: 'jianshe', bankAliasName: '建设银行', bankName: '建设银行'},
  { code: 'zhongguo', bankAliasName: '中国银行', bankName: '中国银行'},
  { code: 'xingye', bankAliasName: '兴业银行', bankName: '兴业银行'},
  { code: 'zhongxin', bankAliasName: '中信银行', bankName: '中信银行'},
  { code: 'guangfa', bankAliasName: '广发银行', bankName: '广发银行'},
  { code: 'shanghai', bankAliasName: '上海银行', bankName: '上海银行'},
  { code: 'pingan', bankAliasName: '平安银行', bankName: '平安银行'},
  { code: 'minsheng', bankAliasName: '民生银行', bankName: '民生银行'},
  { code: 'pufa', bankAliasName: '浦发银行', bankName: '浦东发展银行'},
  { code: 'beijing', bankAliasName: '北京银行', bankName: '北京银行'},
  { code: 'huaxia', bankAliasName: '华夏银行', bankName: '华夏银行'},
  { code: 'youchu', bankAliasName: '邮政储蓄银行', bankName: '邮政储蓄银行'},
  { code: 'guangda', bankAliasName: '光大银行', bankName: '光大银行'}
]
export const bankCode = (name) => {
  const bank = bankList.filter(item => item.bankName.indexOf(name) > -1)
  return bank.length > 0 ? bank[0].code : ''
}