// 常量配置
const global = {
  ENV_PROD: 'production',
  GOODS_LIST_STYLE: 'goods-list-style',
  ACTIVE_LIST: [
    { phone: '恭喜尾号3987已激活额度，成功借款15000元' },
    { phone: '恭喜尾号7890获取提额特权，额度提升5000元' },
    { phone: '平台不收任何费用，近期诈骗案件高发，谨防上当受骗' },
    { phone: '恭喜尾号1880已激活额度，成功借款30000元' },
    { phone: '恭喜尾号1880已激活额度，成功借款9000元' },
    { phone: '恭喜尾号5290已激活额度，成功借款18000元' },
    { phone: '恭喜尾号6650获取提现特权，成功借款10000元' },
  ],
  MENU_PATH: [
    { path: '/', name: 'HOME', index: '0' },
    { path: '/integral', name: 'Integral', index: '1' },
    { path: '/member', name: 'Member', index: '2' },
    { path: '/my', name: 'My', index: '3' },
  ],
  CUSTOMER_SERVICE: 'https://1990121.s2.udesk.cn/im_client/?web_plugin_id=54106',
  SORT_DESC: 'DESC', // 倒序
  SORT_ASC: 'ASC', // 顺序
  CREDIT_BACK: 'CREDIT_BACK',
  THMEM: '#4671eb',
  CREDIT_SCENE_CASH: 'CASH', // 授信场景值-备用金
  CREDIT_SCENE_CONSUME: 'CONSUME', // 授信场景值-消费
  LOCAL_ROUTER_PATH: 'router_path',
  AD_POSITION_HOME_TOP: 'HOME_TOP', // 首页顶部
  AD_POSITION_HOME_POPUP: 'HOME_POPUP', // 首页弹框
  AD_POSITION_HOME_PRODUCT: 'HOME_PRODUCT', // 首页产品位
  AD_POSITION_HOME_BANNAR: 'HOME_BANNAR', // 首页bannar
  AD_POSITION_HOME_BANNAR2: 'HOME_BANNAR2', // 首页bannar2
  AD_POSITION_HOME_MEMBER: 'HOME_MEMBER', // 首页会员位
  AD_POSITION_HOME_WELFARE: 'HOME_WELFARE', // 首页福利位
  AD_POSITION_HOME_MOFANG: 'HOME_MOFANG', // 首页魔方
  AD_POSITION_HOME_FINANCE: 'HOME_FINANCE', // 首页金融
  AD_POSITION_PHONE_SKU: 'PHONE_SKU', // 手机充值SKU位置
  AD_POSITION_PHONE: 'PHONE', // 手机充值
  AD_POSITION_INSTALLMENT_PRODUCT: 'INSTALLMENT_PRODUCT', // 分期页产品位
  AD_POSITION_INSTALLMENT_WELFARE: 'INSTALLMENT_WELFARE', // 分期页福利位
  AD_POSITION_HOME_MIDDLE: 'HOME_MIDDLE', // 首页中间
  AD_POSITION_MY: 'MY', // 我的页面广告位
  AD_POSITION_INTEGRAL: 'INTEGRAL', // 积分页广告位
  AD_POSITION_INTEGRAL_WELFARE: 'INTEGRAL_WELFARE', // 积分页-福利位
  AD_POSITION_BORROW: 'BORROW', // 借钱广告位
  AD_POSITION_MY_QUOTA: 'MY_QUOTA', // 我的也额度广告位
  AD_POSITION_MY_QUOTA1: 'MY_QUOTA1', //我的额度1广告位
  AD_POSITION_MY_QUOTA2: 'MY_QUOTA2', //我的额度2广告位
  AD_POSITION_MY_QUOTA3: 'MY_QUOTA3', //我的额度3广告位
  AD_POSITION_SAVE_MONEY: 'SAVE_MONEY', // 省钱卡广告位
  AD_POSITION_MEMBER: 'MEMBER', // 会员广告位
  AD_POSITION_GIFTBAG: 'GIFTBAG', // 礼包广告位
  AD_POSITION_LOAN_FINISH: 'LOAN_FINISH', // 借款完成页
  GOODS_REGION_GOOD_LIST_TOP: 'GOOD_LIST_TOP', // 商品列表顶部
  GOODS_REGION_RECOMMEND: 'RECOMMEND', // 商品推荐区域
  GOODS_REGION_NEW: 'NEW', // 商品推荐区域
  GOODS_REGION_HOT_LIST: 'HOT_LIST',
  GOODS_REGION_PREMIUM_RECOMMEND: 'PREMIUM_RECOMMEND', // 精品推荐
  GOODS_REGION_FOLLOW: 'FOLLOW', // 猜你喜欢
  GOODS_REGION_CULLING_CONVERT: 'POINT_CULLING_CONVERT', // 精选兑换
  GOODS_REGION_INTEGRAL_EXCHANGE: 'INTEGRAL_EXCHANGE', // 积分兑换
  URL_TYPE_IN_URL: 'IN_URL', // 内部连接
  URL_TYPE_OUT_URL: 'OUT_URL', // 外部连接
  REGION_TYPE_MY: 'MY', // 我的
  // REGION_TYPE_HOME: 'HOME', // 首页中间
  REGION_TYPE_HOT: 'HOT', // 热门
  REGION_TYPE_HOME_MAIN_PRODUCR: 'HOME_MAIN_PRODUCT', // 首页主产品
  REGION_TYPE_INSTALLMENT: 'INSTALLMENT', // 分期页
  GUIDE_JOIN_PRODUCT: 'GUIDE_JOIN_PRODUCT', // 引导联登产品
  GUIDE_WHOLE_PRODUCT: 'GUIDE_WHOLE_PRODUCT', // 引导全流程产品
  GUIDE_LOAN_PRODUCT: 'GUIDE_LOAN_PRODUCT', // 现金借款引导
  ICON_FUN_MY: 'MY', // 我的页面icon
  ICON_FUN_MY_QUOTA: 'MY_QUOTA', // 我的额度icon
  ICON_FUN_APP_HOME: 'APP_HOME', // 首页icon
  ICON_FUN_APP_HOME_TOP: 'APP_HOME_TOP', // 首页顶部
  ICON_FUN_SHOP_HOME: 'SHOP_HOME', // 购买页icon
  ICON_FUN_INSTALLMENT: 'INSTALLMENT', // 分期购物icon
  ICON_FUN_MALL: 'MALL', // 商城icon
  USER_FLOW_IDCARD: 'IDCARD', //待实名
  USER_FLOW_FINISH: 'FINISH', //完成授信
  USER_FLOW_INFO: 'INFO', //需要录入基础信息
  USER_FLOW_IDCARD_NAME: 'CreditFacilities', // 授信页面name
  USER_FLOW_INFO_NAME: 'CustInfoResult', // 授信页面name
  PAY_TYPE_BILLPAY: 'BILLPAY', //支付类型，账单支付
  PAY_TYPE_ALIPAY: 'ALIPAY', //支付类型，支付宝支付
  PAY_TYPE_WECHATPAY: 'WECHATPAY', //支付类型，微信支付
  PAY_TYPE_QUICKPAY: 'QUICKPAY', //快捷支付
  PAY_TYPE_PAYLATER: 'PAYLATER', //先享后付
  DEFAULT_CHANNEL: 'C004', // 默认渠道 恰客
  CASHIER_SPEND_RECHARGE: 'SPEND_RECHARGE', // 话费充值
  CASHIER_EQUITY: 'EQUITY', // 权益类
  CASHIER_VIP_BUY: 'VIP_BUY', // 会员购买
  CASHIER_VIP_LOAN_BUY: 'VIP_LOAN_BUY', // 备用金会员购买
  CASHIER_VIP_DISCOUNT: 'VIP_DISCOUNT', // 省钱卡会员购买
  CASHIER_GOODS_ORDER: 'GOODS_ORDER', // 商品订单
  CASHLOAN_PRODUCT: 38, // 备用金产品id
  CONSUME_INSTALMENT_SCENE: 'CONSUME', // 默认授信场景值
  LOCAL_CREDIT_PRODUCT: 'creditProduct', // 授信产品
  LOCAL_CREDIT_SCENE: 'creditScene', // 授信场景
  APP_VERSION_VERIFY: 'VERIFY',
  APP_VERSION_OFFICIAL: 'OFFICIAL',
  ADDRESS_CHECKED: 'address-checked',
  PARAM_ORDERCONFIRM: 'param-orderConfirm',
  PARAM_ORDERCONFIRM_EXTRA_INFO: 'param-orderConfirm-extra-info',
  PARAM_AFTERSALES: 'param-afterSales',
  GOODS_DETAIL_ADDRESS: 'goods-detail-address',
  STORE_NAME: '轻享花',
  EDUCATION_OPTIONS: [
    // 学历
    { text: '初中', value: '1' },
    { text: '高中', value: '2' },
    { text: '中专', value: '3' },
    { text: '专科', value: '4' },
    { text: '本科', value: '5' },
    { text: '硕士', value: '6' },
    { text: '博士及以上', value: '7' },
  ],
  COMPANY_POSITION_OPTIONS: [
    // 职业
    { text: '上班族', value: '1' },
    { text: '企业主', value: '2' },
    { text: '个体户', value: '3' },
    { text: '自由职业', value: '4' },
  ],
  LIVE_STATUS_OPTIONS: [
    // 住房状况
    { text: '自置商品房', value: '1' },
    { text: '租房', value: '2' },
    { text: '集体宿舍', value: '3' },
    { text: '其他', value: '4' },
  ],
  MARRIAGE_OPTIONS: [
    // 婚姻状况
    { text: '未婚', value: '1' },
    { text: '已婚', value: '2' },
    { text: '离异', value: '3' },
    { text: '丧偶', value: '4' },
  ],
  RELATIONSHOP1_OPTIONS: [
    // 亲属关系
    { text: '父母', value: 'parent' },
    { text: '配偶', value: 'spouse' },
    { text: '子女', value: 'children' },
    { text: '兄弟姐妹', value: 'brothers' },
    { text: '其他', value: 'other' },
  ],
  RELATIONSHOP2_OPTIONS: [
    // 其他关系
    { text: '同事', value: 'colleague' },
    { text: '朋友', value: 'friend' },
    { text: '其他', value: 'classmate' },
  ],
  PERIOS_OPTIONS: [
    // 贷款时间
    { text: '1个月', value: '1' },
    { text: '3个月', value: '3' },
    { text: '6个月', value: '6' },
    { text: '12个月', value: '12' },
    { text: '24个月', value: '24' },
  ],
  LOANUSAGE_OPTIONS: [
    // 贷款用途
    { text: '个人消费', value: '个人消费' },
  ],
  LOANUSAGE_OPTIONS2: [
    // 贷款用途
    { text: '个人日常消费', value: '个人日常消费' },
    { text: '装修', value: '装修' },
    { text: '教育', value: '教育' },
    { text: '手机数码', value: '手机数码' },
    { text: '电器', value: '电器' },
    { text: '医疗', value: '医疗' },
    { text: '租房', value: '租房' },
    { text: '家具家居', value: '家具家居' },
    { text: '旅游', value: '旅游' },
    { text: '婚庆', value: '婚庆' },
  ],
  GOODS_ORDER_STATUS: {
    PAYING: '待付款',
    VERIFY: '待审核',
    WAIT_DELIVER: '待发货',
    WAIT_RECEIVING: '待收货',
    WAIT_COMMENT: '待评论',
    FINISHED: '已完成',
    CANCEL: '已取消',
  },
  ORDER_STATUS: {
    REPAY: '待付款',
    PAYING: '支付中',
    PAIED: '已付款',
    VERIFY: '人工审核',
    FAILED: '订单失败',
    PART: '订单部分完成',
    FINISH: '订单全部完成',
    REFUND_VERIFY: '退款审核',
    VERIFY_NOPASS: '审核不通过',
    REFUND: '已退款',
    REFUND_FAILED: '退款失败',
    REFUNDING: '退款中',
  },
  MEMBER_ORDER_STATUS: {
    REPAY: '待付款',
    PAYING: '支付中',
    PAIED: '已激活',
    VERIFY: '人工审核',
    FAILED: '订单失败',
    PART: '订单部分完成',
    FINISH: '订单全部完成',
    REFUND_VERIFY: '退款审核',
    VERIFY_NOPASS: '审核不通过',
    REFUND: '已退款',
    REFUND_FAILED: '退款失败',
    REFUNDING: '退款中',
  },
  ORDER_CHL_STATUS: {
    NON: '服务商未处理',
    PROCESSING: '服务商处理中',
    FINISH: '完成',
    PART: '部分完成',
    FAILED: '服务商处理失败',
  },
  ORDER_PAY_TYPE: {
    WECHATPAY: '微信支付',
    ALIPAY: '支付宝支付',
    UNIONPAY: '云闪付',
    QUICKPAY: '银行卡支付',
    BILLPAY: '订单分期',
    PAYLATER: '先享后付',
  },
  IOS_DOWN_LINK: 'https://apps.apple.com/cn/app/id6744612865',
  // IOS_DOWN_LINK: 'itms-services://?action=download-manifest&url=https://yjkjshop.oss-cn-shenzhen.aliyuncs.com/yjkjshop-oss/appstore/plist-ypg.plist',
  ANDRIOD_DOWN_LINK: 'https://appstore.chaojg.com/chaojg.apk',
}
export default global
