// import '@/plugins/dev-tools.js'

import { createApp } from 'vue'
import App from '@/App.vue'
import store from '@/store'
import { createPinia } from 'pinia'
import router from '@/router'
import '@/styles/reset.scss'
import 'vant/lib/index.css' // 全局引入样式
import '@/styles/index.scss'
import '@/styles/goods.scss'
// import 'amfe-flexible'
import vant from 'vant'
import './permission'
import directive from './directive' // directive
import { onToast, onToastSucc, onToastFail } from './utils/toast'
import {
  productRouter,
  adRouter,
  goodsRouter,
  menuRouter,
  funcRouter,
  customerService,
} from '@/utils/toRouter'
import { updateAdClick } from './api/base'
import { formatMoney, parseTime, validPhone, formatValue, px2rem } from '@/utils/common'
import appJS from '@/utils/appJS'
import VueClipboard from 'vue3-clipboard'
import logo from '@/assets/logo.png'
import global from '@/constant/Global'
import '@/assets/font/index.css' // 字体导入
import '@/utils/IsIphoneX'
import VueDOMPurifyHTML from 'vue-dompurify-html'
import NavigationBar from '@/components/NavigationBar'
import FooterFiexd from '@/components/FooterFiexd'
// import VConsole from 'vconsole'
import '@/utils/vconsole'
import Vue3Marquee from 'vue3-marquee'
// if (import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'staging') {
//   var vConsole = new VConsole()
// }
// svg图标
// import 'virtual:svg-icons-register'
// import SvgIcon from '@/components/SvgIcon'
// 注册指令
import plugins from './plugins' // plugins

const pinia = createPinia()
const app = createApp(App)
app.use(pinia)

// 全局方法挂载
app.config.globalProperties.logo = logo
app.config.globalProperties.onToast = onToast
app.config.globalProperties.onToastSucc = onToastSucc
app.config.globalProperties.onToastFail = onToastFail
app.config.globalProperties.formatMoney = formatMoney
app.config.globalProperties.validPhone = validPhone
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.formatValue = formatValue
app.config.globalProperties.updateAdClick = updateAdClick
app.config.globalProperties.$global = global
app.config.globalProperties.$productRouter = productRouter
app.config.globalProperties.$adRouter = adRouter
app.config.globalProperties.$goodsRouter = goodsRouter
app.config.globalProperties.$menuRouter = menuRouter
app.config.globalProperties.$funcRouter = funcRouter
app.config.globalProperties.$px2rem = px2rem
app.config.globalProperties.$customerService = customerService
app.config.globalProperties.appJS = appJS
directive(app)
app.use(router)
app.use(store)
app.use(VueDOMPurifyHTML)
app.use(Vue3Marquee)
app.use(VueClipboard, {
  autoSetContainer: true,
})
app.use(vant)
app.use(plugins)
// app.component('svg-icon', SvgIcon)
app.component('navigation-bar', NavigationBar)
app.component('footer-fiexd', FooterFiexd)
app.mount('#app')

window._czc = window._czc || []
;(function () {
  var um = document.createElement('script')
  um.src = 'https://s4.cnzz.com/z.js?id=1281418232&async=1'
  var s = document.getElementsByTagName('script')[0]
  s.parentNode.insertBefore(um, s)
})()

router.isReady().then(() => {
  const { channel } = router.currentRoute.value?.query
  if (channel) {
    window._czc.push(['_setCustomVar', 'channel', channel, 2])
  }
})
