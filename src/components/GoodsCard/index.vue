<template>
  <div class="goods-list-style">
    <div
      :class="`goods-list-style-item ${round ? 'round' : ''}`"
      v-for="item in goodsList"
      :key="item.spuId"
      @click="$goodsRouter(item)"
    >
      <img class="goods-img" :src="item.image" />
      <div class="goods-info">
        <div class="goods-info-top">
          <div class="goods-title overflow-2">
            {{ item.spuTitle }}
          </div>
          <!-- <div class="goods-spec overflow-1">
            {{ item.detailTitle }}
          </div> -->
          <div class="goods-tags">
            <!-- <div v-if="item.billFlag==='Y'" class="tag-item">
              <div class="text">先享后付</div>
            </div> -->
            <div v-if="item.vipBuyFlag === 'Y'" class="tag-item">
              <div class="text">分期专享</div>
            </div>
            <div class="tag-item" v-for="tag in item.goodsLabels" :key="tag">
              <div class="text">{{ tag }}</div>
            </div>
          </div>
        </div>
        <div class="goods-info-bottom">
          <div class="goods-price">
            <div class="goods-sales-price">
              ￥<span class="big-number">{{ formatValue(item.priceDown, 'split')[0] }}</span
              >.{{ formatValue(item.priceDown, 'split')[1] }}
            </div>
            <div v-if="item.saleNum" class="sale-count">已售{{ item.saleNum }}+</div>
            <!-- <div v-if="item.vipBuyFlag === 'N'" class="goods-market-price">
              ￥{{ formatMoney(item.price) }}
            </div>
            <div v-else class="goods-member-price">
              <img>
              <span>￥{{ formatMoney(item.vipPrice) }}</span>
            </div> -->
          </div>
          <!-- <img class="cart-img" src="@/assets/icons/cart.png"> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => [],
  },
  round: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
</style>