<template>
  <div class="navigation-bar">
    <slot name="left"></slot>
    <van-button v-if="back" class="btn back-btn" @click="handleBack()">
      <van-icon name="arrow-left" />
    </van-button>
    <div class="title">
      <slot>
        {{ title }}
      </slot>
    </div>
    <div class="menu-popover-wrap">
      <van-popover
        v-if="menus.length"
        :actions="menus"
        placement="bottom-end"
        :offset="[0, -10]"
        class="menu-popover"
        overlay-class="menu-popover-overlay"
        @select="onMenusSelect"
      >
        <template #reference>
          <van-button class="btn menu-btn">
            <van-icon name="ellipsis" />
          </van-button>
        </template>
      </van-popover>
    </div>
    <slot name="right"></slot>
  </div>
</template>

<script setup>
defineOptions({
  name: 'NavigationBar',
})
import { onBeforeMount } from 'vue'

const router = useRouter()
const props = defineProps({
  back: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  menus: {
    type: Array,
    default: () => [],
  },
  beforeBack: {
    type: Function,
  },
})
function onMenusSelect(evt) {
  if (evt.onClick) {
    evt.onClick()
  }
}
async function handleBack() {
  if (props.beforeBack) {
    const res = await props.beforeBack()
    if (res === false) return
  }
  router.back()
}
</script>

<style lang="scss" scoped>
.navigation-bar {
  padding-top: var(--safe-area-inset-top);
  height: 44px;
  display: flex;
  .title {
    flex: 1;
    font-size: 16px;
    // line-height: 44px;
    // text-align: center;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 40px;
  }
  .btn {
    flex: none;
    border: none;
    background: transparent;
    font-size: 16px;
    .van-icon {
      font-size: 16px;
    }
  }
  .back-btn {
    position: absolute;
  }
  .menu-popover-wrap {
    position: absolute;
    right: 0;
    // width: 40px;
    :deep(.van-popover__wrapper) {
      position: relative;
    }
  }
  .menu-btn {
    // position: absolute;
    position: relative;
  }

  // .back-btn {
  //   border: none;
  //   background: transparent;
  //   font-size: 16px;
  //   .van-icon {
  //     font-size: 16px;
  //   }
  // }
}
</style>
