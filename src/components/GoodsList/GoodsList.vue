<script setup>
import { onMounted, useTemplateRef, watch } from 'vue'
import GoodsListItem from './GoodsListItem.vue'
import MySwiper from '../MySwiper'
import { goodsList, regionGoodsPage } from '@/api/goods'
import { getAdList } from '@/api/base'
import { filter } from '@/utils/adIsShow'
import { getCurrentInstance } from 'vue'

const props = defineProps({
  listType: {
    type: Object,
    default() {
      return {
        type: '',
        value: '',
      }
    },
  },
  searchText: {
    type: String,
    default: '',
  },
  isAdShow: {
    type: Boolean,
    default: false,
  },
})
const { proxy } = getCurrentInstance()
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
})
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const listRef = ref()
const reloading = ref(false)
const swiperDatas = ref([])

const loadData = async () => {
  // console.log('loadData: ', loading.value)

  // 防止并发导致商品重复或乱序
  if (loading.value) {
    return
  }
  loading.value = true
  // console.log('商品列表 loadData: ', props.listType, props.searchText)
  try {
    let res = null
    if (props.listType.type === 'region') {
      res = await regionGoodsPage({
        ...queryParams.value,
        regionType: props.listType.value,
      })
    } else {
      res = await goodsList({
        ...queryParams.value,
        categoryId: props.listType.value,
        keywords: props.searchText.value ? props.searchText.value : undefined,
      })
    }
    const data = Array.isArray(res.data) ? res.data : []
    if (reloading.value) {
      list.value = data // 重新加载时替换数组
    } else {
      list.value = [...list.value, ...data]
    }
    if (list.value.length >= res.total) {
      finished.value = true
    } else {
      queryParams.value.pageNum++
    }
  } catch {
    finished.value = true // 防止死循环
  } finally {
    loading.value = false
    reloading.value = false
  }
}

const initSwiperDatas = async () => {
  const { data } = await getAdList({ regionType: [proxy.$global.GOODS_REGION_GOOD_LIST_TOP] })

  swiperDatas.value = await filter(data?.[proxy.$global.GOODS_REGION_GOOD_LIST_TOP] ?? [])
}

const reload = () => {
  // console.log('商品列表 reload: ')
  // 重置数据
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  reloading.value = true
  finished.value = false
  loadData()
}

onMounted(async () => {
  reload()
  await initSwiperDatas()
})

watch(
  () => props.listType,
  () => {
    reload()
  }
)

watch(
  () => props.searchText,
  () => {
    reload()
  }
)

defineExpose({
  reload,
})
</script>

<template>
  <div class="goods-list">
    <van-list
      ref="listRef"
      :loading="loading"
      :finished="finished"
      finished-text="——没有更多了——"
      @load="loadData"
      :immediate-check="false"
    >
      <div class="goods-container">
        <!-- 重新加载中时显示骨架 开始 -->
        <van-skeleton
          class="skeleton"
          v-if="reloading"
          :loading="reloading"
          v-for="i in 5"
          :key="`skeletion-${i}`"
        >
          <template #template>
            <div class="sk-item">
              <van-skeleton-image class="image" />
              <van-skeleton-paragraph class="para" />
              <van-skeleton-paragraph class="para" />
            </div>
          </template>
        </van-skeleton>
        <div class="list" v-for="(v, i) of 2">
          <!-- 广告位占位 -->
          <div v-if="props.isAdShow && i === 0 && swiperDatas.length" class="goods-swiper">
            <!-- 广告位占位 -->
            <my-swiper :swiperDatas="swiperDatas"></my-swiper>
          </div>
          <!-- 重新加载中时显示骨架 结束 -->
          <template v-for="(goods, index) of list">
            <GoodsListItem
              v-if="swiperDatas?.length ? index % 2 !== i : index % 2 === i"
              :key="goods.spuId"
              :goods="goods"
              :data-index="index"
              @click="() => $goodsRouter(goods)"
            />
          </template>
        </div>
      </div>
    </van-list>
  </div>
</template>

<style lang="scss" scoped>
.goods-list {
  // 支持滚动
  margin: 12px 10px;

  .goods-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    align-items: start;
    .list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 100%;
      min-width: 0;
      .goods-swiper {
        width: 100%;
        height: 211px;
        background: #fff;
        border-radius: 12px;
        font-size: 12px;
      }
    }

    .skeleton {
      padding: 0;

      .sk-item {
        box-sizing: border-box;
        padding: 10px;
        width: 100%;
        background-color: #fff;
        border-radius: 15px;
        // padding: 8px;
        overflow: hidden;
        text-align: left;
        padding-bottom: 8px;

        .image {
          width: 100%;
          height: 172px;
          border-radius: 15px;
          width: 100%;
        }

        .para {
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
