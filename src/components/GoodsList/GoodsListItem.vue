<script setup>
import memberPriceBg from '@/assets/images/goods/会员价背景.png'
import VipPrice from '@/components/VipPrice.vue'

const props = defineProps({
  goods: {
    type: Object,
    default() {
      return {}
    },
  },
})
</script>

<template>
  <div class="goods-list-item">
    <div class="top">
      <img class="image" :src="goods.image" />
      <div class="title overflow-2">{{ goods.spuTitle }}</div>
    </div>
    <div v-if="goods.goodsLabels?.length > 0" class="tags">
      <div class="tag-item" v-for="tag in goods.goodsLabels" :key="tag">
        <div class="tag-text">{{ tag }}</div>
      </div>
    </div>
    <div class="bottom">
      <div class="price">
        <span class="price-symbol">￥</span>
        <span class="price-integer">{{ ~~goods.priceDown }}</span>
        <span v-if="(goods.priceDown + '').split('.')[1]" class="price-decimal"
          >.{{ (goods.priceDown + '').split('.')[1] }}</span
        >
        <div v-if="goods.saleNum" class="sale-count">已售{{ goods.saleNum }}+</div>
      </div>
      <vip-price v-if="goods.memberDiscounts === 'Y'" :value="goods.vipPrice" />
      <!-- <div
        v-if="goods.memberDiscounts === 'Y'"
        class="vip-price-wrapper"
        :style="{ visibility: goods.memberDiscounts === 'Y' ? 'visible' : 'hidden' }"
      >
        <img class="bg" :src="memberPriceBg" />
        <span class="price"
          ><span class="price-symbol">￥</span
          ><span class="price-integer">{{ ~~goods.vipPrice }}</span
          ><span v-if="(goods.vipPrice + '').split('.')[1]" class="price-decimal"
            >.{{ (goods.vipPrice + '').split('.')[1] }}</span
          ></span
        >
      </div> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.goods-list-item {
  min-width: 0; // 不设置，卡片宽度会显示不正确
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: 5px;
  box-sizing: border-box;
  .top {
    .image {
      width: 100%;
      border-radius: 12px;
    }
    .title {
      padding: 0 5px;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      font-weight: 400;
    }
  }

  .tags {
    display: flex;
    margin-top: 5px;
    overflow-x: auto;
    margin: 5px 5px 0;
    .tag-item {
      word-break: keep-all;
      border-radius: 4px;
      border: 1px solid #fe671a;
      margin-right: 5px;
      padding: 0 5px;
      .tag-text {
        font-size: 12px;
        line-height: 17px;
        // transform: scale(0.8);
        transform-origin: center center;
        color: #fe671a;
      }
    }
  }

  .bottom {
    padding: 0 5px 5px;

    .price {
      position: relative;
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin-top: 4px;
      line-height: 25px;
      .price-symbol {
        font-size: 70%;
      }
      .price-decimal {
        font-size: 14px;
      }
      .sale-count {
        position: absolute;
        right: 0;
        bottom: 4px;
        font-size: 12px;
        color: #bbbbbb;
        font-weight: normal;
        line-height: 17px;
      }
    }

    .vip-price {
    }
    // .vip-price-wrapper {
    //   position: relative;
    //   line-height: 1;
    //   margin-top: 4px;

    //   .bg {
    //     width: 100%;
    //     height: 28px;
    //     display: block;
    //   }

    //   .price {
    //     position: absolute;
    //     left: 48%;
    //     top: 50%;
    //     transform: translateY(-50%);
    //     font-size: 15px;
    //     font-weight: bold;
    //     line-height: 1;
    //     margin-top: 0;
    //     color: #333;
    //     .price-symbol {
    //       font-size: 12px !important;
    //     }
    //     // .price-integer {
    //     //   font-size: 12px;
    //     // }
    //     .price-decimal {
    //       font-size: 12px;
    //     }
    //     // &::first-letter {
    //     //   font-size: 12px;
    //     //   font-weight: bold;
    //     // }
    //   }
    // }
  }
}
</style>
