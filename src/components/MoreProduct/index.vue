<template>
  <div class="item" v-for="item in list" :key="item" @click="productRouter(item)">
    <div class="title">
      <div class="product-name">
        <img class="logo" :src="item.icon">
        <span class="text">{{ item.name }}</span>
      </div>
      <div class="product-tag">{{ item.lable }}</div>
    </div>
    <div class="content">
      <div class="quota">
        <div class="amount">{{ formatMoney(item.quota, 0) }}</div>
        <div class="text">平均额度（元）</div>
      </div>
      <div class="ad-words">{{ item.adWords }}</div>
      <div class="apply-btn theme-bg">立即申请</div>
    </div>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    },
    isCheckMember: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['memberCheck'])
  const productRouter = (item) => {
    if (props.isCheckMember) {
      emit('memberCheck', item)
    } else {
      proxy.$productRouter(item)
    }
  }
</script>

<style lang="scss" scoped>
  .item{
    background: #FFFFFF;
    border-radius: 8px;
    padding: 14px 12px;
    padding-bottom: 20px;
    .title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .product-name{
        font-size: 15px;
        color: #363636;
        display: flex;
        align-items: center;
        .logo{
          width:14px;
          height: 14px;
          margin-right: 8px;
        }
      }
      .product-tag{
        font-size: 12px;
        transform: scale(0.9);
        border-radius: 1px;
        color: #CC7A2C;
        background: #FFEED7;
        padding: 2px;
      }
    }
    .content{
      display: flex;
      justify-content: space-between;
      margin-top: 7px;
      .quota{
        margin-right: 10px;
        .amount{
          font-size: 22px;
          font-weight: 500;
          color: #000000;
          font-family: Roboto-Medium, Roboto;
        }
        .text{
          font-size: 12px;
          transform: scale(0.9);
          margin-left: -5px;
          color: #868686;
          margin-top: 5px;
        }
      }
      .ad-words{
        flex:1;
        font-size: 12px;
        color: #868686;
        margin-top: 5px;
      }
      .apply-btn {
        font-size: 12px;
        width: 70px;
        height: 25px;
        line-height: 25px;
        border-radius: 13px;
        color: #ffffff;
        text-align: center;
      }
    }
  }
  .item +.item{
    margin-top: 10px;
  }
</style>