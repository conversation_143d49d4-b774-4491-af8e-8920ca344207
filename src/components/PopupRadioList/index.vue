<template>
  <div class="flex flex-direction" style="height:100%">
    <div class="radio-list-header text-center text-lg">
      {{ title }}
    </div>
    <div v-if="search" class="search-container">
      <van-search v-model="searchKey" placeholder="请输入搜索关键字" clearable @clear="onSearch" @search="onSearch" />
    </div>
    <div class="padding-xs bg-gray"></div>
    <div class="list-container flex-sub">
      <van-radio-group v-model="checked">
        <van-cell-group>
          <van-cell v-for="item in list" :key="item.id" :title="item.name" clickable @click="onConfirm(item)">
            <template #right-icon>
              <van-radio :name="item.id" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    list: {
      type: Array,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    search: {
      type: <PERSON>olean,
      default: false
    }
  })
  const searchKey = ref('')
  const checked = ref('')
  const emit = defineEmits(["on-confirm",'on-close', 'on-search'])
  const onClose = () => emit('on-close')
  // 确认
  const onConfirm = (item) => {
    checked.value = item.id
    emit('on-confirm', item)
  }
  const onSearch = () => {
    emit('on-search', searchKey.value)
  }
</script>

<style lang="scss" scoped>
  .list-container{
    overflow-y: auto;
  }
  .radio-list-header{
    line-height: 50px;
  }
  .van-cell{
    font-size: 15px !important;
  }
</style>