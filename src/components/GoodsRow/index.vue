<template>
  <div class="goods-row-style">
    <div v-for="(v, i) of 2" class="list">
      <template v-for="(item, index) in goodsList">
        <div
          class="goods-row-style-item"
          v-if="index % 2 === i"
          :key="item.spuId"
          @click="$goodsRouter(item)"
        >
          <img class="goods-img" :src="item.image" />
          <div class="goods-info">
            <div class="goods-data">
              <div class="goods-title overflow-2">
                {{ item.spuTitle }}
              </div>
              <div v-if="item.goodsLabels?.length > 0" class="goods-tags">
                <div class="tag-item" v-for="tag in item.goodsLabels" :key="tag">
                  <div class="text">{{ tag }}</div>
                </div>
              </div>
            </div>
            <div class="goods-price-wrap">
              <div class="price">
                <div class="goods-sales-price">
                  <span class="symbol">￥</span
                  ><span class="integer">{{ (item.priceDown + '').split('.')[0] }}</span
                  ><span v-if="(item.priceDown + '').split('.')[1]" class="decimal"
                    >.{{ (item.priceDown + '').split('.')[1] }}</span
                  >
                  <div v-if="item.saleNum" class="sale-count">已售{{ item.saleNum }}+</div>
                </div>
              </div>
              <vip-price v-if="item.memberDiscounts === 'Y'" :value="item?.vipPrice" />

              <!-- <div class="vip-price" v-if="item.memberDiscounts === 'Y'">
                <div class="vip-price-right">
                  <span class="symbol">¥</span><span>{{ (item.vipPrice + '').split('.')[0] }}</span>
                  <span v-if="(item.vipPrice + '').split('.')[1]" class="decimal"
                    >.{{ (item.vipPrice + '').split('.')[1] }}</span
                  >
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import VipPrice from '@/components/VipPrice.vue'
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.goods-row-style {
  .goods-title {
    font-size: 14px;
    color: #333;
    // margin: 0 10px;
    line-height: 18px;
  }
}
.goods-price-wrap {
  font-size: 0;
  .goods-sales-price {
    position: relative;
    font-size: 18px;
    font-weight: 600;
    line-height: 25px;
    color: #333;
    .symbol {
      font-size: 12px;
    }
    .decimal {
      font-size: 14px;
    }
    .sale-count {
      position: absolute;
      right: 0;
      bottom: 4px;
      font-size: 12px;
      color: #bbbbbb;
      font-weight: normal;
      line-height: 17px;
    }
  }
  .goods-sales-price::first-letter {
    font-size: 13px;
    zoom: 0.97;
    font-weight: 600;
  }
  .goods-market-price {
    margin-left: 9px;
  }
  // .vip-price {
  //   // height: 17px;
  //   // border-radius: 4px;
  //   // background: linear-gradient(90deg, #fce7ce 27.62%, #e3c6a4 98.57%);
  //   background: url('@/assets/images/goods/会员价背景.png');
  //   background-size: 100% 100%;
  //   width: 156px;
  //   height: 28px;
  //   margin-top: 2px;
  //   display: inline-flex;
  //   align-items: center;

  //   &-left {
  //     background: radial-gradient(171.88% 688.67% at -3.49% -71.88%, #b09671 0%, #34271f 100%);
  //     width: 43px;
  //     height: 17px;
  //     border-radius: 4px;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     color: #f9e1c4;
  //     .text {
  //       font-size: 12px;
  //       zoom: 0.83;
  //     }
  //   }
  //   &-right {
  //     margin: 0 8px 0 5px;
  //     margin-left: 74px;
  //     font-size: 12px;
  //     font-weight: bold;
  //     color: #403227;
  //     // span {
  //     //   display: inline-block;
  //     //   font-size: 12px;
  //     //   zoom: 0.66;
  //     //   font-weight: 400;
  //     // }
  //     .symbol {
  //       font-size: 12px;
  //     }
  //     .decimal {
  //       font-size: 12px;
  //     }
  //   }
  // }
}
</style>
