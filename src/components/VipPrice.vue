<template>
  <div class="vip-price" :class="size">
    <span class="price">
      <span class="price-symbol">￥</span>
      <span class="price-integer">{{ ~~value }}</span>
      <span v-if="value.toString().includes('.')" class="price-decimal"
        >.{{ value.toString().split('.')[1] }}</span
      >
    </span>
  </div>
</template>

<script setup>
const props = defineProps({
  value: {
    type: Number,
    default: 0,
  },
  size: {
    type: String,
    default: 'default',
  },
})
</script>

<style lang="scss" scoped>
.vip-price {
  border-image: url('@/assets/images/goods/会员价背景.png') 10 30 10 160 fill;
  border-width: 5px 10px 5px 70px;
  border-style: solid;
  border-image-width: 5px 15px 5px 80px;
  // display: flex;
  display: inline-flex;
  // width: auto;
  align-items: center;
  height: 26px;
  min-width: 140px;
  box-sizing: border-box;
  justify-content: center;
  color: #333;
  line-height: 1;
  font-size: 15px;
  font-weight: bold;
  &.small {
    height: 20px;
    border-width: 2px 10px 2px 60px;
    font-size: 14px;
    border-image-width: 3px 15px 3px 70px;
    min-width: 120px;
    .price-symbol {
      font-size: 12px;
    }
    // .price-integer {
    //   // font-size: 11px;
    // }
    .price-decimal {
      font-size: 12px;
    }
  }

  // .price {
  .price-symbol {
    font-size: 12px;
  }
  // .price-integer {
  //   font-size: 15px;
  // }
  .price-decimal {
    font-size: 12px;
  }
  // }

  // border-radius: 10px;
  // padding: 10px;
}
</style>
