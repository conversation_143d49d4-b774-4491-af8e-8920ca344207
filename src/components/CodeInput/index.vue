<template>
    <div class="verification-container">
      <input
        v-for="(code, index) in verificationCodes"
        :key="index"
        v-model="verificationCodes[index]"
        @input="handleInput(index, $event)"
        @keydown="handleKeyDown(index, $event)"
        @paste="handlePaste"
        :ref="el => inputFieldRef[index] = el"
        maxlength="1"
        type="tel"
        class="verification-input"
      />
    </div>
  </template>
  
  <script setup>
  const emits = defineEmits(['validCode']);
  const { proxy } = getCurrentInstance();
  // proxy.$refs[`input${index - 1}`]
  const verificationCodes = ref(['', '', '', '', '', '']);
  
  const handleInput = (index, event) => {
    const value = event.target.value;
    verificationCodes.value[index] = value;
  
    // 判断是否输入完成
    if (verificationCodes.value.join('').length === 6) {
      emits('validCode', verificationCodes.value.join(''));
    }
  
    // 自动跳到下一个输入框
    if (value && index < verificationCodes.value.length - 1) {
      const nextInput = event.target.nextElementSibling;
      if (nextInput) {
        nextTick(() => {
          nextInput.focus();
        });
      }
    }
  }
  
  const handleKeyDown = (index, event) => {
    // 处理删除操作
    if (event.key === 'Backspace' && !event.target.value && index > 0) {
      const prevInput = event.target.previousElementSibling;
      if (prevInput) {
        nextTick(() => {
          prevInput.focus();
        });
      }
    }
  };
  const inputFieldRef = ref([]);
  const handlePaste = (event) => {
    const clipboardData = event.clipboardData || window.clipboardData;
    const pastedText = clipboardData.getData('text');
    const codes = pastedText.trim().substring(0, 6).split('');
    alert(codes)
    verificationCodes.value = codes.concat(Array(6 - codes.length).fill(''));
  
    // nextTick 方法来确保在更新 DOM 之后设置焦点。我们通过 $refs.inputField 引用最后一个输入框，并使用 focus 方法将焦点设置在最后一个输入框中
    nextTick(() => {
      const lastInput = inputFieldRef.value[verificationCodes.value.length - 1];
      if (lastInput) {
        lastInput.focus();
      }
    });
  };
  // 获取焦点
  const init = () => {
    nextTick(() => {
      inputFieldRef.value[0].focus();
      verificationCodes.value = ['', '', '', '', '', '']
    })
  }
  defineExpose({ init })
  </script>
  <style lang="scss" scoped>
  .verification-container {
    display: flex;
  }
  
  .verification-input {
    width: 38px;
    margin-right: 10px;
    text-align: center;
    font-size: 20px;
    border-bottom: 1px solid #D8D8D8;
    &:last-child {
      margin-right: 0;
    }
  }
  
  .verification-input:focus {
    outline: none;
    // border-color: #007bff;
    // box-shadow: 0 0 5px #007bff;
  }
  </style>