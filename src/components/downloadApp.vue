<template>
  <div v-if="maskIsShow" class="mask" id="mask" @click="toggleMask(false)">
    <div class="info">
      <span>点击右上角的</span>
      <div class="bg">
        <img src="@/assets/images/download/dian.png" />
      </div>
      <div class="flex align-center margin-top-xs">
        <span>选择在浏览器打开</span>
        <img src="@/assets/images/download/browser.png" class="browser" />
      </div>
      <img class="arrow" src="@/assets/images/download/arrow.png" />
    </div>
  </div>

  <van-dialog
    v-model:show="copyDialogIsShow"
    title="当前环境不支持下载文件"
    message="请手动复制下载链接，前往浏览器下载。"
    class="copy-dialog"
  >
    <p class="message">请手动复制下载链接，前往浏览器下载。</p>
    <input type="text" v-model="currentUrl" readonly />
    <!-- <textarea rows="2" readonly>{{ currentUrl }}</textarea> -->
  </van-dialog>
  <!-- <div class="copy-tip">
    <p>当前环境不支持下载文件，下载地址已复制到剪贴板，请前往浏览器下载。</p>
  </div> -->
</template>

<script setup>
// import { showToast } from 'vant'
import global from '@/constant/Global'
import ua from '@/utils/ua'
import { useToggle } from '@vueuse/core'
import { onMounted } from 'vue'
// import appJs, { env } from '@/utils/appJS'
// import copyToClipboard from 'copy-to-clipboard'
// import clipboard from 'clipboardy'
// import { Dialog, showDialog } from 'vant'
const [maskIsShow, toggleMask] = useToggle(false)
const [copyDialogIsShow, toggleCopyDialogIsShow] = useToggle(false)
const currentUrl = location.href
// 是否是uniapp内的webview

// const isUniApp = computed(() => !!env.value.nvue)

onMounted(async () => {
  // console.log('onMounted')
  // document.addEventListener('visibilitychange', () => {
  //   console.log('visibilitychange', document.visibilityState, document.hidden)
  //   if (document.visibilityState === 'hidden') {
  //     showToast('页面标签页隐藏')
  //   }
  // })
})

async function download() {
  // ios微信通过扫码或长按图片识别二维码以及公众号链接是可以下载app的，从聊天消息点击链接打开页面禁止下载app
  // 安卓的微信禁止下载app

  // const a = document.createElement('a')
  // a.href = global.ANDRIOD_DOWN_LINK
  // a.download = 'yj.apk'
  // console.log(a)
  // a.click()

  // location.href = 'http://*************:4200/'
  // window.open(global.ANDRIOD_DOWN_LINK, '_self')
  // const currentUrl = location.href

  // setTimeout(async () => {
  //   console.log(document.visibilityState)
  //   console.log(location.href)
  //   // await clipboard.write(currentUrl)

  //   // 当前环境支持
  //   if (navigator.clipboard) {
  //     showDialog({
  //       title: '当前环境不支持下载文件',
  //       message: '下载地址已复制到剪贴板，请前往浏览器下载。',
  //     })
  //   }

  //   const copyed = copyToClipboard(currentUrl, {
  //     // debug: true,
  //     format: 'text/plain',
  //     message: '当前环境不支持下载文件，请手动复制下载链接，前往浏览器下载。',
  //   })
  //   console.log(copyed)
  //   if (copyed) {
  //     showDialog({
  //       title: '当前环境不支持下载文件',
  //       message: '下载地址已复制到剪贴板，请前往浏览器下载。',
  //     })
  //   }
  //   // copyToClipboard(location.href, {
  //   //   debug: true,
  //   //   message: '当前环境不支持下载文件，下载地址已复制到剪贴板，请前往浏览器下载。',
  //   // })
  //   // showToast('当前环境不支持下载文件，下载地址已复制到剪贴板，请前往浏览器下载。')
  // }, 1000)

  if (ua.os.is('ios')) {
    // location.href = global.IOS_DOWN_LINK
    window.open(global.IOS_DOWN_LINK, '_self')
    return
  }
  // showToast('下载')

  if (ua.browser.is('WeChat') || ua.browser.is('QQBrowser') || ua.browser.is('QQBrowserLite')) {
    window.location.href = 'http://a.app.qq.com/o/simple.jsp?pkgname=com.chaojg.app'
    setTimeout(() => {
      toggleMask(true)
    }, 500)
    return
  }

  // app内不支持下载文件，需要从外部浏览器打开链接
  // if (isUniApp.value) {
  //   appJs.appOtherWebView(currentUrl, '下载', true)
  //   return
  // }

  window.open(global.ANDRIOD_DOWN_LINK, '_self')

  // try {
  //   if (!navigator.clipboard) {
  //     throw new Error('当前环境不支持复制到剪贴板')
  //   }
  //   await navigator.clipboard.writeText(currentUrl)
  // } catch (e) {
  //   // 自动复制失败
  //   toggleCopyDialogIsShow(true)
  //   return
  // }

  // showDialog({
  //   title: '当前环境不支持下载文件',
  //   message: '下载地址已复制到剪贴板，请前往浏览器下载。',
  // })
}

defineExpose({
  download,
  toggleMask,
})
</script>

<style lang="scss" scoped>
.mask {
  // width: 100%;
  // height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: 0.7);
  z-index: 9999;
  .info {
    position: relative;
    color: #ffffff;
    padding-top: 40px;
    padding-left: 30px;
    font-size: 18px;
    .bg {
      background: #ffffff;
      border-radius: 2px;
      color: #000;
      padding: 0 3px;
      display: inline-block;
      height: 15px;
      margin-left: 2px;
      img {
        width: 15px;
      }
    }
    .browser {
      width: 18px;
      height: 18px;
      display: inline-block;
      margin-left: 2px;
    }
    .arrow {
      width: 80px;
      position: absolute;
      right: 10px;
      top: 20px;
    }
  }
}
.copy-dialog {
  .message {
    color: #999;
    text-align: center;
    font-size: 14px;
    margin-top: 8px;
  }
  input {
    width: 100%;
    display: block;
    box-sizing: border-box;
    padding: 10px;
    border: none;
    outline: none;
    font-size: 14px;
    color: #333;
    background: #f5f5f5;
    margin-top: 8px;
  }
  // textarea {
  //   width: 100%;
  //   // height: 100%;
  //   border: none;
  //   outline: none;
  //   display: block;
  //   padding: 10px;
  //   box-sizing: border-box;
  // }
}
</style>
