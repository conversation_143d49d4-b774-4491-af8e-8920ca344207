<template>
  <van-popup
    v-model:show="show"
    safe-area-inset-bottom
    teleport="body"
    :style="{ height: '100%', width: '100%' }"
    class="full-screen"
  >
    <navigation-bar :isShowBack="false" :pageName="title">
      <template #nav-left>
        <van-icon name="cross" size="22" class="text-gray" @click="show = false" />
      </template>
    </navigation-bar>
    <iframe class="external-links" :src="url" scrolling="auto" frameborder="0" id="iframe"></iframe>
  </van-popup>
</template>

<script setup>
const show = ref(false)
const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
})
defineExpose({ show })
</script>

<style lang="scss" scoped>
.content {
  font-size: 14px;
  margin: 15px;
  margin-top: 50px;
}
</style>
