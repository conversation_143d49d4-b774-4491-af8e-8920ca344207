<template>
  <div class="device-info-collect">
    <div ref="d1" class="" style="width: 100vw; height: 100vh; position: absolute">
      <!-- {{ d1Size }} -->
    </div>
    <div ref="d2" class="" style="width: 100dvw; height: 100dvh; position: absolute">
      <!-- {{ d2Size }} -->
    </div>
  </div>
</template>

<script setup>
import deviceId from '@/utils/deviceId'
import { useElementSize, useScreenSafeArea, useWindowSize, useCssVar } from '@vueuse/core'
import { onMounted, reactive, nextTick, toValue, useTemplateRef, onUnmounted, watch } from 'vue'
// import { cloneDeep, toPlainObject } from 'lodash-es'
import ua from '@/utils/ua'
import { useStore } from 'vuex'

const d1Ref = useTemplateRef('d1')
const d2Ref = useTemplateRef('d2')

const screenSafeArea = reactive(useScreenSafeArea())
const windowSize = reactive(useWindowSize())
const htmlSize = reactive(useElementSize(document.documentElement))
const bodySize = reactive(useElementSize(document.body))
const store = useStore()

const user = computed(() => store.getters.userInfo)

const d1Size = reactive(useElementSize(d1Ref))
const d2Size = reactive(useElementSize(d2Ref))

// const info = computed(() => ({
//   deviceId,
//   screenSafeArea,
//   windowSize,
//   screen: {
//     availHeight: window.screen.availHeight,
//     availWidth: window.screen.availWidth,
//     availLeft: window.screen.availLeft,
//     availTop: window.screen.availTop,
//     height: window.screen.height,
//     width: window.screen.width,
//   },
//   visualViewport: {
//     height: window.visualViewport?.height,
//     width: window.visualViewport?.width,
//     scale: window.visualViewport?.scale,
//     offsetTop: window.visualViewport?.offsetTop,
//     offsetLeft: window.visualViewport?.offsetLeft,
//   },
//   htmlSize,
//   bodySize,
//   ua,
//   userId: user.value?.id ?? null,
//   // css: {
//   vw: d1Size.width,
//   vh: d1Size.height,
//   dvw: d2Size.width,
//   dvh: d2Size.height,
//   // },
//   // d1Size,
//   // d2Size,
// }))

onMounted(async () => {
  screenSafeArea.update()
  await nextTick()

  onUnmounted(
    watch(
      () => user.value?.id,
      (userId = null) => {
        // 友盟事件有长度限制，所以分多次保存
        // window?._czc?.push([
        //   '_trackEvent',
        //   '[用户设备]',
        //   '[设备ID]',
        //   JSON.stringify({ userId, deviceId }),
        //   1,
        // ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[ua]',
          JSON.stringify({
            userId,
            deviceId,
            ua,
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[screen]',
          JSON.stringify({
            userId,
            deviceId,
            screen: {
              availHeight: window.screen.availHeight,
              availWidth: window.screen.availWidth,
              availLeft: window.screen.availLeft,
              availTop: window.screen.availTop,
              height: window.screen.height,
              width: window.screen.width,
            },
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[visualViewport]',
          JSON.stringify({
            userId,
            deviceId,
            visualViewport: {
              height: window.visualViewport?.height,
              width: window.visualViewport?.width,
              scale: window.visualViewport?.scale,
              offsetTop: window.visualViewport?.offsetTop,
              offsetLeft: window.visualViewport?.offsetLeft,
            },
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[screenSafeArea]',
          JSON.stringify({
            userId,
            deviceId,
            screenSafeArea,
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[windowSize]',
          JSON.stringify({
            userId,
            deviceId,
            windowSize,
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[htmlSize]',
          JSON.stringify({
            userId,
            deviceId,
            htmlSize,
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[bodySize]',
          JSON.stringify({
            userId,
            deviceId,
            bodySize,
          }),
          1,
        ])
        window?._czc?.push([
          '_trackEvent',
          '[用户设备]',
          '[vwvh]',
          JSON.stringify({
            userId,
            deviceId,
            vw: d1Size.width,
            vh: d1Size.height,
            dvw: d2Size.width,
            dvh: d2Size.height,
          }),
          1,
        ])
      }
    )
  )

  // onUnmounted(
  //   watch(
  //     info,
  //     (value) => {
  //       console.log(JSON.parse(JSON.stringify(value)))
  //       // 友盟统计
  //       window?._czc?.push([
  //         '_trackEvent',
  //         '[用户设备]',
  //         '[上报设备信息]',
  //         JSON.stringify(value),
  //         1,
  //       ])
  //     },
  //     {
  //       deep: true,
  //     }
  //   )
  // )
})
</script>

<style lang="scss" scoped>
.device-info-collect {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  // z-index: 10000;
  z-index: -100;
  opacity: 0;
  // display: none;

  pointer-events: none;
  font-size: 14px;
  line-height: 1.2;
  overflow-y: auto;
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  box-sizing: border-box;
}
</style>
