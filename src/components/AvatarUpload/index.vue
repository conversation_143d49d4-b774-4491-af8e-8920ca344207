<template>
  <div>
    <van-popup :safe-area-inset-bottom="true" v-model:show="showChoose" :style="{background:'inherit'}" position="bottom">
        <div class="choose-wrapper">
          <div class="camera item solid-bottom">
            <label for="camera">拍照</label>
            <input type="file" id="camera" accept="image/*" class="file-input" capture="camera" @change="updateFile" />
          </div>
          <div class="photo-album item">
            <label for="photoAlbum">从相册选择</label>
            <input type="file" id="photoAlbum" accept="image/*" mutiple="mutiple" class="file-input" @change="updateFile" />
          </div>
        </div>
        <div class="cancel-wrapper" @click="showChoose = false">取消</div>
      </van-popup>
    <van-popup
      class="bg-tran"
      v-model:show="showCropper"
      :style="{ width: '100%', height: '100%' }"
    >
      <div class="flex-column-center height100">
        <vueCropper
          ref="cropper"
          :img="option.img"
          :outputSize="option.outputSize"
          :outputType="option.outputType"
          :info="option.info"
          :full="option.full"
          :autoCropWidth="option.autoCropWidth"
          :autoCropHeight="option.autoCropHeight"
          :canMove="option.canMove"
          :canMoveBox="option.canMoveBox"
          :original="option.original"
          :autoCrop="option.autoCrop"
          :fixed="option.fixed"
          :fixedNumber="option.fixedNumber"
          :centerBox="option.centerBox"
          :infoTrue="option.infoTrue"
          :fixedBox="option.fixedBox"
          :high="option.high"
          :mode="option.mode"
        ></vueCropper>
        <div class="popup_bottom" :class="{ 'iphonex-bottom': isIphoneX }">
          <div class="bottom_item"><span @click="cancelCropper">取消</span></div>
          <div class="bottom_item"><span @click="rotateImage"><van-icon name="replay" /></span></div>
          <div class="bottom_item text-blue"><span @click="getCropBlob">确定</span></div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
  import 'vue-cropper/dist/index.css'
  import { VueCropper }  from 'vue-cropper'
  import { showToast,showFailToast } from 'vant'
  import { uploadBase64 } from '@/api/base'
  import { saveCustInfo } from '@/api/customer'
  const  isIphoneX = window.isIphoneX
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const cameraFileRef = ref(null)
  const photoAlbumFileRef = ref(null)
  const data = reactive({
    option: {
      img: '',
      outputSize: 0.8,
      info: false, // 裁剪框的大小信息
      outputType: 'jpeg', // 裁剪生成图片的格式
      canScale: true, // 图片是否允许滚轮缩放
      autoCrop: true, // 是否默认生成截图框
      autoCropWidth: window.innerWidth - 100 + 'px', // 默认生成截图框宽度
      autoCropHeight: window.innerWidth - 100 + 'px', // 默认生成截图框高度
      high: true, // 是否按照设备的dpr 输出等比例图片
      fixedBox: true, // 固定截图框大小 不允许改变
      fixed: true, // 是否开启截图框宽高固定比例
      fixedNumber: [1, 1], // 截图框的宽高比例
      full: true, // 是否输出原图比例的截图
      canMoveBox: false, // 截图框能否拖动
      original: false, // 上传图片按照原始比例渲染
      centerBox: false, // 截图框是否被限制在图片里面
      infoTrue: false, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
      mode: '100% auto' // 图片默认渲染方式
    }
  })
  const showCropper = ref(false)
  const imageAccept = ref('/jpg,/png,/jpeg')
  const imageFileName = ref('')
  const cropper = ref(null)
  const showChoose = ref(false)
  const { option } = toRefs(data)
  const updateFile = (event) => {
    showChoose.value = false
    beforeRead(event.target.files[0])
  }
  const beforeRead = (file) => {
    if (!imageAccept.value.includes(getFileSuffix(file.type))) {
      return showToast('请上传 jpg/png 格式图片')
    }
    showCropper.value = true
    imageFileName.value = file.name
    // 本地图片转成base64，用于截图框显示本地图片
    imageToBase64(file)
    document.getElementById("camera").value=''
    document.getElementById("photoAlbum").value=''
  }
  const getFileSuffix = (fileName) => {
    return fileName.match(/\/\w+$/)[0].toLowerCase()
  }
  // 将本地图片转化为Base64，否则vue-cropper组件显示不出要本地需要剪裁的图片
  const imageToBase64 = (file) => {
    let reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      // 截图框中的图片
      option.value.img = reader.result
    }
    reader.onerror = function (error) {
      console.log('Error: ', error)
    }
  }
  // 旋转图片
	const rotateImage = () => {
    cropper.value.rotateRight()
  }
  // 取消截图上传头像
  const cancelCropper = () => {
	  showCropper.value = false // 隐藏切图遮罩
	}
  // 获取截图
  const getCropBlob = async () =>{
    cropper.value.getCropData((data) => {
      if(data.length > 1024 * 1024) {
        imgCompress(data)
      } else {
        uploadSubmit(data)
      }
    })
  }
  const uploadSubmit = (base64) => {
    showToast('上传中', 0)
    uploadBase64({base64, fileName: imageFileName.value}).then(res=> {
      saveCustInfo({custInfo: {avatar: res.link}}).then(res => {
        showToast('头像更改成功！')
        store.dispatch('GetInfo')
        showCropper.value = false // 隐藏切图遮罩
      })
    }).catch(() => {
      showFailToast('头像更改失败！')
    })
  }
  // 压缩图片
  const imgCompress = (base64) => {
    let quality = 1
    if( 1024 * 1024 < base64.length < 1024 * 1024 * 3) {
      quality = 0.5
    } else if(base64.length > 1024 * 1024 * 3){
      quality = 0.1
    }
    let canvas = document.createElement("canvas"); //创建画布
    let ctx = canvas.getContext("2d");
    let img = new Image()
    img.src = base64
    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      base64 = canvas.toDataURL("image/jpeg", quality) //将图片转为Base64 之后预览要用
      uploadSubmit(base64)
    }
  }
  defineExpose({ showChoose, beforeRead })
</script>

<style lang="scss" scoped>
  .height100 {
    position: relative;
    height: 100%;
  }
  .flex-column-center{ 
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
  }
  .vue-cropper {
      background: #000;
      .cropper-view-box {
        outline: 1px solid #fff !important;
        outline-color: #fff !important;
    }
  }
  .popup_bottom{
    position: fixed;
    background: #ffffff;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    .bottom_item{
      flex:1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
  }
  .file-input{
    display: none;
  }
  .choose-wrapper{
    background: #FFFFFF;
    border-radius: 14px;
    margin: 0 15px;
    .item{
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #363636;
      text-align: center;
      padding: 18px;
    }
    label{
      display: inline-block;
      width: 100%;
    }
  }
  .cancel-wrapper{
    height: 57px;
    background: #FFFFFF;
    border-radius: 14px;
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #363636;
    text-align: center;
    line-height: 57px;
    margin: 9px 15px 15px;
  }
</style>