<template>
  <van-popup round v-model:show="show">
    <div class="confirm-popup">
      <div class="title">{{ title }}</div>
      <div class="content">
        <div class="btn cancel text-black" @click.stop="show = false">{{ cancelText }}</div>
        <div class="btn theme-linear-gradient text-white" @click.stop="onConfirm">
          {{ confirmText }}
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
const show = ref(false)
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  confirmText: {
    type: String,
    default: '确认',
  },
})
const emit = defineEmits(['onConfirm'])
const onConfirm = () => {
  show.value = false
  emit('onConfirm')
}
defineExpose({ show })
</script>

<style lang="scss" scoped>
.confirm-popup {
  width: 235px;
  background: #ffffff;
  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;
    .btn {
      width: 90px;
      height: 32px;
      border-radius: 7px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
    .btn.cancel {
      background: #eeeeee;
    }
  }
}
</style>