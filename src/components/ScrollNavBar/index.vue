<template>
    <div class="page" @scroll="onScrollChange" :style="pageStyle">
      <navigation-bar
        :pageName="pageName"
        @onLeftClick="onBackClick"
        :nav-bar-style="navBarStyle"
      ></navigation-bar>
      <div class="page-content" :class="{ 'iphonex-top': isIphoneX, 'iphonex-bottom': isIphoneX }">
        <slot></slot>
      </div>
    </div>
  </template>

  <script setup>
    const isIphoneX = window.isIphoneX
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const router = useRouter()
    const user = computed(() => store.getters.userInfo)
    const scrollTopValue= ref(-1)
    const ANCHOR_SCROLL_TOP = 64
    const props = defineProps({
        pageName: {
            type: String,
            default: ''
        },
        pageStyle: {
            type: Object,
            default: () => {}
        },
        rgbColor: {
            type: String,
            default: '244,88,75'
        },
        customBack: {
            type: Boolean,
            default: false
        }
    })
    const emit = defineEmits(['back'])
    const data = reactive({
      navBarStyle: {
        backgroundColor: '',
        position: 'fixed',
        'border-bottom': 'none'
      },
    })
    const { navBarStyle } = toRefs(data)
    const onScrollChange = ($e) => {
      scrollTopValue.value = $e.target.scrollTop
      let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
      navBarStyle.value.backgroundColor = 'rgba('+ props.rgbColor +', ' + opacity + ')'
      navBarStyle.value.zIndex = 9999
    }
    const onBackClick = () => {
      if (props.customBack) {
        emit('back')
        return
      }
      router.go(-1)
    }
  </script>

  <style lang="scss" scoped>
    .page{
      height: 100%;
      width: 100%;
      overflow: hidden;
      overflow-y: auto;
      position: absolute;
      .page-content{
        padding-top: 69px;
      }
      .page-content.iphonex-top{
        // padding-top: 88px !important;
        padding-top: calc(44px + var(--safe-area-inset-top));
      }
    }
  </style>