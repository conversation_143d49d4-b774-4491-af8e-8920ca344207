<template>
  <div class="flex flex-direction" style="height:100%">
    <div class="check-list-header flex justify-between align-center bg-gray text-blue padding-lr-sm">
      <span class="text-lg" @click="onClose">取消</span>
      <span class="text-lg" @click="onConfirm">确定({{chooseNum}}个)</span>
    </div>
    <div v-if="search" class="search-container">
      <van-search v-model="searchKey" placeholder="请输入搜索关键字" clearable @clear="onSearch" @search="onSearch" />
    </div>
    <div class="all-check flex justify-between padding-lr padding-bottom-sm padding-top">
      <span class="text-lg">全部</span>
      <van-checkbox v-model="allChecked" @change="toggleAll" />
    </div>
    <div class="padding-xs bg-gray"></div>
    <div class="list-container flex-sub">
      <van-checkbox-group v-model="checked" ref="checkboxGroup" @change="onChangeCheck">
         <van-cell-group>
          <van-cell
            v-for="(item, index) in list"
            clickable
            :key="item"
            :title="item.name"
            @click="toggle(index)"
          >
            <template #right-icon>
              <van-checkbox
                :name="item"
                :ref="el => checkboxRefs[index] = el"
                @click.stop
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    list: {
      type: Array,
      default: () => {}
    },
    search: {
      type: Boolean,
      default: false
    }
  })
  const searchKey = ref('')
  const chooseNum = ref(0)
  const checked = ref([]);
  const allChecked = ref(false)
  const checkboxRefs = ref([])
  const checkboxGroup = ref(null)
  const toggle = (index) => {
    checkboxRefs.value[index].toggle()
  };
  const onChangeCheck = () => {
    chooseNum.value = checked.value.length
  }
  // 全选
  const toggleAll = () => {
    checkboxGroup.value.toggleAll(allChecked.value)
  }
  // 初始化
  onBeforeUpdate(() => {
    checkboxRefs.value = []
  })
  const emit = defineEmits(["on-confirm",'on-close', 'on-search'])
  const onClose = () => emit('on-close')
  // 确认
  const onConfirm = () => {
    emit('on-confirm', checked, props.list.length)
  }
  const onSearch = () => {
    allChecked.value = false
    emit('on-search', searchKey.value)
  }
</script>

<style lang="scss" scoped>
  .list-container{
    overflow-y: auto;
  }
  .van-cell{
    font-size: 15px !important;
  }
</style>