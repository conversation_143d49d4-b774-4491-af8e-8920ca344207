<script setup>
import { ref } from 'vue'
import AgreementPopup from '@/components/AgreementPopup'

const agree = defineModel({ default: false })
const agreementRef = ref()

const onClick = () => {
  console.log('点击')
  agreementRef.value.show = true
  agree.value = true
}
</script>

<template>
  <div class="agreement">
    <van-checkbox v-model="agree" checked-color="#4671eb" icon-size="16" />
    <div class="text">
      我已阅读并知晓
      <span class="highlight" @click="onClick">《个人信息查询及使用授权》</span>
    </div>
    <agreement-popup
      url="/agreement/personal-info-auth.htm"
      title="个人信息查询及使用授权"
      ref="agreementRef"
    ></agreement-popup>
  </div>
</template>

<style lang="scss" scoped>
.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px auto;
  .text {
    margin-left: 6px;
    font-size: 12px;
    font-weight: 400;
    font-family: Roboto;
    color: rgba(0, 0, 0, 0.3);
  }
  .highlight {
    color: #4671eb;
    cursor: pointer;
  }
}
</style>