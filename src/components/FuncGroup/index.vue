<template>
  <div class="func-group" v-if="list.length > 0">
    <div class="func-group-item" :style="itemStyle" v-for="(item, index) in list" :key="index" @click="$funcRouter(item)">
      <img :style="{width: $px2rem(imgWidth+'px')}" :src="item.icon">
      <div class="text" :style="textStyle">{{ item.name }}</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
    list: {
      type: Array,
      default: () => []
    },
    itemStyle: {
      type: Object,
      default: {
        'flex': '0 0 25%'
      }
    },
    imgWidth: {
      type: Number,
      default: 36
    },
    background: {
      type: String,
      default: '#ffffff'
    },
    textStyle: {
      type: Object,
      default: {
        'color': '#333333',
        'margin-top': '3px'
      }
    }
  })
</script>

<style lang="scss" scoped>
  .func-group {
    display: flex;
    flex-flow: row wrap;
    padding: 0px 10px;
    .func-group-item{
      text-align: center;
      img{
        display: block;
        margin: 0 auto;
      }
      .text{
        font-size: 12px;
        transform: scale(0.9);
        font-weight: 400;
        line-height: 16px;
      }
    }
  }
</style>