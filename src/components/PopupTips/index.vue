<template>
  <van-popup round v-model:show="show">
    <div class="popup-container">
      <div class="title">{{ title }}</div>
      <div class="content">
        {{ content }}
      </div>
      <div class="btn theme-linear-gradient" @click="onClick">{{ btnText }}</div>
    </div>
  </van-popup>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    btnText: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['onClick'])
  const show = ref(false)
  const onClick = () => {
    show.value = false
    emit('onClick')
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .popup-container{
    width: 195px;
    padding: 20px;
    .title{
      font-size: 17px;
      font-weight: bold;
      color: #363636;
      text-align: center;
      margin-top: 3px;
    }
    .content{
      margin-top: 11px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 18px;
    }
    .btn{
      margin-top: 20px;
      height: 36px;
      border-radius: 18px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 36px;
    }
  }
</style>
