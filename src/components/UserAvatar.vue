<template>
  <img :src="url" alt="用户头像" class="user-avatar" />
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const user = computed(() => store.getters.userInfo)

const defaultAvatars = Object.values(
  import.meta.glob('@/assets/images/avatars/default/*.png', { import: 'default', eager: true })
)
const maleAvatars = Object.values(
  import.meta.glob('@/assets/images/avatars/male/*.png', { import: 'default', eager: true })
)
const femaleAvatars = Object.values(
  import.meta.glob('@/assets/images/avatars/female/*.png', { import: 'default', eager: true })
)

const url = computed(() => {
  if (user.value?.custIdCard?.gender === '1') {
    return maleAvatars[user.value.id % maleAvatars.length]
  }

  if (user.value?.custIdCard?.gender === '2') {
    return femaleAvatars[user.value.id % femaleAvatars.length]
  }

  return defaultAvatars[(user.value?.id ?? 0) % defaultAvatars.length]
})
</script>

<style lang="scss" scoped>
.user-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #e4eaff;
  border: 1px solid #fff;
}
</style>
