<template>
  <div class="product">
    <div class="title">
      <div class="name">{{ modelValue.name }}</div>
      <div class="label">{{ modelValue.lable }}</div>
    </div>
    
    <div class="content">
      <div class="quota">
        <div class="text">可用额度（元）</div>
        <div class="amount">{{ formatMoney(modelValue.quota, 0) }}</div>
        <div class="tips">可用额度即将失效，请尽快提现</div>
      </div>
      <div class="btn theme-text" @click="productRouter()">去提现</div>
    </div>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {}
    },
    isCheckMember: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['memberCheck'])
  const productRouter = () => {
    if (props.isCheckMember) {
      emit('memberCheck', props.modelValue)
    } else {
      proxy.$productRouter(props.modelValue)
    }
  }
</script>

<style lang="scss" scoped>
  .product{
    background: url('@/assets/images/top-product-bg.png');
    background-size: 100% 100%;
    padding: 0 22px;
    margin-top: 10px;
    overflow:hidden;
    padding-bottom: 23px;
    .title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 13px 0;
      .name{
        font-size: 16px;
        color: #ffffff;
        font-weight: 500;        
      }
      .label{
        font-size: 13px;
        color: #ffffff;
        opacity: 0.5;
      }
    }
    .content{
      padding-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      overflow:hidden;
      .quota{
        .tips{
          background-size: 100% 100%;
          font-size: 12px;
          color: #FFFFFF;
          padding: 3px 7px;
          background-color: rgb(255, 255, 255, 0.26);
          border-radius: 10px;
          margin-top: 5px;
        }
        .text{
          font-size: 12px;
          margin-top: 3px;
          color: #ffffff;
          opacity: 0.5;
        }
        .amount{
          font-size: 29px;
          font-family: Roboto-Medium, Roboto;
          font-weight: 500;
          color: #FFFFFF;
          margin-top: 5px;
          line-height: 35px;
        }
      }
      .btn{
        width: 78px;
        height: 35px;
        background: #FFFFFF;
        border-radius: 18px;
        text-align: center;
        color: #F52E26;
        line-height: 35px;
        font-size: 13px;
        margin-bottom: 6px;
      }
    }
  }
</style>