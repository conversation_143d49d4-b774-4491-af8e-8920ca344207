<template>
  <van-popup
    v-model:show="show"
    safe-area-inset-bottom
    :style="{ height: '100%', width: '100%'}"
  >
    <navigation-bar :isShowBack="false" pageName="产品服务协议">
      <template #nav-left>
        <van-icon name="cross" size="22" class="text-gray" @click="show=false"/>
      </template>
    </navigation-bar>
    <iframe class="external-links" src="/agreement/product-service.htm" scrolling="auto" frameborder="0" id="iframe"></iframe>
  </van-popup>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const show = ref(false)
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .content{
    font-size: 14px;
    margin: 15px;
    margin-top: 50px;
  }
</style>