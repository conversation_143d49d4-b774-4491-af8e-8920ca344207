<template>
  <div class="search" :style="searchStyle">
    <img class="search-icon" :src="icon" alt="" srcset="" />
    <span class="search-hint">请输入搜索关键字</span>
  </div>
</template>

<script setup>
import searchIcon from '@/assets/icons/search-icon.png'
const props = defineProps({
  bgColor: {
    default: '#ffffff',
    type: String
  },
  hintColor: {
    default: '#999999',
    type: String
  },
  icon: {
    default: searchIcon,
    type: String
  }
})
const searchStyle = computed(() => {
  return {
    backgroundColor: props.bgColor
  }
})
const hintStyle = computed(() => {
  return {
    color: props.hintColor
  }
})
</script>

<style lang="scss" scoped>
.search {
  background-color: rgba(255, 255, 255, 0.3);
  width: 100%;
  margin: 6px;
  border-radius: 16px;
  height: 32px;
  display: flex;
  align-items: center;
  .search-icon {
    margin-left: 8px;
    width: 16px;
  }
  .search-hint {
    margin-left: 8px;
    font-size: 12px;
    color: #999999;
  }
}
</style>
