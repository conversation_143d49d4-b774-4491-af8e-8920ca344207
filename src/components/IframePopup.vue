<template>
  <van-popup
    class="iframe-popup"
    :show="show"
    closeable
    teleport="body"
    position="bottom"
    @close="cancel('close')"
  >
    <header>
      <span>{{ title }}</span>
    </header>
    <main>
      <iframe :src="src" frameborder="0"></iframe>
    </main>
    <!-- <footer></footer> -->
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onCancel, onConfirm } = useConfirmDialog()

const title = defineModel('title', { default: '' })
const src = defineModel('src', { default: '' })
const show = defineModel({ default: false })
onReveal((data) => {
  title.value = data.title
  src.value = data.src
  show.value = true
})
onCancel(() => {
  show.value = false
  title.value = ''
  src.value = ''
})
onConfirm(() => {
  show.value = false
  title.value = ''
  src.value = ''
})

defineExpose({
  reveal,
  cancel,
})
</script>

<style lang="scss" scoped>
.iframe-popup {
  width: 100vw;
  width: 100dvw;
  height: 100vh;
  height: 100dvh;
  max-width: 100vw;
  max-width: 100dvw;
  max-height: 100vh;
  max-height: 100dvh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  :deep() {
    .van-popup__close-icon {
      top: calc(var(--van-popup-close-icon-margin) + var(--safe-area-inset-top));
    }
  }
  header {
    // width: 100%;
    height: 44px;
    background: #fff;
    box-sizing: content-box;
    padding-top: var(--safe-area-inset-top);
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
    font-size: 16px;
  }
  main {
    flex: 1;
    min-height: 0;
    iframe {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  // footer {
  //   height: 44px;
  //   background: #fff;
  // }
}
</style>
