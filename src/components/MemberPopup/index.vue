<template>
  <van-popup class="receive-popup overflow-inherit" v-model:show="show" round :close-on-click-overlay="false">
    <div class="receive-wrap">
      <div class="header">
        <div class="vip-name">轻享花会员</div>
        <div class="vip-desc">权益月均预计可省<span>800</span>元</div>
      </div>
      <div class="popup-list">
        <div class="item" v-for="(item, index) in viewList" :key="index">
          <img class="item-img" :src="item.image">
          <div class="content solid-bottom">
            <div class="left">
              <div class="title">{{ item.title }}</div>
            </div>
            <div class="right">{{ item.desc }}</div>
          </div>
        </div>
      </div>
      <div class="btn-wrapper">
        <div class="btn no-open" @click="handleCancel">暂不开通</div>
        <div class="btn open" @click="handelMember">立即开通</div>
      </div>
    </div>
    <van-icon class="receive-popup-close" name="close" @click="show=false" />
  </van-popup>
</template>

<script setup>
  import { getMemberConfig, getMemberView } from '@/api/member'
  const router = useRouter()
  const store = useStore()
  const isFirst = ref(0)
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    path: {
      type: String,
      default: ''
    },
    sceneCode: {
      type: String,
      default: ''
    }
  })
  const show = ref(false)
  const user = computed(() => {
    if(store.getters.userInfo) {
      getConfig()
    }
    return store.getters.userInfo
  })
  onMounted(() => {
    if(user.value) {
      getConfig()
    }
  })
  const getConfig = () => {
    if(isFirst.value === 0) {
      isFirst.value ++ // 防住多次获取
      getMemberConfig({ sceneCode: props.sceneCode }).then(res => {
        data.configData = res.data
        if (res.data.popUpFlag !== 'N') {
          getMemberView({ partnerId: res.data.partnerId,
            virtualMsg: { viewCode: 'WINDOWS' }}).then(res => {
              viewList.value = res.data.WINDOWS
          })
        }
      })
    }
  }
  const data = reactive({
    configData: {}
  })
  const viewList = ref([])
  const emit = defineEmits(['cancel'])
  const handleCancel = () => {
    show.value = false
    if(data.configData.popUpFlag === 'Y') {
      emit('cancel')
    }
  }
  // 是否弹框
  const memberCheck = () => {
    if(user.value) {
      let result = false
      if(data.configData.popUpFlag === 'Y' || data.configData.popUpFlag === 'FORCE') {
        show.value = true
        result = true
      }
      return result
    } else {
      proxy.appJS.appLogin()
    }
  }
  //开通会员
  const handelMember = () => {
    show.value = false
    router.push('/member?partnerId='+data.configData.partnerId)
  }
  defineExpose({ memberCheck })
</script>

<style lang="scss" scoped>
  .receive-wrap{
    width: 315px;
    padding-bottom: 20px;
    .header{
      height: 110px;
      background-size: 100% 100%;
      padding-left: 20px;
      .vip-name{
        padding-top: 28px;
        font-size: 22px;
        font-family: PingFang SC;
        font-weight: bold;
        color: #F5D5B0;
      }
      .vip-desc{
        margin-top: 8px;
        font-size: 15px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #F5D5B0;
        span{
          font-size: 21px;
          font-weight: bold;
        }
      }
    }
    .popup-list{
      padding: 0 20px;
      max-height: 245px;
      overflow-y: auto;
      .item{
        display: flex;
        align-items: center;
        height: 70px;
        &-img{
          width: 22px;
          height: 22px;
        }
        .content{
          display: flex;
          flex: 1;
          justify-content: space-between;
          align-items: center;
          margin-left: 10px;
          height: 70px;
          .left{
            .title{
              font-size: 16px;
              font-family: PingFang SC;
              font-weight: bold;
              color: #1C2631;
            }
            .desc{
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left top;
              font-family: PingFang SC;
              font-weight: 400;
              color: #666666;
              margin-top: 10px;
            }
          }
          .right{
            font-size: 15px;
            font-family: PingFang SC;
            font-weight: bold;
            color: #C6AB79;
            margin-right: 10px;
          }
        }
      }
    }
    .btn-wrapper{
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      padding-top: 10px;
      .btn{
        width: 130px;
        height: 49px;
        line-height: 49px;
        background: #FFFFFF;
        border: 1px solid #666666;
        border-radius: 24px;
        font-size: 15px;
        color: #666666;
        text-align: center;
      }
      .btn.open{
        background: #37394D;
        color: #F5D5B0;
      }
    }
    .agreement{
      padding-top: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #9B9B9B;
      .img-icon{
        width: 15px;
        height: 15px;
        flex-shrink: 0;
        margin-right: 2px;
      }
    }
  }
  .receive-popup-close{
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    color: #FFFFFF;
    font-size: 26px;
  }
</style>
