<script setup>
const searchText = defineModel({ default: '' })

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  }
})
</script>

<template>
  <van-search class="search-bar" v-model="searchText" :disabled="props.disabled" placeholder="搜索商品名称">
    <template #left-icon>
      <img class="icon" src="@/assets/images/search-bar/搜索.png" />
    </template>
  </van-search>
</template>

<style lang="scss" scoped>
.search-bar {
  display: flex;
  align-items: center;
  flex: 1;
  border: 1px solid #fff;
  border-radius: 64px;
  height: 32px;
  line-height: 32px;
  padding: 0 16px;

  .icon {
    width: 19px;
    height: 19px;
    margin-left: 4px;
    margin-right: 2px;
  }

  :deep(input) {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #fff;
    font-size: 16px;
    margin-bottom: 1px;

    &::placeholder {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      color: #fff;
    }
  }

  :deep(.van-search__content) {
    padding-left: 0;
  }

  :deep(.van-icon-clear) {
    color: #fff;
  }

  --van-search-background: rgba(255,255,255, 30%);
  --van-search-content-background: none;
  --van-field-input-disabled-text-color: #fff;
}
</style>