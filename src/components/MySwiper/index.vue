<template>
  <van-swipe
    class="my-swipe"
    :style="swipeStyle"
    :autoplay="3000"
    lazy-render
    indicator-color="white"
    @change="onChange"
  >
    <van-swipe-item
      v-for="item in adList"
      :key="item.id"
      @click="$adRouter(item)"
      :touchable="false"
    >
      <img :src="item.pic" />
    </van-swipe-item>
  </van-swipe>
</template>

<script setup>
import { add as addAdEventRecord } from '@/utils/adEventRecord.js'
import { filter } from '@/utils/adIsShow'

const props = defineProps({
  swiperDatas: {
    type: Array,
    default: () => [],
  },
  swipeStyle: {
    type: Object,
    default: () => {},
  },
})

const currentIndex = ref(0)

const adList = ref([])
// console.log('adList', adList)
defineExpose({
  list: adList,
})
watch(
  () => props.swiperDatas,
  async (newValue) => {
    adList.value = await filter(newValue)
    trackEvent(currentItem.value)
  },
  { immediate: true }
)
const currentItem = computed(() => adList.value[currentIndex.value])

function onChange(index) {
  // console.log(index)
  currentIndex.value = index
  trackEvent(currentItem.value)
}
// import { until } from '@vueuse/core'
// import adPosition from '@/utils/adPosition.js'
// import store from '@/store'

function trackEvent(item) {
  // const user = store.getters.userInfo
  // until(adPosition.isReady)
  //   .toBe(true)
  //   .then(() => {
  //     window._czc.push([
  //       '_trackEvent',
  //       `[广告][曝光][${adPosition.state.value?.[item.regionType] ?? item.regionType}][${
  //         item.id
  //       }][${item.name}]`,
  //       '[广告曝光]',
  //       JSON.stringify({
  //         userId: user?.id,
  //         partnerId: user?.partnerId,
  //         adId: item.id,
  //         adName: item.name,
  //       }),
  //       1,
  //     ])
  //   })
  addAdEventRecord(item, 'EXPOSURE')
}

onMounted(() => {
  // props.swiperDatas?.[0] && trackEvent(props.swiperDatas[0])
  trackEvent(currentItem.value)
})

onActivated(() => {
  trackEvent(currentItem.value)
})
</script>

<style lang="scss" scoped>
.my-swipe {
  border-radius: 8px;
  img {
    width: 100%;
    display: block;
  }
}
</style>
