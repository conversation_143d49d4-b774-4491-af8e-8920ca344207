<template>
  <van-popup
    v-model:show="show"
    closeable
    close-icon-position="top-left"
    safe-area-inset-top
    safe-area-inset-bottom
    :style="{ height: '100%', width: '100%'}"
    >
    <div v-html="content" class="content"></div>
  </van-popup>
</template>

<script setup>
  import { productAgreement } from '@/api/product'
  const props = defineProps({
    modelValue: {
      type: Number
    }
  })
  watch(() => props.modelValue, (newValue, oldValue) => {
    getAgreement()
  })
  const show = ref(false)
  const content = ref('')
  const getAgreement = () => {
    productAgreement({idList: [props.modelValue]}).then(res => {
      content.value = res.data[0].protocolContent
    })
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .content{
    font-size: 14px;
    margin: 15px;
    margin-top: 50px;
  }
</style>