<template>
  <van-popup
    v-model:show="show"
    teleport="body"
    closeable
    class="customer-service overflow-inherit"
    :style="{
      width: $px2rem('280px'),
      borderRadius: $px2rem('25px'),
      // background:
      // 'linear-gradient(180deg, #4671eb -9.29%, #F5B8B3 18.46%, #F6C7C3 22.72%, #F6D6D3 27.27%, #F6E6E5 33.27%, #F6F6F6 41.06%)',
    }"
  >
    <div class="kefu">
      <!-- <img class="kefu-img" src="@/assets/images/components/kefu-img.png"> -->
      <div class="title">请选择您要联系的客服</div>
      <div class="decs">若您遇到问题，请尝试以下方式联系客服</div>
      <div class="action-wrapper">
        <div class="phone" @click="callNumber">电话客服 ：{{ phone }}</div>
        <div v-if="false" class="inline" @click="handleInline">
          <img style="width: 19px; height: 18px" src="@/assets/icons/深色客服.png" />
          <span style="margin-left: 6px">在线客服</span>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { getAppVersion } from '@/utils/auth'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const show = ref(false)
const props = defineProps({
  phone: {
    type: String,
    default: '18320801743',
  },
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const handleInline = () => {
  show.value = false
  proxy.$customerService()
}
const callNumber = () => {
  show.value = false
  const phone = props.phone.replaceAll(' ', '')
  window.location.href = 'tel:' + phone
}
defineExpose({ show })
</script>
<style scoped lang="scss">
.customer-service {
  background: linear-gradient(180deg, #aabefb 10%, #d6dffa 20%, #f4f6fa 30%);

  :deep(.van-popup__close-icon) {
    color: #2246b7;
  }
}

.kefu {
  position: relative;
  border-radius: 20px;

  .title {
    font-size: 17px;
    color: #2246b7;
    font-weight: 600;
    text-align: center;
    margin-top: 25px;
  }

  .decs {
    font-size: 14px;
    font-weight: 400;
    color: #2246b7;
    opacity: 0.5;
    margin-top: 13px;
    text-align: center;
  }

  .action-wrapper {
    background-color: #fff;
    border-radius: 25px;
    padding: 16px 16px 23px 16px;
    margin-top: 14px;
  }

  .inline {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 43px;
    border: 1px solid #4671eb;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 400px;
    color: #4671eb;
    text-align: center;
    margin-top: 12px;
  }

  .phone {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: #fff;
    background-color: #4671eb;
    border-radius: 25px;
    padding: 12px 0;
  }

  .cancel {
    margin-top: 30px;
    font-size: 20px;
    color: #2f80ed;
    line-height: 25px;
    text-align: center;
    font-weight: bold;
  }
}
</style>
