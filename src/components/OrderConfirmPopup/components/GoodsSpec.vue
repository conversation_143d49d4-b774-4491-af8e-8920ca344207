<template>
  <div class="goods-spec" v-if="spuInfo.specType === 'MORE'">
    <div class="spec-item" v-for="(item, index) in goodsSpecData" :key="index">
      <div class="spec-title">{{ item.value }}</div>
      <div class="flex spec-flex">
        <div
          :class="`spec-value ${item.checked == leaf.id ? 'checked' : ''}`"
          v-for="(leaf, index2) in item.leaf"
          :key="index2"
          @click="tabSpec(index, index2)"
        >
          {{ leaf.value }}
        </div>
      </div>
    </div>
    <div class="goods-number">
      <span class="number-label">数量</span>
      <van-stepper
        v-model="skuNum"
        integer
        min="1"
        @change="changeNum"
        button-size="24px"
        input-width="40px"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const emit = defineEmits(['updateSpecSku', 'updateSkuNum'])

const props = defineProps({
  spuInfo: {
    type: Object,
    default() {
      return {}
    },
  },
  goodsSpecData: {
    type: Array,
    default() {
      return []
    },
  },
  skuList: {
    type: Array,
    default() {
      return []
    },
  },
})

const spuInfo = computed(() => props.spuInfo)
const goodsSku = computed(() => props.goodsSku)
const goodsSpecData = computed(() => props.goodsSpecData)
const skuList = computed(() => props.skuList)
const skuNum = defineModel('skuNum')

// 规格点击
const tabSpec = (index, index2) => {
  const specList = goodsSpecData.value
  const leaf = specList[index].leaf[index2]
  specList[index].checked = leaf.id
  skuList.value.forEach((sku) => {
    let i = 0
    sku.specs.forEach(function (skuSpec) {
      specList.forEach(function (spec) {
        if (spec.id == skuSpec.specId && spec.checked == skuSpec.specValueId) {
          i = i + 1
        }
      })
    })
    if (i === specList.length) {
      // 选取了全部规格、
      emit('updateSpecSku', specList, sku)
    }
  })
}

const changeNum = (val) => {
  if (!val) return
  emit('updateSkuNum', val)
}
</script>

<style lang="scss" scoped>
.goods-spec {
  overflow-y: auto;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  margin: 10px;

  .spec-item {
    margin-bottom: 35px;

    .spec-title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
      line-height: 20px;
      margin-bottom: 4px;
    }

    .spec-flex {
      flex-flow: row wrap;

      .spec-value {
        padding: 6px 11px;
        background: #f5f5f5;
        border-radius: 5px;
        font-size: 14px;
        color: #333;
        margin-right: 15px;
        margin-top: 12px;
        border: 1px solid #f4f4f4;
      }

      .spec-value.checked {
        color: var(--primary-color);
        background: color-mix(in srgb, var(--primary-color) 10%, white);
        border: 1px solid var(--primary-color);
      }
    }
  }

  .goods-number {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .number-label {
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }

    margin-bottom: 10px;
    margin-right: 5px;
  }
}
</style>