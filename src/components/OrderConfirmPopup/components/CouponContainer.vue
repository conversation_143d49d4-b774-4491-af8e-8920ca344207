<template>
  <div class="coupon">
    <div class="label">优惠券</div>
    <div class="right">
      <div class="checked-text" @click="handleSelect">
        <span v-if="orderInfo.tickets?.length > 0">
          <span v-if="checkedCoupon.ticketNo">{{ checkedCoupon.name }}</span>
          <span v-else>请选择优惠券</span>
        </span>
        <span v-else class="text-gray">无可用优惠券</span>
      </div>
      <van-icon name="arrow" color="#222222"></van-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedCoupon = inject('checkedCoupon')
const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['showCoupon'])
const handleSelect = () => {
  if(props.orderInfo.tickets?.length > 0) {
    emit('showCoupon')
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .coupon{
    display: flex;
    height: 46px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 0 12px;
    background: #ffffff;
    margin: 10px;
    .label{
      font-size: 14px;
      color: #0E0E0E;
    }
    .right{
      font-size: 12px;
      color: #FA2528;
      display: flex;
    }
  }
</style>