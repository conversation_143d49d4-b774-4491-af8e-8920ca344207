<template>
  <div class="coupon-item">
    <div class="coupon-ticket isUse">
      <div class="coupon-amount-container" v-if="data.ticketDefine">
          <!-- 固定金额 -->
          <div class="coupon_amount" v-if="data.ticketDefine.discountType == 'FIXED'">
              <span>￥</span>{{  data.ticketDefine.ticketDiscount && data.ticketDefine.ticketDiscount.value }}
          </div>
          <!-- 折扣 -->
          <div class="coupon_amount" v-else-if="data.ticketDefine.discountType == 'PERCENT'">
              {{  data.ticketDefine.ticketDiscount && data.ticketDefine.ticketDiscount.value }}<span>折</span>
          </div>
          <div class="coupon_limit">{{ data.ticketDefine.ruleDesc }}</div>
      </div>
      <div class="coupon-content-container">
          <div class="coupon-content" v-if="data.ticketDefine">
            <div class="name">{{ data.name }}</div>
            <div class="remark" v-if="data.ticketDefine.ticketThreshold && data.ticketDefine.ticketThreshold.type === 'MIN_AMOUNT'">满{{ data.ticketDefine.ticketThreshold.amount }}元可用</div>
            <div class="time">{{ secondsToDate(data.startTime) }}~{{ secondsToDate(data.endTime) }}</div>
            <div class="srouce">
              来源于{{ data.receiveSource === 'TAKE' ? '客户主动领取': 
              data.receiveSource === 'GRANT' ? '系统发放' : data.receiveSource === 'ORDER' ? '普通商品购买' : 
              data.receiveSource === 'BENEFIT' ? '会员权益购买' : data.receiveSource === 'AUTO' ? '系统赠送' : 
              data.receiveSource ==='VIP' ? '' : '会员权益领取' }}
            </div>
          </div>
      </div>
    </div>
</div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { secondsToDate } from '@/utils/date'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  unavailable: {
    type: Boolean,
    default: false
  }
})
const checkedCoupon = inject('checkedCoupon')
</script>
<style scoped lang='scss'>
  .coupon-item{
    margin-bottom: 12px;
  }
    .coupon-ticket{
        width: 343px;
        height: 95px;
        background: url('@/assets/images/market/coupon-bg.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        box-sizing: border-box;
        .coupon-amount-container{
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
          width: 114px;
          height: 100%;
          .coupon_amount{
            font-family: DIN, DIN;
            font-size: 32px;
            color: #704B35;
            font-weight: bold;
            span{
              font-size: 16px;
            }
          }
          .coupon_limit{
            font-size: 14px;
            color: #704B35;
            line-height: 16px;
            margin-top: 5px;
          }
        }

        .coupon-content-container{
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            padding: 10px 20px;

            .coupon-content{
                display: flex;
                flex-flow: column;
                justify-content: center;
                height: 100%;
                .name{
                  font-size: 14px;
                  color: #704B35;
                  font-weight: bold;
                  line-height: 16px;
                }
                .time{
                  font-size: 12px;
                  color: rgba(112,75,53,0.6);
                  line-height: 14px;
                  margin-top: 2px;
                }
                .srouce,.remark{
                  margin-top: 4px;
                  font-size: 12px;
                  color: rgba(112,75,53,0.6);
                  margin-top: 4px;
                  line-height: 14px;
                }
            }

            .coupon-btn{
                font-weight: bold;
                font-size: 12px;
                color: #FFFFFF;
                text-align: center;
                button{
                    min-width: 55px;
                    height: 24px;
                    line-height: 24px;
                    background: #C2C2C2;
                    border-radius: 12px;
                    border: none;
                }
            }

            .coupon-tag{
                position: absolute;
                top: 0;
                right: 0;
                width: 54px;
                height: 43px;
                
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }

        &.isUse{
            .coupon_amount{
              color: #704B35;
            }
            .coupon_limit{
              color: #704B35;
            }
        }


    }

    .coupon-rule{
        background: #F7F7F7;
        border-radius: 0px 0px 8px 8px;
        padding: 10px;
        div{
            font-weight: 400;
            font-size: 12px;
            color: #7B7B7B;
            line-height: 18px;
            text-align: left;
            word-wrap: break-word;
        }
        &.isUse{
            background: #FDF8F9;
        }
    }
</style>