<template>
  <div class="goods-info">
    <div class="goods-img">
      <img :src="goodsSku.thumbPics ? goodsSku.thumbPics : spuInfo.thumbPics" />
    </div>
    <div class="goods-price-checked">
      <div class="price-container">
        <div class="price">
          ￥{{ goodsSku.salesPrice ? goodsSku.salesPrice : spuInfo.priceDown }}
        </div>
        <vip-price v-if="goodsSku.vipPrice" :value="goodsSku.vipPrice" size="small" ></vip-price>
        <!-- <div class="vip-price-bar" v-if="goodsSku.vipPrice !== undefined">
          <img class="vip-bg" src="@/assets/images/goods/详情页会员价背景小.png" />
          <div class="vip-price">￥{{ formatMoney(goodsSku.vipPrice) }}</div>
        </div> -->
      </div>
      <div class="checked" v-if="spuInfo.specType === 'MORE'">
        <div class="checked-label">已选：</div>
        <div class="checked-value flex">
          <div
            class="checked-item flex overflow-1"
            v-for="(item, index) in goodsSpecData"
            :key="index"
          >
            <template v-if="item.checked">
              <div v-for="(item2, index2) in item.leaf" :key="index2">
                <div v-if="item.checked == item2.id">{{ item2.value }}/</div>
              </div>
            </template>
          </div>
          <div class="checked-label">{{ skuNum }}{{ spuInfo.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watchEffect } from 'vue'
import VipPrice from '@/components/VipPrice.vue'

const props = defineProps({
  spuInfo: {
    type: Object,
    default() {
      return {}
    },
  },
  goodsSku: {
    type: Object,
    default() {
      return {}
    },
  },
  goodsSpecData: {
    type: Object,
    default() {
      return {}
    },
  },
  skuNum: {
    type: Number,
    default: 1,
  },
})

const spuInfo = computed(() => props.spuInfo)
const goodsSku = computed(() => props.goodsSku)
const goodsSpecData = computed(() => props.goodsSpecData)

watchEffect(() => {
  console.log('spuInfo', spuInfo.value)
  console.log('goodsSku', goodsSku.value)
})

const skuNum = computed(() => props.skuNum)
</script>

<style lang="scss" scoped>
.goods-info {
  display: flex;
  align-items: flex-end;
  margin: 10px;

  .goods-img {
    border-radius: 10px;

    img {
      width: 64px;
      height: 64px;
      border-radius: 15px;
      margin-right: 10px;
    }
  }

  .goods-price-checked {
    margin-bottom: 16px;

    .price-container {
      display: flex;
      align-items: center;

      .price {
        font-size: 24px;
        font-weight: bold;
        color: #e9362e;

        .mini-font {
          font-size: 16px;
        }
      }

      .price::first-letter {
        font-size: 70%;
      }

      .vip-price-bar {
        min-width: 115px;
        height: 20px;
        border-radius: 14px;
        background: linear-gradient(171.92deg, #fff4e9 15.23%, #ffecd1 116.86%);
        display: flex;
        align-items: center;
        margin-left: 10px;

        .vip-bg {
          width: 64px;
          height: 20px;
          display: inline-block;
        }

        .vip-price {
          font-size: 12px;
          color: #333;
          font-weight: bold;
          margin-left: -10px;
          margin-right: 8px;
        }

        .vip-price::first-letter {
          font-size: 9px;
          font-weight: bold;
        }
      }
    }

    .checked {
      margin-top: 6px;
      color: #999999;
      font-size: 14px;
      font-weight: 400;
      transform-origin: left top;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: flex;

      .checked-label {
        white-space: nowrap;
      }
    }
  }
}
.vip-price {
  margin-left: 10px;
}
</style>
