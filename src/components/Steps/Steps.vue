<script setup>
const emit = defineEmits(['clickItem'])

const props = defineProps({
  step: {
    type: Number,
    default: 2,
  },
  items: {
    type: Array,
    default() {
      return []
    },
  },
})

const onClickItem = (index) => {
  emit('clickItem', index)
}
</script>

<template>
  <div class="steps">
    <div class="steps-container">
      <div class="step" v-for="(_, index) of props.items" @click="() => onClickItem(index)">
        <div class="step-item inactive" v-if="props.step < index">
          <img src="@/assets/images/components/steps/step-inactive.png" />
          <div class="index">{{ index + 1 }}</div>
        </div>
        <div class="step-item active" v-if="props.step === index">
          <img src="@/assets/images/components/steps/step-active.png" />
        </div>
        <div class="step-item complete" v-if="props.step > index">
          <img src="@/assets/images/components/steps/step-complete.png" />
        </div>
      </div>
    </div>
    <div class="text-container">
      <div class="text" v-for="(item, index) of props.items" @click="() => onClickItem(index)">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.steps {
  padding: 10px;
  background-color: #fff;

  .steps-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background-color: #fff;

    // 线条
    & > .step:not(:last-child) {
      > .step-item::after {
        content: '';
        z-index: 0;
        position: absolute;
        top: calc(50% + 1px);
        left: calc(50%);
        width: 100%;
        height: 3px;
        background: #eee;
        transform: translateY(-50%);
      }

      > .step-item.complete::after {
        background: #f43a2a;
      }
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .step-item {
        width: 100%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: relative;
      }

      .inactive {
        position: relative;

        img {
          width: 24px;
          height: 24px;
          position: relative;
          z-index: 1;
        }

        .index {
          font-size: 15px;
          font-weight: 600;
          color: #cfcfcf;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          margin-top: 1px;
          z-index: 1;
        }
      }

      .active {
        img {
          width: 24px;
          height: 24px;
          position: relative;
          z-index: 1;
        }

        .title {
          color: #333;
        }
      }

      .complete {
        img {
          width: 30px;
          height: 30px;
          position: relative;
          z-index: 1;
        }
      }
    }
  }

  .text-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    text-align: center;

    .text {
      font-size: 14px;
      font-weight: 400;
      color: #cfcfcf;
      margin-top: 6px;
    }
  }
}
</style>
