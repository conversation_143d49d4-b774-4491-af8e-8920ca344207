<template>
  <van-popup
    :show="show"
    position="bottom"
    safe-area-inset-bottom
    closeable
    class="dev-tools-popup"
    @click-overlay="devPopup.cancel()"
    @click-close-icon="devPopup.cancel()"
  >
    <!-- <van-cell-group title="灰度测试">
      <van-cell title="状态" label="开启灰度测试,30分钟后自动关闭">
        <template #right-icon>
          <van-switch v-model="pre.enable" />
        </template>
      </van-cell>
      <van-field v-model="pre.url" label="灰度服务地址" />
    </van-cell-group> -->
    <header>开发者工具</header>
    <main>
      <van-tabs v-model:active="active">
        <van-tab title="UAT测试" name="uat">
          <van-cell title="状态" label="10分钟后自动关闭">
            <template #right-icon>
              <van-switch v-model="uat.enable" disabled />
            </template>
          </van-cell>
          <van-cell title="水印" label="">
            <template #right-icon>
              <van-switch v-model="uat.watermark" />
            </template>
          </van-cell>
          <van-cell title="服务地址" :label="uat.url">
            <template #right-icon>
              <van-button type="primary" size="small" @click="toUat()"> 前往 </van-button>
            </template>
          </van-cell>
          <!-- <van-field v-model="uat.url" label="重定向地址" label-align="top">
            <template #button>
              <van-button type="primary" size="small" @click="toRedirect()"> 前往 </van-button>
            </template>
          </van-field>
          <van-cell title="其他操作" label="">
            <van-button type="primary" size="small" @click="redirect.resetUrl()">重置</van-button>
          </van-cell> -->
        </van-tab>
        <van-tab title="应用版本" name="appChannel">
          <van-cell title="版本" label="选择版本">
            <template #right-icon>
              <van-radio-group v-model="appChannel" class="app-channel-radio-group">
                <van-radio name="VERIFY">审核版</van-radio>
                <van-radio name="PROD">正式版</van-radio>
              </van-radio-group>
            </template>
          </van-cell>
        </van-tab>
        <van-tab title="设备" name="device">
          <van-cell title="DeviceId" :label="deviceId" />
          <van-cell title="Ua" :label="ua.ua.toString()" />
          <van-cell title="Os" :label="ua.os.toString()" />
          <van-cell title="Browser" :label="ua.browser.toString()" />
          <van-cell title="Device" :label="ua.device.toString()" />
          <van-cell title="Engine" :label="ua.engine.toString()" />
        </van-tab>
        <van-tab title="其他" name="tools">
          <div class="dev-tools-btn-group">
            <van-button type="primary" size="small" @click="toLogin()">H5登录</van-button>
            <van-button type="primary" size="small" @click="toCashLoan()">金融入口</van-button>
            <van-button type="primary" size="small" @click="toMenmber()">大卡入口</van-button>
            <van-button type="primary" size="small" @click="toCashLoanPre()">问卷入口</van-button>
            <van-button type="primary" size="small" @click="refresh()">刷新</van-button>
          </div>
          <van-field v-model="toPageUrl" label="页面跳转" label-align="top">
            <template #button>
              <van-button type="primary" size="small" @click="toPage()"> 前往 </van-button>
            </template>
          </van-field>
        </van-tab>
      </van-tabs>
      <!-- <van-collapse v-model="activeNames">
        <van-collapse-item title="自动重定向" name="redirect">
          <van-cell title="状态" label="30分钟后自动关闭">
            <template #right-icon>
              <van-switch v-model="redirect.enable" />
            </template>
          </van-cell>
          <van-field v-model="redirect.url" label="重定向地址" />
        </van-collapse-item>

        <van-collapse-item title="应用版本" name="appChannel">
          <van-cell title="版本" label="选择版本">
            <template #right-icon>
              <van-radio-group v-model="appChannel" class="app-channel-radio-group">
                <van-radio name="VERIFY">审核版</van-radio>
                <van-radio name="PROD">正式版</van-radio>
              </van-radio-group>
            </template>
          </van-cell>
        </van-collapse-item>

        <van-collapse-item title="UA" name="ua">
          <van-cell title="Ua" :label="ua.ua.toString()" />
          <van-cell title="Os" :label="ua.os.toString()" />
          <van-cell title="Browser" :label="ua.browser.toString()" />
          <van-cell title="Device" :label="ua.device.toString()" />
          <van-cell title="Engine" :label="ua.engine.toString()" />
        </van-collapse-item>
        <van-collapse-item title="工具" name="tools">
          <div class="dev-tools-btn-group">
            <van-button type="primary" @click="toLogin()">H5登录</van-button>
            <van-button type="primary" @click="refresh()">刷新</van-button>
          </div>
        </van-collapse-item>
      </van-collapse> -->
    </main>
  </van-popup>

  <Motion v-show="dev.enable" as-child drag :dragConstraints="floatBtnContainerRef">
    <van-button class="dev-tools-float-btn" @click="devPopup.reveal()">
      <van-icon name="setting-o" />
      <!-- <span>DEV-TOOLS</span> -->
    </van-button>
  </Motion>
  <div ref="float-btn-container" class="dev-tools-float-btn-container"></div>
  <!-- <van-popup :show="uaPopup.isRevealed" class="ua-popup" @click-overlay="uaPopup.cancel()">
    <van-cell-group>
      <van-cell title="Ua" :label="ua.ua.toString()" />
      <van-cell title="Os" :label="ua.os.toString()" />
      <van-cell title="Browser" :label="ua.browser.toString()" />
      <van-cell title="Device" :label="ua.device.toString()" />
      <van-cell title="Engine" :label="ua.engine.toString()" />
    </van-cell-group>
  </van-popup> -->
  <van-watermark v-if="uat.enable && uat.watermark" content="UAT" />
</template>

<script setup>
import { useTemplateRef, reactive } from 'vue'
import { useConfirmDialog, useToggle } from '@vueuse/core'
import { motion, Motion } from 'motion-v'
import router from '@/router'
import ua from '@/utils/ua'
import { uat, dev } from '@/plugins/dev-tools.js'
import { appVersion as appChannel } from '@/utils/auth'
import deviceId from '@/utils/deviceId'

const floatBtnContainerRef = useTemplateRef('float-btn-container')
const [show, toggleShow] = useToggle(false)

const devPopup = reactive(useConfirmDialog())
const uaPopup = reactive(useConfirmDialog())
const activeNames = ref([])
const active = ref('uat')

devPopup.onReveal(() => {
  toggleShow(true)
})
devPopup.onConfirm(() => {
  toggleShow(false)
})
devPopup.onCancel(() => {
  toggleShow(false)
})

function toLogin() {
  router.replace('/register')
}
function toUat() {
  window.location.href = uat.url
}

function toCashLoan() {
  router.push({ path: '/cash-loan' })
  toggleShow(false)
}

function toMenmber() {
  router.push({ path: '/member' })
  toggleShow(false)
}

function toCashLoanPre() {
  router.push({ path: '/cash-loan-pre' })
  toggleShow(false)
}

async function showUA() {
  // alert(ua)
  console.log(ua)
  toggleShow(false)

  const { data } = await uaPopup.reveal()
  console.log(data)
  toggleShow(true)
}
function refresh() {
  window.location.reload()
}

const toPageUrl = ref(location.origin)
function toPage() {
  location.href = toPageUrl.value
}

// function goPre() {
//   window.location.href = pre.url
// }
onMounted(() => {})
</script>

<style lang="scss" scoped>
.dev-tools-popup {
  // width: 100%;
  // height: 100%;
  // background-color: #f5f5f5;
  font-size: 14px;
  // min-height: 60vh;
  height: 70vh;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  header {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding: 16px 0;
    flex: none;
  }
  main {
    flex: 1;
    min-height: 0;
    .van-tabs {
      height: 100%;
    }
  }
}
.dev-tools-float-btn-container {
  position: fixed;
  top: calc(var(--safe-area-inset-top) + 44px + 8px);
  left: 8px;
  bottom: calc(var(--safe-area-inset-bottom) + 8px);
  right: 8px;
  z-index: 1000;
  pointer-events: none;
}
.dev-tools-float-btn {
  position: fixed;
  bottom: 8px;
  bottom: calc(var(--safe-area-inset-bottom) + 8px);
  right: 8px;
  z-index: 1000;
}
.app-channel-radio-group {
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.dev-tools-btn-group {
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.ua-popup {
  font-size: 14px;
}
</style>
