<template>
    <van-popup v-model:show="show" style="background: none;">
        <div class="content">
            <img class="close" src="@/assets/images/needvip/close.png" @click="close" />
            <div class="center-content">
                <div class="title">抱歉！仅限会员购买</div>
                <div class="desc">此优惠商品仅限会员购买</div>
                <div class="desc">赶快开通会员享受</div>
                <div class="bottom-button" @click="onClickGo">前往会员中心</div>
            </div>
        </div>
    </van-popup>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();
const user = computed(() => store.getters.userInfo)

const show = ref(false)

const open = () => {
    show.value = true
}

const close = () => {
    show.value = false
}

defineExpose({
    open
})

const onClickGo = () => {
    close()
    if (user.value) {
        router.push('/member')
    } else {
        proxy.appJS.appLogin()
    }
}
</script>

<style lang="scss" scoped>
.content {
    width: 292px;
    height: 280px;
    background-image: url('@/assets/images/needvip/background.png');
    background-size: cover;
    background-position: center;
    position: relative;

    .close {
        position: absolute;
        width: 13px;
        top: 30px;
        right: 0px;
        padding: 10px;
        transition: transform 0.2s ease;

        &:active {
            transform: scale(0.9);
        }
    }

    .center-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 110px 36px 28px 36px;

        .title {
            color: #333333;
            font-size: 19px;
            font-weight: bold;
            margin-bottom: 13px;
            background: linear-gradient(to right, #b52502, #611300);
            /* 渐变背景，从红到绿 */
            -webkit-background-clip: text;
            /* 将背景裁剪为文字形状 */
            background-clip: text;
            /* 标准属性，但兼容性稍差 */
            color: transparent;
            /* 文字颜色设为透明，显示背景渐变 */
        }

        .desc {
            color: #666666;
            font-size: 15px;
            margin-bottom: 6px;
        }

        .bottom-button {
            width: 219px;
            height: 40px;
            line-height: 40px;
            background: linear-gradient(180deg, #FF3927 0%, #FF8C64 100%);
            border-radius: 25px;
            color: #fff;
            font-size: 15px;
            text-align: center;
            margin-top: 16px;
            transition: background-color 0.2s ease;

            &:active {
                background: linear-gradient(180deg, #ff4c3b 0%, #ff9773 100%);
            }
        }
    }
}
</style>