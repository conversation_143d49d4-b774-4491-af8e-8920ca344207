<template>
  <van-popup
    v-model:show="visible"
    round
    position="bottom"
    class="select-address-popup"
    teleport="body"
    @closed="onClosed"
  >
    <div class="my-address-page">
      <div class="title">选择收货地址</div>
      <img class="close-img" @click="onClickBack" src="@/assets/images/goods/colse.png" />
      <div class="my-address-page-context">
        <div class="list">
          <div class="item" v-for="(item, index) in addressList" :key="index">
            <div class="info" @click="handleChoose(item)">
              <div class="content">
                {{ item.province + item.city + item.district + item.address }}
              </div>
              <div class="name-phone">
                <div class="name">{{ item.contactName }}</div>
                <div class="phone">{{ item.contactTel }}</div>
              </div>
              <img
                v-if="item.id === props.addressInfo.id"
                class="selected-address"
                src="@/assets/images/goods/selected-address.png"
              />
            </div>
            <div class="action">
              <div class="action-left" @click="handleDefault(item)">
                <van-checkbox :model-value="item.isDefault === 'Y'" checked-color="#F43727" />
                <span :class="{ 'is-default': item.isDefault === 'Y' }">{{
                  item.isDefault === 'Y' ? '已设为默认' : '设为默认'
                }}</span>
              </div>
              <div class="action-right">
                <div class="edit" @click="handleUpdate(item)">
                  <img src="@/assets/images/goods/edit-address.png" />
                  <div class="btn">编辑</div>
                </div>
                <div class="delete" @click="handleRemove(item)">
                  <img src="@/assets/images/goods/delete-address.png" />
                  <div class="btn">删除</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="btn theme-linear-gradient" @click="handleUpdate()"><van-icon name="plus" /> 新增收货地址</div>
        </div>
      </div>
      <van-popup round v-model:show="show">
        <div class="warning-popup">
          <div class="title">确定删除收货地址？</div>
          <div class="content">
            <div class="btn cancel text-black" @click="show = false">取消</div>
            <div class="btn theme-linear-gradient text-white" @click="submitDelete">确认</div>
          </div>
        </div>
      </van-popup>
    </div>
    <EditAddress ref="editAddressRef" @added="onAdded" @edited="onEdited" />
  </van-popup>
</template>

<script setup>
import { listAddress, updateDefault, deleteAddress } from '@/api/address'
import EditAddress from './EditAddress.vue'

const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const visible = ref(false)
const addressList = ref([])
const show = ref(false)
const deleId = ref(null)
const editAddressRef = ref()

const emit = defineEmits(['choose', 'update', 'delete'])

const props = defineProps({
  type: {
    type: String,
    default: '1',
  },
  addressInfo: {
    type: Object,
    default: {},
  },
})

const onClickBack = () => {
  visible.value = false
}

// 更新默认
const handleDefault = (item) => {
  if (item.isDefault !== 'Y') {
    updateDefault({ id: item.id }).then((res) => {
      proxy.onToastSucc(() => {
        getList()
      }, '更新成功！')
    })
  }
}

// 删除地址
const handleRemove = (item) => {
  deleId.value = item.id
  show.value = true
}

const submitDelete = () => {
  deleteAddress({ id: deleId.value }).then((res) => {
    show.value = false
    emit('delete', deleId.value)
    proxy.onToastSucc(() => {
      getList()
    }, '删除成功！')
  })
}

// 选择
const handleChoose = (item) => {
  emit('choose', JSON.parse(JSON.stringify(item)))
  onClickBack()
}

// 新增编辑
const handleUpdate = (item) => {
  editAddressRef.value.open(item)
}

const getList = () => {
  listAddress({ pageNum: 1, pageSize: 100 }).then((res) => {
    addressList.value = res.data
  })
}

const open = () => {
  visible.value = true
  getList()
}

const onClosed = () => {
  // 关闭内部弹窗
  show.value = false
}

const onAdded = (item) => {
  getList()
}

const onEdited = (item) => {
  emit('update', item)
  getList()
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.my-address-page {
  // height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  // position: absolute;
  background-color: #f6f6f6;
  border-radius: 10px 10px 0 0;
  max-height: 60vh;
  max-height: 60dvh;

  .title {
    font-size: 15px;
    font-weight: bold;
    color: #000;
    text-align: center;
    margin: 20px 0 10px 0;
  }

  .close-img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    padding: 10px;
  }

  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    position: relative;

    .list {
      flex: 1;
      .item {
        margin: 10px;
        background: #ffffff;
        border-radius: 8px;
        padding: 12px;

        .info {
          padding-bottom: 9px;
          border-bottom: 1px solid #ededee;
          position: relative;

          .name-phone {
            display: flex;
            font-size: 14px;
            // font-weight: bold;
            color: #333333;
            line-height: 17px;

            .name {
              margin-right: 10px;
            }
          }

          .selected-address {
            position: absolute;
            bottom: 15px;
            right: 13px;
            width: 15px;
            height: auto;
          }

          .content {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 600;
            color: #000000;
            line-height: 20px;
            margin-bottom: 6px;
          }
        }

        .action {
          display: flex;
          justify-content: space-between;
          align-items: center;
          // padding: 9px 16px 14px;
          padding-top: 9px;

          &-left {
            display: flex;
            align-items: center;
            font-size: 13px;
            font-weight: 400;
            color: #999999;

            .is-default {
              color: #f43727;
            }

            .van-checkbox {
              --van-checkbox-size: 15px;
              margin-right: 5px;
            }
          }

          &-right {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            font-weight: 400;
            padding-right: 9px;
            .edit,
            .delete {
              display: flex;
              align-items: center;
              img {
                width: 13px;
                height: auto;
                margin-right: 5px;
              }
            }
            .delete {
              margin-left: 20px;
            }
          }
        }
      }
    }

    .footer {
      background: #ffffff;
      padding: 7px 16px;
      padding-bottom: calc(7px + var(--safe-area-inset-bottom));
      position: sticky;
      bottom: 0;
      width: calc(100% - 32px);

      .btn {
        border-radius: 8px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
}

.warning-popup {
  width: 235px;
  background: #ffffff;

  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
  }

  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;

    .btn {
      width: 90px;
      height: 32px;
      border-radius: 16px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
    }

    .btn.cancel {
      background: #eeeeee;
    }
  }
}
</style>
