<template>
  <div class="tip">
    <div :class="[!props.inverseColor ? 'title-tip' : 'title-tip-inverse']">{{ stateLabel }}</div>
    <div class="left-time" v-if="timeVisible">
      <span :class="[!props.inverseColor ? 'left-time-count' : 'left-time-count-inverse']">{{ leftTimeHours }}</span>
      <span :class="[!props.inverseColor ? 'left-time-dot' : 'left-time-dot-inverse']">:</span>
      <span :class="[!props.inverseColor ? 'left-time-count' : 'left-time-count-inverse']">{{ leftTimeMinutes }}</span>
      <span :class="[!props.inverseColor ? 'left-time-dot' : 'left-time-dot-inverse']">:</span>
      <span :class="[!props.inverseColor ? 'left-time-count' : 'left-time-count-inverse']">{{ leftTimeSeconds }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useSeckillTimer } from '@/hooks/useSeckillTimer'

const props = defineProps({
  inverseColor: {
    type: Boolean,
    default: false,
  },
})

const { currentSession, currentSessionType, leftTimeDays, leftTimeHours, leftTimeMinutes, leftTimeSeconds } = useSeckillTimer()

const stateLabel = computed(() => {
  if (currentSession.value) {
    const daysStr = leftTimeDays.value ? `${leftTimeDays.value}` : ''
    if (currentSessionType.value === 'ongoing') {
      return '距结束' + daysStr
    } else if (currentSessionType.value === 'willstart') {
      return '距开始' + daysStr
    }
  } else {
    return ''
  }
})

// 存在当前场次或下一场时可见
const timeVisible = computed(() => {
  return currentSession.value
})
</script>

<style lang="scss" scoped>
.tip {
  display: flex;
  align-items: center;
  justify-content: center;

  .title-tip {
    font-size: 12px;
    color: #1D0200;
    opacity: 50%;
    white-space: nowrap;
    text-align: right;
    margin-right: 2px;
  }

  .title-tip-inverse {
    font-size: 12px;
    color: #fff;
    white-space: nowrap;
    text-align: right;
    margin-right: 2px;
  }

  .left-time {
    display: flex;
    align-items: center;

    .left-time-count {
      font-size: 12px;
      font-weight: bold;
      color: #fff;
      background-color: #f03927;
      border-radius: 4px;
      padding: 3px;
      vertical-align: text-top;
      white-space: nowrap;
    }

    .left-time-dot {
      font-size: 16px;
      color: #f03927;
      margin: 0 2px;
      padding-bottom: 4px;
    }

    .left-time-count-inverse {
      font-size: 12px;
      font-weight: bold;
      color: #f03927;
      background-color: #fff;
      border-radius: 4px;
      padding: 3px;
      vertical-align: text-top;
      white-space: nowrap;
    }

    .left-time-dot-inverse {
      font-size: 16px;
      color: #fff;
      margin: 0 2px;
      padding-bottom: 4px;
    }
  }
}
</style>