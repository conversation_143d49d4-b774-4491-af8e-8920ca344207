<template>
  <div class="list">
    <div
      class="list-item"
      v-for="item in goodsList"
      :key="item.spuId"
      @click="() => onClickGoods(item)"
    >
      <img class="image" :src="item.thumbPics" />
      <div class="info">
        <div class="info-top">
          <div class="title overflow-2">
            {{ item.spuTitle }}
          </div>
          <div class="desc overflow-2">
            {{ item.detailTitle }}
          </div>
        </div>
        <div class="info-bottom">
          <div class="price">
            秒杀价￥<span class="big-number">{{ item.seckillPrice }}</span>
          </div>
          <div class="loot">
            <div class="loot-content">
              <img class="loot-image" src="@/assets/images/time-limit/闪电.png" />
              <span class="loot-text">抢</span>
              <img class="loot-arraw" src="@/assets/images/time-limit/向右箭头.png" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useSeckillStore } from '@/store/seckill'

const props = defineProps({
  goodsList: {
    type: Array,
    default: () => [],
  },
  round: {
    type: Boolean,
    default: false,
  },
})

const router = useRouter()
const seckillStore = useSeckillStore()

const onClickGoods = async (item) => {
  // 跳转到商品详情页面，是秒杀活动
  router.push({
    path: '/goods-detail-seckill',
    query: {
      goodsId: item.id,
      sessionId: seckillStore.currentSession.id,
      timeLimit: '1',
      isVip: seckillStore.currentSession.isVip,
    },
  })
}
</script>

<style lang="scss" scoped>
.list {
  margin: -5px 10px 10px 10px;

  .list-item {
    margin-bottom: 10px;
    border-radius: 10px;
    background-color: #fff;
    padding: 8px;
    display: flex;

    .image {
      height: 105px;
      aspect-ratio: 1;
      // border: 1px solid #eee;
      border-radius: 10px;
    }

    .info {
      flex: 1;
      margin-left: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: stretch;

      .info-top {
        margin-bottom: 8px;

        .title {
          margin-top: 4px;
          font-size: 15px;
          font-weight: bold;
          color: #333;
        }

        .desc {
          margin-top: 4px;
          font-size: 13px;
          color: #999;
        }
      }

      .info-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff3ef;
        border-radius: 18px;
        margin-right: 16px;
        padding-left: 12px;

        .price {
          font-size: 13px;
          font-weight: bold;
          color: #4671eb;

          .big-number {
            font-size: 16px;
            font-weight: bold;
            color: #4671eb;
          }
        }

        .loot {
          width: 50px;
          height: 35px;
          background: linear-gradient(180deg, #ffa375, #ff2419);
          border-radius: 0 35px 35px 0;
          padding-right: 8px;

          .loot-content {
            display: flex;
            align-items: center;
            height: 35px;
            margin-left: -10px;
          }

          .loot-image {
            height: 46px;
          }

          .loot-text {
            font-size: 20px;
            font-weight: bold;
            color: #fff;
          }

          .loot-arraw {
            height: 9px;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>