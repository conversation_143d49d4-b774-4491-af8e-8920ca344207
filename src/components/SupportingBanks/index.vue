<template>
  <van-popup round v-model:show="show" teleport="body" closeable>
    <div class="supporting-banks">
      <div class="title solid-bottom">支持银行</div>
      <div class="bank-list">
        <template v-for="item in bankList">
          <div v-if="item.code !== 'zhaoshang' && item.code !== 'nongye'" class="bank-item">
            <div :class="`logo solid bank-logo ${item.code}`"></div>
            <div class="name">{{ item.bankAliasName }}</div>
          </div>
        </template>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { bankList } from '@/constant/bank'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const show = ref(false)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang="scss">
.supporting-banks {
  width: 330px;
  .title {
    font-size: 18px;
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 20px;
  }
  .bank-list {
    // display: flex;
    // flex-wrap: wrap; // 换行
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 10px 10px 30px;
    gap: 20px 0;
    .bank-item {
      // width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: start;
      // margin-top: 20px;
      .logo {
        width: 50px;
        height: 50px;
        border-radius: 5000px;
        display: block;
      }
      .name {
        font-size: 12px;
        margin-top: 10px;
        text-align: center;
      }
    }
  }
}
</style>
