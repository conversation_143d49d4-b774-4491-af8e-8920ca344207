<template>
  <van-popup class="ad-popup" v-model:show="show" :close-on-click-overlay="false">
    <div class="loading-img">
      <img class="loader" src="@/assets/images/credit/credit-loading.png" />
    </div>
    <div class="loading-text text-center">授信申请中...</div>
  </van-popup>
</template>

<script setup>
const show = ref(false)
defineExpose({ show })
</script>

<style lang="scss" scoped>
.loading-img {
  width: 187px;
  height: 187px;
  background: url('@/assets/images/credit/credit-render.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: hue-rotate(220deg);
  .loader {
    width: 115px;
    height: 115px;
    animation: load 1.4s infinite linear;
  }
  @keyframes load {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
.loading-text {
  font-size: 15px;
  color: #ffffff;
  line-height: 21px;
  margin-top: 4px;
}
</style>
