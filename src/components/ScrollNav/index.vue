<template>
    <div class="page" @scroll="onScrollChange" ref="pageRef" :style="pageStyle">
        <div class="nav-wrapper theme-bg" :class="{ 'iphonex': isIphoneX }">
          <div class="nav-content" :class="{ 'iphonex-top': isIphoneX }" :style="navBarStyle">
            <div class="left" @click="onLeftClick">
                <!-- 默认状态 -->
                <van-icon v-if="isDefault && isShowBack" size="22" name="arrow-left" />
                <!-- 定制具名插槽 -->
                <slot name="nav-left"></slot>
            </div>
            <div class="center">
                <!-- 默认状态 -->
                <span class="page-title" v-if="isDefault">{{ pageName }}</span>
                <!-- 定制具名插槽 -->
                <slot name="nav-center"></slot>
            </div>
            <div class="right">
            <!-- 定制具名插槽 -->
                <slot name="nav-right"></slot>
            </div>
          </div>
        </div>
        <slot></slot>
    </div>
  </template>
  
  <script setup>
    const { proxy } = getCurrentInstance()
    const router = useRouter()
    const store = useStore()
    const isIphoneX = window.isIphoneX
    const pageRef = ref(null)
    // 滚动值
    const scrollTopValue= ref(-1)
    const ANCHOR_SCROLL_TOP = 64
    const data = reactive({
        navBarStyle: {
            backgroundColor: '',
            position: 'fixed'
        }
    })
    const { navBarStyle } = toRefs(data)
    const props = defineProps({
        rgbColor: {
            type: String,
            default: '255,103,26'
        },
        isDefault: {
            default: true,
            type: Boolean
        },
        isShowBack: {
            default: true,
            type: Boolean
        },
        // 默认状态下页面标题的名称
        pageName: {
            default: '',
            type: String
        },
        pageStyle: {
            type: Object,
            default: () => {}
        }
    })
    onActivated(() => {
        pageRef.value.scrollTop = scrollTopValue.value
    })
    // 滚动
    const onScrollChange = ($e) => {
      scrollTopValue.value = $e.target.scrollTop
      let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
      navBarStyle.value.backgroundColor = 'rgba('+ props.rgbColor +', ' + opacity + ')'
      emit('scrollChange', $e.target.scrollTop)
    }
    const emit = defineEmits(['onLeftClick', 'scrollChange'])
    const onLeftClick = () => {
        //store.commit('SET_ISIOSMOVEBACK', false) // 重置变量isIosMoveBack
        emit('onLeftClick')
    }
  </script>
  
  <style lang="scss" scoped>
    .page{
        height: 100%;
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
        position: absolute;
        .nav-wrapper.iphonex{
            height: 88px;
        }
        .nav-wrapper{
            box-sizing: border-box;
            height: 69px;
        .nav-content{
            width: 100%;
            height: 44px;
            display: flex;
            justify-content: space-between;
            padding-top: 25px;
            align-items: center;
            flex-shrink: 0;
            z-index: 1999;
            position: fixed;
            top: 0;
            color: #FFFFFF;
            font-size: 18px;
            .left {
                padding-left: 6px;
                width: 32px;
                display: flex;
                align-items: center;
            }
            .right {
                display: flex;
                padding-right: 9px;
                align-items: center;
                width: 32px;
                position: relative;
                font-size: 14px;
            }
            .center {
                display: flex;
                height: 100%;
                flex-grow: 1;
                padding: 0 5px;
                .page-title {
                    align-self: center;
                    margin: 0 auto;
                    font-size: 18px;
                    color: #ffffff;
                    white-space:nowrap;
                }
            }
        }
      }
    }
  </style>