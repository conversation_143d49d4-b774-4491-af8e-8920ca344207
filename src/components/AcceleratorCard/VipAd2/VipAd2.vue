<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const onClick = () => {
  router.push({ path: '/member' })
}
</script>

<template>
  <div class="vip-ad2">
    <img src="@/assets/images/cashier/one-coin.png" />
    <div class="text">恭喜！获得了<span class="highlight">开通会员</span>资格</div>
    <div class="btn" @click="onClick">立即领取</div>
  </div>
</template>

<style lang="scss" scoped>
.vip-ad2 {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 15px;
  margin: 10px;
  padding: 6px 8px;

  img {
    width: 43px;
    margin-right: 12px;
  }

  .text {
    color: #333;
    font-size: 15px;
    font-weight: 400;
    flex-grow: 1;
    text-align: left;
  }

  .highlight {
    color: #4671eb;
  }

  .btn {
    width: 70px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #fff;
    letter-spacing: 0.5;
    border-radius: 26px;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
    margin-right: 7px;
  }
}
</style>