<template>
  <div class="store-info" :style="cssStyle">
    <img src="@/assets/images/goods/store-logo.png">
    <span class="text">轻享花</span>
  </div>
</template>

<script setup>
  const props = defineProps({
    cssStyle: {
      type: Object,
      default: () => {}
    }
  })
</script>

<style lang="scss" scoped>
  .store-info{
    display: flex;
    align-items: center;
    img{
      width: 26px;
      height: 26px;
      margin-right: 6px;
    }
    .text{
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 22px;
    }
  }
</style>
