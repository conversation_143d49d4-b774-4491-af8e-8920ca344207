<template>
  <van-popup v-model:show="show"
    :close-on-click-overlay="false"
    round
    closeable
    :style="{ top: '30%' }"
  >
    <div class="code-content">
        <div div class="title">请输入短信验证码</div>
        <div class="tips">已发送短信验证码至 {{ phoneFormat(user.phone) }}</div>
        <div class="margin-top">
            <!-- <code-input ref="codeInputRef" @validCode="updateCode" /> -->
        </div>
        <div class="reset-wrapper">
            <div class="text">收不到验证码？</div>
            <div :class="`send-btn theme-text ${times > 0 ? '' : 'theme-border'}`" @click="getCode">{{ codeText }}</div>
        </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import CodeInput from '@/components/CodeInput'
import { phoneFormat } from '@/utils/common'
import { smsCode } from '@/api/cashier'
import { showToast } from 'vant'
import { nextTick } from 'vue';
const { proxy } = getCurrentInstance();
const user = computed(() => store.getters.userInfo)
const store = useStore();
const route = useRoute();
const router = useRouter();
const codeText = ref('重新发送')
const show = ref(false)
const times = ref(0)
const codeInputRef = ref(null)
const codeType = ref('')
const props = defineProps({
    orderId: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['codeSucc'])
const getCode = () => {
    if(times.value === 0) {
        times.value = 60
        codeText.value = '重新获取（'+ times.value + "s）"
        const timer = setInterval(function(){
        times.value--
        codeText.value = '重新获取（'+ times.value + "s）"
        if(times.value === 0){
            clearInterval(timer)
            codeText.value = "重新获取"
        }
        },1000)
        if (codeType.value ==='cashier') { // 收银台消费分期
            smsCode({ orderId : props.orderId }).then(res => {
                showToast('发送成功，请注意查收')
            })
        }
        nextTick(() => {
            codeInputRef.value.init()
        })
    }
}
const updateCode = (val) => {
    emit('codeSucc', val)
}
const initCode = (type) => {
    codeType.value = type
    show.value = true
    getCode()
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show, initCode })
</script>
<style scoped lang='scss'>
    .code-content{
        padding: 30px 20px;
        .title{
            font-weight: 500;
            font-size: 22px;
            color: #222222;
            line-height: 31px;
        }
        .tips{
            font-weight: 400;
            font-size: 13px;
            color: #A8A8A8;
            line-height: 19px;
        }
        .reset-wrapper{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            .text{
                font-size: 12px;
                color: #363636;
                line-height: 17px;
            }
            .send-btn{
                height: 27px;
                border-radius: 13px;
                font-size: 12px;
                line-height: 27px;
                text-align: center;
                padding: 0 13px;
            }
        }
    }
    :deep(.van-popup--center) {
        top: 30% !important;
    }
</style>