<template>
  <div class="title">
    <div class="block-wrap"><div class="arrow left"></div><div class="block"></div></div>
    <div class="text">{{ title }}</div>
    <div class="block-wrap"><div class="block"></div><div class="arrow right"></div></div>
  </div>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: ''
    }
  })
</script>

<style lang="scss" scoped>
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .text{
      margin: 0 9px;
      font-size: 16px;
      font-weight: 500;
      color: #363636;
      line-height: 22px;
    }
    .block-wrap{
      display: flex;
      align-items: center;
      font-size: 18px;
      .arrow{
        width: 5px;
        height: 5px;
        border: 2px solid #363636;
        border-left-width: 0;
        border-bottom-width: 0;
      }
      .arrow.left{
        transform:rotate(225deg);
      }
      .arrow.right{
        transform:rotate(45deg);
      }
      .block{
        width: 8px;
        height: 8px;
        background: #363636;
        font-size: 0;
        transform:rotate(45deg);
      }
    }
    
  }
</style>