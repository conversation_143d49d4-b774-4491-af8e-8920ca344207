<template>
  <div class="goods-list">
    <div class="goods-item" v-for="(goods, index) in goodsList" :key="index">
      <div class="goods-wrapper">
        <img class="goods-img" :src="goods.albumPics ? goods.albumPics : goods.thumbPics" />
        <div class="goods-info">
          <div class="goods-info-top">
            <div class="goods-name overflow-1">{{ goods.spuTitle }}</div>
            <div class="goods-spec">已选择 {{ goods.spec }}</div>
          </div>
          <div class="goods-price-quantity">
            <div class="price">￥{{ goods.price ? goods.price : goods.salesPrice }}</div>
            <div class="quantity">x{{ goods.quantity }}</div>
          </div>
        </div>
      </div>
      <div
        v-if="
          !ORDERTYPE.includes(orderType) &&
          (state === 'FINISHED' || state === 'WAIT_RECEIVING' || state === 'WAIT_DELIVER')
        "
        class="action-bar"
      >
        <div class="btn cancel" @click.stop="onClick(goods)">退换/售后</div>
      </div>
    </div>
  </div>
</template>

<script setup>
const ORDERTYPE = ['DIRECT_ADD', 'ECARD', 'TELE', 'COUPON']
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => [],
  },
  state: {
    type: String,
    default: '',
  },
  orderId: {
    type: String,
    default: '',
  },
  orderType: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['onAfterSales'])
const onClick = (goods) => {
  emit('onAfterSales', { orderId: props.orderId, goodsList: [goods] })
}
</script>

<style lang="scss" scoped>
.goods-wrapper {
  display: flex;
  .goods-img {
    width: 89px;
    height: 89px;
    flex-shrink: 0;
    margin-right: 6px;
    border-radius: 16px;
  }
  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    .goods-name {
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 18px;
      margin-right: 60px;
    }
    .goods-spec {
      margin-top: 7px;
      font-size: 14px;
      transform-origin: left top;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 17px;
      margin-right: 20px;
    }
    .goods-price-quantity {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      position: absolute;
      top: 0px;
      right: 5px;
      .price {
        font-size: 15px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #333;
        line-height: 18px;
      }
      .price::first-letter {
        font-size: 70%;
      }
      .quantity {
        margin-top: 7px;
        font-size: 14px;
        font-weight: 400;
        color: #999;
        line-height: 17px;
      }
    }
  }
}
.goods-item + .goods-item {
  margin-top: 14px;
}
.action-bar {
  padding-bottom: 14px;
  display: flex;
  justify-content: flex-end;
  .btn {
    padding: 10px 16px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 15px;
    border-radius: 25px;
  }
  .btn.cancel {
    color: #333;
    border: 1px solid #333;
  }
  .btn + .btn {
    margin-left: 10px;
  }
}
</style>