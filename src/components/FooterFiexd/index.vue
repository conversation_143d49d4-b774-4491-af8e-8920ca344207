<template>
    <div class="footer-fixed shadow"  :style="footerStyle">
      <slot></slot>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  footerStyle: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .footer-fixed{
        background: #FFFFFF;
        padding: 10px 16px;
        position: fixed;
        padding-bottom: calc(10px + var(--safe-area-inset-bottom));
        bottom: 0;
        width: calc(100% - 32px);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        z-index: 1200;
    }
    .footer.iphonex-bottom{
        // padding-bottom: 51px !important;
    }
</style>