<template>
    <div class="pdf-viewer">
        <template v-for="item in pageNum" :key="item">
            <canvas :id="`pdf-canvas-${item}`" class="pdf-page" />
        </template>
        <van-empty
            v-if="loadError"
            image="error"
            description="PDF加载出错了..."
        />
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import * as pdfjs from 'pdfjs-dist'
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'
import { closeToast } from 'vant'
const loadError = ref(false)
const detail = ref({})
let pdfDoc = null // 一定不能使用响应式的数据，会报错Cannot read from private field---pdf.js
const pageNum = ref(0)
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
const loadingPdf = (url) => {
  const loadingTask = pdfjs.getDocument(url)
  loadingTask.promise
    .then((pdf) => {
      pdfDoc = pdf;
      pageNum.value = pdf.numPages;
      nextTick(() => {
        renderPage();
      });
    })
    .catch(() => {
      closeToast();
      loadError.value = true;
    });
}
function renderPage(num = 1) {
  pdfDoc.getPage(num).then((page) => {
    const canvas = document.getElementById(`pdf-canvas-${num}`);
    const ctx = canvas.getContext('2d');
    const scale = 1;
    const viewport = page.getViewport({ scale });
    // 画布大小,默认值是width:300px,height:150px
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    // 画布的dom大小, 设置移动端,宽度设置铺满整个屏幕
    const { clientWidth } = document.body;
    // 根据pdf每页的宽高比例设置canvas的高度
    canvas.style.height = `${
      clientWidth * (viewport.height / viewport.width)
    }px`;
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    page.render({
      canvasContext: ctx,
      viewport,
    });
    if (num < pageNum.value) {
      renderPage(num + 1);
    } else {
      closeToast();
    }
  });
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ loadingPdf })
</script>
<style scoped lang='scss'>
  .pdf-viewer{
    height: 100%;
    overflow-y: auto;
  }
</style>