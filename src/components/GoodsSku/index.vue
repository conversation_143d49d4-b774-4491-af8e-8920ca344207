<template>
  <van-popup
    v-model:show="show"
    round
    position="bottom"
    :style="{}"
    class="sku-popup"
    @closed="onClosed"
  >
    <div class="sku-wrapper">
      <div class="goods-info">
        <div class="goods-img">
          <img :src="goodsSku.thumbPics ? goodsSku.thumbPics : spuInfo.thumbPics" />
        </div>
        <div class="goods-price-checked">
          <div class="price-container">
            <div class="price">
              ￥{{ goodsSku.salesPrice ? goodsSku.salesPrice : spuInfo.priceDown }}
            </div>
            <div class="vip-price-bar" v-if="goodsSku.vipPrice !== undefined">
              <img class="vip-bg" src="@/assets/images/goods/详情页会员价背景小.png" />
              <div class="vip-price">￥{{ formatMoney(goodsSku.vipPrice) }}</div>
            </div>
          </div>
          <div class="checked" v-if="spuInfo.specType === 'MORE'">
            <div class="checked-label">已选：</div>
            <div class="checked-value flex">
              <div
                class="checked-item flex overflow-1"
                v-for="(item, index) in goodsSpecData"
                :key="index"
              >
                <template v-if="item.checked">
                  <div v-for="(item2, index2) in item.leaf" :key="index2">
                    <div v-if="item.checked == item2.id">{{ item2.value }}/</div>
                  </div>
                </template>
              </div>
              <div class="checked-label">{{ skuNum }}{{ spuInfo.unit }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="goods-spec" v-if="spuInfo.specType === 'MORE'">
        <div class="spec-item" v-for="(item, index) in goodsSpecData" :key="index">
          <div class="spec-title">{{ item.value }}</div>
          <div class="flex spec-flex">
            <div
              :class="`spec-value ${item.checked == leaf.id ? 'checked' : ''}`"
              v-for="(leaf, index2) in item.leaf"
              :key="index2"
              @click="tabSpec(index, index2)"
            >
              {{ leaf.value }}
            </div>
          </div>
        </div>
        <div class="goods-number">
          <span class="number-label">数量</span>
          <van-stepper
            v-model="skuNum"
            integer
            @change="changeNum"
            button-size="24px"
            input-width="40px"
          />
        </div>
      </div>
      <div class="footer">
        <div
          :class="`btn theme-linear-gradient ${
            stockDiasbled || spuInfo.saleable === 'N' ? 'disabled' : ''
          }`"
          @click="onConfirm"
        >
          {{ confirmText }}
        </div>
      </div>
      <img class="close-img" @click="show = false" src="@/assets/images/goods/colse.png" />
    </div>
  </van-popup>
</template>

<script setup>
import { showToast } from 'vant'
const isIphoneX = window.isIphoneX
const emit = defineEmits(['updateSpecSku', 'updateSkuNum', 'onConfirm', 'selectAddress'])

const props = defineProps({
  spuInfo: {
    type: Object,
    default: () => {},
  },
  skuList: {
    type: Array,
    default: () => [],
  },
  goodsSku: {
    type: Object,
    default: () => {},
  },
  goodsSpecData: {
    type: Array,
    default: () => [],
  },
  modalSkuType: {
    type: String,
    default: '',
  },
  addressInfo: {
    type: Object,
    default() {
      return {}
    },
  },
})
const show = ref(false)
const skuNum = defineModel('skuNum')
const cheight = ref('600px')
const stockDiasbled = ref(false)
const confirmText = ref('确定')

onMounted(() => {
  if (window.screen.height > 667) {
    cheight.value = '700px'
  }
})

// 规格点击
const tabSpec = (index, index2) => {
  const leaf = props.goodsSpecData[index].leaf[index2]
  props.goodsSpecData[index].checked = leaf.id
  props.skuList.forEach((sku) => {
    let i = 0
    sku.specs.forEach(function (skuSpec) {
      props.goodsSpecData.forEach(function (spec) {
        if (spec.id == skuSpec.specId && spec.checked == skuSpec.specValueId) {
          i = i + 1
        }
      })
    })
    if (i === props.goodsSpecData.length) {
      // 选取了全部规格、
      stockDiasbled.value = sku.stock < 1 ? true : false
      emit('updateSpecSku', props.goodsSpecData, sku)
    }
  })
}

const changeNum = (val) => {
  emit('updateSkuNum', val)
}

const onConfirm = () => {
  let canDo = true
  try {
    props.goodsSpecData.forEach(function (spec) {
      if (!spec.checked) {
        canDo = false
        showToast('请选择' + spec.value)
        throw new Error() // 跳出循环
      }
    })
  } catch (e) {}
  if (canDo) {
    if (!stockDiasbled.value) {
      show.value = false
      emit('onConfirm')
    } else {
      showToast('当前商品规格暂无库存')
    }
  }
}

const openForCart = () => {
  show.value = true
  confirmText.value = '加入购物车'
}

const onClosed = () => {
  // 关闭时恢复默认数据
  confirmText.value = '确定'
}

// 选择收获地址
const handleAdrress = () => {
  emit('selectAddress')
}

defineExpose({ openForCart, show })
</script>

<style lang="scss" scoped>
.sku-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  // height: 100%;
  background-color: #f6f6f6;
  border-radius: 10px 10px 0 0;
  min-height: 400px;
  max-height: 80vh;
  max-height: 80dvh;

  .close-img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 24px;
    height: 24px;
    padding: 10px;
  }

  .goods-info {
    display: flex;
    align-items: flex-end;
    margin: 0 10px;
    flex: none;

    .goods-img {
      border-radius: 10px;
      margin-top: -17px;

      img {
        width: 94px;
        height: 94px;
        border-radius: 15px;
        margin-right: 10px;
      }
    }

    .goods-price-checked {
      margin-bottom: 16px;

      .price-container {
        display: flex;
        align-items: center;

        .price {
          font-size: 24px;
          font-weight: bold;
          color: #e9362e;

          .mini-font {
            font-size: 16px;
          }
        }

        .price::first-letter {
          font-size: 70%;
        }

        .vip-price-bar {
          min-width: 115px;
          height: 20px;
          border-radius: 14px;
          background: linear-gradient(171.92deg, #fff4e9 15.23%, #ffecd1 116.86%);
          display: flex;
          align-items: center;
          margin-left: 10px;

          .vip-bg {
            width: 64px;
            height: 20px;
            display: inline-block;
          }

          .vip-price {
            font-size: 12px;
            color: #333;
            font-weight: bold;
            margin-left: -10px;
            margin-right: 8px;
          }

          .vip-price::first-letter {
            font-size: 9px;
            font-weight: bold;
          }
        }
      }

      .checked {
        margin-top: 6px;
        color: #999999;
        font-size: 14px;
        font-weight: 400;
        transform-origin: left top;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: flex;

        .checked-label {
          white-space: nowrap;
        }
      }
    }
  }

  .address-info {
    display: flex;
    align-items: center;
    border-radius: 10px;
    background-color: #fff;
    padding: 10px;
    margin: 5px 10px;
  }

  .goods-spec {
    overflow-y: auto;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px;
    flex: 1;
    min-height: 0;
    .spec-item {
      margin-bottom: 35px;

      .spec-title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        line-height: 20px;
        margin-bottom: 4px;
      }

      .spec-flex {
        flex-flow: row wrap;

        .spec-value {
          padding: 6px 11px;
          background: #f5f5f5;
          border-radius: 5px;
          font-size: 14px;
          color: #333;
          margin-right: 15px;
          margin-top: 12px;
          border: 1px solid #f4f4f4;
        }

        .spec-value.checked {
          color: var(--primary-color);
          background: color-mix(in srgb, var(--primary-color) 10%, #fff);
          border: 1px solid var(--primary-color);
        }
      }
    }

    .goods-number {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .number-label {
        font-size: 14px;
        font-weight: bold;
        color: #333;
      }

      margin-bottom: 10px;
      margin-right: 5px;
    }
  }

  .footer {
    background: #ffffff;
    padding: 7px 16px;
    padding-bottom: calc(7px + var(--safe-area-inset-bottom));
    // position: fixed;
    // bottom: 0;
    // width: calc(100% - 32px);
    box-sizing: border-box;
    margin-top: auto;
    flex: none;
    .btn {
      border-radius: 7px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 14px;
      color: #ffffff;
    }
    .btn.disabled {
      background: #e1e1e1 !important;
      box-shadow: 0px 3 4px 0px rgba(225, 225, 225, 0.5);
    }
  }
}

.sku-popup {
  overflow: visible;
  // min-height: 200px;
}
.iphonex-bottom {
  // padding-bottom: 44px !important;
  // padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}
</style>
