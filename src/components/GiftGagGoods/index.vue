<template>
  <div class="goods-list" v-if="list.length>0">
    <div class="title">人气最推荐</div>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="——没有更多了——"
      :immediate-check="false"
      @load="getList"
    >
      <div class="goods-list-style">
        <div class="goods-list-style-item" v-for="item in list" :key="item.spuId" @click="$goodsRouter(item)">
          <img class="goods-img" :src="item.image">
          <div class="goods-info">
            <div class="goods-info-top">
              <div class="goods-title overflow-2">
                {{ item.spuTitle }}
              </div>
              <!-- <div class="goods-spec overflow-1">
                {{ item.detailTitle }}
              </div> -->
              <div class="goods-tags">
                <div v-if="item.billFlag==='Y'" class="tag-item">
                  <div class="text">分期专享</div>
                </div>
                <div class="tag-item" v-for="tag in item.goodsLabels" :key="tag">
                  <img v-if="tag === '现货速发'" class="thunder" src="@/assets/images/goods/thunder.png"/>
                  <div class="text">{{ tag }}</div>
                </div>
              </div>
            </div>
            <div class="goods-buy">
              <div class="goods-price">
                <div class="goods-sales-price">
                  ￥{{ formatMoney(item.priceDown) }}
                </div>
                <div class="goods-market-price">
                  ￥{{ formatMoney(item.price) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script setup>
  import { goodsList } from '@/api/goods'
  const loading = ref(false)
  const finished = ref(false)
  const list = ref([])
  const data = reactive({
    queryParams: {
      billFlag: 'Y',
      pageNum: 1,
      pageSize: 10
    }
  })
  const { queryParams } = toRefs(data)
  const getList = () => {
    loading.value = true
    goodsList(queryParams.value).then(res => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
.goods-img{
  border-radius: 6px;
}
.goods-list{
  padding: 0 10px;
  .title{
    text-align: center;
    margin: 20px 0 13px 3px;
    font-size: 20px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: 700;
    color: #333;
    line-height: 21px;
  }

  .goods-list-style{
    &-item{
      display: flex;
      padding: 10px;
      background: #FFFFFF;
      margin-bottom: 10px;
      border-radius: 10px;
      .goods-img{
        width: 105px;
        height: 105px;
      }
      .goods-info{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        flex: 1;
        .goods-info-top{
          .goods-title{
            font-size: 14px;
            font-weight: 400;
            color: #333;
            line-height: 20px;
          }
          .goods-spec{
            margin-top: 1px;
            font-size: 12px;
            transform: scale(0.9);
            transform-origin: left top;
            color: #999999;
            line-height: 16px;
          }
          .goods-label{
            display: flex;
            .scale{
              border-radius: 2px;
              border: 1px solid #E9362E;
              font-size: 12px;
              transform: scale(0.8);
              transform-origin: left top;
              padding: 1px 3px;
              color: #E9362E;
              margin-top: 4px;
            }
          }
        }
        .goods-info-bottom{
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          .cart-img{
            width: 23px;
            height: 23px;
          }
        }
      }
    }
  }
}
.goods-price{
  display: flex;
  align-items: center;
  .goods-market-price{
    margin-left: 5px;
  }
}
.goods-tags{
  display: flex;
  margin-top: 6px;
  overflow-x: auto;
  .tag-item{
    word-break: keep-all;
    border-radius: 3px;
    border: 1px solid #FE671A;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .thunder {
      width: 8px;
      height: 11px;
      margin-left: 5px;
      margin-top: 2px;
      margin-bottom: 2px;
      margin-right: -2px;
    }
    .text{
      font-size: 14px;
      color: #FE671A;
    }
  }
}
.goods-info{
  .goods-price{
    .goods-sales-price{
      font-weight: 600;
      font-family: Roboto;
      font-size: 14px;
      color: #333;
      line-height: 21px;
    }
    .goods-market-price{
      font-size: 14px;
      color: #333;
      line-height: 18px;
      display: flex;
      align-items: flex-end;
    }
    .goods-member-price{
      margin-top: 5px;
      font-size: 12px;
      height: 15px;
      color: #5D3F2D;
      background: #FBDEC4;
      border-radius: 2px;
      padding-right: 5px;
      display: flex;
      align-items: center;
      img{
        width: 26px;
        height: 15px;
      }
    }
  }
}
</style>