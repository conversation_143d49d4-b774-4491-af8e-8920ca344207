<template>
  <div class="page" @scroll="onScrollChange" ref="pageRef">
    <div class="bg"></div>
    <div class="nav-wrapper">
      <div class="nav-content" :style="navBarStyle">
        <div class="left" @click="onLeftClick">
          <!-- 默认状态 -->
          <van-icon v-if="isDefault && isShowBack" size="22" name="arrow-left" />
          <!-- 定制具名插槽 -->
          <slot name="nav-left"></slot>
        </div>
        <div class="center">
          <!-- 默认状态 -->
          <span class="page-title" v-if="isDefault">{{ pageName }}</span>
          <!-- 定制具名插槽 -->
          <slot name="nav-center"></slot>
        </div>
        <div class="right">
          <!-- 定制具名插槽 -->
          <slot name="nav-right"></slot>
        </div>
      </div>
    </div>
    <div class="children">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const router = useRouter()
const store = useStore()
const isIphoneX = window.isIphoneX
const pageRef = ref(null)
// 滚动值
const scrollTopValue = ref(-1)
const ANCHOR_SCROLL_TOP = 64
const data = reactive({
  navBarStyle: {
    backgroundColor: '',
    // position: 'fixed'
  },
})
const { navBarStyle } = toRefs(data)
const props = defineProps({
  rgbColor: {
    type: String,
    default: '255,103,26',
  },
  isDefault: {
    default: true,
    type: Boolean,
  },
  isShowBack: {
    default: true,
    type: Boolean,
  },
  // 默认状态下页面标题的名称
  pageName: {
    default: '',
    type: String,
  },
  pageStyle: {
    type: Object,
    default: () => {},
  },
})
onActivated(() => {
  pageRef.value.scrollTop = scrollTopValue.value
})
// 滚动
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  emit('scrollChange', $e.target.scrollTop)
}
const emit = defineEmits(['onLeftClick', 'scrollChange'])
const onLeftClick = () => {
  //store.commit('SET_ISIOSMOVEBACK', false) // 重置变量isIosMoveBack
  emit('onLeftClick')
}
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  .bg {
    position: absolute;
    top: 0;
    z-index: 0;
    width: 100%;
    height: 479px;
    background: linear-gradient(
      180deg,
      #4671eb 0%,
      #f5b8b3 39.77%,
      #f6c7c3 45.86%,
      #f6d6d3 52.39%,
      #f6e6e5 60.99%,
      #f6f6f6 72.14%
    );
  }

  .children {
    // position: absolute;
    // height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    min-height: 0;
    // top: 0;
    z-index: 1;
    // top: 69px;
    // top: calc(44px + var(--safe-area-inset-top));
    width: 100%;
    // height: calc(100% - 44px - var(--safe-area-inset-top));
  }
  .children.iphonex {
    // top: 88px;
    // top: calc(44px + var(--safe-area-inset-top));
  }

  .nav-wrapper.iphonex {
    // height: 88px;
    // height: calc(69px + var(--safe-area-inset-top));
    // background: linear-gradient(180deg, #4671eb, #f47368 88px);
  }

  .nav-wrapper {
    box-sizing: border-box;
    width: 100%;
    // height: 69px;
    background: linear-gradient(180deg, #4671eb, #f46559);
    // position: absolute;
    // top: 0;
    z-index: 2;

    .nav-content {
      width: 100%;
      height: 44px;
      display: flex;
      justify-content: space-between;
      padding-top: var(--safe-area-inset-top);
      align-items: center;
      flex-shrink: 0;
      z-index: 1999;
      // position: fixed;
      // top: 0;
      color: #ffffff;
      font-size: 18px;

      .left {
        padding-left: 10px;
        display: flex;
        align-items: center;

        .van-icon {
          padding: 8px;
          text-align: center;
        }
      }

      .right {
        display: flex;
        padding-right: 10px;
        align-items: center;
        position: relative;
        font-size: 14px;
        min-width: 38px;
      }

      .center {
        display: flex;
        height: 100%;
        flex-grow: 1;
        padding: 0 5px;

        .page-title {
          align-self: center;
          margin: 0 auto;
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
