<template>
  <div class="active-loading-page">
    <navigation-bar :is-show-back="false" />
    <div class="active-loading-page-content">
      <div class="active-loading-img">
        <img class="loader" src="@/assets/images/credit/credit-loading.png">
      </div>
      <div class="tips1">信息安全保护中</div>
      <div class="tips2">请耐心等待</div>
    </div>
  </div>
</template>

<script setup name="ActiveLoading">
  import { creditQuota } from '@/api/customer'
  import { getConfing } from '@/api/base';
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const store = useStore()
  const backRouter = ref('')
  onMounted( async () => {
    backRouter.value = sessionStorage.getItem('backUrl')
    const ress = await getConfing({ key: 'LOCAL_CREDIT_SCENE'})
    let sceneCode = ress.data.length > 0 ? ress.data[0].configValue : proxy.$global.CREDIT_SCENE_CONSUME
    if (localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE) 
    && localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE)!== 'undefined') {
      sceneCode = localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE)
    }
    creditQuota({requestType: 'credit', sceneCode }).then(res => {
      store.dispatch('GetInfo')
    })
    setTimeout(() => {
      if (backRouter.value) {
        sessionStorage.removeItem('backUrl')
        router.replace(backRouter.value)
      } else {
        router.push('/my/quota')
      }
    }, 3000)
  })
</script>

<style lang="scss" scoped>
  .active-loading-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #ffffff;
    &-content{
      overflow: hidden;
      padding-top: 130px;
      text-align: center;
      .active-loading-img{
        width: 198px;
        height: 198px;
        margin: 0 auto;
        background: url('@/assets/images/credit/credit-render.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .loader {
        width: 115px;
        height: 115px;
        animation: load 1.4s infinite linear;
      }
      @keyframes load {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .tips1{
        font-weight: 500;
        font-size: 20px;
        color: #333333;
        line-height: 28px;
        text-align: center;
        margin-top: 18px;
      }
      .tips2{
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: center;
        margin-top: 10px;
      }
    }
  }
</style>