<template>
  <div class="active-finish-page">
    <div class="header" @click="goBack">
      完成
    </div>
    <div class="active-finish-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <img src="@/assets/images/active-success.png">
      <div class="title">{{quotaData.ultimaConsum ? '激活成功': '激活中'}}</div>
      <div class="result">授信额度</div>
      <div :class="`result-quota ${quotaData.ultimaConsum?'':'text'}`">
        {{ quotaData.ultimaConsum ? formatMoney(quotaData.ultimaConsum, 0) : '授信额度计算中' }}
      </div>
      <div class="tips">
        该额度仅为平台智能评估额度，本平台仅提供信息展示，不参与任何放款行为，最终审核放款以资方机构为准
      </div>
      <div class="btn-wrapper">
        <div class="back-btn btn theme-text theme-border" @click="goBack">返回</div>
        <div class="quota-btn btn theme-linear-gradient theme-border" @click="toQuota">查看结果</div>
      </div>
    </div>
  </div>
</template>

<script setup name="ActiveFinish">
  import { creditQuota } from '@/api/customer'
  const { proxy } = getCurrentInstance()
  const  isIphoneX = window.isIphoneX
  const router = useRouter()
  const route = useRoute()
  const backRouter = ref('')
  const data = reactive({
    quotaData: {}
  })
  const { quotaData } = toRefs(data)
  onMounted(() => {
    backRouter.value = sessionStorage.getItem('backUrl')
    if(localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE) && 
    localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE) !== 'undefined') { // 指定产品授信
      creditQuota({requestType: 'query', sceneCode: localStorage.getItem(proxy.$global.LOCAL_CREDIT_SCENE)}).then(res => {
        localStorage.removeItem(proxy.$global.LOCAL_CREDIT_SCENE)
        quotaData.value = res.data
      })
    } else {
      creditQuota({requestType: 'query', 
      sceneCode: proxy.$global.CONSUME_INSTALMENT_SCENE }).then(res => {
        quotaData.value = res.data
      })
    }
  })
  const goBack = () => {
    if(backRouter.value) {
      sessionStorage.removeItem('backUrl')
      router.push(backRouter.value)
    } else {
      router.push('/')
    }
  }
  const toQuota = () => {
    router.push('/my/quota?path=/')
  }
</script>

<style lang="scss" scoped>
  .active-finish-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #ffffff;
    .header{
      height: 44px;
      line-height: 44px;
      text-align: right;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      padding-right: 16px;
      margin-top: 44px;
    }
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      text-align: center;
      img{
        width: 30px;
        height: 30px;
        margin: 0 auto;
        margin-top: 17px;
      }
      .title{
        margin-top: 10px;
        font-size: 18px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #363636;
        line-height: 25px;
      }
      .result{
        font-size: 15px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #363636;
        line-height: 21px;
        margin-top: 35px;
      }
      .result-quota{
        margin-top: 14px;
        background: url('@/assets/images/credit/active-bg.png');
        background-size: 100% 100%;
        font-size: 62px;
        font-family: Roboto-Medium, Roboto;
        font-weight: bold;
        color: #363636;
        overflow: hidden;
      }
      .result-quota.text{
        font-size: 34px;
        color: #A8A8A8;
      }
      .explain{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #C2784F;
        line-height: 17px;
        margin-top: 13px;
      }
      .btn-wrapper{
        display: flex;
        margin-top: 47px;
        padding: 0 40px;
        justify-content: space-between;
        .btn{
          width: 137px;
          height: 49px;
          border-radius: 25px;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 49px;
          text-align: center;
        }
        .back-btn{
          background: #ffffff;
        }
      }
      
      .tips{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #A8A8A8;
        line-height: 17px;
        padding: 0 30px;
        margin-top: 10px;
        text-align: left;
      }
    }
  }
</style>