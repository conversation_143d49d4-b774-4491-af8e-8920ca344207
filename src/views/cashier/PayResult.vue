<template>
  <div class="result-page">
    <navigation-bar
      :navBarStyle="{ backgroundColor: '#f9f9f9' }"
      @onLeftClick="onBackClick"
    ></navigation-bar>
    <div class="result-page-context">
      <goods-order
        v-if="orderSource === $global.CASHIER_GOODS_ORDER"
        :state="state"
        :order-id="orderId"
        :pay-type="payType"
        @backIndex="backIndex"
        @onBackClick="onBackClick"
      ></goods-order>
      <telphone-order
        v-if="orderSource === $global.CASHIER_SPEND_RECHARGE"
        :state="state"
        @onBackClick="onBackClick"
        @backIndex="backIndex"
      >
      </telphone-order>
      <member-order
        v-if="
          orderSource === $global.CASHIER_VIP_BUY ||
          orderSource === $global.CASHIER_VIP_LOAN_BUY ||
          orderSource === $global.CASHIER_VIP_DISCOUNT
        "
        :state="state"
        :orderSource="orderSource"
        @onBackClick="onBackClick"
        @backIndex="backIndex"
      />
      <equity-order
        v-if="orderSource === $global.CASHIER_EQUITY"
        :state="state"
        :order-id="orderId"
        @onBackClick="onBackClick"
        @backIndex="backIndex"
      >
      </equity-order>
      <!-- 只有购买商品和手机充值订单才展示开通会员提示 -->
      <!-- <VipAd2 v-if="orderSource===$global.CASHIER_GOODS_ORDER || orderSource===$global.CASHIER_SPEND_RECHARGE"/> -->
      <GoodsList :listType="{ type: 'region', value: $global.GOODS_REGION_FOLLOW }" />
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import GoodsOrder from './components/payResult/GoodsOrder'
import TelphoneOrder from './components/payResult/TelphoneOrder'
import MemberOrder from './components/payResult/MemberOrder'
import EquityOrder from './components/payResult/EquityOrder'
import VipAd2 from '@/components/AcceleratorCard/VipAd2/VipAd2.vue'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
const state = ref('')
const orderId = ref('')
const payType = ref('')
const orderSource = ref('')
const route = useRoute()
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()

const onBackClick = () => {
  if (orderSource.value === proxy.$global.CASHIER_GOODS_ORDER) {
    router.push({ path: '/my/goods-order-detail', query: { orderId: orderId.value } })
  } else {
    router.go(-1)
  }
}
// 返回首页
const backIndex = () => {
  router.push('/')
}
onMounted(() => {
  state.value = route.query.state
  orderId.value = route.query.orderId
  payType.value = route.query.type
  orderSource.value = route.query.orderSource
})
</script>

<style lang="scss" scoped>
.result-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #f9f9f9;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #f9f9f9;
    text-align: center;
    .fail {
      .btn {
        margin-top: 57px;
      }
    }
    .success {
      .tips {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #363636;
        margin-top: 7px;
      }
      .bill-pay {
        margin: 0 30px;
        margin-top: 9px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #a8a8a8;
        line-height: 20px;
      }
      .detail {
        margin-top: 30px;
      }
      .index {
        width: 278px;
        height: 40px;
        line-height: 40px;
        border-radius: 26px;
        border: 1px solid #fe9a1f;
        font-size: 18px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #fe9a1f;
        margin: 0 auto;
        margin-top: 20px;
      }
    }
    img {
      width: 60px;
      height: 60px;
    }
    .pay-state {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #363636;
      line-height: 25px;
      margin-top: 15px;
    }
    .btn {
      width: 280px;
      height: 46px;
      background: #fe9a1f;
      border-radius: 26px;
      margin: 0 auto;
      font-size: 18px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 46px;
    }
  }
}
</style>