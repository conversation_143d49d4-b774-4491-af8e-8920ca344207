<template>
    <div class="pay-centext">
      <div class="pay-product-header" @click="checkedPay">
        <div class="pay-info"><img class="logo" :src="payInfo.channelIcon"><span class="name">{{ payInfo.payName }}</span></div>
        <img class="radio" v-if="payInfo.id === modelValue" src="@/assets/images/cashier/active.png">
        <img class="radio" v-else src="@/assets/images/cashier/inactive.png">
      </div>
    </div>
  </template>
  
  <script setup>
    const props = defineProps({
      payInfo: {
        type: Object,
        default: () => {}
      },
      modelValue: {
        type: Number,
        default: null
      }
    })
    const emit = defineEmits(['update:modelValue','updatePayProduct'])
    const checkedPay = () => {
      emit('update:modelValue', props.payInfo.id)
      emit('updatePayProduct')
    }
  </script>
  
  <style lang="scss" scoped>
    .pay-centext{
      background: #FFFFFF;
      border-radius: 4px;
      margin: 10px 5px 0;
    }
  </style>