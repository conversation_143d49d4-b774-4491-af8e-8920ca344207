<template>
  <van-popup round :show="show" @click-overlay="cancel()">
    <div class="authorize-popup">
      <div class="title">{{ title }}激活说明</div>
      <div class="content">
        <div class="p1">您正在申请{{ title }}、请您充分阅读提示内容</div>
        <div class="p2">1、额度评估时需要查询您的设备信息，有助于提升通过率，请您授权操作；</div>
        <div class="p2">
          2、审核过程中，本平台客服及合作方不会单独向用户收费或要求线下打款，谨防诈骗。
        </div>
      </div>
      <div class="btn-wrapper">
        <!-- <div class="btn text-black cancel" @click="authorizeOut">放弃提升</div> -->
        <div class="btn text-white theme-linear-gradient confirm" @click="authorizeGet">
          去授权获得提升
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog, useToggle } from '@vueuse/core'
import appJs from '@/utils/appJs'
import { closeToast, showLoadingToast } from 'vant'

const [show, toggleShow] = useToggle(false)
const props = defineProps({
  title: {
    type: String,
    default: '授信',
  },
})
const emit = defineEmits(['authorizeGet', 'authorizeOut'])
const { reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
onReveal(() => {
  toggleShow(true)
})
onCancel(() => {
  toggleShow(false)
})
onConfirm((data) => {
  toggleShow(false)
  emit('authorizeGet', data)
})
async function authorizeGet() {
  toggleShow(false)
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    })
    const phoneInfo = await appJs.appGetPhoneInfo()
    closeToast()
    confirm({ phoneInfo })
  } catch (error) {
    // closeToast()
    confirm({})
    // cancel(error)
  }
}

defineExpose({ show, reveal, confirm, cancel })
</script>

<style lang="scss" scoped>
.authorize-popup {
  width: 260px;
  padding: 20px;
  padding-bottom: 28px;
  background: #ffffff;
  border-radius: 7px;
  .title {
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
    text-align: center;
  }
  .content {
    margin-top: 10px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    .p1 {
      margin-top: 12px;
      margin-bottom: 10px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #363636;
      line-height: 20px;
    }
  }
  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 18px;
    .cancel {
      border: 1px solid #cccccc;
    }
    .btn {
      width: 120px;
      height: 40px;
      border-radius: 21px;
      line-height: 40px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
  }
}
</style>
