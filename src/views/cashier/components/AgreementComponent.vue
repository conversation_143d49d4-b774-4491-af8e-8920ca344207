<template>
  <div>
    <van-popup v-model:show="modelValue" safe-area-inset-bottom position="bottom" round>
      <div class="title">
        <div class="text-gray close" @click="colsePopup">取消</div>
        相关协议
      </div>
      <div class="list padding-bottom">
        <div
          class="solid-top item"
          @click="
            agreementPopupRef.reveal({
              title: '个人信息查询及使用授权',
              src: '/agreement/personal-info-auth.htm',
            })
          "
        >
          《个人信息查询及使用授权》
        </div>
        <!-- <div
          class="solid-top item"
          @click="
            agreementPopupRef.reveal({
              title: '电子签章授权书',
              src: '/agreement/yql-sign.htm',
            })
          "
        >
          《电子签章授权书》
        </div> -->
        <div
          class="solid-top item"
          @click="
            agreementPopupRef.reveal({
              title: '借款合同',
              src: '/agreement/loan-contract.htm',
            })
          "
        >
          《借款合同》
        </div>
      </div>
    </van-popup>
    <iframe-popup ref="agreementPopup" />
  </div>
</template>

<script setup>
import { useTemplateRef } from 'vue'
import IframePopup from '@/components/IframePopup.vue'
const modelValue = defineModel('modelValue', { type: Boolean, default: false })
const agreementPopupRef = useTemplateRef('agreementPopup')
const colsePopup = () => {
  modelValue.value = false
}
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  height: 60px;
  line-height: 60px;
  font-size: 18px;
  position: relative;
  .close {
    position: absolute;
    left: 20px;
    font-size: 14px;
  }
}
.list {
  padding-bottom: 50px;
  .item {
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    text-align: center;
  }
}
</style>
