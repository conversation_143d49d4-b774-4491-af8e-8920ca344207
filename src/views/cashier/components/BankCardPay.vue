<template>
  <div class="bank-card-pay-centext">
    <div class="title" @click="checkedPay">
      <div class="pay-info"><img class="logo" :src="payInfo.channelIcon"><span class="name">{{ payInfo.payName }}</span></div>
      <!-- <img class="radio" v-if="payInfo.id === modelValue" src="@/assets/icons/cashier/active.png">
      <img class="radio" v-else src="@/assets/icons/cashier/inactive.png"> -->
      <van-checkbox :model-value="payInfo.id === modelValue" />
    </div>
    <div class="bank-list" v-if="payInfo.id === modelValue">
      <template v-if="(bankList.length > 0)">
        <div v-for="(item, index) in bankList" :key="index" class="bank-item" @click="checkBank(item)">
          <div :class="`bank-logo ${ bankCode(item.bankName) }`"></div>
          <div class="bank-info solid-top">
            <div class="bank-name">{{ item.bankName }}（{{ stringBankAccount(item.cardNoShort) }}）</div>
            <!-- <img class="radio" v-if="(item.id === currentBank)" src="@/assets/icons/cashier/active.png">
            <img class="radio" v-else src="@/assets/images/cashier/inactive.png"> -->
            <van-checkbox :model-value="item.id === currentBank" />
          </div>
        </div>
      </template>
      <div class="add" @click="toBindCardBill">
        <img class="add-icon" src="@/assets/images/cashier/add.png">
        <div class="text solid-top">
          添加银行卡
          <van-icon size="12" color="#cccccc" name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { bankCardList } from '@/api/bankcard'
  import { bankCode } from '@/constant/bank'
  const router = useRouter()
  const props = defineProps({
    payInfo: {
      type: Object,
      default: () => {}
    },
    modelValue: {
      type: Number,
      default: null
    },
    orderId: {
      type: String,
      default: ''
    }
  })
  const bankList = ref([])
  const currentBank = ref(0)
  onMounted(() => {
    getBankList()
  })
  const getBankList = () => {
    bankCardList({pageNum:1, pageSize:100, channelId: props.payInfo.channelId,
      bankCardType: 'QUICKPAY', orderId: props.orderId,
      cmId: props.payInfo.cmId}).then(res=> {
      if(res.data.length>0) {
        if(!currentBank.value) {
          currentBank.value = res.data[0].id
          emit('updateCheckBank', res.data[0].id)
        }
        bankList.value = res.data
      }
    })
  }
  // 绑卡
  const toBindCardBill = () => {
    router.push({path: '/my/bankcard/add',
    query: { channelId: props.payInfo.bankChannelId,
      type: props.payInfo.cardBindType, orderId: props.orderId,
      cmId: props.payInfo.cmId,
    returnUrl: '/cashier?orderId='+ props.orderId }})
  }
  const emit = defineEmits(['update:modelValue', 'updateCheckBank', 'updatePayProduct'])
  const checkBank = (item) => {
    currentBank.value = item.id
    emit('updateCheckBank', item.id)
  }
  const checkedPay = () => {
    if ( props.payInfo.id !== props.modelValue ) {
      emit('update:modelValue', props.payInfo.id)
      emit('updatePayProduct')
      // 查询银行卡
      if(bankList.value.length === 0) {
        getBankList()
      }
    }
  }
  const stringBankAccount = (accountNo) => {
    if(accountNo)
    return accountNo.slice(accountNo.length-4,accountNo.length)
  }
</script>

<style lang="scss" scoped>
  .bank-card-pay-centext{
    background: #FFFFFF;
    border-radius: 4px;
    margin: 10px 5px 0;
    .title{
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 15px;
      padding-right: 16px;
      .pay-info{
        display: flex;
        align-items: center;
        img{
          width: 24px;
          height: 24px;
        }
        .name{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-left: 10px;
        }
      }
      // img.radio{
      //   width: 16px;
      //   height: 16px;
      // }
      .van-checkbox{
        --van-checkbox-size:16px;
      }
    }
    .bank-list{
      padding-left:15px;
      .bank-item{
        display: flex;
        align-items: center;
        .bank-logo{
          width: 24px;
          height: 24px;
          display: block;
          margin-right: 12px;
        }
        .bank-info{
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 17px 0;
          padding-right: 17px;
          .bank-name{
            font-size: 14px;
            color: #222222;
            line-height: 22px;
          }
          .van-checkbox{
            --van-checkbox-size:16px;
          }
        }
      }
      .add{
        display: flex;
        align-items: center;
        color: #999999;
        .add-icon{
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }
        .text{
          flex: 1;
          padding: 13px 0;
          font-size: 13px;
          font-weight: 500;
          color: #363636;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 17px;
        }
      }
    }
  }
</style>