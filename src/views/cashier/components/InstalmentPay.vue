<template>
  <div class="instalment-context">
    <div class="bill-header" @click="checkedPay">
      <div class="pay-info">
        <div class="name-quota">
          <img class="logo" :src="payInfo.channelIcon" />
          <div class="name">{{ payInfo.payName }}<sub>花要尽兴，更要放心</sub></div>
        </div>
        <div class="quota" v-if="payInfo.quota && billFlag !== 'N'">
          可用{{
            formatNumber({ padRight: 2, prefix: '￥' })(payInfo.quota.consumeUltimaAmount)
          }}，总额度{{ formatNumber({ padRight: 2, prefix: '￥' })(payInfo.quota.consumeAmount) }}
        </div>
      </div>
      <van-checkbox :model-value="payInfo.id === modelValue" />
      <!-- <img class="radio" v-if="payInfo.id === modelValue" src="@/assets/images/cashier/active.png">
      <img class="radio" v-else src="@/assets/images/cashier/inactive.png"> -->
    </div>
    <div class="instalment-info">
      <div class="redit-state" v-if="payInfo.creditInfo.creditStatus !== 'SUCC'">
        {{
          payInfo.creditInfo.creditStatus === 'NON' || user.flow !== 'FINISH'
            ? '您未获得授信额度，请先前往授信激活'
            : payInfo.creditInfo.creditStatus === 'APPLY'
            ? '您提交授信额度计算中，请耐心等待'
            : '温馨提示:当前您的该笔订单暂时无法使用轻享卡支付:建议您可以尝试更换其他支付方式哦~'
        }}
        <van-icon name="arrow" />
      </div>
      <div v-else-if="billFlag === 'N'" class="redit-state">
        该订单暂不支持分期，请选择其他方式支付
      </div>
      <div
        v-if="
          payInfo.creditInfo.creditStatus !== 'FIAL' &&
          new Date().getTime() < payInfo.creditInfo.allowTime
        "
        class="text-df text-grey margin-top-xs"
      >
        下次可发起授信时间：{{ parseTime(payInfo.creditInfo.allowTime) }}
      </div>
      <div v-else class="redit-succ">
        <div v-if="billFlag !== 'N'" class="product-periods-wrap">
          <div class="product-periods">
            <div class="list">
              <div
                :class="`periods-item text-center ${
                  item.productResponse.subProductId === subProductId
                    ? 'active theme-text theme-border'
                    : ''
                }`"
                v-for="item in payInfo.trials"
                :key="item.term"
                @click="checkPeriods(item)"
              >
                <div class="">
                  <!-- <span v-if="item.productResponse.term === 1">不分期</span> -->
                  <span
                    >￥{{ formatMoney(item.productResponse.monthlyPayment) }}x{{
                      item.productResponse.termTypeValue
                    }}期</span
                  >
                </div>
                <div class="sub">免手续费</div>

                <!-- <span
                  v-if="item.productResponse.term > 1 && item.productResponse.termType === 'DAY'"
                >
                  <span>￥{{ formatMoney(item.productResponse.monthlyPayment) }}</span> x
                  <span
                    >{{ item.productResponse.term }}({{
                      item.productResponse.term * item.productResponse.termTypeValue
                    }}天)</span
                  >
                </span>
                <span v-else>
                  <span v-if="item.productResponse.termType === 'DAY'">
                    ￥{{ formatMoney(item.productResponse.monthlyPayment) }} ({{
                      item.productResponse.termTypeValue
                    }}天)
                  </span>
                  <span v-if="item.productResponse.termType === 'MONTH'">
                    ￥{{ formatMoney(item.productResponse.monthlyPayment) }} ({{
                      item.productResponse.term
                    }}个月)
                  </span>
                </span> -->
              </div>
            </div>
          </div>
          <div
            v-if="payInfo.trials && payInfo.trials.length > 0"
            class="periods-detail"
            @click="handleBill"
          >
            <img src="@/assets/icons/info.png" />
            <span class="text">查看分期明细</span>
            <div v-if="productResponse && productResponse.term > 1" class="product-period">
              （分{{ productResponse.term }}期还款/每期{{ 1
              }}{{ productResponse.termType === 'DAY' ? '天' : '月' }}）
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-divider style="margin: 0 14px" />
    <van-popup
      position="bottom"
      v-model:show="showBill"
      closeable
      round
      safe-area-inset-bottom
      class="bill-wrapper"
      teleport="body"
    >
      <header>
        <div class="header-title solid-bottom">还款计划</div>
      </header>
      <main>
        <!-- <div class="bill-wrapper"> -->
        <div class="explain solid-bottom">
          年化综合资金成本 {{ parseFloat(billData.yearRate * 100) }}%，总息费 ¥{{
            formatMoney(billData.totalInterest)
          }}
        </div>
        <div class="bill-list">
          <div class="bill-item" v-for="item in billData.repayPlan" :key="item.periodNo">
            <div class="left theme-text">{{ item.periodNo }}期</div>
            <div class="division">
              <div class="circle"></div>
              <div class="line"></div>
            </div>
            <div class="right">
              <div class="title">{{ formatMoney(item.repayAmount) }}</div>
              <div class="repay-info">
                本金：{{ formatMoney(item.repayPrincipal) }}+息费：{{
                  formatMoney(item.repayInterest)
                }}
              </div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </main>
    </van-popup>
  </div>
</template>

<script setup>
import { accAdd } from '@/utils/common'
import { showDialog } from 'vant'
import formatNumber from 'format-number'
const store = useStore()
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const subProductId = ref(0)
const showBill = ref(false)
const periodProduct = ref('')
const data = reactive({
  billData: null,
  checkedProduct: null,
  productResponse: null,
})
const { billData, checkedProduct, productResponse } = toRefs(data)
const props = defineProps({
  payInfo: {
    type: Object,
    default: () => {},
  },
  modelValue: {
    type: Number,
    default: null,
  },
  orderId: {
    type: String,
    default: '',
  },
  billFlag: {
    type: String,
    default: '',
  },
})
const emit = defineEmits([
  'update:modelValue',
  'updatePayProduct',
  'updatePeriods',
  'updateSubProduct',
])
const initProduct = (payInfo) => {
  if (payInfo.trials && payInfo.trials.length > 0) {
    subProductId.value = subProductId.value || payInfo.trials[0].productResponse.subProductId
    checkedProduct.value = checkedProduct.value || payInfo.trials[0]
    productResponse.value = productResponse.value || payInfo.trials[0].productResponse
    emit('updatePeriods', checkedProduct.value?.productResponse?.productId, true)
    emit('updateSubProduct', checkedProduct.value?.productResponse?.subProductId)
  }
}
watch(
  () => props.payInfo,
  (v1, v2) => {
    if (v1 !== v2) {
      initProduct(v1)
    }
  },
  { deep: true, immediate: true }
)
// const productId = computed(() => { // 默认第一条
//   let id = 0
//   if(props.payInfo.trials && props.payInfo.trials.length > 0) {
//     id = props.payInfo.trials[0].productResponse.subProductId
//     checkedProduct.value = props.payInfo.trials[0]
//     emit('updatePeriods', checkedProduct.value.productResponse.productId, true)
//     emit('updateSubProduct', checkedProduct.value.productResponse.subProductId)
//   }
//   return id
// })

const checkedPay = () => {
  emit('update:modelValue', props.payInfo.id)
  checkedProduct.value?.productResponse?.productId &&
    emit('updatePeriods', checkedProduct.value?.productResponse?.productId)
  emit('updatePayProduct')
}
const checkPeriods = (item) => {
  checkedProduct.value = item
  subProductId.value = item.productResponse.subProductId
  productResponse.value = item.productResponse
  emit('update:modelValue', props.payInfo.id)
  emit('updatePeriods', item.productResponse.productId)
  emit('updateSubProduct', item.productResponse.subProductId)
}
// 还款计划
const handleBill = () => {
  console.log(checkedProduct.value)
  if (subProductId.value) {
    billData.value = checkedProduct.value.trialResponse
    let total = 0
    billData.value.repayPlan.map((item) => {
      total = accAdd(total, item.repayInterest)
    })
    billData.value.totalInterest = total
    showBill.value = true
  } else {
    showDialog('请选择期数')
  }
}
</script>

<style lang="scss" scoped>
.instalment-context {
  border-radius: 10px;
  margin: 5px;
  .bill-header {
    padding: 20px 16px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .pay-info {
      // display: flex;
      // align-items: center;
      .logo {
        width: 24px;
        height: 24px;
        display: block;
        // margin-right: 10px;
        margin-right: 4px;
        // filter: hue-rotate(0deg) contrast(1.2);
        // transform: scaleY(1.02) translateY(-1px);
        // animation: flame-animation 3s ease-in-out infinite;
        // @keyframes flame-animation {
        //   0% {
        //     filter: hue-rotate(0deg) contrast(1.2) brightness(1);
        //     transform: scale(1) translateY(0);
        //   }
        //   50% {
        //     filter: hue-rotate(10deg) contrast(1.4) brightness(1.1);
        //     transform: scale(1.05) translateY(-2px);
        //   }
        //   100% {
        //     filter: hue-rotate(0deg) contrast(1.2) brightness(1);
        //     transform: scale(1) translateY(0);
        //   }
        // }
      }
      .name-quota {
        display: flex;
        // flex-direction: column;
        // justify-content: center;
        align-items: center;
        .name {
          font-weight: 600;
          font-size: 15px;
          color: #333333;
          // line-height: 20px;
          sub {
            color: #c1c1bf;
            font-size: 14px;
            font-weight: 400;
            margin-left: 4px;
          }
        }
      }
      .quota {
        font-size: 13px;
        // transform: scale(0.9);
        // transform-origin: left center;
        color: #333;
        // line-height: 16px;
        margin-top: 4px;
      }
    }
    .van-checkbox {
      --van-checkbox-size: 16px;
    }
  }
  .instalment-info {
    padding: 20px 16px;
    background: #ffffff;
    border-radius: 8px;
    // margin-top: -10px;
    padding-top: 0;
    .redit-succ {
      .quota {
        font-size: 14px;
      }
      // .product-periods-wrap {
      // }
      .product-periods {
        overflow-y: hidden;
        overflow-x: scroll;
        // flex-wrap: wrap;
        // margin-top: -10px;
        .list {
          width: fit-content;
          display: flex;
          justify-content: space-between;
          gap: 10px;
        }
        .periods-item {
          width: 108px;
          box-sizing: border-box;
          height: 60px;
          // line-height: 45px;
          background: #f3f3f4;
          border-radius: 8px;
          // border: 1px solid #f4f4f4;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
          // margin-top: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px solid transparent;
          line-height: 1.2;
          .sub {
            color: #999999;
          }
        }
        .periods-item.active {
          background: #f3f3f4;
          border: 2px solid #4671eb;
          color: #4671eb;
          .sub {
            color: #4671eb66;
          }
        }
      }
    }
    .redit-state {
      font-size: 12px;
      color: #4671eb;
      background: #4671eb0d;
      border: 1px solid #4671eb;
      border-radius: 4px;
      padding: 8px;
      line-height: 1.2;
      display: flex;
      align-items: center;
      .van-icon {
        margin-left: auto;
      }
    }
    .periods-detail {
      color: #999999;
      display: flex;
      align-items: center;
      margin-top: 10px;
      img {
        width: 13px;
        height: 13px;
      }
      .text {
        display: inline-block;
        margin-left: 3px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
      }
      .product-period {
        font-size: 12px;
        color: #ff3355;
      }
    }
  }
  // .bill-popup {
  //   // max-height: 100%;
  // }
}
.bill-wrapper {
  padding: 30px;
  box-sizing: border-box;
  // max-height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.van-popup__close-icon) {
    top: var(--safe-area-inset-top);
  }
  header {
    flex: none;
  }
  main {
    flex: 1;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header-title {
    font-size: 18px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #363636;
    line-height: 25px;
    padding-bottom: 20px;
  }
  .explain {
    padding: 15px 0 18px 0;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
  }
  .bill-list {
    margin-top: 20px;
    .bill-item {
      display: flex;
      .left {
        width: 44px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: #4671eb0d;
        border-radius: 2px;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
      .division {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 7px;
        .circle {
          width: 6px;
          height: 6px;
          background: #d8d8d8;
          border-radius: 5000px;
          border: 9px solid #ffffff;
        }
        .line {
          width: 2px;
          height: 41px;
          background: #ececec;
          border-radius: 1px;
        }
      }
      .right {
        .title {
          font-size: 17px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #222222;
          line-height: 24px;
        }
        .repay-info {
          margin-top: 6px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #868686;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
