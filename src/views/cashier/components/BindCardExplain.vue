<template>
  <van-popup v-model:show="show" position="bottom" safe-area-inset-bottom round>
    <div class="bind-card-explain">
      <div class="title">分期支付绑卡</div>
      <div class="explain">根据资方要求需要绑定您的实名银行卡信息作为默认还款银行卡，分期成功后资方会按照约定的还款时间扣收款项，您可在【个人中心】查看及管理您的绑卡信息。
      </div>
      <div class="tips theme-text">
        为不影响分期支付使用，请您补充绑卡。
      </div>
      <div class="btn theme-linear-gradient" @click="toBindCard">我已知晓并绑卡</div>
    </div>
  </van-popup>
</template>

<script setup>
  const show = ref(false)
  const emit = defineEmits(['toBindCard'])
  const toBindCard = () => {
    emit('toBindCard')
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .bind-card-explain{
    .title{
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #363636;
      line-height: 23px;
      text-align: center;
      margin: 20px 0;
    }
    .explain{
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #363636;
      line-height: 20px;
      margin: 0 25px;
    }
    .tips{
      font-size: 14px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      line-height: 18px;
      margin: 13px 25px 0;
    }
    .btn{
      height: 40px;
      line-height: 40px;
      border-radius: 22px;
      margin: 50px 15px 20px 15px;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      text-align: center;
    }
  }
</style>