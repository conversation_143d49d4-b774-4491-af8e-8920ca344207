<template>
  <div class="other-pay-centext">
    <div class="pay-product-header" @click="checkedPay">
      <div class="pay-info"><img class="logo" :src="payInfo.channelIcon"><span class="name">{{ payInfo.payName }}</span></div>
      <!-- <img class="radio" v-if="payInfo.id === modelValue" src="@/assets/images/cashier/active.png">
      <img class="radio" v-else src="@/assets/images/cashier/inactive.png"> -->
      <van-checkbox :model-value="payInfo.id === modelValue" />
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    payInfo: {
      type: Object,
      default: () => {}
    },
    modelValue: {
      type: Number,
      default: null
    }
  })
  const emit = defineEmits(['update:modelValue','updatePayProduct'])
  const checkedPay = () => {
    emit('update:modelValue', props.payInfo.id)
    emit('updatePayProduct')
  }
</script>

<style lang="scss" scoped>
  .other-pay-centext{
    background: #FFFFFF;
    border-radius: 4px;
    margin: 10px 5px 0;
  }
  .van-checkbox{
    --van-checkbox-size:16px;
  }
</style>