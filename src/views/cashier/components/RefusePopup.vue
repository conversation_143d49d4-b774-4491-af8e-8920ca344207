<template>
  <van-popup round v-model:show="show">
    <div class="invalid-popup">
      <div class="title">提示</div>
      <div class="content">
        <div>尊敬的用户，很遗憾您的申请未通过，请于以下时间过后再重新申请：</div>
        <div class="margin-top-xs text-center"><span class="time">{{ parseTime(allowTime)}}</span></div>
        <div class="btn" @click="show=false">确认</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
  const props = defineProps({
    allowTime: {
      type: Number,
      default: null
    }
  })
  const show = ref(false)
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .invalid-popup{
    width: 226px;
    padding: 19px;
    .title{
      font-size: 17px;
      font-weight: bold;
      color: #363636;
      text-align: center;
      margin-top: 3px;
    }
    .content{
      margin-top: 21px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #666666;
      line-height: 18px;
      .time{
        font-size: 16px;
        font-weight: bold;
      }
    }
    .btn{
      margin-top: 36px;
      height: 41px;
      background: #FF3355;
      border-radius: 21px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 41px;
    }
  }
</style>