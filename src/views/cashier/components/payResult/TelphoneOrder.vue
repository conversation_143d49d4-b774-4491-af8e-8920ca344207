<template>
  <div class="page-body">
    <template v-if="state === 'Y'">
      <img src="@/assets/images/phone/time_clock.png" alt="time" class="clock_icon" />
      <div class="page-body-title">服务商已接收订单，充值中</div>
      <div class="page-body-tips">慢充服务预计48小时内到账，请耐心留意服务商到账短信</div>
      <div class="page-body-bar">
        <img src="@/assets/images/phone/progress.png" alt="progress_bar" class="page-body-bar-img" />
        <div class="page-body-bar-progress">
          <span>支付成功</span>
          <span class="active">服务商处理中</span>
          <span>话费到账</span>
        </div>        
      </div>
    </template>
    <div v-else class="fail">
      <div class="result">
        <img src="@/assets/icons/cashier/pay-fail.png">
        <div class="pay-state">支付失败</div>
      </div>
    </div>
    <div class="button" @click="onBackClick">返回</div>
  </div>
</template>

<script setup>
  const props = defineProps({
    state: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['onBackClick'])
  const onBackClick = () => {
    emit('onBackClick')
  }
</script>
<style scoped lang="scss">
.page-body {
  width: 100%;
  text-align: center;
  padding-top: 17px;
  padding-bottom: 12px;

  .clock_icon {
    width: 82px;
    height: 82px;
  }

  .result{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 18px;
  }

  &-title {
    font-size: 19px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 26px;
    margin-top: 24px;
  }

  &-tips {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #A8A8A8;
    line-height: 17px;
    margin-top: 5px;
  }

  &-bar {
    width: 100%;
    margin: 20px auto;
    text-align: center;

    &-img {
      width: 227px;
      height: 20px;
    }

    &-progress {
      width: 80%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #A8A8A8;
      line-height: 21px;
      margin: auto;

      .active {
        font-weight: bold;
        color: #333333;
      }
    }
  }

  .button {
    margin: 0 auto;
    width: 104px;
    height: 37px;
    border-radius: 26px;
    font-size: 15px;
    font-weight: 400;
    color: #333;
    line-height: 37px;
    background-color: #fff;
    border: 1px solid #c8c8cc;
  }
}
.fail{
  img{
    width: 28px;
    height: 28px;    
  }
  .pay-state{
    font-size: 25px;
    font-weight: 600;
    color: #333;
  }
}
</style>
