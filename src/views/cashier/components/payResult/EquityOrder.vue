<template>
  <div class="pay-result-page">
    <div v-if="state === 'Y'" class="success">
      <div class="result">
        <img src="@/assets/images/cashier/pay-success.png" />
        <span class="pay-state">支付成功</span>
      </div>
      <div class="content">
        <div class="label-group">
          <div class="label">订单状态：</div>
          <div class="value">{{ $global.ORDER_CHL_STATUS[orderInfo.chnlOrderStatus] }}</div>
        </div>
        <div class="label-group" v-if="orderInfo.productGroup ==='DIRECT_CHARGE'">
          <div class="label">充值账户：</div>
          <div class="value">{{ orderInfo.chargeAcctid }}</div>
        </div>
        <div class="label-group">
          <div class="label">订单号：</div>
          <div class="value">{{ orderId }}</div>
        </div>
        <div class="label-group">
          <div class="label">商品单价：</div>
          <div class="value">￥{{ formatMoney(orderInfo.unitPrice) }}</div>
        </div>
        <div class="label-group" v-if="orderInfo.productGroup ==='ECARD_CHARGE'">
          <div class="label">购买数量：</div>
          <div class="value">{{ orderInfo.orderNum }}</div>
        </div>
        <div class="label-group">
          <div class="label">下单时间：</div>
          <div class="value">￥{{ parseTime(orderInfo.createTime) }}</div>
        </div>
        <div class="label-group big">
          <div class="label">支付金额：</div>
          <div class="value">￥{{ formatMoney(orderInfo.realAmount) }}</div>
        </div>
        <div class="btn" @click="onBackClick">返回</div>
      </div>
    </div>
    <div v-else class="fail">
      <div class="result">
        <img src="@/assets/images/cashier/pay-fail.png">
        <div class="pay-state">支付失败</div>
      </div>
      <div class="btn" @click="onBackClick">返回</div>
    </div>
  </div>
</template>

<script setup>
  import { euqityOrder } from '@/api/member'
// import { onMounted } from '@vue/runtime-core'
  const props = defineProps({
    state: {
      type: String,
      default: ''
    },
    orderId:  {
      type: String,
      default: ''
    }
  })
  const data = reactive({
    orderInfo: {}
  })
  const { orderInfo } = toRefs(data)
  const emit = defineEmits(['onBackClick'])
  const onBackClick = () => {
    emit('onBackClick')
  }
  onMounted(() => {
    euqityOrder({ orderId: props.orderId }).then(res => {
      orderInfo.value = res.data
    })
  })
</script>

<style lang="scss" scoped>
.pay-result-page{
  padding-top: 17px;
  padding-bottom: 12px;
}
.success-image{
  width: 100%;
}
  .result{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 18px;
  }

img{
    width: 28px;
    height: 28px;
  }
  .pay-state{
    font-size: 25px;
    font-weight: 600;
    color: #333;
  }
  .btn{
    margin: 0 auto;
    width: 104px;
    height: 37px;
    border-radius: 26px;
    font-size: 15px;
    font-weight: 400;
    color: #333;
    line-height: 37px;
    background-color: #fff;
    border: 1px solid #c8c8cc;
  }

.content{
  margin: 10px;
  padding: 10px 0 28px 0;
  border-radius: 15px;
  background: #ffffff;
  .label-group{
    display: flex;
    justify-content: space-between;
    padding: 12px 18px;
    font-size: 14px;
    color: #333333;
  }
  .label-group.big{
    font-size: 16px;
    font-weight: bold;
  }
}
.page-body-button {
  width: 280px;
  height: 46px;
  text-align: center;
  border-radius: 26px;
  line-height: 44px;
  font-size: 18px;
  font-family: PingFang-SC-Medium, PingFang-SC;
  font-weight: 500;
  color: #FFFFFF;
  margin: 12px auto;
}
</style>
