<template>
  <div class="pay-reslut-page">
    <div v-if="state === 'Y'" class="success">
      <div class="result">
        <img src="@/assets/images/cashier/pay-success.png">
        <div class="pay-state">支付成功</div>
      </div>
      <!-- <div class="tips">请前往订单中心查看订单状态</div> -->
      <div class="btn-group">
        <div class="btn" @click="backMember">查看订单</div>
        <div class="btn" @click="backMember">返回</div>
      </div>
    </div>
    <div v-else class="fail">
      <div class="result">
        <img src="@/assets/images/cashier/pay-fail.png">
        <div class="pay-state">支付失败</div>
      </div>
      <div class="btn" style="margin: 0 auto;" @click="onBackClick">返回</div>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    state: {
      type: String,
      default: ''
    },
    orderSource: {
      type: String,
      default: ''
    }
  })
  const { proxy } = getCurrentInstance() 
  const router = useRouter()
  const emit = defineEmits(['onBackClick', 'backIndex'])
  // 进入详情页
  const toDetail = () => {
    router.replace('/member/equity-order')
  }
  const onBackClick = () => {
    emit('onBackClick')
  }
  const backMember = () => {
    if (props.orderSource === proxy.$global.CASHIER_VIP_BUY) {
      proxy.$menuRouter('/member')
    } else if (props.orderSource === proxy.$global.CASHIER_VIP_DISCOUNT) {
      router.replace('/save-money')
    } else if (props.orderSource === proxy.$global.CASHIER_VIP_LOAN_BUY) {
      emit('onBackClick')
    }
  }
</script>

<style lang="scss" scoped>
  .pay-reslut-page{
    padding-top: 17px;
    padding-bottom: 12px;
  }
  .result{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 18px;
  }
  .success{
    .tips{
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #363636;
      margin-top: 7px;
    }
    .bill-pay{
      margin: 0 30px;
      margin-top: 9px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #A8A8A8;
      line-height: 20px;
    }
    .detail{
      margin-top: 30px;
    }
    .index{
      width: 278px;
      height: 40px;
      line-height: 40px;
      border-radius: 26px;
      border: 1px solid #FE9A1F;
      font-size: 18px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #FE9A1F;
      margin: 0 auto;
      margin-top: 20px;
    }
  }
  img{
    width: 28px;
    height: 28px;
  }
  .pay-state{
    font-size: 25px;
    font-weight: 600;
    color: #333;
  }
  .btn-group {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .btn{
    width: 104px;
    height: 37px;
    border-radius: 26px;
    font-size: 15px;
    font-weight: 400;
    color: #333;
    line-height: 37px;
    background-color: #fff;
    border: 1px solid #c8c8cc;
  }
</style>