<template>
  <div class="pay-reslut-page">
    <div v-if="state === 'Y'" class="success">
      <!-- <van-icon name="success" class="success-icon" /> -->
      <van-icon name="checked" class="success-icon" />
      <div class="result">
        <!-- <img src="@/assets/images/cashier/pay-success.png" /> -->
        <div class="pay-state">
          {{ payType === $global.PAY_TYPE_BILLPAY ? '订单已提交审核' : '支付成功' }}
        </div>
      </div>
      <!-- <div class="tips">请前往订单中心查看订单状态</div> -->
      <div class="bill-pay" v-if="payType === $global.PAY_TYPE_BILLPAY">
        您的分期订单审核已经提交，资方正在加速审核，<br />请耐心等待最终审核结果以资方为准
      </div>
      <div class="btn-group">
        <div class="btn" @click="backIndex">返回首页</div>
        <div class="btn" @click="toDetail">查看订单</div>
      </div>
    </div>
    <div v-else class="fail">
      <van-icon name="clear" class="fail-icon" />
      <div class="result">
        <!-- <img src="@/assets/images/cashier/pay-fail.png" /> -->
        <div class="pay-state">支付失败</div>
      </div>
      <div class="btn" style="margin: 0 auto" @click="onBackClick">重新支付</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  state: {
    type: String,
    default: '',
  },
  payType: {
    type: String,
    default: '',
  },
  orderId: {
    type: String,
    default: '',
  },
})
const router = useRouter()
const { proxy } = getCurrentInstance()
const emit = defineEmits(['onBackClick', 'backIndex'])
// 进入详情页
const toDetail = () => {
  router.push({ path: '/my/goods-order-detail', query: { orderId: props.orderId } })
}
const onBackClick = () => {
  emit('onBackClick')
}
const backIndex = () => {
  emit('backIndex')
}
</script>

<style lang="scss" scoped>
.pay-reslut-page {
  padding-top: 17px;
  padding-bottom: 12px;
}
.result {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 18px;
  margin-top: 14px;
}
.success {
  .tips {
    font-size: 14px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #363636;
    margin-top: 7px;
    margin-bottom: 18px;
  }
  .bill-pay {
    margin: 0 30px;
    margin-top: 9px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #a8a8a8;
    line-height: 20px;
    margin-bottom: 18px;
  }
}
img {
  width: 28px;
  height: 28px;
}
.pay-state {
  font-size: 25px;
  font-weight: 600;
  color: #333;
}
.btn-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.btn {
  width: 104px;
  height: 37px;
  border-radius: 26px;
  font-size: 15px;
  font-weight: 400;
  color: #333;
  line-height: 37px;
  background-color: #fff;
  border: 1px solid #c8c8cc;
}
.success-icon {
  display: block;
  width: 38px;
  height: 38px;
  font-size: 38px;
  color: #07c261;
  margin: 0 auto;
}
.fail-icon {
  display: block;
  width: 38px;
  height: 38px;
  font-size: 38px;
  color: #C1C1C5;
  margin: 0 auto;
}
</style>
