<template>
  <div class="main">
    <keep-alive>
      <component :is="isComponent"></component>
    </keep-alive>
    <div v-if="!user && show" class="activity-wrapper" :class="{ iphonex: isIphoneX }">
      <div class="activity-block">
        <img class="colse" src="@/assets/images/close.png" @click="show = false" />
        <div class="content">
          <span>登录·轻享花商城，轻奢生活享花就花</span>
          <div class="login-btn" @click="handleLogin">立即登录</div>
        </div>
      </div>
    </div>
    <div class="tool-bar">
      <div
        class="tool-bar-item"
        @click="onItemClick(item, index)"
        v-for="(item, index) in toolBarData"
        :key="index"
      >
        <!-- <img
          v-if="index === selectIndex"
          class="tool-bar-item-img"
          :src="item.iconSrcSelected"
          alt=""
        />
        <img v-else class="tool-bar-item-img" :src="item.iconSrc" alt="" /> -->
        <img
          :key="`${index}-${index === selectIndex}`"
          class="tool-bar-item-img"
          :class="{ 'is-home': item.title === '首页' && index === selectIndex }"
          :src="index === selectIndex ? item.iconSrcSelected : item.iconSrc"
        />
        <p
          class="tool-bar-item-name"
          :style="{
            display:
              (item.title === '首页' && index !== selectIndex) || item.title !== '首页'
                ? 'block'
                : 'none',
          }"
          :class="{ 'tool-bar-item-name theme-text': index === selectIndex }"
        >
          {{ item.title }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup name="Main">
import Home from '@/views/home/<USER>'
import My from '@/views/my/Index'
// import Integral from '@/views/integral/Index'
import Integral from '@/views/find/find.vue'
import { getToken, getUserInfo } from '@/utils/auth'
import Member from '@/views/member/Index'
import { onActivated } from 'vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const route = useRoute()
const router = useRouter()
const store = useStore()
const selectIndex = ref(0)
const isComponent = shallowRef(Home)
const toolBarData = computed(() => store.getters.menuList)
const user = computed(() => store.getters.userInfo)
const show = ref(true)
// 强制更新登录状态
onActivated(() => {
  if (getToken() && getUserInfo()) {
    store.commit('SET_USER', getUserInfo())
  }
  pushComponent()
})
onMounted(() => {})
watch(
  () => store.state.app.menuPath,
  (newValue, oldValue) => {
    console.log(newValue, 'newValue')
    onChangeComponent(newValue)
  }
)
const pushComponent = () => {
  const componentName = route.params.componentName
  if (!componentName) return
  onChangeComponent(componentName)
}
const onChangeComponent = (name) => {
  console.log(name, 'name')
  let _index = 0
  let _item = null
  toolBarData.value.map((item, index) => {
    if (item.url === name) {
      _index = index
      _item = item
    }
  })
  onItemClick(_item, _index)
}
const onItemClick = (item, index) => {
  console.log(item, 'item')
  if (item) {
    if (item.type === '1' || item.type === '3') {
      // 1内部连接，3购物车
      onChangeFragment(item.url)
      store.commit('MENU_PATH', item.url)
      selectIndex.value = index
    } else if (item.type === '2') {
      proxy.appJS.appOtherWebView(item.url)
    } else if (item.type === '4') {
      router.push(item.url)
    }
  } else {
    // 非正常状态下跳转首页
    onChangeFragment('/')
    store.commit('MENU_PATH', '/')
    selectIndex.value = 0
  }
}

const tabs = [
  { name: 'home', path: '/', component: Home },
  { name: 'my', path: '/my', component: My },
  { name: 'member', path: '/member', component: Member },
  { name: 'integral', path: '/integral', component: Integral },
]
const onChangeFragment = (componentName) => {
  const tab = tabs.find((item) => item.path === componentName)
  if (tab) {
    isComponent.value = tab.component
    store.commit('MENU_PATH', tab.path)
    router.replace({
      query: route.query,
      hash: '#' + tab.name,
    })
  }
}
const handleLogin = () => {
  proxy.appJS.appLogin()
}

onActivated(() => {
  const { hash } = route
  if (hash) {
    const name = hash.slice(1)
    const tab = tabs.find((item) => item.name === name)
    if (tab) {
      onChangeFragment(tab.path)
    }
  }
})
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #f4f6fa;
  display: flex;
  flex-direction: column;
  .activity-wrapper.iphonex {
    // bottom: 93px;
    // bottom: calc(49px + var(--safe-area-inset-bottom));
    // bottom: var(--safe-area-inset-bottom);
    // bottom: max(0, 10px);
  }
  .activity-wrapper {
    position: fixed;
    bottom: 49px;
    bottom: calc(49px + var(--safe-area-inset-bottom));
    left: 10px;
    z-index: 999;
    .activity-block {
      width: 355px;
      box-sizing: border-box;
      margin: 0 auto;
      position: relative;
      background: rgba(0, 0, 0, 0.7);
      // border-radius: 8px 8px 8px 8px;
      border-radius: 999vw;
      margin-bottom: 12px;
      padding: 8px 10px 8px 17px;
      .colse {
        position: absolute;
        left: -4px;
        top: -4px;
        width: 14px;
        height: 14px;
      }
      .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          font-size: 14px;
          color: #ffffff;
          font-weight: 400;
          flex: 1;
        }
        .login-btn {
          width: 63px;
          height: 21px;
          font-size: 11px;
          font-weight: 400;
          color: #ffffff;
          background: linear-gradient(180deg, #2659eb, #a4c5ff);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 16px;
        }
      }
    }
  }
  .tool-bar {
    width: 100%;
    // height: 49px;
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-top: 1px solid #efefef;
    box-shadow: 0px -7px 29.3px 0px #10245714;
    border-radius: 20px 20px 0 0;
    border: none;
    box-sizing: content-box;
    padding-bottom: var(--safe-area-inset-bottom);
    // margin-top: calc(-49px - var(--safe-area-inset-bottom));
    z-index: 10;
    .tool-bar-item {
      text-align: center;
      padding: 8px 0px;
      width: 44px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .tool-bar-item-img {
        width: 24px;
        height: 24px;
      }
      .tool-bar-item-img.is-home {
        width: 36px;
        height: 36px;
      }
      .tool-bar-item-name {
        font-size: 12px;
        transform: scale(0.9);
        margin-top: 4px;
        transform-origin: center top;
      }
    }
  }
}
</style>
