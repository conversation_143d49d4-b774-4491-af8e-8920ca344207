<template>
  <div class="download-container">
    <div class="mask" id="mask">
      <img class="wx-tips" src="@/assets/images/download/three/wx-tips.png" />
    </div>
    <div class="header">
      <img class="logo" src="@/assets/images/download/three/top-img.png" />
    </div>
    <div class="main-top">
      <div class="main-tips">
        <img src="@/assets/images/download/three/arrow-right.png" />
        <span>首借专享福利 新人特权7天有效</span>
        <img src="@/assets/images/download/three/arrow-left.png" />
      </div>
      <div class="main-max">初审成功，额度预估</div>
      <div class="main-quota">¥<span>165,000</span></div>
      <div class="main-tips2">您的额度已经下发！请下载APP领取更多额度！</div>
    </div>
    <div class="main-bottom">
      <img src="@/assets/images/download/three/fire.png" />
      <div class="">{{ personNum }}人已领取</div>
    </div>
    <div class="download-btn" @click="handleDownload">
      立即下载领取<span v-if="countDown > 0">（{{ countDown }}s）</span>
    </div>
    <footer-info />
  </div>
</template>

<script setup>
import onDownload from '@/utils/downloadApp'
import FooterInfo from './components/FooterInfo'
import { pvuvOutside } from '@/utils/pvuv'
const route = useRoute()
const { proxy } = getCurrentInstance()
const iosExplain = ref(false)
const countDown = ref(5)
const personNum = ref(230200)
let interval = null
const handleDownload = () => {
  window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'download-btn'])
  onDownload()
  countDown.value = 0
  clearInterval(interval)
}
const timerId = ref(null)

const numberMethod = () => {
  personNum.value += Math.floor(Math.random() * 10 + 1)
  localStorage.setItem('personNum', personNum.value)
}
onUnmounted(() => {
  // 清除定时器
  if (timerId.value) {
    clearInterval(timerId.value)
    timerId.value = null
  }
  if (interval.value) {
    clearInterval(interval.value)
  }
})
onMounted(async () => {
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  document.title = '轻享花'
  if (u.toLowerCase().indexOf('micromessenger') > -1 || u.toLowerCase().indexOf(' qq/') > -1) {
    document.getElementById('mask').style.display = 'block'
  } else {
    countDown.value = 5
    interval = setInterval(() => {
      countDown.value--
      if (countDown.value === 0) {
        clearInterval(interval)
        window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'auto'])
        onDownload()
      }
    }, 1000)
  }
  let _personNum = localStorage.getItem('personNum')
  if (_personNum) {
    personNum.value = Number(_personNum)
  }
  timerId.value = setInterval(numberMethod, 1000) // 每1000毫秒调用一次yourMethod方法
})
</script>

<style lang="scss" scoped>
.download-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
  background: #fbebe1;
  .mask {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    background: rgba($color: #000000, $alpha: 0.85);
    z-index: 9999;
    img {
      position: absolute;
      width: 322px;
      height: auto;
      right: 20px;
      top: 70px;
    }
  }
  .header {
    margin-top: -85px;
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
  .main-top {
    width: 343px;
    height: 184px;
    margin: -5px auto;
    background: linear-gradient(180deg, #ffe1e0 0%, #ffffff 100%);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.7);
    position: relative;
    .main-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 17px;
      img {
        width: 20px;
        height: 7.5px;
        display: block;
      }
      span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.7);
        line-height: 16px;
        margin: 0 6px;
        font-weight: bold;
      }
    }
    .main-max {
      margin-top: 10px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.7);
      line-height: 16px;
      text-align: center;
      font-weight: bold;
    }
    .main-quota {
      text-align: center;
      span {
        font-family: DIN, DIN;
        font-size: 64px;
        color: #fe483f;
        line-height: 75px;
        text-align: center;
        font-weight: bold;
      }
    }
    .main-quota::first-letter {
      font-size: 36px;
      color: #fe483f;
      line-height: 42px;
    }
    .main-tips2 {
      font-size: 15px;
      color: rgba(0, 0, 0, 0.8);
      line-height: 18px;
      text-align: center;
      font-weight: bold;
      margin-top: 12px;
      span {
        font-family: DIN, DIN;
        font-weight: bold;
        font-size: 28px;
        color: #fe483f;
        line-height: 33px;
      }
    }
  }
  .main-bottom {
    width: 343px;
    height: 31px;
    background: #fe483f;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.7);
    margin: -35px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    padding-top: 33px;
    img {
      width: 17px;
      height: 17px;
      display: block;
      margin-right: 5px;
    }
  }
  .download-btn {
    margin: 20px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    background: linear-gradient(180deg, #f7d7a3 0%, #f3c078 100%);
    border-radius: 24px;
    font-weight: 500;
    font-size: 20px;
    color: #663527;
  }
}
</style>
