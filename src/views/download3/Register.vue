<template>
  <div class="register-container">
    <div class="header">
      <img class="logo" src="@/assets/images/download/three/top-img.png" />
    </div>
    <div class="main-top">
      <div class="main-tips">
        <img src="@/assets/images/download/three/arrow-right.png" />
        <span>首借专享福利 新人特权7天有效</span>
        <img src="@/assets/images/download/three/arrow-left.png" />
      </div>
      <div class="main-max">最高可借额度(元）</div>
      <div class="main-quota">
        ¥<count-to :start-val="2000" :end-val="200000" :duration="3000"></count-to>
      </div>
      <div class="main-tips2">年利率低至<span>7.2</span>起</div>
    </div>
    <div class="main-bottom">
      <img src="@/assets/images/download/three/fire.png" />
      <div class="">{{ personNum }}人已领取</div>
    </div>
    <div class="form-wrapper">
      <div class="form-group">
        <input v-model="form.phone" type="tel" placeholder="输入本人常用手机号" />
      </div>
      <div class="form-group" v-if="form.phone.length === 11">
        <div class="input-code">
          <input
            ref="codeRef"
            type="tel"
            v-model="form.code"
            @input="resetInputVal"
            placeholder="请输入验证码"
          />
          <div class="line"></div>
          <div class="code-text" :class="`${times > 0 ? 'disabled' : ''}`" @click="getCode">
            {{ codeText }}
          </div>
        </div>
      </div>
      <div class="register-btn" @click="handleRegister">查看我的额度</div>
      <agreement-content v-model="form.checked" />
    </div>
    <img class="yj-expain" src="@/assets/images/download/three/bottom-img.png" />
    <footer-info></footer-info>
  </div>
</template>

<script setup>
import { smsCode } from '@/api/base'
import { login } from '@/api/login'
import { showToast } from 'vant'
import { pvuvOutside } from '@/utils/pvuv'
import AgreementContent from './components/AgreementContent'
import FooterInfo from './components/FooterInfo'
import localforage from 'localforage'
import { CountTo } from 'vue3-count-to'
import { onBeforeMount } from 'vue'
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const codeText = ref('发送验证码')
const disabled = ref(true)
const times = ref(0)
const codeRef = ref(null)
const iosExplain = ref(false)
const personNum = ref(230200)
const data = reactive({
  form: {
    phone: '',
    requestType: 'H5',
    partnerCode: '',
    code: '',
    checked: true,
  },
})
const { form } = toRefs(data)
watch(form.value, (newVal) => {
  if (
    newVal.checked &&
    newVal.phone &&
    newVal.phone.length === 11 &&
    newVal.code &&
    ('' + newVal.code).length === 6
  ) {
    disabled.value = false
  } else {
    disabled.value = true
  }
})
const getCode = () => {
  if (times.value === 0) {
    if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
      showToast('请填写正确手机号码')
      return
    }
    times.value = 60
    codeText.value = times.value + 's重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '发送验证码'
      }
    }, 1000)
    codeRef.value.focus()
    smsCode({ phone: form.value.phone, smsType: 'VERIFY_JYQ' })
  }
}
const handleRegister = () => {
  if (disabled.value) return
  login(form.value).then((res) => {
    window._czc.push(['_trackEvent', 'download', 'register', form.value.phone, 1, undefined])
    proxy.onToastSucc(() => {
      router.push('/download3/product')
    }, '激活成功，快去下载使用吧')
  })
}
const resetInputVal = (value) => {
  const code = '' + form.value.code
  if (code > 6) {
    form.value.code = code.substring(0, 6)
  }
}
const timerId = ref(null)

const numberMethod = () => {
  personNum.value += Math.floor(Math.random() * 10 + 1)
  localStorage.setItem('personNum', personNum.value)
}
onUnmounted(() => {
  // 清除定时器
  if (timerId.value) {
    clearInterval(timerId.value)
    timerId.value = null
  }
})

onMounted(async () => {
  let _personNum = localStorage.getItem('personNum')
  if (_personNum) {
    personNum.value = Number(_personNum)
  }
  timerId.value = setInterval(numberMethod, 1000) // 每1000毫秒调用一次yourMethod方法
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  form.value.partnerCode = route.query.channel
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  document.title = '轻享花'
})
</script>

<style lang="scss" scoped>
.register-container {
  background: #fbebe1;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: absolute;
  .header {
    margin-top: -85px;
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
  .main-top {
    width: 343px;
    height: 184px;
    margin: 0 auto;
    background: linear-gradient(180deg, #ffe1e0 0%, #ffffff 100%);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.7);
    position: relative;
    .main-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 17px;
      img {
        width: 20px;
        height: 7.5px;
        display: block;
      }
      span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.7);
        line-height: 16px;
        margin: 0 6px;
        font-weight: bold;
      }
    }
    .main-max {
      margin-top: 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.7);
      line-height: 16px;
      text-align: center;
      font-weight: bold;
    }
    .main-quota {
      text-align: center;
      span {
        font-family: DIN, DIN;
        font-size: 64px;
        color: #fe483f;
        line-height: 75px;
        text-align: center;
        font-weight: bold;
      }
    }
    .main-quota::first-letter {
      font-size: 36px;
      color: #fe483f;
      line-height: 42px;
    }
    .main-tips2 {
      font-size: 15px;
      color: rgba(0, 0, 0, 0.8);
      line-height: 18px;
      text-align: center;
      font-weight: bold;
      margin-top: 6px;
      span {
        font-family: DIN, DIN;
        font-weight: bold;
        font-size: 28px;
        color: #fe483f;
        line-height: 33px;
      }
    }
  }
  .main-bottom {
    width: 343px;
    height: 31px;
    background: #fe483f;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.7);
    margin: -35px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    padding-top: 33px;
    img {
      width: 17px;
      height: 17px;
      display: block;
      margin-right: 5px;
    }
  }
  .form-wrapper {
    margin: 12px 16px 0;
    border-radius: 12px;
    .form-group {
      background: #ffffff;
      border-radius: 24px;
      height: 48px;
      display: flex;
      align-items: center;
      padding: 0 24px;
      input {
        background: transparent;
        font-size: 16px;
        width: 100%;
      }
      .input-code {
        display: flex;
        align-items: center;
        flex: 1;
        input {
          flex: 1;
        }
        .line {
          height: 23px;
          width: 1px;
          background: #e7e7e7;
        }
        .code-text {
          font-size: 14px;
          color: #2f80ed;
          line-height: 20px;
          margin-left: 20px;
        }
      }
    }
    .form-group + .form-group {
      margin-top: 8px;
    }
  }
  .register-btn {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    background: linear-gradient(180deg, #f7d7a3 0%, #f3c078 100%);
    border-radius: 24px;
    font-weight: 500;
    font-size: 20px;
    color: #663527;
  }
  .yj-expain {
    width: 100%;
    height: auto;
    display: block;
  }
}
</style>
