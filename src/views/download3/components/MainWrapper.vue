<template>
  <div ></div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .main-top{
    width: 343px;
    height: 184px;
    margin: 0 auto;
    background: linear-gradient( 180deg, #FFE1E0 0%, #FFFFFF 100%);
    border-radius: 24px;
    border: 1px solid rgba(255,255,255,0.7);
    position: relative;
    .main-tips{
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 17px;
      img{
        width: 20px;
        height: 7.5px;
        display: block;
      }
      span{
        font-size: 14px;
        color: rgba(0,0,0,0.7);
        line-height: 16px;
        margin: 0 6px;
        font-weight: bold;
      }
    }
    .main-max{
      margin-top: 6px;
      font-size: 14px;
      color: rgba(0,0,0,0.7);
      line-height: 16px;
      text-align: center;
      font-weight: bold;
    }
    .main-quota{
      text-align: center;
      span{
        font-family: DIN,DIN;
        font-size: 64px;
        color: #FE483F;
        line-height: 75px;
        text-align: center;
        font-weight: bold;
      }
    }
    .main-quota::first-letter{
      font-size: 36px;
      color: #FE483F;
      line-height: 42px;
    }
    .main-tips2{
      font-size: 15px;
      color: rgba(0,0,0,0.8);
      line-height: 18px;
      text-align: center;
      font-weight: bold;
      margin-top:6px;
      span{
        font-family: DIN, DIN;
        font-weight: bold;
        font-size: 28px;
        color: #FE483F;
        line-height: 33px;
      }
    }
  }
  .main-bottom{
    width: 343px;
    height: 31px;
    background: #FE483F;
    border-radius: 24px;
    border: 1px solid rgba(255,255,255,0.7);
    margin: -35px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    padding-top: 33px;
    img{
      width: 17px;
      height: 17px;
      display: block;
      margin-right: 5px;
    }
  }
</style>