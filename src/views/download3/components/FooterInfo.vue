<template>
  <div class="tips">
    <div class="p1">郑重提示</div>
    <div class="p2">杜绝借款犯罪，倡导合法借款，合理消费，信守借贷合约</div>
    <div class="p3">
        <div>粤ICP备2023057753号</div>
        <div>投资有风险 借贷需谨慎</div>
        <div>资金来源：平台资金由正规持牌金融机构提供</div>
        <div>借款额度、放款时间以实际审批为准</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .tips{
      text-align: center;
      color: #7E7E7E;
      margin-top:23px;
      padding-bottom: 47px;
      padding-top: 0;
      .p1{
        font-size: 14px;
        color: rgba(0,0,0,0.5);
        line-height: 17px;
      }
      .p2{
        font-size: 12px;
        line-height: 17px;
      }
      .p3{
        margin-top: 12px;
        font-size: 12px;
        color: rgba(0,0,0,0.3);
        line-height: 17px;
      }
    }
</style>