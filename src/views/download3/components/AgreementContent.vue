<template>
    <div>
        <div class="agreement-wrapper">
            <div class="agreement">
                <van-checkbox v-model="checked" @change="changeCheck" checked-color="#FF671A" icon-size="16" />
                <span class="margin-left-xs"><span class="grey">我已阅读并同意</span>
                <span @click="agreementView('register')">《用户注册协议》</span><span class="grey">和</span>
                <span @click="agreementView('privacy')">《隐私政策》</span>
                </span>
            </div>
        </div>
        <van-popup
            v-model:show="show"
            safe-area-inset-bottom
            :style="{ height: '100%', width: '100%'}"
            class="full-screen"
        >
            <navigation-bar :isShowBack="false" :pageName="agreementName" :noPaddTop="true">
            <template #nav-left>
                <van-icon name="cross" size="22" class="text-gray" @click="show=false"/>
            </template>
            </navigation-bar>
            <iframe class="external-links" :src="agreementUrl" scrolling="auto" frameborder="0" id="iframe"></iframe>
        </van-popup>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const checked = ref(true)
const agreementName = ref('')
const agreementUrl = ref('')
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: true
    }
})
const emit = defineEmits(['update:modelValue'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const agreementView = (title) => {
    agreementName.value = title === 'register' ? '用户注册协议' : '隐私政策'
    agreementUrl.value = title === 'register' ? '/agreement/download/register.htm' : '/agreement/download/privacy.htm'
    show.value = true
}
const changeCheck = (val) => {
   emit('update:modelValue', val)
}
</script>
<style scoped lang='scss'>
    .agreement-wrapper{
      margin-top: 8px;
      .agreement{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #2F80ED;
        .grey{
          color: #A8A8A8;
        }
      }
    }
</style>