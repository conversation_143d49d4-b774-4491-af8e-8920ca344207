<template>
  <div class="download-container">
    <div class="mask" id="mask">
      <img class="wx-tips" src="@/assets/images/download/three/wx-tips.png" />
    </div>
    <div class="page-context">
      <img class="down-btn" @click="handleDownload()" src="@/assets/images/download/down-btn.png" />
    </div>
  </div>
</template>

<script setup>
import onDownload from '@/utils/downloadApp'
import { pvuvOutside } from '@/utils/pvuv'
const route = useRoute()
const { proxy } = getCurrentInstance()
const iosExplain = ref(false)
onMounted(async () => {
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  document.title = '轻享花'
})

function handleDownload() {
  window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'download-btn'])
  onDownload()
}
</script>

<style lang="scss" scoped>
.download-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
  .mask {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    background: rgba($color: #000000, $alpha: 0.7);
    z-index: 9999;
    img {
      position: absolute;
      width: 238px;
      height: 124px;
      right: 0;
    }
  }
  .page-context {
    width: 100vw; /* 视口宽度 */
    height: 100vh; /* 视口高度 */
    background-image: url('@/assets/images/download/yunji-img.png'); /* 替换为你的图片路径 */
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    .down-btn {
      display: block;
      width: 285px;
      height: 55px;
      position: fixed;
      bottom: 70px;
      left: calc(50% - 142px);
    }
  }
}
</style>
