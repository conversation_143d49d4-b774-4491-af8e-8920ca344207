<script setup>
import { onMounted } from 'vue'
import { categoryList } from '@/api/goods'

const category = defineModel('category', { default: '' })
const subCategory = defineModel({ default: '' })
const level1Categorys = ref([])
const level2Categorys = ref([])
const items = ref([])

const onCategoryChanged = () => {
  // 一级分类改变后，二级分类随着改变，当前选择项如果失效也设为第一项或空
  const parent = category.value ? level1Categorys.value.find(t => t.categoryId === category.value) : null
  if (parent) {
    items.value = level2Categorys.value.filter(t => t.parentId === parent.categoryId)
    if (!items.value.find(t => t.categoryId === subCategory.value)) {
      subCategory.value = ''
    }
  } else {
    items.value = []
    subCategory.value = ''
  }
}

onMounted(async () => {
  // 获取商品一级分类
  const level1Res = await categoryList({ level: 1 })
  const level2Res = await categoryList({ level: 2 })
  level1Categorys.value = Array.isArray(level1Res.data) ? level1Res.data : []
  level2Categorys.value = Array.isArray(level2Res.data) ? level2Res.data : []
  onCategoryChanged()
})

watch(() => category.value, () => {
  onCategoryChanged()
})

const onClickItem = (item) => {
  // 切换选中状态
  if (subCategory.value === item.categoryId) {
    subCategory.value = ''
  } else {
    subCategory.value = item.categoryId
  }
}
</script>

<template>
  <div class="list" v-show="items.length > 0">
    <div class="item" v-for="(item, index) of items" :key="index" @click="() => onClickItem(item)">
      <div :class="['icon', (subCategory === item.categoryId) && 'icon-activated']">
        <img v-if="item.icon" :src="item.icon" />
      </div>
      <div class="name">{{ item.categoryName }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px 0;
  padding: 15px 12px;
  margin: 2px;
  background-color: #fff;
  border-radius: 10px;
  box-sizing: border-box;
  margin: 12px 10px;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;

  .icon {
    background-color: #ececec;
    border: 2px solid #ececec;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 37px;
      height: 37px;
    }

    margin-bottom: 4px;
  }

  .icon-activated {
    border: 2px solid #f5938b;
  }

  .name {
    color: #333;
    font-size: 14px;
    white-space: nowrap;
  }
}
</style>