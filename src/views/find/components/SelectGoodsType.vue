<script setup>
import { onMounted } from 'vue'
import { categoryList } from '@/api/goods'

const emit = defineEmits(['change'])

const props = defineProps({
  regions: {
    type: Array,
    default() {
      return []
    }
  }
})
const items = ref(JSON.parse(JSON.stringify(props.regions)))

const current = defineModel({ default: {
  type: '',
  value: '',
  name: '',
} })

const reload = async () => {
  // 获取商品一级分类
  const res = await categoryList({ level: 1, navStatus: 'Y' })
  // console.log('res.data', res.data)
  const arr = (Array.isArray(res.data) ? res.data : []).filter(t => {
    return true
  })
  items.value = props.regions.concat(arr.map(t => {
    return {
      type: 'category',
      name: t.categoryName,
      value: t.categoryId,
    }
  }))
  if (items.value.length > 0 && !items.value.find(t => t.type === current.value.type && t.value === current.value.value)) {
    current.value = items.value.length > 0 ? items.value[0] : { type: '', value: '', name: '' }
  }
}

const onClickItem = (item) => {
  if (item.type === current.value.type && item.value === current.value.value) {
    return
  }

  current.value = item
  emit('change', item)
}

onMounted(() => {
  reload()
})

defineExpose({
  reload,
})
</script>

<template>
  <div class="category-list">
    <div :class="['category-item', (item.type === current.type && item.value === current.value) && 'category-item-activated']"
      v-for="(item, index) of items" :key="index" @click="() => onClickItem(item)">
      {{ item.name }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.category-list {
  background: linear-gradient(180deg, #f47a6f, #f5b2ac);
  padding-left: 10px;
  padding-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 2px;
  overflow-x: scroll;

  .category-item {
    font-size: 16px;
    color: rgba(255,255,255,0.5);
    white-space: nowrap;
    user-select: none;
    cursor: pointer;
    padding: 8px 5px 24px 5px;
  }

  .category-item-activated {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    display: inline-block;
    position: relative;
    padding: 8px 5px 0px 5px;

    &::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -16px;
      width: 20px;
      height: 16px;
      background: url('@/assets/images/goods/current.png') no-repeat center;
      background-size: contain;
    }
  }
}
</style>
