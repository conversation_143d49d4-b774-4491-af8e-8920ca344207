<script setup>
import SearchBar from '@/components/SearchBar/SearchBar.vue'
import SelectGoodsType from './components/SelectGoodsType.vue'
import SelectGoodsSubType from './components/SelectGoodsSubType.vue'
import { computed, getCurrentInstance, watch } from 'vue'
import { useRouter } from 'vue-router'
import { isMenuPath } from '@/utils/common'
import GoodsList from '@/components/GoodsList/GoodsList.vue'

const { proxy } = getCurrentInstance()
const router = useRouter()

const goodsListRef = ref()

const loading = ref(false)
const searchText = ref('')
const currentGoodsType = ref({
  type: 'region',
  value: proxy.$global.GOODS_REGION_RECOMMEND,
})
const category = ref('')
const subCategory = ref('')
const regions = ref([
  {
    type: 'region',
    value: proxy.$global.GOODS_REGION_RECOMMEND,
    name: '推荐',
  }
])

const onRefresh = () => {
  loading.value = false
  goodsListRef.value.reload()
}

watch(() => currentGoodsType.value, () => {
  if (currentGoodsType.value.type === 'category') {
    category.value = currentGoodsType.value.value
  } else {
    category.value = ''
  }
  // 当前类型改变后，搜索文字置空
  searchText.value = ''
})

const listType = computed(() => {
  const result = {
    type: currentGoodsType.value.type,
    value: currentGoodsType.value.value,
  }
  // 如果子分类选择了，商品列表使用的商品分类id使用子分类的
  if (currentGoodsType.value.type == 'category' && subCategory.value) {
    result.value = subCategory.value
  }
  // console.log('商品分类改变：', result)
  return result
})

// 搜索
const routerSearch = () => {
  router.push('/base-search')
}

const onBackClick = () => {
  if(!isMenuPath()) {
    router.go(-1)
  }
}
</script>

<template>
  <div class="find">
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="theme-text">
      <div class="header">
        <img class="back" v-if="isMenuPath()" src="@/assets/icons/white-back.png" @click="onBackClick"/>
        <SearchBar v-model="searchText" @click="routerSearch" />
      </div>
      <SelectGoodsType v-model="currentGoodsType" style="padding-bottom: 0;" :regions="regions"/>
      <SelectGoodsSubType v-model="subCategory" v-model:category="category" />
      <GoodsList ref="goodsListRef" :listType="listType" :searchText="searchText"/>
    </van-pull-refresh>
  </div>
</template>

<style lang="scss" scoped>
.find {
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  z-index: 99;

  .header {
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #f53828, #f47a6f);
    padding: 50px 20px 6px 10px;
  }

  .back {
    width: 9px;
    height: 16px;
    padding: 6px;
    margin-right: 6px;
  }
  
  .search {
    flex: 1;
    background: none;
  }

  .card {
    background-color: #f6f6f6;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
    padding: 10px;
  }
}
</style>