<template>
  <div class="user-info">
    <!-- <img
      class="cart"
      @click="customerServiceRef.show = true"
      src="@/assets/images/home/<USER>"
    /> -->
    <img class="setting" @click="onClickSetting" src="@/assets/icons/设置.png" />
    <div class="avatar" @click="onAvatar">
      <!-- <img class="img" v-if="modelValue && modelValue.custInfo.avatar" :src="modelValue.custInfo.avatar">
      <img class="img" v-else src="@/assets/images/my/avatar.png"> -->
      <user-avatar class="img" />
      <img
        v-if="modelValue && modelValue.vipCust"
        class="vip-label"
        src="@/assets/images/my/vip-label.png"
      />
    </div>
    <div class="account">
      <template v-if="modelValue">
        <div class="user-phone">{{ phoneFormat(modelValue.phone) }}</div>
        <!-- <div class="user-decs" v-if="!modelValue.vipCust">尊敬的普通会员 您好！</div> -->
        <!-- <div class="user-decs" v-else>尊敬的VIP用户 您好！</div> -->
      </template>
      <div v-else class="login" @click="toLogin">
        <div class="p1">Hi,请登录账号</div>
        <!-- <div class="p2">尊敬的普通会员 您好！</div> -->
      </div>
    </div>
    <avatar-upload ref="avatarUploadRef" />
    <!-- <customer-service ref="customerServiceRef" /> -->
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import AvatarUpload from '@/components/AvatarUpload'
import { phoneFormat } from '@/utils/common'
import { useAvatarAuth } from '../hooks/useAvatarAuth'
import CustomerService from '@/components/CustomerService'
import UserAvatar from '@/components/UserAvatar'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const customerServiceRef = ref(null)
const emit = defineEmits('handleSetting')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
})
const toLogin = () => {
  proxy.appJS.appLogin()
}
const handleCart = () => {
  // 购物车
  if (props.modelValue) {
    router.push('/shopping-cart')
  } else {
    proxy.appJS.appLogin()
  }
}
const onClickSetting = () => {
  emit('handleSetting')
}
const avatarUploadRef = ref(null)
const onAvatar = () => {
  // if(props.modelValue) {
  //   useAvatarAuth(avatarUploadRef.value)
  // } else {
  //   proxy.appJS.appLogin()
  // }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang="scss">
.user-info {
  padding-top: 60px;
  // padding-bottom: 55px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  position: relative;

  .cart {
    position: absolute;
    right: 50px;
    top: 78px;
    width: 26px;
    height: auto;
    display: block;
    // padding: 5px;
  }

  .setting {
    position: absolute;
    right: 15px;
    top: 78px;
    width: 26px;
    height: auto;
    display: block;
    // padding: 5px;
  }

  .avatar {
    position: relative;

    .img {
      width: 58px;
      height: 58px;
      border-radius: 5000px;
      display: inline-block;
      // border: 2px solid #fff;
    }

    .vip-label {
      position: absolute;
      width: 42px;
      height: 18px;
      bottom: 0;
      left: 6px;
    }
  }

  .account {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 8px;

    .user-phone,
    .login {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      line-height: 28px;
    }

    .p2 {
      font-size: 15px;
      font-weight: normal;
      color: rgba(255, 255, 255, 0.5);
      line-height: 15px;
      margin-top: 3px;
    }

    .user-decs {
      font-size: 15px;
      font-weight: normal;
      color: rgba(255, 255, 255, 0.5);
      line-height: 15px;
      margin-top: 3px;
    }
  }
}
</style>
