<template>
  <div class="user-info">
    <img class="cart" @click="customerServiceRef.show=true" src="@/assets/images/components/kefu-icon.png">
    <div class="avatar" @click="onAvatar">
        <img
          class="img"
          v-if="modelValue && modelValue.custInfo.avatar"
          :src="modelValue.custInfo.avatar"
        >
        <img class="img" v-else src="@/assets/images/my/avatar.png">
        <img v-if="modelValue && modelValue.vipCust" class="vip-label" src="@/assets/images/my/vip-label.png">
    </div>
    <div class="account">
      <template v-if="modelValue">
        <div class="user-phone">{{ phoneFormat(modelValue.phone) }}</div>
        <div class="user-decs" v-if="!modelValue.vipCust">尊敬的普通会员 您好！</div>
        <div class="user-decs" v-else>尊敬的VIP用户 您好！</div>
      </template>
      <div v-else class="login" @click="toLogin">
        <div class="p1">登录账号<van-icon name="arrow"></van-icon></div>
        <div class="p2">欢迎来到轻享花～</div>
      </div>
    </div>
    <avatar-upload ref="avatarUploadRef" />
    <customer-service ref="customerServiceRef" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import AvatarUpload from '@/components/AvatarUpload'
import { phoneFormat } from '@/utils/common'
import { useAvatarAuth } from '../hooks/useAvatarAuth'
import CustomerService from '@/components/CustomerService'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const customerServiceRef = ref(null);
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => {}
    }
})
const toLogin = () => {
  proxy.appJS.appLogin()
}
const handleCart = () => { // 购物车
  if(props.modelValue) {
    router.push('/shopping-cart')
  } else {
    proxy.appJS.appLogin()
  }
}
const avatarUploadRef = ref(null)
const onAvatar = () => {
  // if(props.modelValue) {
  //   useAvatarAuth(avatarUploadRef.value)
  // } else {
  //   proxy.appJS.appLogin()
  // }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .user-info{
        padding-top: 100px;
        padding-bottom: 78px;
        margin-left: 17px;
        display: flex;
        align-items: center;
        position: relative;
        .cart{
          position: absolute;
          right: 21px;
          top: 103px;
          width: 36px;
          height: 36px;
          display: block;
        }
        .avatar{
          position: relative;
          .img{
              width: 56px;
              height: 56px;
              border-radius: 5000px;
              display: inline-block;
          }
          .vip-label{
            position: absolute;
            width: 42px;
            height: 18px;
            bottom: 0;
            left: 6px;
          }
        }
        .account{
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 20px;
            .user-phone,.login{
              font-size: 20px;
              color: #FFFFFF;
              line-height: 28px;
            }
            .p2{
              font-size: 12px;
              color: #FFFFFF;
              line-height: 17px;
              margin-top: 6px;
            }
            .user-decs{
                font-size: 12px;
                transform: scale(0.9);
                transform-origin: left top;
                color: #ffffff;
                line-height: 15px;
                margin-top: 7px;
            }
        }
    }
</style>
