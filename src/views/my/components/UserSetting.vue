<template>
  <div class="setting">
    <div class="list">
        <div class="item" @click="handleItem('bankcard')">
            <img src="@/assets/images/my/bankcard.png">
            <div class="text">银行卡</div>
        </div>
        <!-- <div class="item" @click="handleItem('cart')">
            <img src="@/assets/images/my/my-cart.png">
            <div class="text">购物车</div>
        </div> -->
        <div class="item" @click="handleItem('address')">
            <img src="@/assets/images/my/address.png">
            <div class="text">收货地址</div>
        </div>
        <div v-if="getAppVersion() !== 'VERIFY'" class="item" @click="handleItem('service')">
            <img src="@/assets/images/my/customer-service.png">
            <div class="text">联系客服</div>
        </div>
        <div class="item" @click="handleItem('setting')">
            <img src="@/assets/images/my/settings.png">
            <div class="text">系统设置</div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { getAppVersion } from '@/utils/auth'
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    modelValue: {
        type: Object,
        default: null
    }
})
const handleItem = (val) => {
    if(props.modelValue) {
        switch(val) {
            case 'bankcard':
                router.push('/my/bankcard')
                break
            case 'address': // 地址跳转
                router.push('/my/address')
                break
            case 'cart': // 购物车
                router.push('/shopping-cart')
                break
            case 'service': // 客服
                proxy.$customerService()
                break
            case 'setting': // 设置
                emit('handleSetting')
                break
        }
    } else {
        proxy.appJS.appLogin()
    }
}
const emit = defineEmits(['handleSetting'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .setting{
        margin: 15px 12px 0;
    }
    .list{
        display: flex;
        align-items: center;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 15px 10px;
        .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            img{
                width: 28px;
                height: 28px;
                display: block;
            }
            .text{
                margin-top: 9px;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
            }
        }
    }
</style>