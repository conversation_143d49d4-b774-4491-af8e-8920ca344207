<template>
  <div class="user-data">
    <div class="item" @click="handleItem('integral')">
        <div class="value">{{ integral }}</div>
        <div class="text">我的积分</div>
    </div>
    <div class="line"></div>
    <div v-if="vipBig" class="item" @click="handleItem('member')">
        <div class="value label big-text">福利</div>
        <div class="text">我的如意卡</div>
    </div>
    <div v-else class="item" @click="handleItem('integral')">
        <div class="value">0</div>
        <div class="text">返现(元)</div>
    </div>
    <div class="line"></div>
    <div class="item" @click="handleItem('coupon')">
        <div class="value">{{ couponTotal }}</div>
        <div class="text">优惠劵</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    integral: {
        type: Number,
        default: 0
    },
    vipBig: {
      type: Boolean,
      default: false
    },
    couponTotal: {
      type: Number,
        default: 0
    }
})
const handleItem = (val) => {
  switch(val) {
    case 'integral' :
    proxy.$menuRouter('/integral')
    break;
    case 'member' :
    proxy.$menuRouter('/member')
    break;
    case 'coupon': 
    router.push('/coupon')
    break;
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .user-data{
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
        border-radius: 10px;
        display: flex;
        align-items: center;
        padding: 26px 0;
        margin: -50px 12px 0;
        position: relative;
        .item{
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .value{
                font-family: DIN, DIN;
                font-weight: bold;
                font-size: 20px;
                color: #333333;
                line-height: 24px;
            }
            .value.label{
              font-size: 12px;
            }
            .value.label.big-text{
              font-size: 14px;
            }
            .text{
                font-size: 12px;
                color: #666666;
                line-height: 17px;
                margin-top: 6px;
            }
        }
        .line{
            width: 1px;
            height: 25px;
            background: #cccccc;
        }
    }
</style>