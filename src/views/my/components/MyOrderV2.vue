<template>
  <div class="order-member">
    <!-- <div class="member" @click="handleMember">
      <div class="btn">立即开通</div>
    </div> -->
    <div class="order">
      <div class="title">我的订单</div>
      <div class="list">
        <div class="order-item" @click="toOrderList('PAYING')">
          <van-badge :show-zero="false" :content="orderData.PAYING">
            <div class="img-wrapper">
              <img src="@/assets/images/my/待付款.png" />
            </div>
          </van-badge>
          <div class="text">待付款</div>
        </div>
        <div class="order-item" @click="toOrderList('WAIT_DELIVER')">
          <van-badge :show-zero="false" :content="orderData.WAIT_DELIVER">
            <div class="img-wrapper">
              <img src="@/assets/images/my/待发货.png" />
            </div>
          </van-badge>
          <div class="text">待发货</div>
        </div>
        <div class="order-item" @click="toOrderList('WAIT_RECEIVING')">
          <van-badge :show-zero="false" :content="orderData.WAIT_RECEIVING">
            <div class="img-wrapper">
              <img src="@/assets/images/my/待收货.png" />
            </div>
          </van-badge>
          <div class="text">待收货</div>
        </div>
        <div class="order-item" @click="toAfterSales">
          <div class="img-wrapper">
            <img src="@/assets/images/my/退换.png" />
          </div>
          <div class="text">退换/售后</div>
        </div>
        <div class="order-item" @click="toOrderList()">
          <div class="img-wrapper">
            <img src="@/assets/images/my/全部订单.png" />
          </div>
          <div class="text">全部订单</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  orderData: {
    type: Object,
    default: () => {},
  },
})
const toAfterSales = () => {
  if (user.value) {
    router.push('/my/after-sales')
  } else {
    proxy.appJS.appLogin()
  }
}
const toOrderList = (state) => {
  if (user.value) {
    router.push({ path: '/my/goods-order', query: { state } })
  } else {
    proxy.appJS.appLogin()
  }
}
const handleMember = () => {
  proxy.$menuRouter('/member')
}
</script>

<style lang="scss" scoped>
.order-member {
  margin-top: 16px;
  .member {
    height: 50px;
    margin: 0 20px;
    background: url('@/assets/images/my/member.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 12px;
    .btn {
      background: linear-gradient(180deg, #faeecb 0%, #f8d494 100%);
      border-radius: 12px;
      font-weight: 600;
      font-size: 12px;
      color: #3c435c;
      line-height: 17px;
      padding: 3px 12px;
    }
  }
}
.order {
  border-radius: 10px;
  margin: 0 12px;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.02);
  .title {
    font-weight: bold;
    font-size: 15px;
    color: #333333;
    line-height: 20px;
    padding: 10px 0 0 13px;
  }
  .list {
    display: flex;
    flex-flow: row wrap;
    padding: 14px 10px 18px;
    .order-item {
      flex-basis: 20%;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      .img-wrapper {
        display: block;
        position: relative;
        margin-bottom: 4px;
        img {
          width: 34px;
          height: 29px;
        }
      }
      .text {
        font-size: 14px;
        color: #999999;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}
:deep(.van-badge--top-right) {
  top: 6px;
  right: 2px;
}
</style>
