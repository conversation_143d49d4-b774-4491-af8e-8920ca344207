<template>
  <div class="bank-list">
    <template v-if="(bankList.length > 0)">
      <div v-for="(item, index) in bankList" :key="index" class="bank-item" @click="checkBank(item)">
        <div :class="`bank-logo ${ bankCode(item.bankName) }`"></div>
        <div class="bank-info">
          <div class="bank-name">{{ item.bankName }}（{{ stringBankAccount(item.cardNoShort) }}）</div>
          <img class="radio" v-if="(item.id === modelValue)" src="@/assets/icons/cashier/active.png">
          <img class="radio" v-else src="@/assets/icons/cashier/inactive.png">
        </div>
      </div>
    </template>
    <div class="add" @click="toBindCardBill">
      <img class="add-icon" src="@/assets/icons/cashier/bank.png">
      <div class="text">
        使用新卡收款
        <van-icon size="12" color="#cccccc" name="arrow" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { bankCode } from '@/constant/bank'
  import activeIcon from '@/assets/icons/cashier/active.png'
  import inactiveIcon from '@/assets/icons/inactive-circle.png'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const props = defineProps({
    bankList: {
      type: Array,
      default: () => []
    },
    channelId: {
      type: String,
      default: 'C103'
    },
    isH5: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Number,
      default: null
    },
  })
  const emit = defineEmits(['update:modelValue'])
  // 绑卡
  const toBindCardBill = () => {
    router.push({path: props.isH5 ? '/h5/bank' : '/my/bankcard/add', 
    query: { channelId: props.channelId, type: proxy.$global.PAY_TYPE_QUICKPAY }})
  }
  const stringBankAccount = (accountNo) => {
    if(accountNo)
    return accountNo.slice(accountNo.length-4,accountNo.length)
  }
  const checkBank = (item) => {
    emit('update:modelValue', item.id)
  }
</script>

<style lang="scss" scoped>
  .bank-list{
    margin-top: 12px;
    background: #ffffff;
    .bank-item{
      display: flex;
      align-items: center;
      padding: 0 20px 0 26px;
      .bank-logo{
        width: 24px;
        height: 24px;
        display: block;
        margin-right: 12px;
      }
      .bank-info{
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 17px 0;
        .bank-name{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 22px;
        }
      }
    }
    .add{
      display: flex;
      align-items: center;
      color: #999999;
      padding: 0 20px 0 26px;
      .add-icon{
        width: 20px;
        height: 20px;
        margin-right: 14px;
      }
      .text{
        flex: 1;
        padding: 13px 0;
        font-size: 13px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #363636;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
</style>