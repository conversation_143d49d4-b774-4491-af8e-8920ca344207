<template>
  <div class="order-member">
    <!-- <div class="member" @click="handleMember">
      <div class="btn">立即开通</div>
    </div> -->
    <div class="order">
      <div class="title">我的订单</div>
      <div class="list">
        <div class="order-item" @click="toOrderList('PAYING')">
          <van-badge :show-zero="false" :content="orderData.PAYING">
            <img src="@/assets/images/my/wait-pay.png">
          </van-badge>
          <div class="text">待付款</div>
        </div>
        <div class="order-item" @click="toOrderList('WAIT_DELIVER')">
          <van-badge :show-zero="false" :content="orderData.WAIT_DELIVER">
            <img src="@/assets/images/my/pay-first.png">
          </van-badge>
          <div class="text">待发货</div>
        </div>
        <div class="order-item" @click="toOrderList('WAIT_RECEIVING')">
          <van-badge :show-zero="false" :content="orderData.WAIT_RECEIVING">
            <img src="@/assets/images/my/wait-receiving.png">
          </van-badge>
          <div class="text">待收货</div>
        </div>
        <div class="order-item" @click="toAfterSales">
          <img src="@/assets/images/my/after-sales.png">
          <div class="text">退换/售后</div>
        </div>
        <div class="order-item" @click="toOrderList()">
          <img src="@/assets/images/my/all-order.png">
          <div class="text">全部订单</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const router = useRouter()
  const user = computed(() => store.getters.userInfo)
  const props = defineProps({
    orderData: {
      type: Object,
      default: () => {}
    }
  })
  const toAfterSales = () => {
    if (user.value) {
      router.push('/my/after-sales')
    } else {
      proxy.appJS.appLogin()
    }
  }
  const toOrderList = (state) => {
    if (user.value) {
      router.push({ path: '/my/goods-order', query: { state } })
    } else {
      proxy.appJS.appLogin()
    }
  }
  const handleMember = () => {
    proxy.$menuRouter('/member')
  }
</script>

<style lang="scss" scoped>
  .order-member{
    margin-top: 15px;
    .member{
      height: 50px;
      margin: 0 20px;
      background: url('@/assets/images/my/member.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 12px;
      .btn{
        background: linear-gradient( 180deg, #FAEECB 0%, #F8D494 100%);
        border-radius: 12px;
        font-weight: 600;
        font-size: 12px;
        color: #3C435C;
        line-height: 17px;
        padding: 3px 12px;
      }
    }
  }
  .order{
    border-radius: 10px;
    margin: 0 12px;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
    .title{
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      padding: 17px 0 0 22px;
    }
    .list{
      display: flex;
      flex-flow: row wrap;
      padding: 18px 10px 23px;
      .order-item{
        flex-basis: 20%;
        text-align: center;
        img{
          width: 36px;
          height: 36px;
        }
        .text{
          font-size: 12px;
          color: #333333;
          line-height: 17px;
        }
      }
    }
  }
  :deep(.van-badge--top-right){
    top: 10px;
    right: 2px;
  }
</style>