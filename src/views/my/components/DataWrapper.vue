<template>
  <div class="data-wrapper">
    <div class="order item" @click="toEquityOrder">
      <img src="@/assets/icons/my/my-order.png">
      <div class="text">权益订单</div>
    </div>
    <div class="integral item" @click="toIntegral">
      <div class="integral-num num">{{ (integralData.avaliableIntegral && user) ? integralData.avaliableIntegral : 0 }}</div>
      <div class="text">积分</div>
    </div>
    <div class="cashback item" @click="toOtayonii">
      <div class="cashback-amount num">0</div>
      <div class="text">返现(元)</div>
    </div>
    <div class="coupon item">
      <div class="coupon-num num">0</div>
      <div class="text">优惠券</div>
    </div>
  </div>
</template>

<script setup>
  import { getIntegral } from '@/api/customer'
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const { proxy } = getCurrentInstance()
  const data = reactive({
    integralData: {}
  })
  const { integralData } = toRefs(data)
  const emit = defineEmits(['toRouter'])
  onMounted(() => {
    if(user.value) {
      // 个人积分
      getIntegral().then(res => {
        integralData.value = res.data
      })
    }
  })
  const toEquityOrder = () => {
    emit('toRouter', '/member/equity-order', 1)
  }
  const toIntegral = () => {
    proxy.$menuRouter('/integral')
  }
  const toOtayonii = () => {
    emit('toRouter', '/my/otayonii', 1)
  }
</script>

<style lang="scss" scoped>
  .data-wrapper{
    display: flex;
    margin-top: 12px;
    .item{
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      img{
        width: 32px;
        height: 32px;
        display: block;
        margin: 0 auto;
      }
      .text{
        font-size: 12px;
        font-weight: 400;
        color: #363636;
      }
      .num{
        font-size: 16px;
        font-weight: 500;
        color: #454746;
        margin-bottom: 8px;
      }
    }
  }
</style>