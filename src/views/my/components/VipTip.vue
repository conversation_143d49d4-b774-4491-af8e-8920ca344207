<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const onClick = () => {
  router.push({ path: '/member' })
}
</script>

<template>
  <div class="vip-tip" @click="onClick">
    <img style="width: 15px; height: auto;" src="@/assets/images/my/皇冠.png" />
    <img style="width: auto; height: 16px; margin-left: 5px;" src="@/assets/images/my/极享VIP.png" />
    <img style="width: auto; height: 25px; margin-left: auto;" src="@/assets/images/my/立即开通.png" />
  </div>
</template>

<style lang="scss" scoped>
.vip-tip {
  margin: 10px 12px;
  padding: 9px 12px 9px 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: linear-gradient(42.54deg, #B52502 4.58%, #26221F 82.28%);
  border-radius: 7px;

  img {
    width: 100%;
    display: block;
  }
}
</style>