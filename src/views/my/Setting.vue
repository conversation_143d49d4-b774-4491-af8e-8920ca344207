<template>
  <div class="setting-page">
    <navigation-bar pageName="设置" @onLeftClick="onBackClick"></navigation-bar>
    <div class="setting-page-content">
      <div class="fun-wrapper">
        <router-link to="/my/bankcard">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">银行卡</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/my/address">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">收货地址</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/my/app">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">关于我们</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/my/account">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">账号与安全</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <!-- <div class="my-cell" @click="openApp">
          <div class="cell-content solid-bottom">
            <div class="cell-title">系统权限</div>
            <div class="cell-value">
              <van-icon name="arrow" />
            </div>
          </div>
        </div> -->
        <router-link to="/my/privacy/center">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">隐私中心</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
      </div>
      <div class="logout-btn" v-if="user" @click="onLogout">退出登录</div>
      <div class="logout-btn" v-else @click="onLogin">立即登录</div>
    </div>
  </div>
</template>

<script setup>
import { showToast } from 'vant'
const { proxy } = getCurrentInstance()
const router = useRouter()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const onBackClick = () => {
  router.go(-1)
}
const onLogout = () => {
  store.dispatch('FedLogOut').then(() => {
    proxy.appJS.appLogout()
    showToast({
      message: '退出成功！',
      onClose() {
        router.replace('/#home')
      },
    })
  })
}
const onLogin = () => {
  proxy.appJS.appLogin({
    redirect: '/#home',
  })
}
const openApp = () => {
  proxy.appJS.appSystemPermission()
}
</script>

<style lang="scss" scoped>
.setting-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border: none;
  }
  &-content {
    flex-grow: 1;
    overflow: hidden;

    .logout-btn {
      margin: 10px;
      height: 47px;
      line-height: 47px;
      color: #333333;
      font-size: 16px;
      text-align: center;
      background: #ffffff;
      border-radius: 8px;
    }
  }
}
</style>
