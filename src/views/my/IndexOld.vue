<template>
  <div class="my-page" @scroll="onScrollChange" ref="myRef">
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="theme-text">
      <div class="my-page-header">
        <div class="header-icon" :style="navBarStyle">
          <div class="title" v-if="scrollTopValue>10">个人中心</div>
        </div>
        <user-info v-model="user"></user-info>
      </div>
      <user-data :integral="integralValue" :vip-big="vipBig" :coupon-total="couponTotal"></user-data>
      <my-order :order-data="orderData" />
      <div v-if="user?.phone && user.creditIntentionFlag==='Y'" class="bannar-wrapper">
        <router-link to="/cash-loan-pre">
          <img src="@/assets/images/my/loan-pre.png" class="bannar-img">
        </router-link>
      </div>
      <div v-else-if="swiperDatas.length>0" class="bannar-wrapper">
        <my-swiper :swiperDatas="swiperDatas"></my-swiper>
      </div>
      <user-setting v-model="user" @handleSetting="handleSetting"></user-setting>
    </van-pull-refresh>
  </div>
</template>

<script setup>
  import { getAdList, getDictData } from '@/api/base'
  import { orderState } from '@/api/goods'
  import { getMemberInfo } from '@/api/member'
  import { couponsNumber } from '@/api/market'
  import MySwiper from '@/components/MySwiper'
  import UserInfo from './components/UserInfo'
  import UserData from './components/UserData'
  import MyOrder from './components/MyOrder'
  import UserSetting from './components/UserSetting'
  const  isIphoneX = window.isIphoneX
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const router = useRouter()
  const user = computed(() => store.getters.userInfo)
  const integralValue = computed(() => store.getters.integralValue)
  const swiperDatas = ref([])
  const loading = ref(false)
  const myRef = ref(null)
  const scrollTopValue= ref(-1)
  const ANCHOR_SCROLL_TOP = 64
  const vipBig = ref(false)
  const couponTotal = ref(0)
  const testWhiteList = ref([])
  const data = reactive({
    navBarStyle: {
      backgroundColor: '',
      position: 'fixed'
    },
    orderData: {
      PAYING: 0,
      WAIT_DELIVER: 0,
      WAIT_RECEIVING: 0
    }
  })
  const { navBarStyle, orderData } = toRefs(data)
  const onScrollChange = ($e) => {
    scrollTopValue.value = $e.target.scrollTop
    let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
    navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
  }
  const initData = async () => {
    await getAdList({ regionType: [proxy.$global.AD_POSITION_MY] }).then(res => {
      if(res.data && JSON.stringify(res.data) !== '{}') {
        swiperDatas.value = res.data[proxy.$global.AD_POSITION_MY]
      }
    })
    if (user.value) {
      store.dispatch('IntegralData') // 获取积分
      // 大卡会员标识
      const memberRess = await getMemberInfo({ vipType: 'VIP' })
      if (memberRess.data.length > 0) {
        memberRess.data.map(item => {
          if (item.vipCust) {
            vipBig.value = true
          }
        })
      }
      await orderState().then(res => {
        if(res.data) {
          orderData.value['PAYING'] = res.data.PAYING || 0
          orderData.value['WAIT_DELIVER'] = res.data.WAIT_DELIVER || 0
          orderData.value['WAIT_RECEIVING'] = res.data.WAIT_RECEIVING || 0
          orderData.value['VERIFY'] = res.data.VERIFY || 0
          orderData.value['WAIT_DELIVER'] = res.data.WAIT_DELIVER || 0
        } else {
          orderData.value = {
            PAYING: 0,
            WAIT_DELIVER: 0,
            WAIT_RECEIVING: 0
          }
        }
      })
      couponsNumber().then(res => {
        // 计算优惠券
        const coupons = res.data
        if(res.data?.length > 0) {
          coupons.map(item => {
            if(item.state === 'UNUSED') {
              couponTotal.value = item.quantity
            }
          })
        }
      })
    }
    getDictData({ dictType: 'h5_test_white' }).then(res => {
      let whiteList = []
      if (res.data?.length > 0) {
        res.data.map(item => {
          whiteList.push(item.dictValue)
        })
      }
      testWhiteList.value = whiteList
    })
  }
  watch(() => store.getters.userInfo, (newValue, oldValue) => { // 用户变更重新获取数据
    if (newValue) {
      if (!(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())) {
        initData()
      }
    }
  });
  onMounted(() => {
    initData()
  })
  onActivated(() => {
    if (user.value) { // 更新客户信息
      store.dispatch('GetInfo')
    }
  })
  // 判断登录
  const loginValid = (callback) => {
    if(user.value) {
      callback()
    } else {
      proxy.appJS.appLogin()
    }
  }
  const handleSetting = () => {
    loginValid(() => {
      router.push('/system-setting')
    })
  }
  const onRefresh = async () => {
    await initData()
    loading.value = false
  }
</script>

<style lang="scss" scoped>
  .my-page{
    height: 100%;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;
    z-index: 99;
    &-header{
      background: url('@/assets/images/my/my-bg.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      position: relative;
      .header-icon{
        position: fixed;
        top: 0;
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        // 适配手机 stateBar
        padding-top: 25px;
        z-index: 999;
        .title{
          font-size: 18px;
          color: #101010;
        }
      }
    }
    .bannar-wrapper{
      margin: 15px 12px 0;
      .bannar-img{
        width: 100%;
        height: auto;
      }
    }
  }
</style>