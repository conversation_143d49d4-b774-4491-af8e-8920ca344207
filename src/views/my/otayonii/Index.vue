<template>
  <div class="otayonii-page">
    <navigation-bar pageName="我的金豆" @onLeftClick="onBackClick"></navigation-bar>
    <div class="otayonii-page-content">
      <div class="main-content">
        <div class="otayonii-label">我的金豆</div>
        <div class="otayonii-num">
          {{ otayonii }}
        </div>
        <div class="flex justify-between">
          <div class="flex-btn" @click="onWithdrawal">立即提现</div>
          <div class="flex-btn" @click="onRecharge">立即充值</div>
        </div>
      </div>
      <div class="ad-content">
        <img src="@/assets/images/my/otayonii-01.png">
        <img src="@/assets/images/my/otayonii-02.png">
      </div>
    </div>
  </div>
</template>

<script setup>
  import { showToast } from 'vant';
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const store = useStore()
  const otayonii = ref(0)
  const onBackClick = () => {
    router.go(-1)
  }
  const data = reactive({
    integralData: {}
  })
  const { integralData } = toRefs(data)
  const onRecharge = () => {
    showToast('系统升级中，暂未开放')
  }
  const onWithdrawal = () => {
    router.push('/my/otayonii/withdrawal')
  }
  onMounted(() => {
  })
</script>

<style lang="scss" scoped>
  .otayonii-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    :deep(.nav-bar .center .page-title){
      color: #ffffff;
    }
    .nav-bar {
      background: #447DEF !important;
      border-bottom: none;
      color: #ffffff;
    }
    &-content {
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      margin-top: -1px;
      .main-content{
        background: #447DEF;
        padding: 0 24px;
        .otayonii-label{
          text-align: center;
          color: #ffffff;
          font-size: 12px;
          line-height: 17px;
          margin-top: 36px;
        }
        .otayonii-num{
          font-size: 32px;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 45px;
          text-align: center;
        }
        .flex-btn{
          margin-top: 27px;
          margin-bottom: 21px;
          width: 157px;
          height: 46px;
          background: #FFFFFF;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 400;
          color: #447DEF;
          line-height: 46px;
          text-align: center;
        }
      }
      .ad-content{
        padding: 10px;
        img {
          width: 100%;
        }
        img +img {
          margin-top: 10px;
        }
      }
    }
  }
</style>