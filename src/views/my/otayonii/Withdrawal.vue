<template>
  <div class="withdrawal-page">
    <navigation-bar pageName="提现" @onLeftClick="onBackClick">
      <template #nav-right>
        <div class="nav-right" @click="withdrawalLog">提现记录</div>
      </template>
    </navigation-bar>
    <div class="withdrawal-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="main-content">
        <div class="otayonii-num">
          {{ otayonii }}
        </div>
        <div class="otayonii-amount">（约0元）</div>
      </div>
      <div class="withdrawal-amount">
        <div class="title">选择提现金额</div>
        <div class="list">
          <div
            :class="`list-item ${ index === curTab ? 'active': '' }`"
            v-for="(item, index) in wdamt"
            :key="index"
            @click="onChange(index)"
          >
            <div class="amount">{{ item.amount }}<span>元</span></div>
            <div class="times" v-if="item.number">剩余{{ item.number }}次</div>
          </div>
        </div>
        <div class="account-amount">
          <div class="amount-input">
            <span>￥</span>
            <div class="choose-amount">{{ wdamt[curTab].amount }}</div>
          </div>
          <div class="tips">实际到账：{{ wdamt[curTab].amount }}元</div>
        </div>
      </div>
      <div class="withdrawal-bank">
        <div class="title">选择提现金额</div>
        <bank-list :channel-id="C103" />
      </div>
      <div :class="`agreement-wrapper ${isIphoneX ? 'iphonex' : ''}`">
        <div class="agreement-btn" @click="handleApply">申请提现</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import BankList from '../components/BankList'
  import { showToast } from 'vant';
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const store = useStore()
  const otayonii = ref(0)
  const curTab = ref(0)
  const wdamt = ref([
    { amount: 100, number: 10 },
    { amount: 500, number: 10 },
    { amount: 1000, number: 10 },
    { amount: 2000, number: 10 },
    { amount: 3000, number: 0 },
    { amount: 5000, number: 0 }
  ])
  const onBackClick = () => {
    router.go(-1)
  }
  const data = reactive({
    integralData: {}
  })
  const { integralData } = toRefs(data)
  const onWithdrawal = () => {
    router.push('/my/otayonii/withdrawal')
  }
  const onChange = (index) => {
    curTab.value = index
  }
  const withdrawalLog = () => {}
  const handleApply = () => {
    showToast('系统升级中，暂未开放')
  }
  onMounted(() => {
  })
</script>

<style lang="scss" scoped>
  .withdrawal-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    :deep(.nav-bar .center .page-title){
      color: #ffffff;
    }
    .nav-bar {
      background: #447DEF !important;
      border-bottom: none;
      color: #ffffff;
    }
    &-content {
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      margin-top: -1px;
      .main-content{
        background: #447DEF;
        padding: 0 24px;
        .otayonii-num{
          font-size: 32px;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 45px;
          text-align: center;
          margin-top: 27px;
        }
        .otayonii-amount{
          font-size: 16px;
          color: #FFFFFF;
          line-height: 22px;
          text-align: center;
          margin-bottom: 36px;
        }
      }
      .withdrawal-bank{
        padding-top: 10px;
        background: #FFFFFF;
        margin-top: 10px;
        .title{
          padding: 0 16px;
          font-size: 16px;
          font-weight: bold;
          color: #222222;
          line-height: 22px;
        }
      }
      .withdrawal-amount{
        padding-top: 4px;
        background: #FFFFFF;
        margin-top: 10px;
        .title{
          padding: 16px;
          font-size: 16px;
          font-weight: bold;
          color: #222222;
          line-height: 22px;
        }
        .list{
          padding: 0 16px;
          display: flex;
          justify-content: space-between;
          flex-flow: wrap;
          &-item{
            width: 106px;
            height: 66px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            .amount{
              font-size: 20px;
              font-weight: 500;
              color: #222222;
              line-height: 28px;
              span{
                font-size: 15px;
              }
            }
            .times{
              font-size: 13px;
              font-weight: 400;
              color: #666666;
              line-height: 18px;
              margin-top: 2px;
            }
          }
          &-item.active{
            position: relative;
            overflow: hidden;
            .amount, .times{
              color: #447DEF;
            }
          }
        }
        .account-amount{
          padding: 0 16px;
          .amount-input{
            display: flex;
            align-items: flex-end;
            border-bottom: 1px solid #EDEDED;
            padding: 10px 0;
            span{
              font-size: 22px;
              font-weight: 500;
              color: #222222;
              padding-bottom: 2px;
            }
            .choose-amount{
              font-size: 30px;
              font-weight: 500;
              color: #222222;
            }
          }
          .tips{
            margin-top: 5px;
            margin-bottom: 13px;
            font-size: 12px;
            transform: scale(0.9);
            transform-origin: left top;
            font-weight: 400;
            color: #222222;
          }
        }
      }
    }
  }
  .nav-right{
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    width: 120px;
    left: -30px;
  }
  .agreement-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: calc(100% - 30px);
    padding: 15px;
    .agreement-btn{
      height: 44px;
      background: #FF3355;
      border-radius: 22px;
      line-height: 44px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
    }
  }
  .agreement-wrapper.iphonex{
    // padding-bottom: 44px;
    padding-bottom: calc(15px + var(--safe-area-inset-bottom));
  }
</style>