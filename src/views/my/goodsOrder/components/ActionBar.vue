<template>
  <div>
    <div class="action-bar">
      <div
        class="btn cancel"
        :class="{ 'is-detail-page': isDetailPage }"
        v-if="orderInfo.status === 'PAYING' && orderInfo.outTime > 0"
        @click.stop="onClick('cancel')"
      >
        取消订单
      </div>
      <div
        class="btn cancel"
        :class="{ 'is-detail-page': isDetailPage }"
        v-if="
          !ORDERTYPE.includes(orderInfo.orderType) &&
          (orderInfo.status === 'FINISHED' ||
            orderInfo.status === 'WAIT_DELIVER' ||
            orderInfo.status === 'WAIT_RECEIVING')
        "
        @click.stop="onClick('return')"
      >
        退换/售后
      </div>
      <div
        class="btn cancel"
        :class="{ 'is-detail-page': isDetailPage }"
        v-if="
          orderInfo.status === 'CANCEL' || (orderInfo.status === 'PAYING' && orderInfo.outTime <= 0)
        "
        @click.stop="onClick('delete')"
      >
        删除订单
      </div>
      <div
        class="btn theme-linear-gradient"
        v-if="orderInfo.status === 'PAYING' && orderInfo.outTime > 0"
        @click.stop="onClick('payment')"
      >
        立即付款
      </div>
      <div
        class="btn theme-linear-gradient"
        v-if="
          orderInfo.status === 'CANCEL' ||
          orderInfo.status === 'FINISHED' ||
          orderInfo.status === 'WAIT_DELIVER'
        "
        @click.stop="onClick('againbuy')"
      >
        再买一单
      </div>
      <div
        class="btn text-white theme-bg"
        v-if="orderInfo.status === 'WAIT_RECEIVING'"
        @click.stop="onClick('confirm')"
      >
        确认收货
      </div>
      <!-- <div
        class="btn cancel"
        :class="{ 'is-detail-page': isDetailPage }"
        v-if="getAppVersion() !== 'VERIFY' && isDetailPage"
        @click.stop="$customerService"
      >
        在线客服
      </div> -->
    </div>
    <confirm-dialog ref="deleteDialogRef" title="确定删除该订单？" @onConfirm="submitDelete" />
    <confirm-dialog ref="cancelDialogRef" title="确定取消该订单？" @onConfirm="submitCancel" />
  </div>
</template>

<script setup>
import { getAppVersion } from '@/utils/auth'
import { delOrder, cancelOrder, submitReceipt } from '@/api/goods-order'
import ConfirmDialog from '@/components/ConfirmDialog'
const props = defineProps({
  orderInfo: {
    type: Object,
    defualt: () => {},
  },
  isDetailPage: {
    type: Boolean,
    default: false,
  },
})
const { proxy } = getCurrentInstance()
const router = useRouter()
const cancelDialogRef = ref(null)
const deleteDialogRef = ref(null)
const emit = defineEmits(['onDelete', 'onCancel', 'onAfterSales'])
const ORDERTYPE = ['DIRECT_ADD', 'ECARD', 'TELE', 'COUPON']
const route = useRoute()
const {
  path,
  query: { state },
} = route
const onClick = (action) => {
  switch (action) {
    case 'delete':
      deleteDialogRef.value.show = true
      return
    case 'againbuy':
      againBuy()
      return
    case 'cancel':
      cancelDialogRef.value.show = true
      return
    case 'payment':
      router.push({ path: '/cashier', query: { orderId: props.orderInfo.orderId } })
      return
    case 'confirm':
      confirmReceipt()
      return
    case 'return':
      onAfterSales()
    default:
      return
  }
}
// 删除订单
const submitDelete = () => {
  delOrder({ orderId: props.orderInfo.orderId }).then((res) => {
    proxy.onToastSucc(() => {
      emit('onDelete')
    }, '删除成功！')
    // 在订单详情页删除订单，删除完订单要退回订单列表页
    if (path === '/my/goods-order-detail') {
      router.replace({ path: '/my/goods-order', query: { state } })
    }
  })
}
// 取消订单
const submitCancel = () => {
  cancelOrder({ orderId: props.orderInfo.orderId }).then((res) => {
    proxy.onToastSucc(() => {
      emit('onCancel')
    }, '取消成功！')
  })
}
// 再买一单
const againBuy = () => {
  const params = []
  props.orderInfo.goodsList.map((item) => {
    params.push({
      spuId: item.spuId,
      skuId: item.skuId,
      quantity: item.quantity,
    })
  })
  localStorage.setItem(
    'param-orderConfirm',
    JSON.stringify({
      shoppingFlag: 'N',
      msOrderSpus: params,
    })
  )
  router.push('/order-confirm')
}
// 确认收货
const confirmReceipt = () => {
  submitReceipt({ orderId: props.orderInfo.orderId }).then((res) => {
    proxy.onToastSucc(() => {
      emit('onConfirm')
    }, '确认成功！')
  })
}
const onAfterSales = () => {
  emit('onAfterSales', { orderId: props.orderInfo.orderId, goodsList: props.orderInfo.goodsList })
}
</script>

<style lang="scss" scoped>
.action-bar {
  padding-bottom: 12px;
  display: flex;
  justify-content: flex-end;
  .btn {
    padding: 10px 15px;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 18px;
    border-radius: 25px;
    color: #fff;
  }
  .btn.cancel {
    color: #333333;
    background: #f9f9f9;
  }
  .btn.cancel.is-detail-page {
    border: 1px solid #333333;
    background: #fff;
  }
  .btn + .btn {
    margin-left: 10px;
  }
}
.warning-popup {
  width: 235px;
  background: #ffffff;
  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;
    .btn {
      width: 90px;
      height: 32px;
      background: #eeeeee;
      border-radius: 16px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
  }
}
</style>
