<template>
  <div class="goods-order-detail-page">
    <navigation-bar
      @onLeftClick="onBackClick"
      :navBarStyle="{ background: '#F9F9F9', height: 'auto' }"
    >
      <template #nav-center>
        <div class="header">
          <!-- <img v-if="orderInfo.status === 'PAYING' || orderInfo.status === 'WAIT_DELIVER'" src="@/assets/images/goods/wait-pay-icon.png">
          <img v-if="orderInfo.status === 'CANCEL'" src="@/assets/images/goods/order-close-icon.png">
          <img v-if="orderInfo.status === 'FINISHED' || orderInfo.status === 'WAIT_RECEIVING'" src="@/assets/images/goods/order-finish-icon.png"> -->
          <span class="order-info-status">
            {{
              orderInfo.status === 'PAYING'
                ? '待支付'
                : orderInfo.status === 'CANCEL'
                ? '交易关闭'
                : orderInfo.status === 'FINISHED'
                ? '交易成功'
                : orderInfo.status === 'WAIT_DELIVER'
                ? '卖家待发货'
                : orderInfo.status === 'WAIT_RECEIVING'
                ? '卖家已发货'
                : orderInfo.status === 'VERIFY'
                ? '订单审核中'
                : '订单详情'
            }}
          </span>
          <div class="out-time" v-if="orderInfo.status === 'PAYING'">
            <span>仅剩</span>
            <van-count-down
              v-if="orderInfo.outTime"
              :time="orderInfo.outTime * 1000"
              format="HH:mm:ss"
              @finish="getInfo"
            />
            <span v-else class="van-count-down">00:00:00</span>
            <span>订单将自动关闭</span>
          </div>
          <div class="order-info-cancel-msg" v-if="orderInfo.status === 'CANCEL'">
            交易关闭原因：{{ orderInfo.cancelMsg }}
          </div>
        </div>
      </template>
    </navigation-bar>
    <div class="goods-order-detail-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div v-if="orderInfo.addressInfo" class="address-info">
        <div class="name-phone">
          <div class="name">{{ orderInfo.addressInfo.contactName }}</div>
          <div class="phone">{{ orderInfo.addressInfo.contactTel }}</div>
        </div>
        <div class="address">
          {{
            orderInfo.addressInfo.province +
            orderInfo.addressInfo.city +
            orderInfo.addressInfo.district +
            orderInfo.addressInfo.address
          }}
        </div>
      </div>
      <div class="info-wrapper">
        <div class="goods-info">
          <div class="content">
            <order-goods-row
              :state="orderInfo.status"
              :goods-list="orderInfo.goodsList"
              :order-id="orderInfo.orderId"
              :order-type="orderInfo.orderType"
              @onAfterSales="onAfterSales"
            />
            <van-cell-group>
              <van-cell
                title="商品总价"
                :value="'￥' + orderInfo.totalAmount"
                :border="false"
              ></van-cell>
              <van-cell
                v-if="orderInfo.mbAmount"
                title="会员优惠"
                :value="'-￥' + orderInfo.mbAmount"
                :border="false"
                class="value-red"
              ></van-cell>
              <van-cell
                v-if="orderInfo.tvAmount"
                title="优惠券抵扣"
                :value="'-￥' + orderInfo.tvAmount"
                :border="false"
                class="value-red"
              ></van-cell>
              <van-cell title="运费" value="￥0" :border="false"></van-cell>
              <van-cell
                v-if="orderInfo.pointAmount"
                title="积分抵扣"
                :value="'-' + orderInfo.pointAmount"
                :border="false"
                class="value-red"
              ></van-cell>
              <van-cell
                :title="['PAYING', 'CANCEL'].includes(orderInfo.status) ? '待付款' : '实付款'"
                :value="'￥' + orderInfo.payAmount"
                :border="false"
                class="value-red"
              ></van-cell>
            </van-cell-group>
          </div>
        </div>
        <!-- <van-divider style="margin: 0 15px" /> -->
        <van-cell-group class="order-info">
          <van-cell title="订单编号：" :border="false" class="order-id-cell">
            <template #value>
              <span>{{ orderInfo.orderId }}</span>
              <van-divider vertical />
              <span class="copy-btn" @click="onCopy(orderInfo.orderId)">复制</span>
            </template>
          </van-cell>
          <van-cell title="下单时间：" :value="orderInfo.createTime" :border="false" />
          <van-cell
            v-if="orderInfo.payTime"
            title="支付方式："
            :border="false"
            :is-link="orderInfo.payType === 'BILLPAY'"
            :clickable="false"
            class="pay-type-cell"
          >
            <template #value>
              <button
                class="bill-text"
                :class="{
                  bill: orderInfo.payType === 'BILLPAY',
                }"
                :disabled="!orderInfo.billId"
                @click="onBill"
              >
                <!-- {{ orderInfo.status }} -->
                {{ orderInfo.exchangeType === 'RECHARGE' ? '奖品兑换' : orderInfo.payTypeName }}
              </button>
            </template>
          </van-cell>
          <van-cell
            v-if="orderInfo.payTime"
            title="支付时间："
            :value="orderInfo.payTime"
            :border="false"
          />
          <van-cell
            v-if="orderInfo.ecardInfo"
            title="卡号信息："
            :border="false"
            class="ecard-cell"
          >
            <template #value>
              <span>{{ orderInfo.ecardInfo.cardNo }}</span>
              <van-divider vertical />
              <span class="copy-btn" @click="onCopy(orderInfo.ecardInfo.cardNo)">复制</span>
            </template>
          </van-cell>
          <van-cell
            v-if="orderInfo.ecardInfo"
            title="卡密信息："
            :border="false"
            class="ecard-cell"
          >
            <template #value>
              <span v-if="pwdShow">{{ orderInfo.ecardInfo.password }}</span>
              <span v-else>{{ maskPwd(orderInfo.ecardInfo.password) }}</span>
              <van-divider vertical />
              <span v-if="pwdShow" class="copy-btn" @click="onCopy(orderInfo.ecardInfo.password)"
                >复制</span
              >
              <span v-else class="copy-btn" @click="viewPwd()">查看卡密</span>
            </template>
          </van-cell>
          <van-cell
            v-if="orderInfo.virtualMsg"
            title="充值账号："
            :value="orderInfo.virtualMsg.chargeAccount"
            :border="false"
          />
        </van-cell-group>
        <van-cell-group v-if="orderInfo.deliveryInfo" class="delivery-info" :border="false">
          <van-cell title="配送信息：" :border="false">
            <template #value>
              <span class="more" @click="handleLogistics"
                >查看轨迹<van-icon name="arrow" :size="12"
              /></span>
            </template>
          </van-cell>
          <van-cell
            title="承运公司："
            :value="orderInfo.deliveryInfo.companyName"
            :border="false"
          />
          <van-cell
            title="物流单号："
            :value="orderInfo.deliveryInfo.deliverySn"
            :border="false"
            class="delivery-cell"
          >
            <template #value>
              <span>{{ orderInfo.deliveryInfo.deliverySn }}</span>
              <van-divider vertical />
              <span class="copy-btn" @click="onCopy(orderInfo.deliveryInfo.deliverySn)">复制</span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div
        style="font-size: 16px; color: 333; font-weight: 600; text-align: center; line-height: 26px"
      >
        热销榜单
      </div>
      <GoodsList :listType="listType" />
    </div>
    <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
      <action-bar
        :order-info="orderInfo"
        :is-detail-page="true"
        @onDelete="getInfo"
        @onCancel="getInfo"
        @onConfirm="getInfo"
        @onAfterSales="onAfterSales"
      />
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import OrderGoodsRow from '@/components/OrderGoodsRow'
import ActionBar from './components/ActionBar'
import { getGoodsOrder } from '@/api/goods-order'
import { showToast } from 'vant'
import GuessYouLike from '@/components/GoodsList/GuessYouLike.vue'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const { orderId, state } = route.query
const pwdShow = ref(false)
const data = reactive({
  orderInfo: {},
})
const { orderInfo } = toRefs(data)
const listType = ref({
  type: 'region',
  value: proxy.$global.GOODS_REGION_FOLLOW,
})
const onBackClick = () => {
  router.replace({ path: '/my/goods-order', query: { state } })
}
onMounted(() => {
  getInfo()
})
const onBill = () => {
  router.push({ path: '/my/bill/repayment', query: { billId: orderInfo.value.billId } })
  // router.push('/my/bill')
}
const handleLogistics = () => {
  router.push({ path: '/my/order-logistics', query: { orderId } })
}
const onCopy = (orderId) => {
  let oInput = document.createElement('input')
  oInput.value = orderId
  document.body.appendChild(oInput)
  oInput.select()
  document.execCommand('Copy')
  showToast('复制成功')
  oInput.remove()
}
const onAfterSales = (data) => {
  sessionStorage.setItem(proxy.$global.PARAM_AFTERSALES, JSON.stringify(data))
  router.push('/my/after-sales-form')
}
const viewPwd = () => {
  showToast({ message: '请勿将卡密复制截图给他人，谨防诈骗！', duration: 3000 })
  pwdShow.value = true
}
const maskPwd = (str) => {
  const firstFourChar = str.substring(0, 4)
  const lastFourChar = str.substring(str.length - 4)
  const senistiveInfo = str.substring(4, str.length - 4)
  const mask = '*'
  return firstFourChar + senistiveInfo.replace(/[^-]/g, mask) + lastFourChar
}
const getInfo = () => {
  getGoodsOrder({ orderId }).then((res) => {
    orderInfo.value = res.data[0]
  })
}
</script>

<style lang="scss" scoped>
.goods-order-detail-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .header {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    img {
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }
    .order-info-status {
      font-size: 16px;
      font-family: Roboto;
      font-weight: bold;
      line-height: 25px;
      color: #000;
    }
    .out-time,
    .order-info-cancel-msg {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      line-height: 17px;
      font-size: 14px;
      margin-bottom: 6px;
      color: #000;
      font-weight: 400;
      .van-count-down {
        color: #f43727;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    background: #f9f9f9;
    .out-time {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #333333;
      line-height: 18px;
      margin-bottom: 12px;
      .van-count-down {
        color: #f43727;
        font-size: 12px;
        line-height: 18px;
      }
    }
    .address-info {
      margin: 10px;
      padding: 10px 18px;
      padding-bottom: 13px;
      background: #ffffff;
      border-radius: 10px;
      .title {
        font-size: 15px;
        font-weight: bold;
        color: #333;
        line-height: 18px;
      }
      .name-phone {
        display: flex;
        align-items: center;
        font-size: 15px;
        font-weight: 400;
        color: #333;
        line-height: 18px;

        .phone {
          color: #999;
          margin-left: 6px;
        }
      }
      .address {
        margin-top: 6px;
        font-size: 12px;
        font-size: 15px;
        font-weight: 400;
        color: #333;
      }
    }
    .info-wrapper {
      background-color: #fff;
      border-radius: 10px;
      margin: 10px;
      overflow: hidden;
      // padding-bottom: 20px;
      padding: 12px;
      // margin-bottom: 20px;
      .van-cell-group {
        padding: 4px 0;
      }
      .van-cell {
        padding: 4px 0;
        // line-height: 1.2;
        :deep(.van-cell__title) {
          width: 100px;
          flex: none;
          font-size: 14px;
          font-weight: 400;
        }
        :deep(.van-cell__value) {
          color: #999999f5;
          font-size: 15px;
          font-weight: 400;
        }
        :deep(.van-badge__wrapper) {
          color: #000;
          font-size: 8px;
        }
        .van-divider--vertical {
          border-color: #979797;
        }
        .copy-btn {
          color: #333333f5;
          font-size: 15px;
        }
      }
      .order-id-cell {
      }
      .pay-type-cell {
        .bill-text.bill {
          color: #f43727;
          border: none;
          padding: 0;
          background: none;
          &:disabled {
            // opacity: 0.5;
          }
        }
      }
    }
    .goods-info {
      background: #ffffff;
      // padding-top: 10px;
      // padding: 12px;
      .title {
        display: flex;
        align-items: center;
        padding: 17px 15px;
        img {
          width: 26px;
          height: 26px;
          margin-right: 8px;
        }
        .text {
          font-size: 14px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #333333;
          line-height: 20px;
        }
      }
      .content {
        // margin: 0 7px;
        .group-cell {
          display: flex;
          justify-content: space-between;
          padding: 5px;
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 18px;
          // padding-right: 0;
          .value {
            font-size: 15px;
            font-weight: bold;
          }
          .value::first-letter {
            font-size: 70%;
          }
        }
        .group-cell-total {
          display: flex;
          justify-content: space-between;
          padding: 17px 5px 20px;
          font-size: 14px;
          .value {
            font-size: 15px;
            font-weight: bold;
            color: #f43727;
          }
          .value::first-letter {
            font-size: 70%;
          }
        }
      }
      .van-cell {
        :deep(.van-cell__value) {
          color: #000;
          font-weight: 500;
        }
        &.value-red {
          :deep(.van-cell__value) {
            color: #f43727;
          }
        }
      }
    }
    .order-info {
      // margin: 10px 5px 0px 5px;
      // background: #ffffff;
      // border-radius: 10px;
    }
    .delivery-info {
      // margin: 10px;
      // padding: 10px;
      background: #ffffff;
      border-radius: 10px;
      .title {
        font-size: 13px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #222222;
        line-height: 18px;
        display: flex;
        justify-content: space-between;
        .more {
          display: inline-block;
          font-size: 12px;
          transform: scale(0.9);
          font-weight: 400;
          color: #999999;
        }
      }
    }
    .order-info-group {
      padding: 5px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 18px;
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #333;
        span {
          text-indent: 2em;
        }
      }
      .value {
        margin-left: 10px;
        font-weight: 500;
        font-size: 14px;
        font-weight: 500;
        color: #999;
        line-height: 18px;
        display: flex;
        align-items: center;
        .copy-btn {
          display: block;
          text-align: center;
          border-radius: 9px;
          font-size: 15px;
          transform-origin: left center;
          font-weight: 400;
          color: #333;
          line-height: 22px;
        }
        .bill {
          span {
            color: #f43727;
          }
        }
        .bill-text {
          display: flex;
          align-items: center;
          span {
            margin-right: 2px;
          }
        }
      }
    }
  }
}
.footer {
  background: #ffffff;
  padding: 10px 10px;
  position: fixed;
  bottom: 0;
  width: calc(100% - 20px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}
</style>
