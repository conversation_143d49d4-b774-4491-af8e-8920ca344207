<template>
    <div class="page">
      <navigation-bar pageName="物流轨迹" @onLeftClick="onBackClick"></navigation-bar>
      <div class="page-content">
        <swiper
          class="swiperBox"
          :class="{ 'padding' : list.length > 1 }"
          :slides-per-view="1.03"
          :space-between="10"
          @slideChange="onSlideChange"
        >
            <swiper-slide v-for="(item, index) in list" :key="index">
              <div class="header">
                <img class="img" :src="item.albumPics" />
                <div class="logistics">
                  <div class="status">{{ item.statusDesc }}</div>
                  <div class="name">物流公司：<span class="text">{{ item.companyName }}</span></div>
                  <div class="order">
                    <span>快递单号：<span class="text">{{ item.deliverySn }}</span></span>
                    <van-tag
                      color="#EEEEEE"
                      round
                      v-clipboard:copy="item.deliverySn"
                      v-clipboard:success="clipboardSuccess"
                    >复制</van-tag>
                  </div>
                </div>
              </div>
            </swiper-slide>
        </swiper>
        <div class="logistics-data">
          <van-steps
            direction="vertical"
            v-if="list.length > 0 && list[activeIndex].datas.length > 0"
            >
            <van-step v-for="item in list[activeIndex].datas">
              <h3>{{ item.context }}</h3>
              <p>{{ item.time }}</p>
            </van-step>
          </van-steps>
          <van-empty v-else>暂无轨迹</van-empty>
        </div>
      </div>
    </div>
  </template>

  <script setup>
    import { orderLogistics } from '@/api/goods-order'
    import { Swiper, SwiperSlide } from 'swiper/vue'
    import { showToast } from 'vant'
    import 'swiper/css'
    const { proxy } = getCurrentInstance()
    const isIphoneX = window.isIphoneX
    const router = useRouter()
    const route = useRoute()
    const orderId = route.query.orderId
    const list = ref([])
    const activeIndex = ref(0)
    const onBackClick = () => {
      router.go(-1)
    }
    const onSlideChange = (el) => {
      activeIndex.value = el.activeIndex
    }
    const clipboardSuccess = ()=> {
      showToast('复制成功');
    }
    onMounted(() => {
        orderLogistics({ orderId }).then(res => {
          list.value = res.data
        })
    })

  </script>

  <style lang="scss" scoped>
    .page{
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      position: absolute;
      &-content{
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        .swiperBox.padding{
            padding: 0 12px;
          }
        .swiperBox{
          margin: 10px 0;
          padding-left: 12px;
          .header{
            padding: 16px;
            background: #ffffff;
            display: flex;
            align-items: center;
            border-radius: 6px;
            .img{
              width: 70px;
              height: 70px;
              border-radius: 6px;
            }
            .logistics{
              flex: 1;
              .status{
                font-weight: bold;
                font-size: 16px;
                color: #E9362E;
                line-height: 23px;
                margin-left: 8px;
              }
              .name{
                font-size: 13px;
                color: #666666;
                line-height: 19px;
                margin-top: 6px;
                margin-left: 10px;
                .text{
                  font-weight: bold;
                  font-size: 13px;
                  color: #222222;
                }
              }
              .order{
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 13px;
                color: #666666;
                line-height: 19px;
                margin-left: 10px;
                margin-top: 4px;
                .text{
                  font-weight: bold;
                  font-size: 13px;
                  color: #222222;
                }
                .van-tag{
                  color: #666666;
                  font-size: 12px;
                  transform: scale(0.9);
                  margin-right: 15px;
                }
              }
            }
          }
        }
        .logistics-data{
          flex: 1;
          margin: 0 12px 30px;
          border-radius: 6px;
          overflow-y: auto;
        }
      }
    }
  </style>
