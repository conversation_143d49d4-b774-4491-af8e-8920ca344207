<template>
  <div class="goods-order-list-page">
    <navigation-bar
      pageName="我的订单"
      :navBarStyle="{ backgroundColor: '#fff', color: '#333', fontSize: '16px', fontWeight: 600 }"
      @onLeftClick="onBackClick"
    ></navigation-bar>
    <div class="nav-list">
      <div
        :class="`nav-item ${item.value === queryParams.status ? 'active' : ''}`"
        v-for="(item, index) in navList"
        :key="index"
        @click="handleNav(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="goods-order-list-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-pull-refresh
        v-if="list.length > 0"
        v-model="refreshing"
        class="list"
        @refresh="onRefresh"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="——没有更多了——"
          @load="getList"
          :immediate-check="false"
        >
          <div class="item" v-for="(item, index) in list" :key="index" @click="handleDetail(item)">
            <div class="title">
              <div class="store-info">订单号：{{ item.orderId }}</div>
              <div class="order-status">
                <div class="status-text">
                  {{ $global.GOODS_ORDER_STATUS[item.status] }}
                </div>
              </div>
            </div>
            <div class="goods-wrapper">
              <div v-if="item.goodsList.length > 1" class="multi-goods">
                <div class="img-list">
                  <div v-for="(item, index) in item.goodsList" :key="index">
                    <img class="goods-img" v-if="index <= 2" :src="item.thumbPics" />
                  </div>
                </div>
                <div class="price-quantity">
                  <div class="price">
                    <span class="big-number"
                      >￥{{ formatValue(item.goodsList[0].salesPrice, 'split')[0] }}</span
                    ><span class="small-price"
                      >.{{ formatValue(item.goodsList[0].salesPrice, 'split')[1] }}</span
                    >
                  </div>
                  <div class="quantity">x{{ item.goodsList[0].quantity }}</div>
                </div>
              </div>
              <div v-else class="signle-goods">
                <img class="goods-img" :src="item.goodsList[0].thumbPics" />
                <div class="name-spec">
                  <div class="name overflow-2">{{ item.goodsList[0].spuTitle }}</div>
                  <div class="spec">{{ item.goodsList[0].spec }}</div>
                  <div v-if="item.virtualMsg?.chargeAccount" class="chargeAccount">
                    充值账号：{{ item.virtualMsg?.chargeAccount }}
                  </div>
                </div>
                <div class="price-quantity">
                  <div class="price">
                    <span class="big-number"
                      >￥{{ formatValue(item.goodsList[0].salesPrice, 'split')[0] }}</span
                    ><span class="small-price"
                      >.{{ formatValue(item.goodsList[0].salesPrice, 'split')[1] }}</span
                    >
                  </div>
                  <div class="quantity">x{{ item.goodsList[0].quantity }}</div>
                </div>
              </div>
            </div>
            <div class="pay-amount">
              <span class="label">实付款</span>
              <span class="amount"><span class="symbol">￥</span>{{ item.payAmount }}</span>
            </div>
            <div class="goods-order-list-page-footer">
              <div class="pay-status">
                <div v-if="item.status === 'PAYING' && item.outTime > 0">
                  支付剩余：
                  <van-count-down :time="item.outTime * 1000" @finish="() => onFinish(item)" />
                </div>
              </div>
              <action-bar
                class="action-wrapper"
                :class="{ 'is-verify': item.status === 'VERIFY' }"
                :order-info="item"
                @onDelete="resetQuery"
                @onCancel="resetQuery"
                @onConfirm="resetQuery"
                @onAfterSales="onAfterSales"
              />
            </div>
            <div v-if="item.ecardFlag === 'Y'" class="card-wrapper" @click.stop="handleCard">
              <div class="flex-sub">本订单套餐包括礼品卡商品</div>
              <div class="info">查看详情</div>
              <van-icon class="arrow-icon" color="#999999" :size="12" name="arrow" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else>
        <van-empty :description="loading ? '加载中..' : '暂无数据'" />
      </div>
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import ActionBar from './components/ActionBar'
import { listGoodsOrder } from '@/api/goods-order'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const store = useStore()
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const navList = [
  { label: '全部', value: '' },
  { label: '待付款', value: 'PAYING' },
  { label: '待发货', value: 'WAIT_DELIVER' },
  { label: '待收货', value: 'WAIT_RECEIVING' },
  { label: '已完成', value: 'FINISHED' },
]
const data = reactive({
  queryParams: {
    pageNum: '1',
    pageSize: '10',
    status: '',
  },
})
const { queryParams } = toRefs(data)
// 用来修复切换分页后，数据不正确的问题，因为加载中时没有显示切换分类
// 且数据返回时间不一致，每次请求后id + 1，id大于最后请求的才能更新数据
const lastGetListRequestId = ref(0)
const state = ref('')

const onBackClick = () => {
  router.push({ name: 'Main', params: { componentName: '/my' } })
}
const handleNav = async (item) => {
  state.value = item.value
  queryParams.value.status = item.value
  loading.value = true
  resetQuery()
}
const handleCard = () => {
  router.push('/my/cardpwd')
}
// 详情
const handleDetail = (item) => {
  router.push({
    path: '/my/goods-order-detail',
    query: { orderId: item.orderId, state: state.value || queryParams.value.status },
  })
}
// 重置查询
const resetQuery = () => {
  queryParams.value.pageNum = 1
  list.value = []
  getList()
}
// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
}
const getList = () => {
  const requestId = lastGetListRequestId.value + 1
  lastGetListRequestId.value += 1
  listGoodsOrder(queryParams.value)
    .then((res) => {
      if (requestId >= lastGetListRequestId.value) {
        loading.value = false
        list.value = [...list.value, ...res.data]
        if (list.value.length >= res.total) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      }
    })
    .catch(() => {
      if (requestId >= lastGetListRequestId.value) {
        loading.value = false
        finished.value = true // 防止死循环
      }
    })
}
const onAfterSales = (data) => {
  sessionStorage.setItem(proxy.$global.PARAM_AFTERSALES, JSON.stringify(data))
  router.push('/my/after-sales-form')
}
const onFinish = (item) => {
  // 设置为超时
  item.outTime = 0
}
onMounted(() => {
  queryParams.value.status = route.query.state ? route.query.state : ''
  getList()
})
</script>

<style lang="scss" scoped>
.goods-order-list-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .nav-bar {
    :deep(.left) {
      padding-left: 9px;
      color: #333;
    }
    :deep(.page-title) {
      font-size: 16px;
    }
  }
  .nav-list {
    height: 44px;
    display: flex;
    align-items: center;
    background: #ffffff;
    .nav-item {
      height: 44px;
      flex: 1;
      font-size: 14px;
      font-weight: 400;
      color: #999;
      line-height: 44px;
      text-align: center;
    }
    .nav-item.active {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      position: relative;
    }
    .nav-item.active::after {
      //主要是这个
      content: '';
      display: block;
      margin: 0 auto;
      width: 30px;
      height: 5px;
      background: linear-gradient(270deg, rgba(250, 104, 79, 0.0001) 3.7%, #4671eb 93.92%);
      border-radius: 3px;
      position: absolute;
      left: calc(50% - 15px);
      bottom: 12px;
    }
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    // display: flex;
    // flex-direction: column;
    background: #f9f9f9;
    .list {
      min-height: 100%;
      height: max-content;

      .item {
        margin: 10px;
        background: #ffffff;
        border-radius: 10px;
        .title {
          display: flex;
          justify-content: space-between;
          padding: 10px;
          .store-info {
            display: flex;
            align-items: center;
            font-size: 15px;
            font-weight: 400;
            color: #000;
            line-height: 18px;
            // .text {
            //   font-size: 13px;
            //   font-family: PingFang-SC-Bold, PingFang-SC;
            //   font-weight: bold;
            //   color: #363636;
            //   line-height: 18px;
            // }
          }
          .status-text {
            font-size: 14px;
            font-weight: 400;
            color: #f43727;
            line-height: 16px;
          }
          .pay-status {
            width: 112px;
            height: 16px;
            background: url('@/assets/images/goods/wait-pay-bg.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            .label {
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left center;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #e9362e;
              margin: 0 8px 0 5px;
              line-height: 16px;
            }
            .van-count-down {
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left center;
              color: #ffffff;
              line-height: 16px;
            }
          }
        }
        .goods-wrapper {
          padding: 0 10px;
          // margin-top: 10px;
          .goods-img {
            width: 89px;
            height: 89px;
            display: block;
            flex-shrink: 0;
            border-radius: 16px;
          }
          .price-quantity {
            margin-left: 8px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            .price {
              margin-top: 2px;
              font-weight: bold;
              font-size: 15px;
              color: #333;
              line-height: 18px;
              &::first-letter {
                font-size: 12px;
              }
            }
            .quantity {
              margin-top: 7px;
              font-size: 14px;
              font-weight: 400;
              color: #999;
              line-height: 17px;
            }
          }
          .multi-goods {
            display: flex;
            .img-list {
              flex: 1;
              display: flex;
              .goods-img + .goods-img {
                margin-left: 10px;
              }
            }
          }
          .signle-goods {
            display: flex;
            .name-spec {
              flex: 1;
              margin-left: 5px;
              .name {
                font-size: 15px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 18px;
              }
              .spec,
              .chargeAccount {
                margin-top: 10px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 17px;
              }
              .chargeAccount {
                margin-top: 5px;
              }
            }
          }
        }
        .pay-amount {
          text-align: right;
          font-size: 15px;
          font-weight: 400;
          line-height: 18px;
          padding: 4px 10px;
          color: #333;

          .label {
            font-size: 15px;
          }
          .amount {
            font-size: 18px;
            font-weight: 600;
          }
          .symbol {
            font-size: 12px;
          }
        }
        .action-wrapper {
          margin: 10px 12px 0 0;
        }
        .action-wrapper.is-verify {
          margin: 0;
        }
        .card-wrapper {
          margin-left: 10px;
          padding: 10px 16px 10px 6px;
          display: flex;
          align-items: center;
          border-top: 1px solid #f4f4f4;
          font-size: 12px;
          line-height: 17px;
          .info {
            color: #999999;
            margin-right: 5px;
          }
        }
      }
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    .pay-status {
      // width: 100%;
      font-size: 15px;
      font-weight: 400;
      color: #333;
      padding-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      .van-count-down {
        display: inline-block;
        color: #f43727;
      }
    }
  }
}
.nav-bar {
  border-bottom: none;
}
</style>
