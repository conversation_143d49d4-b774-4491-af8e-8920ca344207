<template>
  <div class="equity-order-page">
    <navigation-bar pageName="我的票券" @onLeftClick="onBackClick" />
    <div class="equity-order-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="my-tabs">
        <div
          class="tab-item"
          v-for="(item, index) in tabs"
          :key="index"
          :class="{ 'active theme-text': item.key === curTab }"
          @click="onChange(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="tab-content">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="——没有更多了——"
            @load="getList"
            :immediate-check="false"
          >
          <quota-voucher :list="list"></quota-voucher>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { listVoucher, writtenOff } from '@/api/voucher'
  import QuotaVoucher from './components/QuotaVoucher'
  import { showConfirmDialog } from 'vant'
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const store = useStore()
  const router = useRouter()
  const route = useRoute()
  const loading = ref(false)
  const finished = ref(false)
  const list = ref([])
  const tabs = ([
    {key: 'QUOTA', name:'提额券'},
    {key: '1', name:'话费券'},
    {key: '2', name: '免息劵'},
    {key: '3', name: '还款劵'}
  ])
  const curTab = ref('QUOTA')
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
  })
  const { queryParams } = toRefs(data)
  const getList = async () => {
    loading.value = true
    let ress = null
    if (curTab.value === 'QUOTA') {
       ress = await listVoucher(queryParams.value)
    }
    if(ress) {
      list.value = [...list.value, ...ress.data]
      if(list.value.length >= ress.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }
    loading.value = false
    finished.value = true // 防止死循环
  }
  const onChange = (item) => {
    list.value = []
    curTab.value = item.key
    if (item.key === 'QUOTA') {
      finished.value = false
      loading.value = true
      getList()
    }
  }
  const onBackClick = () => {
    router.go(-1)
  }
  const writtenOffFn = (voucherId) => {
    writtenOff({ voucherId }).then(res => {
      console.log(res.data)
      if (res.data?.creditFlag === 'Y') {
        proxy.onToastSucc(() => {
          list.value = []
          getList()
        }, '激活成功')
      } else {
        showConfirmDialog({
          title: '提示',
          message: '当前产品授信额度未可用，暂不能使用提额卡',
          confirmButtonText: '去看看',
          cancelButtonText: '好的'
        })
          .then(() => {
            router.push('/my/quota')
          })
          .catch(() => {
            // on cancel
          });
      }
    })
  }
  provide('writtenOffFn', writtenOffFn)
  onMounted(() => {
    const queryTab = route.query.tab
    curTab.value = queryTab || 'QUOTA'
    getList()
  })
</script>

<style lang="scss" scoped>
  .equity-order-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .my-tabs{
        height: 50px;
        display: flex;
        background: #ffffff;
        .tab-item{
          line-height: 50px;
          flex: 1;
          font-size: 16px;
          text-align: center;
          color: #000000;
          font-family: PingFangSC-Regular, PingFang SC;
        }
        .tab-item.active::after{//主要是这个
          content: '';
          display: block;
          margin: 0 auto;
          margin-top: -3px;
          width: 32px;
          height: 3px;
          background: #FF671A;
          border-radius: 2px;
        }
      }
    }
  }
  .nav-bar {
    border-bottom: none;
  }
</style>