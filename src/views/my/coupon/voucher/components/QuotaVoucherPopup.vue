<template>
  <van-popup
    :close-on-click-overlay="false"
    v-model:show="show"
    class="overflow-inherit"
    :style="{ width: $px2rem('300px'), height: $px2rem('400px'), background: 'transparent' }"
  >
    <div :class="`content ${ data.useType}`">
      <div class="quota">
        <span class="number">{{data.faceValue}}</span>
        <span class="text">元</span>
      </div>
      <div class="rich-text" v-dompurify-html="data.description"></div>
      <div class="btn-wrapper">
        <div :class="`btn ${ data.useType}`" @click="handleWrittenOff">立即激活</div>
        <img class="up" src="@/assets/images/member/quota-btn.png">
      </div>
      <van-icon class="close" color="#cccccc" name="close" @click="show=false"></van-icon>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
})
const writtenOffFn = inject('writtenOffFn')
const handleWrittenOff = () => {
  show.value = false
  writtenOffFn(props.data.voucherId)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .content.CONSUME_QUOTA{
    background: url('@/assets/images/member/quota-popup-bg-consume.png') no-repeat;
    background-size: 100% 100%;
  }
  .content{
    height: 100%;
    background: url('@/assets/images/member/quota-popup-bg-cash.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    .quota{
      padding: 100px 0 0 40px;
      display: flex;
      align-items: flex-end;
      color: #FF4419;
      .number{
        font-weight: bold;
        font-size: 45px;
        line-height: 52px;
        font-family: 'DINAlternate';
      }
      .text{
        display: inline-block;
        font-size: 18px;
        margin-bottom: 8px;
        margin-left: 2px;
      }
    }
    .rich-text{
      width: 238px;
      height: 130px;
      margin: 30px auto 0;
    }
    .btn-wrapper{
      margin: 10px auto 0;
      position: relative;
      text-align: center;
      width: 174px;
      .btn{
        background: linear-gradient( 180deg, #FF671A 0%, #FF4019 100%);
        border-radius: 20px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        width: 174px;
        font-size: 16px;
        color: #FFFFFF;
        font-weight: bold;
        margin: 0 auto;
      }
      .btn.CONSUME_QUOTA{
        background: linear-gradient( 159deg, #1A69FF 0%, #1F3DFA 44%, #193FFF 100%);
      }
      .up{
        position: absolute;
        width: 43px;
        height: 35px;
        display: block;
        top: 6px;
        right: -10px;
      }
    }
    .close{
      display: block;
      position: absolute;
      bottom: -40px;
      left: calc(50% - 10px);
      font-size: 26px;
    }
  }
</style>