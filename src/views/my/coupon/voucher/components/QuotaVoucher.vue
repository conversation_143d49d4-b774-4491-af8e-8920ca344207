<template>
  <div v-if="list.length" class="quota-list">
    <div class="quota-item" v-for="(item, index) in list" :key="index">
      <div class="title">
        <span class="active-time">{{ parseTime(item.activeTime) }}</span> 
        <span :class="`status ${item.status}`">{{ item.statusDesc === '已核销' ? '已激活' : item.statusDesc }}</span>
      </div>
      <div class="content solid-top">
        <img v-if="item.useType === 'CASH_QUOTA'" class="logo" src="@/assets/images/member/quota-icon-cash.png">
        <img v-if="item.useType === 'CONSUME_QUOTA'" class="logo" src="@/assets/images/member/quota-icon-consume.png">
        <div class="info">
          <div class="info-l">
            <div class="info-name">{{ item.voucherName }}</div>
            <div class="valid-time">有效期至{{ item.endTime }}</div>
            <div v-if="item.status ==='WRITTEN_OFF'" class="tips">额度已生效，恭喜获得额度提升</div>
            <div v-else class="tips">额度待生效，请激活使用</div>
          </div>
          <div v-if="item.status === 'VALID'" class="info-btn" @click="handleQuota(item)">去激活</div>
          <div v-else-if="item.status === 'WRITTEN_OFF'" class="query-btn" @click="handleQuery(item)">去查看</div>
        </div>
      </div>
    </div>
    <quota-voucher-popup ref="quotaPopupRef" :data="quotaItem"></quota-voucher-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import QuotaVoucherPopup from './QuotaVoucherPopup.vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const quotaPopupRef = ref(null)
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})
const data = reactive({
  quotaItem: {},
})
const { quotaItem } = toRefs(data)
const handleQuota = (item) => {
  quotaItem.value = item
  quotaPopupRef.value.show = true
}
const handleQuery = () => {
  router.push('/my/quota')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .quota-list{
    padding-top: 16px;
    .quota-item +.quota-item{
      margin-top: 10px;
    }
    .quota-item{
      background: #ffffff;
      .title{
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        padding: 0 15px;
        color: #999999;
        .status.VALID{
          color: #FF671A;
        }
        .status.WRITTEN_OFF {
          color: #06BB8D;
        }
      }
      .content{
        padding: 20px 0 20px 15px;
        display: flex;
        .logo{
          width: 40px;
          height: 40px;
          display: block;
        }
        .info{
          flex: 1;
          display: flex;
          justify-content: space-between;
          margin-left: 20px;
          align-items: center;
          .info-l{
            .info-name {
              font-size: 18px;
              color: #333333;
              line-height: 25px;
              font-weight: bold;
            }
            .valid-time{
              margin-top: 8px;
            }
            .valid-time,.tips{
              font-size: 13px;
              color: #999999;
              line-height: 18px;
            }
          }
          .info-btn{
            padding: 0 12px;
            font-size: 13px;
            color: #FFFFFF;
            background: #FF671A;
            border-radius: 17px;
            height: 33px;
            display: flex;
            align-items: center;
            margin-right: 13px;
          }
          .query-btn{
            padding: 0 12px;
            font-size: 13px;
            color: #FF671A;
            background: #FFFFFF;
            border-radius: 17px;
            height: 33px;
            display: flex;
            align-items: center;
            margin-right: 13px;
            border: 1px solid #FF671A;
          }
        }
      }
    }
  }
</style>