import { showConfirmDialog } from 'vant';
export const useAvatarAuth = (avatarUploadRef) => {
  const avatar_auth = localStorage.getItem('avatar_auth')
  if (!avatar_auth || avatar_auth === 'N') {
    showConfirmDialog({
      title: '相册/摄像头权限说明',
      message:
        '便于您使用该功能上传您的照片/图片，请您确认授权，否则无法使用该功能。',
      confirmButtonText: '同意',
      cancelButtonText: '不同意'
    })
    .then(() => {
      localStorage.setItem('avatar_auth', 'Y')
      avatarUploadRef.showChoose = true
    })
    .catch(() => {
      localStorage.setItem('avatar_auth', 'N')
      // on cancel
    });
  } else {
    if (avatar_auth === 'Y') {
      avatarUploadRef.showChoose = true
    }
  }
}