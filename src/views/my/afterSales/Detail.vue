<template>
  <div class="after-sales-detail-page">
    <navigation-bar
      @onLeftClick="onBackClick"
      pageName="售后详情"
      :navBarStyle="{ fontWeight: 600, backgroundColor: '#f9f9f9' }"
    />
    <div class="after-sales-detail-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="state-wrapper">
        <div class="content">
          <template v-if="returnInfo.status === 'APPLY'">
            <div class="p1">等待商家处理</div>
            <div class="p2">售后处理中 请耐心等待审核结果</div>
          </template>
          <template v-else-if="returnInfo.status === 'REFUND'">
            <div class="p1">服务已完成</div>
            <div class="p2">售后已处理 感谢您的支持</div>
          </template>
          <template v-else-if="returnInfo.status === 'CANCEL'">
            <div class="p1">申请已撤销</div>
            <div class="p2">您已撤销此次退款申请，若您的问题未解决，可以在有效期内再次申请</div>
          </template>
        </div>
        <div v-if="returnInfo.status === 'APPLY'" class="business-info">
          <div class="title">您已发起退款申请，请耐心等待商家处理。</div>
          <div class="html-class" v-html="returnInfo.trackingRecord"></div>
        </div>
        <div
          v-if="returnInfo.status === 'CANCEL' || returnInfo.status === 'REFUND'"
          class="business-info"
        >
          <div class="title flex justify-between cancel">
            <div class="label">退款金额</div>
            <div class="value money">￥{{ returnInfo.returnAmount }}</div>
          </div>
          <div style="font-size: 15px; font-weight: 400; color: #999">申请通过后退回原账户</div>
        </div>
        <div class="actions">
          <!-- <div class="btn kefu" @click="onKefu('kefu')">在线客服</div> -->
          <div
            class="btn cancel"
            v-if="!(returnInfo.status === 'REFUND' || returnInfo.status === 'CANCEL')"
            @click.stop="onCancel('return')"
          >
            撤销申请
          </div>
        </div>
      </div>
      <div class="return-flow">
        <div class="title">退款流程</div>
        <div class="item" v-if="returnInfo.status === 'APPLY'">
          <div class="title">
            <img class="dot" src="@/assets/images/my/dot.png" />
            <div class="title-text">退款申请已提交</div>
          </div>
          <div class="desc">退款原因：{{ returnInfo.reason }}</div>
          <div class="time">{{ formatTime(returnInfo.createTime) }}</div>
        </div>
        <div class="item" v-else>
          <div class="title">
            <img class="dot" src="@/assets/images/my/dot.png" />
            <div class="title-text" v-html="returnInfo.trackingRecord"></div>
          </div>
          <div class="desc" v-html="returnInfo.handleNote" />
          <div class="time">{{ formatTime(returnInfo.handleTime) }}</div>
        </div>
      </div>
      <div class="order-info">
        <div class="goods-list">
          <div class="goods-item" v-for="item in returnInfo.goodsList" :key="item.detailId">
            <GoodsInfoCard :goods="item" />
          </div>
        </div>
        <div class="return-info">
          <div class="row">
            <span class="label">退货原因：</span>
            <span class="value">{{ returnInfo.reason }}</span>
          </div>
          <div class="row">
            <span class="label">服务单号：</span>
            <div class="order">
              <span class="order-id">{{ returnInfo.returnId }}</span>
              <van-divider vertical />
              <span
                class="copy-btn"
                v-clipboard:copy="returnInfo.returnId"
                v-clipboard:success="clipboardSuccess"
                >复制</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <confirm-dialog
      ref="confirmDialogRef"
      title="确认撤销当前服务单吗？"
      @onConfirm="onComfirmCancel"
    />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { applyReturnDetail, cancelReturn } from '@/api/goods-order'
import ConfirmDialog from '@/components/ConfirmDialog'
import GoodsInfoCard from './GoodsInfoCard.vue'
import { showToast } from 'vant'
import dayjs from 'dayjs'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const route = useRoute()
const router = useRouter()
const confirmDialogRef = ref(null)
const { returnId, orderStatus } = route.query
const data = reactive({
  returnInfo: {},
})
const { returnInfo } = toRefs(data)
const onBackClick = () => {
  router.push({ path: '/my/after-sales', query: { orderStatus } })
}
const onKefu = () => {
  proxy.$customerService()
}
const onCancel = () => {
  confirmDialogRef.value.show = true
}
const onComfirmCancel = () => {
  cancelReturn({ returnId }).then((res) => {
    proxy.onToastSucc(() => {
      getInfo()
    }, '撤销成功！')
  })
}
const getInfo = () => {
  applyReturnDetail({ returnId }).then((res) => {
    returnInfo.value = res.data
  })
}
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}
const clipboardSuccess = () => {
  showToast('复制成功')
}
onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped>
.after-sales-detail-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    margin-top: -1px;
    .state-wrapper {
      // background: linear-gradient(270deg, #FA6B14 100%, #ffffff 0%);
      padding: 10px;
      background: #ffffff;
      border-radius: 8px;
      margin: 10px;
      .content {
        padding: 2px 0;
        .p1 {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #333;
          line-height: 19px;
        }
        .p2 {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #a8a8a8;
          line-height: 17px;
          margin-top: 4px;
        }
      }
      .actions {
        margin-top: 10px;
        background: #ffffff;
        display: flex;
        align-items: center;
        .btn {
          flex: 1;
          text-align: center;
          background: #ffffff;
          border-radius: 14px;
          border: 1px solid #979797;
          font-size: 15px;
          line-height: 20px;
          padding: 6px 12px;
          color: #333;
        }
        .btn.cancel {
          border: 1px solid #a8a8a8;
        }
        .btn + .btn {
          margin-left: 5px;
        }
      }
    }
    .business-info {
      background: #f9f9f9;
      margin-top: 10px;
      padding: 12px 18px;
      .title {
        font-size: 15px;
        font-weight: 400;
        color: #333;
        margin-bottom: 6px;
      }
      .title.cancel {
        font-size: 16px;
        color: #333;
        line-height: 22px;
        .label {
          font-size: 15px;
          font-weight: 400;
          color: #333;
        }
        .value {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          line-height: 22px;
          &::first-letter {
            font-size: 10px;
          }
        }
      }
    }
    .order-info {
      background: #ffffff;
      border-radius: 8px;
      margin: 10px;
      .goods-list {
        padding: 10px;
        .goods-item {
          display: flex;
          margin-top: 10px;
        }
      }
      .return-info {
        padding: 0 15px 14px 15px;

        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .label {
            font-size: 14px;
            font-weight: 400;
            color: #666666;
          }
          .value {
            flex: 1;
            text-align: right;
            font-size: 14px;
            font-weight: 400;
            color: #000;
          }
        }

        .row:not(:last-child) {
          margin-bottom: 16px;
        }

        .order {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .order-id {
            flex: 1;
            text-align: right;
            font-size: 14px;
            font-weight: 400;
            color: #999;
          }
          .copy-btn {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
  }
}
.nav-bar {
  border-bottom: none;
}
.html-class {
  font-size: 15px;
  font-weight: 400;
  line-height: 18px;
  color: #999;
}

.return-flow {
  background-color: #fff;
  padding: 10px;
  margin: 10px;
  border-radius: 8px;

  .title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    line-height: 18px;
  }

  .item {
    margin-top: 10px;

    .title {
      display: flex;
      align-items: center;

      .dot {
        width: 6px;
        height: 6px;
        margin: 0 14px;
      }

      .title-text {
        color: #999;
        font-size: 15px;
        font-weight: 600;
        line-height: 18px;
      }
    }

    .desc {
      color: #999;
      font-size: 14px;
      font-weight: 400;
      margin-left: 34px;
      margin-top: 9px;
    }

    .time {
      color: #cfcfcf;
      font-size: 14px;
      font-weight: 400;
      margin-left: 34px;
      margin-top: 6px;
      line-height: 17px;
    }
  }
}
</style>
