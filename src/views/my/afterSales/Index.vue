<template>
  <div class="after-sales-list-page">
    <navigation-bar pageName="退换/售后" @onLeftClick="onBackClick"></navigation-bar>
    <div class="nav-list">
      <div
        :class="`nav-item ${item.value === queryParams.status ? 'active' : ''}`"
        v-for="(item, index) in navList"
        :key="index"
        @click="handleNav(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="after-sales-list-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div v-if="list.length > 0" class="list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="——没有更多了——"
            @load="getList"
            :immediate-check="false"
          >
            <div
              class="item"
              v-for="(item, index) in list"
              :key="index"
              @click="handleDetail(item)"
            >
              <div class="title">
                <div class="store-info">
                  {{ parseTime(item.createTime) }}
                </div>
                <div class="order-status">
                  <div class="status-text">
                    {{ $global.GOODS_ORDER_STATUS[item.status] }}
                  </div>
                </div>
              </div>
              <div class="goods-wrapper">
                <div v-if="item.goodsList.length > 1" class="multi-goods">
                  <div class="img-list">
                    <div v-for="(item, index) in item.goodsList" :key="index">
                      <img class="goods-img" v-if="index <= 2" :src="item.thumbPics" />
                    </div>
                  </div>
                  <div class="price-quantity">
                    <div class="price">￥{{ item.goodsList[0].totalAmount }}</div>
                    <div class="quantity">x{{ item.goodsList[0].quantity }}</div>
                  </div>
                </div>
                <div v-else class="signle-goods">
                  <img class="goods-img" :src="item.goodsList[0].thumbPics" />
                  <div class="name-spec">
                    <div class="name overflow-2">{{ item.goodsList[0].spuTitle }}</div>
                    <div class="spec">{{ item.goodsList[0].spec }}</div>
                  </div>
                  <div class="price-quantity">
                    <div class="price">￥{{ item.goodsList[0].totalAmount }}</div>
                    <div class="quantity">x{{ item.goodsList[0].quantity }}</div>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else>
        <van-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { listAfterSales } from '@/api/goods-order'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const router = useRouter()
const store = useStore()
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const navList = [
  { label: '申请中', value: 'APPLY' },
  { label: '已撤销', value: 'CANCEL' },
  { label: '退款完成', value: 'REFUND' },
]
const data = reactive({
  queryParams: {
    pageNum: '1',
    pageSize: '10',
    status: 'APPLY',
  },
})
const orderStatus = ref('')
const { queryParams } = toRefs(data)
const onBackClick = () => {
  router.push({ name: 'Main', params: { componentName: '/my' } })
}
const handleNav = (item) => {
  orderStatus.value = item.value
  queryParams.value.status = item.value
  resetQuery()
}
// 详情
const handleDetail = (item) => {
  router.push({
    path: '/my/after-sales-detail',
    query: { returnId: item.returnId, orderStatus: orderStatus.value },
  })
}
// 重置查询
const resetQuery = () => {
  queryParams.value.pageNum = 1
  list.value = []
  getList()
}
// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
}
const getList = () => {
  listAfterSales(queryParams.value)
    .then((res) => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if (list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum++
      }
    })
    .catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
}
onMounted(() => {
  const status = route.query.orderStatus
  if (user.value && status) {
    orderStatus.value = status
    queryParams.value.status = status
    resetQuery()
  } else {
    getList()
  }
})
</script>

<style lang="scss" scoped>
.after-sales-list-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  // background: #f9f9f9;
  .nav-list {
    height: 44px;
    display: flex;
    align-items: center;
    background: #ffffff;
    .nav-item {
      height: 44px;
      flex: 1;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 44px;
      text-align: center;
    }
    .nav-item.active {
      font-size: 14px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: var(--primary-color);
      position: relative;
    }
    .nav-item.active::after {
      //主要是这个
      content: '';
      display: block;
      margin: 0 auto;
      width: 22px;
      height: 3px;
      background: var(--primary-color);
      border-radius: 2px;
      position: absolute;
      left: calc(50% - 11px);
      bottom: 8px;
    }
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f9f9f9;
    .list {
      .item {
        margin: 10px;
        background: #ffffff;
        border-radius: 8px;
        padding: 10px;
        .title {
          display: flex;
          justify-content: space-between;
          // padding: 15px 10px 0 10px;
          .store-info {
            display: flex;
            align-items: center;
            font-size: 14px;
            line-height: 17px;
            color: #333;
            img {
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }
            .text {
              font-size: 13px;
              font-family: PingFang-SC-Bold, PingFang-SC;
              font-weight: bold;
              color: #333;
              line-height: 18px;
            }
          }
          .status-text {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            line-height: 16px;
          }
          .pay-status {
            width: 112px;
            height: 16px;
            background: url('@/assets/images/goods/wait-pay-bg.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            .label {
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left center;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #4671eb;
              margin: 0 8px 0 5px;
              line-height: 16px;
            }
            .van-count-down {
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left center;
              color: #ffffff;
              line-height: 16px;
            }
          }
        }
        .goods-wrapper {
          padding: 0 10px;
          margin-top: 7px;
          .goods-img {
            width: 89px;
            height: 89px;
            display: block;
            flex-shrink: 0;
            border-radius: 16px;
          }
          .price-quantity {
            margin-left: 8px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-top: 1px;
            .price {
              font-size: 15px;
              font-weight: bold;
              &::first-letter {
                font-size: 70%;
              }
            }
            .quantity {
              margin-top: 5px;
              font-size: 14px;
              font-weight: 400;
              color: #333;
              line-height: 17px;
            }
          }
          .multi-goods {
            display: flex;
            .img-list {
              flex: 1;
              display: flex;
              .goods-img + .goods-img {
                margin-left: 10px;
              }
            }
          }
          .signle-goods {
            display: flex;
            .name-spec {
              flex: 1;
              margin-left: 5px;
              .name {
                font-size: 15px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 600;
                color: #333;
                line-height: 18px;
              }
              .spec {
                margin-top: 5px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 16px;
              }
            }
          }
        }
        .action-wrapper {
          margin: 10px 10px 0 0;
        }
        .card-wrapper {
          margin-left: 10px;
          padding: 10px 16px 10px 6px;
          display: flex;
          align-items: center;
          border-top: 1px solid #f4f4f4;
          font-size: 12px;
          line-height: 17px;
          .info {
            color: #999999;
            margin-right: 5px;
          }
        }
      }
    }
  }
}
.nav-bar {
  border-bottom: none;
}
:deep(.van-pull-refresh) {
  min-height: calc(100vh - 180px);
  min-height: calc(100dvh - 180px);
  display: flex;
  flex-direction: column;
  overflow: unset; // 可选，这个视情况添加
  .van-pull-refresh__track {
    flex: 1;
  }
}
</style>