<script setup>
import { computed } from 'vue'

const props = defineProps({
  goods: {
    type: Object,
    default() {
      return {}
    },
  },
})

const goods = computed(() => props.goods)
</script>

<template>
  <div class="goods-wrapper">
    <img class="goods-img" :src="goods.albumPics ? goods.albumPics : goods.thumbPics" />
    <div class="goods-info">
      <div class="goods-info-top">
        <div class="goods-title-wrapper">
          <div class="goods-name overflow-1">{{ goods.spuTitle }}</div>
          <div class="goods-spec">{{ goods.spec }}</div>
        </div>
        <div class="goods-price-wrapper">
          <div class="price">￥{{ goods.price ? goods.price : goods.salesPrice }}</div>
          <div class="quantity">x{{ goods.quantity }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.goods-wrapper {
  display: flex;

  .goods-img {
    width: 89px;
    height: 89px;
    flex-shrink: 0;
    margin-right: 6px;
    border-radius: 16px;
  }

  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .goods-info-top {
      display: flex;
      justify-content: space-between;

      .goods-title-wrapper {
        .goods-name {
          font-size: 15px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 600;
          color: #333333;
          line-height: 18px;
        }

        .goods-spec {
          margin-top: 5px;
          font-size: 14px;
          transform: scale(0.85);
          transform-origin: left top;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          line-height: 17px;
        }
      }

      .goods-price-wrapper {
        .price {
          font-size: 15px;
          font-weight: 600;
          color: #333;
          margin-top: 2px;

          &::first-letter {
            font-size: 70%;
          }
        }

        .quantity {
          font-size: 14px;
          color: #999;
          text-align: right;
          margin-top: 5px;
          line-height: 17px;
        }
      }
    }

    // 这个样式废弃了吧
    .goods-price-quantity {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .price {
        font-size: 16px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #e9362e;
        line-height: 22px;
      }

      .quantity {
        font-size: 12px;
        transform: scale(0.85);
        transform-origin: left top;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 20px;
      }
    }
  }
}
</style>