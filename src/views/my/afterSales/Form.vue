<template>
  <div class="after-sales-apply-page">
    <navigation-bar
      pageName="申请退款"
      :navBarStyle="{ fontWeight: 600, backgroundColor: '#f9f9f9' }"
      @onLeftClick="onBackClick"
    ></navigation-bar>
    <div class="after-sales-apply-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="goods-list return-nav">
        <div class="goods-item" v-for="(goods, index) in salesData.goodsList" :key="index">
          <GoodsInfoCard :goods="goods" />
        </div>
        <div class="return-way">
          <div class="label-group">
            <div class="label">退货原因</div>
            <div class="value" @click="showReason = true">
              {{ form.reason ? form.reason : '请选择您的退货原因' }}
            </div>
            <van-icon size="18" color="#A8A8A8" name="arrow" />
          </div>
        </div>
        <div class="return-amount">
          <div class="label">退款金额：</div>
          <div class="amount">￥{{ totalAmount }}</div>
        </div>
        <div class="apply-desc">
          <div class="title">申请说明</div>
          <van-field
            v-model="form.desc"
            rows="2"
            autosize
            type="textarea"
            maxlength="100"
            placeholder="补充说明，有利于更好处理售后问题"
            show-word-limit
          />
        </div>
      </div>
      <div class="return-pic return-nav">
        <div class="title">上传凭证<span>(可上传3张图片)</span></div>
        <div class="pics">
          <van-uploader
            name="pics"
            v-model="fileList"
            :max-count="3"
            :before-read="beforeRead"
            :after-read="afterRead"
            @delete="onDelete"
          />
        </div>
      </div>
    </div>
    <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="submit-btn theme-linear-gradient" @click="handleSubmit">提交申请</div>
    </div>
    <van-popup v-model:show="showReason" round position="bottom">
      <van-picker :columns="reasonList" @cancel="showReason = false" @confirm="onReasonConfirm" />
    </van-popup>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { uploadBase64 } from '@/api/base'
import { applyReturn } from '@/api/goods-order'
import { showToast } from 'vant'
import GoodsInfoCard from './GoodsInfoCard.vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const reasonList = [
  { text: '拍多/多拍/不喜欢', value: '拍多/多拍/不喜欢' },
  { text: '未按约定时间发货', value: '未按约定时间发货' },
  { text: '收到商品破损或污迹', value: '收到商品破损或污迹' },
  { text: '协商一致退款', value: '协商一致退款' },
  { text: '其他', value: '其他' },
]
const data = reactive({
  salesData: {},
  form: {
    orderId: undefined,
    detailIds: [],
    desc: undefined,
    proofPics: [],
  },
})
const { salesData, form } = toRefs(data)
const showReason = ref(false)
const totalAmount = ref(0)
const fileList = ref([])
const pictureList = ref([])
const onBackClick = () => {
  router.go(-1)
}
const onReasonConfirm = (val) => {
  form.value.reason = val.selectedValues[0]
  showReason.value = false
}
const beforeRead = (file) => {
  const fileSuffix = file.type.split('/')[1].toLowerCase()
  if (['jpeg', 'jpg', 'bmp', 'png', 'gif'].indexOf(fileSuffix) < 0) {
    showToast('请上传 jpeg/jpg/bmp/png/gif 格式文件')
    return false
  }
  return true
}
const afterRead = (file) => {
  uploadBase64({
    base64: file.content,
    fileName: file.file.name,
  })
    .then((res) => {
      pictureList.value.push(res.link)
    })
    .catch(() => {
      file.status = 'failed'
      file.message = '上传失败'
    })
}
// 删除
const onDelete = (file, detail) => {
  pictureList.value.splice(detail.index, 1)
}
const handleSubmit = () => {
  if (form.value.reason) {
    form.value.proofPics = pictureList.value.length > 0 ? pictureList.value : []
    applyReturn(form.value).then((res) => {
      proxy.onToastSucc(() => {
        router.replace({ name: 'MyAfterSalesDetail', query: { returnId: res.data.returnId } })
      }, '申请成功！')
    })
  } else {
    showToast('请选择退货原因')
  }
}
onMounted(() => {
  salesData.value = JSON.parse(sessionStorage.getItem(proxy.$global.PARAM_AFTERSALES))
  let amount = 0
  let details = []
  salesData.value.goodsList.map((item) => {
    amount += item.payAmount
    details.push(item.detailId)
  })
  totalAmount.value = amount
  form.value.orderId = salesData.value.orderId // 订单号
  form.value.detailIds = details // 明细单号
})
</script>

<style lang="scss" scoped>
.after-sales-apply-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    .return-nav {
      background: #ffffff;
      border-radius: 8px;
      margin: 10px;
    }
    .goods-list {
      padding: 14px;
    }
    .return-way {
      padding: 10px 2px;
      // padding-right: 10px;
      .label-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label {
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 17px;
        }
        .value {
          flex: 1;
          color: #999;
          text-align: right;
          font-size: 15px;
          line-height: 17px;
        }
      }
    }
    .return-amount {
      padding: 10px 2px;
      font-size: 14px;
      color: #333;
      display: flex;
      justify-content: space-between;
      .amount {
        margin-left: auto;
        text-align: right;
        font-size: 16px;
        font-weight: bold;
        color: #f43727;
      }
      .amount::first-letter {
        font-size: 70%;
      }
    }
    .apply-desc {
      overflow: hidden;
      .title {
        font-size: 14px;
        color: #333;
        padding: 10px 2px;
        padding-bottom: 5px;
      }
      :deep(.van-field) {
        background: #f9f9f9;
        border-radius: 4px;
      }
    }
    .return-pic {
      padding: 12px 15px;
      .title {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #333;
        margin-bottom: 16px;
      }
    }
    :deep(.van-uploader__upload) {
      background: #f9f9f9;
      border-radius: 4px;
    }
  }
}
.footer {
  background: #ffffff;
  padding: 10px 16px;
  position: fixed;
  bottom: 0;
  width: calc(100% - 32px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .submit-btn {
    height: 40px;
    border-radius: 7px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    line-height: 40px;
    width: 100%;
    color: #ffffff;
  }
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}
</style>