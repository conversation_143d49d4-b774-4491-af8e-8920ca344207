<template>
  <div class="quota-info">
    <div class="tab-wrapper">
      <div
        :class="`tab-item ${item.productType} ${checked === index ? 'active' : ''}`"
        v-for="(item, index) in data"
        :key="index"
        @click="changeTab(index)"
      >
        {{ item.productName }}
      </div>
    </div>
    <swiper class="swiperBox" @swiper="setSwiper" @slideChange="onSlideChange">
      <swiper-slide v-for="(item, index) in data" :key="index">
        <div class="tab-content">
          <div :class="`tab-quota ${item.productType}`">
            <div class="label1">
              {{ item.creditStatus === 'SUCC' ? '可用额度(元）' : '最高额度(元）' }}
            </div>
            <div class="quota-number" v-if="item.creditStatus === 'SUCC'">
              {{ formatMoney(item.balance) }}
            </div>
            <div :class="`quota-number ${item.creditStatus === 'APPLY' ? 'apply' : ''}`" v-else>
              {{ item.creditStatus === 'APPLY' ? '额度申请中' : '******' }}
            </div>
            <div class="label2">
              {{
                item.productType === 'CASH' ? '额度高，利率低，放款快' : '分期购，零首付，轻松享'
              }}
            </div>
            <div :class="`btn ${item.productType}`" @click="handleUsingLetters(item)">
              <span v-if="item.creditStatus === 'SUCC'">
                {{ item.productType === 'CASH' ? '立即提现' : '立即分期' }}
              </span>
              <span v-else>立即申请</span>
            </div>
          </div>
          <div class="tab-order">
            <div class="btn">限时还款福利</div>
            <div class="next">
              <span class="text">下期预估额度</span>
              <span class="number"
                >{{
                  item.nextPreAmount > 0 && item.creditStatus === 'SUCC'
                    ? item.nextPreAmount
                    : '******'
                }}<span v-if="item.creditStatus === 'SUCC'" class="unit">元</span></span
              >
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
// import { EffectFade } from 'swiper/modules'
import 'swiper/css'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const checked = ref(0)
const swiperRef = ref(null)
const porps = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
const emit = defineEmits(['handleUsingLetters'])
const handleUsingLetters = (item) => {
  emit('handleUsingLetters', item)
}
const setSwiper = (swiper) => {
  swiperRef.value = swiper
}
const changeTab = (index) => {
  checked.value = index
  swiperRef.value.slideTo(index)
}
const onSlideChange = (val) => {
  checked.value = val.activeIndex
}
onMounted(() => {})
</script>
<style scoped lang='scss'>
.quota-info {
  padding-top: 21px;
  .tab-wrapper {
    width: 200px;
    height: 40px;
    background: #ffffff;
    border-radius: 20px;
    margin: 0 auto;
    padding: 3px 4px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .tab-item {
      width: 97px;
      background: #ffffff;
      border-radius: 17px;
      font-size: 14px;
      color: #666666;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tab-item.CONSUME.active {
      background: #fe463e;
      color: #ffffff;
    }
    .tab-item.CASH.active {
      background: #ff5533;
      color: #ffffff;
      font-weight: 600;
    }
  }
  .swiperBox {
    margin: 20px 12px 0;
    .tab-content {
      background: #ffffff;
      border-radius: 12px;
      .tab-quota.CONSUME {
        background: url('@/assets/images/quota/yongqian-bg.png') no-repeat;
        background-size: 351px 145px;
        .quota-number {
          text-shadow: 0px 2px 4px #fe463e;
        }
      }
      .tab-quota.CASH {
        background: url('@/assets/images/quota/xiaofu-bg.png') no-repeat;
        background-size: 351px 145px;
        .quota-number {
          text-shadow: 0px 2px 4px rgba(220, 65, 43, 0.69);
        }
      }
      .tab-quota {
        padding: 22px 0 26px 22px;
        position: relative;
        .label1 {
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
        }
        .quota-number {
          margin-top: 8px;
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 34px;
          color: #ffffff;
          line-height: 40px;
        }
        .quota-number.apply {
          font-size: 24px;
        }
        .label2 {
          margin-top: 15px;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
        }
        .btn {
          position: absolute;
          right: 20px;
          bottom: 26px;
          background: #ffffff;
          border-radius: 7px;
          width: 100px;
          height: 34px;
          font-weight: 600;
          font-size: 15px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .btn.CONSUME {
          color: #fe463e;
        }
        .btn.CASH {
          color: #4671eb;
        }
      }
      .tab-order {
        display: flex;
        justify-content: space-between;
        padding: 10px 25px 15px 21px;
        .btn {
          width: 90px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #fee1a7;
          border-radius: 4px;
          border: 1px solid rgba(255, 188, 94, 0.92);
          font-size: 12px;
          color: #8b5a15;
          margin-top: 6px;
        }
        .next {
          font-size: 12px;
          color: #666666;
          display: flex;
          align-items: center;
          margin-top: 5px;
          .text {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 17px;
            margin-right: 14px;
          }
          .number {
            font-weight: 600;
            font-size: 24px;
            color: #ff4040;
            display: flex;
            align-items: center;
          }
          .unit {
            font-size: 16px;
            color: #ff4040;
          }
        }
      }
    }
  }
}
</style>