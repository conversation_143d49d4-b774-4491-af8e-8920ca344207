<template>
  <div class="my-quota-detail-page">
    <navigation-bar pageName="额度管理" @onLeftClick="onBackClick"></navigation-bar>
    <div class="my-quota-detail-page-context">
      <div class="list">
        <div class="item" v-if="quotaData.quota">
          <div class="title">轻享花分期</div>
          <div class="total-quota">
            <div class="text">总额度(元)</div>
            <div class="quota-amount">{{ formatMoney(quotaData.quota.totalAmount, 0) }}</div>
            <div class="ultima-amount">总剩余可用额度 <span class="red">{{formatMoney(quotaData.quota.ultimaAmount, 0)}}</span></div>
          </div>
          <div class="group-amount">
            <div class="sub-title">额度组成明细</div>
            <div class="sub-amount">商城-消费授信额度：<span class="price"> {{ formatMoney(quotaData.quota.productConsum, 0) }}</span></div>
            <div class="sub-amount">商城-现金授信额度：<span class="price"> {{ formatMoney(quotaData.quota.productEncash, 0) }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { creditQuota } from '@/api/customer'
  const { proxy } = getCurrentInstance()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const store = useStore()
  const data = reactive({
    quotaData: {}
  })
  const { quotaData } = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(() => {
    creditQuota({ requestType: 'query' }).then(res => {
      quotaData.value = res.data
    })
  })
</script>

<style lang="scss" scoped>
  .my-quota-detail-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      .list{
        padding: 10px;
        .item{
          .title{
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000000;
            line-height: 21px;
            padding: 14px 0;
          }
          .total-quota{
            background: #FFFFFF;
            border-radius: 8px;
            padding: 22px 15px;
            .text{
              font-size: 14px;
              font-family: PingFang-SC-Bold, PingFang-SC;
              font-weight: bold;
              color: #222222;
              line-height: 20px;
            }
          }
          .quota-amount{
            font-size: 35px;
            font-family: SFPro-Medium, SFPro;
            font-weight: 500;
            color: #000000;
            line-height: 41px;
            margin-top: 7px;
          }
          .quota-amount.text{
            font-size: 30px;
          }
          .ultima-amount{
            margin-top:6px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #777777;
            line-height: 18px;
            .red {
              color: #E21C09;
            }
          }
          .group-amount{
            background: #FFFFFF;
            border-radius: 8px;
            padding: 18px 14px;
            margin-top: 10px;
            .sub-title{
              font-size: 15px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 21px;
            }
            .sub-amount{
              margin-top: 4px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #626262;
              line-height: 17px;
              .price{
                color: #E21C09;
              }
            }
          }
        }
      }
    }
  }
</style>
