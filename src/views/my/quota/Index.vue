<template>
  <scroll-nav-bar pageName="额度" :page-style="pageStyle" rgb-color="255,255,255">
    <quota-data :data="quotaList" @handleUsingLetters="handleUsingLetters"></quota-data>
    <func-group
      class="icon-group"
      :list="funcGroupList"
      :item-style="{
        margin: $px2rem('7px') + ' 0',
        flex:
          funcGroupList.length === 3
            ? '0 0 33%'
            : funcGroupList.length === 4
            ? '0 0 25%'
            : '0 0 20%',
      }"
      :text-style="{ 'margin-top': $px2rem('10px'), transform: 'none' }"
      background="none"
    />
    <div v-if="advertises[$global.AD_POSITION_MY_QUOTA1]" class="bannar-wrapper">
      <my-swiper :swiperDatas="advertises[$global.AD_POSITION_MY_QUOTA1]"></my-swiper>
    </div>
    <div v-if="advertises[$global.AD_POSITION_MY_QUOTA2]" class="bannar-wrapper">
      <my-swiper :swiperDatas="advertises[$global.AD_POSITION_MY_QUOTA2]"></my-swiper>
    </div>
    <div v-if="advertises[$global.AD_POSITION_MY_QUOTA3]" class="bannar-wrapper">
      <my-swiper :swiperDatas="advertises[$global.AD_POSITION_MY_QUOTA3]"></my-swiper>
    </div>
    <div style="height: 30px"></div>
    <authorize-popup ref="authorizePopupRef"></authorize-popup>
  </scroll-nav-bar>
</template>

<script setup>
import ScrollNavBar from '@/components/ScrollNavBar'
import { getFunGroup } from '@/api/home'
import { getAdList } from '@/api/base'
import QuotaData from './components/QuotaData'
import FuncGroup from '@/components/FuncGroup'
import AuthorizePopup from './components/AuthorizePopup'
import { getProductQuota, creditQuota, saveContacts } from '@/api/customer'
import { useGetPhoneInfo } from '@/hooks/useGetPhoneInfo'
import pageBg from '@/assets/images/quota/quota-bg.png'
import MySwiper from '@/components/MySwiper'
import { showToast } from 'vant'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const funcGroupList = ref([])
const quotaList = ref([])
const authorizePopupRef = ref(null)
const pageStyle = {
  background: `url(${pageBg}) no-repeat`,
  'background-size': `100% ${proxy.$px2rem('319px')}`,
  'background-position': '0 0',
}
const data = reactive({
  advertises: {},
})
const { advertises } = toRefs(data)
const submiCreditQuota = () => {
  creditQuota({ requestType: 'credit', sceneCode: proxy.$global.CREDIT_SCENE_CONSUME }).then(
    (res) => {
      productQuotaGet()
    }
  )
}
const authorizeGet = () => {
  proxy.appJS.appGetPhoneInfo()
}
const getPhoneInfo = (params) => {
  useGetPhoneInfo(params, () => {
    store.dispatch('GetInfo')
    console.log('通讯录提交成功')
  })
  submiCreditQuota()
}
const handleUsingLetters = async (item) => {
  if (item.productType === 'CASH') {
    // 备用金
    router.push('/cash-loan')
  } else {
    if (user.value.flow === proxy.$global.USER_FLOW_FINISH) {
      if (item.creditStatus === 'SUCC') {
        router.push('/installment')
      } else if (item.creditStatus === 'NON') {
        // 授信
        if (user.value.contactsFlag !== 'Y') {
          // 获取通讯录
          // authorizePopupRef.value.show = true
          const { data, isCanceled } = await authorizePopupRef.value.reveal()
          if (!isCanceled && data?.phoneInfo) {
            saveContacts({ contacts: data.phoneInfo }).then(() => {
              store.dispatch('GetInfo')
            })
          }
        }
        // 直接申请授信
        submiCreditQuota()
      } else {
        showToast('申请审核中！')
      }
    } else {
      const name =
        user.value.flow === proxy.$global.USER_FLOW_INFO
          ? proxy.$global.USER_FLOW_INFO_NAME
          : proxy.$global.USER_FLOW_IDCARD_NAME
      localStorage.setItem(proxy.$global.LOCAL_CREDIT_SCENE, proxy.$global.CREDIT_SCENE_CONSUME) // 实名授信通道
      router.push({ name, params: { backUrl: '/my/quota' } })
    }
  }
}
const productQuotaGet = () => {
  getProductQuota().then((res) => {
    if (res.data?.length > 0) {
      quotaList.value = res.data
    } else {
      quotaList.value = []
    }
  })
}
onMounted(() => {
  // window.getPhoneInfo = getPhoneInfo
  getFunGroup({ funcRegion: proxy.$global.ICON_FUN_MY_QUOTA }).then((res) => {
    funcGroupList.value = res.data
  })
  getAdList({
    regionType: [
      proxy.$global.AD_POSITION_MY_QUOTA1,
      proxy.$global.AD_POSITION_MY_QUOTA2,
      proxy.$global.AD_POSITION_MY_QUOTA3,
    ],
  }).then((res) => {
    advertises.value = res.data
  })
  if (user.value) {
    productQuotaGet()
  } else {
    proxy.appJS.appLogin()
  }
})
</script>

<style lang="scss" scoped>
.icon-group {
  margin-top: 24px;
}
.bannar-wrapper {
  margin: 10px 12px 0;
}
</style>
