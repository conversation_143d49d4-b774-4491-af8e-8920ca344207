<template>
  <div class="order-list-page">
    <navigation-bar pageName="贷款记录" @onLeftClick="onBackClick"></navigation-bar>
    <div class="order-list-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div v-if="orderList.length>0" class="list">
        <div v-for="item in orderList" :key="item.orderId" class="list-item">
          <div class="order-time">{{ parseTime(item.createTime, '{y}-{m}-{d}') }}<span class="margin-left-xs">优选贷定制产品</span></div>
          <div class="detail-list">
            <div
              v-for="(detail, index) in item.orderDetails"
              :key="index"
              :class="`detail-item ${(index + 1) < orderList.length ? 'solid-bottom' : ''}`"
            >
              <div class="detail-title">
                <div class="product-name">
                  <img class="product-icon" :src="detail.productIcon">
                  <span>{{detail.productName}}</span>
                </div>
                <div class="product-result">
                  {{ detail.status === 'Y' ? '推荐成功' : '推荐失败' }}
                </div>
              </div>
              <div v-if="detail.productType === 'FORM'" class="detail-content">
                <div class="amount">借{{ formatMoney(item.amount, 0)}}元</div>
                <div class="line"></div>
                <div class="period">{{item.period}}月</div>
              </div>
              <div v-else class="detail-content join">
                申请结果以产品方信息为准
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂无订单数据" />
      </div>
    </div>
    <div class="custom">
      <div class="tips">已根据最新数据更新定制方案，通过率97.9%</div>
      <router-link to="/order-apply">
        <div class="btn theme-text">继续定制</div>
      </router-link>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { listOrder } from '@/api/order'
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const productStatus = [
    {label: '处理中', value: 'WAIT'},
    {label: '申请中', value: 'APPLY'},
    {label: '推荐成功', value: 'SUCC'},
    {label: '推荐失败', value: 'FAIL'}
  ]
  const orderList = ref([]) 
  const data = reactive({
    queryParams: {
      status: '',
      beginTime: '',
      endTime: '',
      pageNum: '1',
      pageSize: '100'
    }
  })
  const { queryParams }  = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  const getList = () => {
    listOrder(queryParams.value).then(res => {
      if(res.rows) {
        orderList.value = res.rows
      }
    })
  }
  const formatStatus = (val) => {
    return productStatus.filter(item => item.value === val)[0].label
  }
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .order-list-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      background: #EFF3FA;
      padding: 15px;
      padding-bottom: 120px;
      .list{
        .list-item{
          .order-time{
            font-size: 13px;
            color: #363636;
          }
          .detail-list{
            margin-top: 10px;
            background: #FFFFFF;
            border-radius: 8px;
            .detail-item {
              padding: 17px;
              .detail-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                .product-name{
                  font-size: 13px;
                  display: flex;
                  align-items: center;
                }
                .product-icon{
                  width: 16px;
                  height: 16px;
                  margin-right: 5px;
                }
                .product-result{
                  font-size: 15px;
                  font-weight: 500;
                  font-family: PingFangSC-Medium, PingFang SC;
                  color: #868686;
                }
              }
              .detail-content{
                margin-top: 10px;
                display: flex;
                align-items: center;
                font-size: 18px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #363636;
                .line{
                  border-left: 1px solid #E0E2E3;
                  height: 14px;
                  margin-left: 7px;
                  margin-right: 6px;
                }
              }
              .detail-content.join{
                font-size: 12px;
              }
            }
          }
        }
        .list-item +.list-item{
          margin-top: 10px;
        }
      }
    }
    .custom{
        position: fixed;
        width: 100%;
        text-align: center;
        background: #ffffff;
        padding: 10px 0 23px 0;
        bottom: 0;
        .tips{
          font-size: 12px;
          color: #A8A8A8;
          margin-bottom: 10px;
        }
        .btn {
          width: 315px;
          margin: 0 auto;
          height: 49px;
          line-height: 49px;
          border-radius: 25px;
          color: #ffffff;
          font-size: 16px;
        }
      }
  }
</style>