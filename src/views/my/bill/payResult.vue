<template>
  <div class="order-finish-page">
    <navigation-bar pageName="提交结果" @onLeftClick="onBackClick"></navigation-bar>
    <div class="order-finish-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="result-wrapper">
        <img src="@/assets/images/cashier/pay-success.png">
        <div class="title">提交成功</div>
        <div class="tips">{{ productType === 'PAYLATER' ? '' : '还款' }}申请已提交，{{ productType === 'PAYLATER' ? '' : '还款' }}结果您可前往{{ productType === 'PAYLATER' ? '会员订单' : '查账还款' }}页面，查看账单最终状态</div>
        <div class="submit-btn" @click="backIndex">返回首页</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const route = useRoute()
  const { productType } = route.query
  const { proxy } = getCurrentInstance()
  const onBackClick = () => {
    router.go(-1)
  }
  // 返回首页
  const backIndex = () => {
    router.push({name: 'Main', params: { componentName: '/' }})
  }
</script>

<style lang="scss" scoped>
  .order-finish-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .result-wrapper{
        padding: 30px;
        text-align: center;
        background: #ffffff;
        border-radius: 12px;
        margin: 10px;
        img{
          width: 75px;
          height: 75px;
        }
        .title{
          font-size: 19px;
          font-weight: 500;
          color: #363636;
          margin-top: 13px;
        }
        .tips{
          font-size: 12px;
          font-weight: 400;
          color: #A8A8A8;
          line-height: 18px;
          margin-top: 5px;
        }
      }
      .submit-btn{
        margin: 0 48px;
        height: 40px;
        background: #ffffff;
        border-radius: 26px;
        border: 1px solid #FF3355;
        margin: 0 auto;
        margin-top: 23px;
        line-height: 40px;
        font-size: 18px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #FF3355;
      }
    }
  }
</style>