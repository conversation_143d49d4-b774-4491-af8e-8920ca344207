<template>
  <div class="bill-pay-page">
    <navigation-bar pageName="支付" @onLeftClick="onBackClick"></navigation-bar>
    <div class="bill-pay-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="pay-amount">
        <div class="amount">￥{{ billData.amount }}</div>
        <div class="text">支付金额（元）</div>
      </div>
      <div class="bank-list">
        <template v-if="(bankList.length > 0)">
          <div v-for="(item, index) in bankList" :key="index" class="bank-item" @click="checkBank(item)">
            <div :class="`bank-logo ${ bankCode(item.bankName) }`"></div>
            <div class="bank-info solid-bottom">
              <div class="bank-name">{{ item.bankName }}（{{ stringBankAccount(item.cardNoShort) }}）</div>
              <img class="radio" v-if="(item.id === currentBank)" src="@/assets/images/cashier/active.png">
              <img class="radio" v-else src="@/assets/images/cashier/inactive.png">
            </div>
          </div>
        </template>
        <div class="add" @click="toBindCardBill">
          <img class="add-icon" src="@/assets/images/cashier/bank.png">
          <div class="text">
            使用新卡{{ productType === 'PAYLATER' ? '支付': '还款' }}
            <van-icon size="12" color="#cccccc" name="arrow" />
          </div>
        </div>
      </div>
      <div class="pay-fixed" :class="{ 'iphonex-bottom': isIphoneX }">
        <div class="confirm-btn theme-linear-gradient" @click="show=true">确定</div>
      </div>
    </div>
    <van-popup round v-model:show="show">
      <div class="popup-container">
        <div class="title">支付确认</div>
        <div class="content">
          您即将完成金额扣除用于{{ productType === 'PAYLATER' ? '支付' : '账单还款' }}
请确保绑定的银行卡余额充足，操作无误
        </div>
        <div class="flex justify-between">
          <div class="btn cacel" @click="show=false">放弃</div>
          <div class="btn theme-linear-gradient" @click="onConfirmPay">继续支付</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { AutomaticRepayment } from '@/api/bill'
  import { bankCardList } from '@/api/bankcard'
  import { stringBankAccount } from '@/utils/common'
  import { productInfo } from '@/api/product'
  import { bankCode } from '@/constant/bank'
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  const { productType } = route.query
  const bankList = ref([])
  const currentBank = ref(0)
  const show = ref(false)
  const data = reactive({
    billData: {}
  })
  const { billData }  = toRefs(data)
  onMounted(async () => {
    billData.value = JSON.parse(localStorage.getItem('billPay'))
    if((!billData.value.repayChnlId || billData.value.repayChnlId === 'NON') && billData.value.productId) {
      const productData = await productInfo({ id: billData.value.productId })
      billData.value.repayChnlId = productData.data.bindChnlId
    }
    getBankList()
  })
  const getBankList = () => {
    bankCardList({pageNum:1, pageSize:100, channelId: billData.value.repayChnlId, 
      orderId: billData.value.billId,
      cmId: billData.value.cmId,
      bankCardType: proxy.$global.PAY_TYPE_QUICKPAY}).then(res=> {
      if(res.data.length>0) {
        if(!currentBank.value) {
          currentBank.value = res.data[0].id
        }
        bankList.value = res.data
      }
    })
  }
  // 绑卡
  const toBindCardBill = () => {
    router.push({path: '/my/bankcard/add', 
    query: { channelId: billData.value.repayChnlId, orderId: billData.value.billId,
      cmId: billData.value.cmId,
      type: proxy.$global.PAY_TYPE_QUICKPAY, returnUrl: '/my/bill/pay' }})
  }
  const checkBank = (item) => {
    currentBank.value = item.id
  }
  const onConfirmPay = () => {
    if (currentBank.value) {
      billData.value.bankCardId = currentBank.value
      billData.value.repayType = 'PLAN'
      AutomaticRepayment(billData.value).then(res => {
        router.replace({path: '/my/bill/pay-result', query: { productType }})
      })
    } else {
      toBindCardBill()
    }
  }
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .bill-pay-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      background: #F5F5F5;
      .pay-amount{
        margin: 10px;
        background: #ffffff;
        border-radius: 4px;
        padding: 20px 0;
        .amount{
          font-size: 28px;
          font-weight: bold;
          color: #E9362E;
          line-height: 21px;
          text-align: center;
        }
        .text{
          margin-top: 3px;
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          color: #666666;
        }
        .amount::first-letter{
          font-size: 40%;
        }
      }
      .bank-list{
        margin: 10px;
        margin-top: 0;
        background: #ffffff;
        border-radius: 4px;
        .bank-item{
          display: flex;
          align-items: center;
          padding: 0 15px;
          .bank-logo{
            width: 24px;
            height: 24px;
            display: block;
            margin-right: 12px;
          }
          .bank-info{
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 17px 0;
            .bank-name{
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 22px;
            }
          }
        }
        .add{
          display: flex;
          align-items: center;
          color: #999999;
          padding: 0 15px;
          .add-icon{
            width: 24px;
            height: 24px;
            margin-right: 14px;
          }
          .text{
            flex: 1;
            padding: 13px 0;
            font-size: 13px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            font-weight: 500;
            color: #363636;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
      }
    }
  }
  .pay-fixed {
    position: fixed;
    bottom: 0;
    width: 100%;
    .confirm-btn{
      height: 45px;
      border-radius: 22px;
      line-height: 45px;
      text-align: center;
      font-size: 18px;
      color: #FFFFFF;
      margin: 0 15px 44px;
    }
  }
  .popup-container{
    width: 195px;
    padding: 20px;
    .title{
      font-size: 17px;
      font-weight: bold;
      color: #363636;
      text-align: center;
      margin-top: 3px;
    }
    .content{
      margin-top: 11px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 18px;
    }
    .cacel.btn{
      background: #EEEEEE;
      font-size: 14px;
      color: #333333;
    }
    .btn{
      width: 90px;
      margin-top: 20px;
      height: 36px;
      border-radius: 18px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 36px;
    }
  }
</style>