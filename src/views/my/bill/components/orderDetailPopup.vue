<template>
  <van-popup class="adress-popup" v-model:show="show" round position="bottom">
    <img class="close-img" @click="show = false" src="@/assets/images/goods/colse.png" />
    <div class="title">订单详情</div>
    <div class="info-list">
      <div class="goods-info">
        <div class="goods-img">
          <img :src="orderDetailInfo?.goodsList[0]?.thumbPics" />
        </div>
        <div class="goods-desc">
          <div class="goods-name">{{ orderDetailInfo?.goodsList[0]?.spuTitle }}</div>
          <div class="goods-spec">已选择{{ orderDetailInfo?.goodsList[0]?.spec }}</div>
          <div class="goods-quantity">x{{ orderDetailInfo?.goodsList[0]?.quantity }}</div>
        </div>
      </div>
      <div class="item-list">
        <div class="item">
          <div class="label">订单编号</div>
          <div class="value">{{ orderDetailInfo?.orderId }}</div>
        </div>
        <div class="item">
          <div class="label">分期本金</div>
          <div class="value">￥{{ orderDetailInfo?.principal }}</div>
        </div>
        <div class="item">
          <div class="label">分期利息</div>
          <div class="value">￥{{ orderDetailInfo?.actualInterest }}</div>
        </div>
        <div class="item">
          <div class="label">创建时间</div>
          <div class="value">{{ orderDetailInfo?.createTime }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="footer">
      <div class="btn theme-linear-gradient" @click="chooseOther">选择其他收货地址</div>
    </div> -->
  </van-popup>
</template>

<script setup>
const isIphoneX = window.isIphoneX
const router = useRouter()
const show = ref(false)
const addressList = ref([])
const props = defineProps({
  orderDetailInfo: {
    type: Object,
    defalut: () => {},
  },
})
const emit = defineEmits(['updateAddress'])
const handleAddress = (item) => {
  show.value = false
  emit('updateAddress', item)
}
const chooseOther = () => {
  router.push({ path: '/my/address', query: { type: '2', goodsId: props.goodsId } })
}
const open = () => {
  show.value = true
}
defineExpose({ show, open })
</script>

<style lang="scss" scoped>
.adress-popup {
  display: flex;
  flex-direction: column;
  padding-bottom: 40px;
}
.title {
  height: 55px;
  line-height: 55px;
  text-align: center;
  border-bottom: 1px solid #ededee;
  font-size: 16px;
  font-family: PingFang-SC-Bold, PingFang-SC;
  font-weight: bold;
  color: #222222;
}
.info-list {
  .goods-info{
    display: flex;
    padding: 16px;
    .goods-img{
      width: 63px;
      height: 63px;
      border-radius: 15px;
      overflow: hidden;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .goods-desc{
      flex: 1;
      padding-left: 16px;
      .goods-name{
        font-size: 15px;
        font-weight: 500;
        color: #333;
        overflow: hidden;
        line-height: 21px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .goods-spec{
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 18px;
      }
      .goods-quantity{
        color: #000;
        font-size: 14px;
        font-weight: 400;
        text-align: right;
      }
    }
  }
}
.item-list{
  padding: 0 16px;
  .item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 21px;
    // border-bottom: 1px solid #ededee;
    color: #000;
    font-size: 15px;
    font-weight: 400;
    margin-top: 13px;
  }

}
// .iphonex-bottom {
//   padding-bottom: 44px !important;
//   padding-bottom: var(--safe-area-inset-bottom);
// }
.close-img {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 16px;
  right: 16px;
}
.footer {
  background: #ffffff;
  padding: 7px 16px;
  position: sticky;
  bottom: 0;
  width: calc(100% - 32px);
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
  .btn {
    border-radius: 7px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
  }
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
}
</style>
