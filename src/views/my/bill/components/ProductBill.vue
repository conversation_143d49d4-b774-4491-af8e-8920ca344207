<template>
  <div>
    <div class="product-type">
      <div class="line theme-bg"></div>
      <div class="product-name">{{ name }}</div>
    </div>
    <div class="product-bill">
      <template v-if="list.length>0">
        <div v-for="item in list" :key="item.billId" class="product-bill-item">
          <div class="item-info">
            <div class="item-label">剩余待还</div>
            <div class="item-amount">￥{{ item.repayAmount }}</div>
            <div class="item-date">最近还款日期：{{ item.repayDay }}</div>
          </div>
          <div class="item-btn theme-linear-gradient" @click="goRepayment(item)">立即还款</div>
        </div>
      </template>
      <div v-else class="empty">
        暂无还款记录
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  list: {
    type: Object,
    default: () => []
  },
  name: {
    type: String,
    default: ''
  }
})
const goRepayment = (item) => {
  router.push({ path: '/my/bill/repayment', query: { billId: item.billId } })
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .product-type{
    display: flex;
    align-items: center;
    padding: 14px 0 0 14px;
    .line{
      width: 3px;
      height: 14px;
      border-radius: 2px;
      margin-right: 4px;
    }
    .product-name{
      font-size: 16px;
      font-weight: 500;
      color: #363636;
    }
  }
  .product-bill-item{
    background: #ffffff;
    display: flex;
    padding: 24px 12px;
    justify-content: space-between;
    align-items: center;
    margin: 0 15px;
    margin-top: 12px;
    border-radius: 8px;
    .item-label{
      font-size: 13px;
      font-weight: 500;
      color: #363636;
      line-height: 18px;
    }
    .item-amount{
      font-size: 25px;
      font-weight: 500;
      color: #363636;
      line-height: 36px;
    }
    .item-amount::first-letter{
      font-size: 72%;
    }
    .item-date{
      font-size: 12px;
      color: #A8A8A8;
      line-height: 17px;
    }
    .item-btn{
      width: 100px;
      height: 36px;
      border-radius: 18px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 36px;
      text-align: center;
    }
  }
  .empty{
    margin: 17px 15px;
    background: #FFFFFF;
    border-radius: 6px;
    height: 118px;
    line-height: 118px;
    font-size: 16px;
    color: #999999;
    text-align: center;
  }
</style>