<template>
  <div class="my-bill-page">
    <navigation-bar pageName="我的账单" @onLeftClick="onBackClick"></navigation-bar>
    <div class="my-bill-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <product-bill
        :list="billData.CASH" name="极享金">
      </product-bill>
      <product-bill 
        :list="billData.CONSUME" 
        name="极刻花"
      ></product-bill>
      <!-- <product-bill 
        :list="billData.PAYLATER" name="先享后付">
      </product-bill> -->
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { billList } from '@/api/bill'
  import ProductBill from './components/ProductBill'
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const store = useStore()
  const activeName = ref('CONSUME')
  const data = reactive({
    billData: {}
  })
  const { billData }  = toRefs(data)
  onMounted(() => {
    getList()
  })
  const getList = () => {
    billList().then(res => {
      billData.value = res.data
    })
  }
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .my-bill-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      background: #F5F5F5;
      :deep(.van-tab__text--ellipsis){
        overflow: inherit;
      }
      :deep(.van-badge--top-right) {
        right: -5px;
      }
    }
  }
</style>