<template>
  <div class="my-app-page">
    <navigation-bar pageName="关于我们" @onLeftClick="onBackClick"></navigation-bar>
    <div class="my-app-page-content">
      <img src="@/assets/images/about-us.png" />
      <div class="version">{{ appVersion }}</div>
      <!-- <div class="">{{ count }}</div>
      <div class="">{{ devMode }}</div> -->
      <!-- <van-radio-group
        v-if="dev.enable"
        v-model="appChannel"
        direction="horizontal"
        class="app-channel"
      >
        <van-radio name="VERIFY">审核版</van-radio>
        <van-radio name="PROD">正式版</van-radio>
      </van-radio-group> -->
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
const { proxy } = getCurrentInstance()
const router = useRouter()
const _appVersion = ref('')
import appJs from '@/utils/appJS'
import { appVersion as appChannel } from '@/utils/auth'
import { useAsyncState, useCounter } from '@vueuse/core'
// import { devMode } from '@/store/devMode'
import { dev } from '@/plugins/dev-tools.js'

const { MODE } = import.meta.env

const { state: appVersion } = useAsyncState(() => proxy.appJS.appVersion(), '', {})
const onBackClick = () => {
  router.go(-1)
}

const { count, inc, dec, set, reset } = useCounter(0, { min: 0, max: 5 })

function openDevMode() {
  inc()
  if (count.value === 5) {
    dev.toggleEnable()
    reset()
  }
}

onMounted(() => {
  // window.appVersion = appVersion
})
// const appVersion = (data) => {
//   // _appVersion.value = data
// }
</script>

<style lang="scss" scoped>
.my-app-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  font-size: 14px;
  &-content {
    flex-grow: 1;
    overflow: hidden;
    text-align: center;
    img {
      margin-top: 23px;
      width: 60px;
      height: 60px;
      border-radius: 8px;
    }
    .version {
      margin-top: 9px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222220;
      line-height: 18px;
    }
  }
}
.app-channel {
  justify-content: center;
}
</style>
