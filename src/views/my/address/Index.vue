<template>
  <div class="my-address-page">
    <navigation-bar
      @onLeftClick="onBackClick"
      pageName="管理收货地址"
      :navBarStyle="{ fontWeight: 600, backgroundColor: '#f9f9f9' }"
    />
    <div class="my-address-page-context">
      <div class="list">
        <div class="item" v-for="(item, index) in addressList" :key="index">
          <div class="info" @click="handleChoose(item)">
            <div class="name-phone">
              <div class="name">{{ item.contactName }}</div>
              <div class="phone">{{ item.contactTel }}</div>
            </div>
            <div class="content">
              {{ item.province + item.city + item.district + item.address }}
            </div>
          </div>
          <div class="action">
            <div class="action-left" @click="handleDefault(item)">
              <img v-if="item.isDefault === 'Y'" src="@/assets/images/cashier/active.png" />
              <img v-else src="@/assets/images/cashier/inactive.png" />
              <span>默认地址</span>
            </div>
            <div class="action-right">
              <div class="btn" @click="handleUpdate(item)">编辑</div>
              <div class="btn" @click="handleRemove(item)">删除</div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <div class="btn theme-linear-gradient" @click="handleUpdate()">
          <van-icon name="plus" />
          新增收货地址</div>
      </div>
    </div>
    <van-popup round v-model:show="show">
      <div class="warning-popup">
        <div class="title">确定删除收货地址？</div>
        <div class="content">
          <div class="btn cancel text-black" @click="show = false">取消</div>
          <div class="btn theme-linear-gradient text-white" @click="submitDelete">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { listAddress, updateDefault, deleteAddress } from '@/api/address'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const addressList = ref([])
const type = route.query.type
const show = ref(false)
const deleId = ref(null)
const onBackClick = () => {
  router.go(-1)
}
// 更新默认
const handleDefault = (item) => {
  if (item.isDefault !== 'Y') {
    updateDefault({ id: item.id }).then((res) => {
      proxy.onToastSucc(() => {
        getList()
      }, '更新成功！')
    })
  }
}
// 删除地址
const handleRemove = (item) => {
  deleId.value = item.id
  show.value = true
}
const submitDelete = () => {
  deleteAddress({ id: deleId.value }).then((res) => {
    show.value = false
    console.log('删除地址', deleId.value)
    proxy.onToastSucc(() => {
      getList()
    }, '删除成功！')
  })
}
// 选择
const handleChoose = (item) => {
  if (type === '1') {
    // 确认订单页面选取地址返回
    // router.replace({name: 'OrderConfirm' , params: {select: 11111111}}) 无法携带参数
    sessionStorage.setItem(proxy.$global.ADDRESS_CHECKED, JSON.stringify(item))
    router.replace({ name: 'OrderConfirm' })
    router.go(-1) // 解决需要点二次返回
  } else if (type === '2') {
    // 返回商品详情页面
    sessionStorage.setItem(proxy.$global.GOODS_DETAIL_ADDRESS, JSON.stringify(item))
    router.push({ path: '/goods-detail', query: { goodsId: route.query.goodsId }, replace: true })
    router.go(-1) // 解决需要点二次返回
  }
}
// 新增编辑
const handleUpdate = (item) => {
  sessionStorage.setItem('address-form', item ? JSON.stringify(item) : '')
  router.push('/my/address-form')
}
onMounted(() => {
  getList()
})
const getList = () => {
  listAddress({ pageNum: 1, pageSize: 100 }).then((res) => {
    const arr = Array.isArray(res.data) ? res.data : []
    addressList.value = arr
    // 如果没有找到全局地址，则删除全局地址
    const globalAddress = sessionStorage.getItem(proxy.$global.GOODS_DETAIL_ADDRESS)
    if (globalAddress && !arr.find((t) => t.id === globalAddress.id)) {
      sessionStorage.removeItem(proxy.$global.GOODS_DETAIL_ADDRESS)
    }
  })
}
</script>

<style lang="scss" scoped>
.my-address-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border-bottom: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    // min-height: 100%;
    flex: 1;
    min-height: 0;
    .list {
      flex: 1 0 0;
      // min-height: 0;
      // height: max-content;
      // background-color: red;
      .item {
        margin: 10px;
        background: #ffffff;
        border-radius: 8px;
        .info {
          padding: 16px;
          padding-bottom: 9px;
          border-bottom: 1px solid #ededee;
          .name-phone {
            display: flex;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            .name {
              margin-right: 20px;
            }
          }
          .content {
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 22px;
            margin-top: 6px;
          }
        }
        .action {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 9px 16px 14px;
          &-left {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
            color: #333;
            img {
              width: 18px;
              height: 18px;
              margin-right: 7px;
            }
          }
          &-right {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #333;
            .btn {
              margin-left: 20px;
            }
          }
        }
      }
    }
    .footer {
      background: #ffffff;
      padding: 7px 16px;
      // position: fixed;
      position: sticky;
      bottom: 0;
      width: calc(100% - 32px);
      padding-bottom: calc(7px + var(--safe-area-inset-bottom));
      .btn {
        border-radius: 7px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 14px;
        color: #ffffff;
      }
    }
    .footer.iphonex-bottom {
      // padding-bottom: 51px !important;
      // padding-bottom: calc(7px + var(--safe-area-inset-bottom));
    }
  }
}
.warning-popup {
  width: 235px;
  background: #ffffff;
  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #333;
    line-height: 22px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;
    .btn {
      width: 90px;
      height: 32px;
      border-radius: 16px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
    }
    .btn.cancel {
      background: #eeeeee;
    }
  }
}
</style>
