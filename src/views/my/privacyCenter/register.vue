<template>
  <div class="privacy-register-page">
    <navigation-bar pageName="用户注册协议" @onLeftClick="onBackClick"></navigation-bar>
    <div class="privacy-register-page-content">
      <iframe class="external-links" src="/register.htm" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .privacy-register-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
    }
  }
</style>