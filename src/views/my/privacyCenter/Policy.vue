<template>
  <div class="privacy-policy-page">
    <navigation-bar pageName="隐私政策" @onLeftClick="onBackClick"></navigation-bar>
    <div class="privacy-policy-page-content">
      <iframe class="external-links" src="/privacy.htm" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .privacy-policy-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
    }
  }
</style>