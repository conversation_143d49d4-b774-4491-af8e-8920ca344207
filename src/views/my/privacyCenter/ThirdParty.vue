<template>
  <div class="privacy-sdk-page">
    <navigation-bar pageName="第三方共享及SDK清单" @onLeftClick="onBackClick"></navigation-bar>
    <div class="privacy-sdk-page-content">
      <iframe class="external-links" src="/agreement/service-sdk.htm" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .privacy-sdk-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      background: #ffffff;
    }
  }
</style>