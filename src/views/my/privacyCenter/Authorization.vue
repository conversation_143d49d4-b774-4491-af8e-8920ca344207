<template>
  <div class="privacy-auth-page">
    <navigation-bar pageName="产品服务协议" @onLeftClick="onBackClick"></navigation-bar>
    <div class="privacy-auth-page-content">
      <iframe class="external-links" src="/agreement/product-service.htm" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .privacy-auth-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      background: #ffffff;
    }
  }
</style>