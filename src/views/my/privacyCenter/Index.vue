<template>
  <div class="privacy-page">
    <navigation-bar pageName="隐私中心" @onLeftClick="onBackClick"></navigation-bar>
    <div class="privacy-page-content">
      <div class="fun-wrapper">
        <router-link to="/my/privacy/policy">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">隐私政策</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/my/privacy/register">
          <div class="my-cell">
            <div class="cell-content solid-bottom">
              <div class="cell-title">用户注册协议</div>
              <div class="cell-value">
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <!-- <router-link to="/my/privacy/third-party">
        <div class="my-cell">
          <div class="cell-content solid-bottom">
            <div class="cell-title">第三方共享及SDK清单</div>
            <div class="cell-value">
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
        </router-link>
        <router-link to="/my/privacy/auth">
        <div class="my-cell">
          <div class="cell-content solid-bottom">
            <div class="cell-title">用户授权协议</div>
            <div class="cell-value">
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
        </router-link> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const router = useRouter()
const store = useStore()
const onBackClick = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.privacy-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border: none;
  }
  &-content {
    flex-grow: 1;
    overflow: hidden;
  }
}
</style>