<template>
  <div class="my-page" @scroll="onScrollChange" ref="myRef">
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="theme-text">
      <div class="my-page-header">
        <UserInfoV2 v-model="user" @handleSetting="handleSetting" />
        <!-- <UserDataV2
          :integral="integralValue"
          :vip-big="vipBig"
          :coupon-total="couponTotal"
        ></UserDataV2> -->
        <!-- <VipTip /> -->
        <MyMoneyCard :order-data="orderData" ref="myMoneyCard" />
        <MyOrderV2 :order-data="orderData" />
      </div>
      <!-- <div
        v-if="user?.phone && user.creditIntentionFlag === 'Y'"
        :class="`${swiperDatas.length >= 2 ? 'bannar-wrapper-2' : 'bannar-wrapper'}`"
      >
        <img
          v-for="(swiperData, index) of swiperDatas"
          :key="index"
          :src="swiperData.pic"
          class="bannar-img"
          @click="$adRouter(swiperData)"
        />
      </div> -->
      <!-- <UserSettingV2 v-model="user" /> -->
      <GuessYouLike />
      <GoodsList ref="goodsListRef" :listType="listType" />
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { getAdList, getDictData } from '@/api/base'
import { orderState } from '@/api/goods'
import { getMemberInfo } from '@/api/member'
import { couponsNumber } from '@/api/market'
// import MySwiper from '@/components/MySwiper'
import UserInfoV2 from './components/UserInfoV2'
import VipTip from './components/VipTip'
import UserDataV2 from './components/UserDataV2'
// import MyOrder from './components/MyOrder'
import UserSettingV2 from './components/UserSettingV2'
import MyOrderV2 from './components/MyOrderV2'
import MyMoneyCard from '@/components/MyMoneyCard/index.vue'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
import GuessYouLike from '@/components/GoodsList/GuessYouLike.vue'
import { useTemplateRef } from 'vue'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const integralValue = computed(() => store.getters.integralValue)
const swiperDatas = ref([])
const loading = ref(false)
const myRef = ref(null)
const scrollTopValue = ref(-1)
const ANCHOR_SCROLL_TOP = 64
const vipBig = ref(false)
const couponTotal = ref(0)
const testWhiteList = ref([])
const myMoneyCard = useTemplateRef('myMoneyCard')
const data = reactive({
  navBarStyle: {
    backgroundColor: '',
    position: 'fixed',
  },
  orderData: {
    PAYING: 0,
    WAIT_DELIVER: 0,
    WAIT_RECEIVING: 0,
  },
})
const goodsListRef = ref()
const listType = { type: 'region', value: proxy.$global.GOODS_REGION_FOLLOW }
const { navBarStyle, orderData } = toRefs(data)
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP
  navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
}
onActivated(() => {
  // 修复登出时，orderData数据未被重置导致徽标存在的问题
  if (!user.value) {
    orderData.value = {
      PAYING: 0,
      WAIT_DELIVER: 0,
      WAIT_RECEIVING: 0,
    }
  }
})
const initData = async () => {
  await getAdList({ regionType: [proxy.$global.AD_POSITION_MY] }).then((res) => {
    if (res.data && JSON.stringify(res.data) !== '{}') {
      swiperDatas.value = res.data[proxy.$global.AD_POSITION_MY]
    }
    // 测试广告图片样式
    // swiperDatas.value = [{
    //   pic: 'https://yunjishop-public-test.oss-cn-shenzhen.aliyuncs.com/yunjishop-public-test/bc6c5cf804874a879ec390bbde147f6e1715149371294.png',
    //   url: '/my/quota',
    // }]
    // swiperDatas.value = [{
    //   pic: 'https://yunjishop-public-test.oss-cn-shenzhen.aliyuncs.com/yunjishop-public-test/046c7c1cd00948edbb58fb1239fdb02f1743586391778.png',
    //   url: '/cash-loan-pre',
    // }, {
    //   pic: 'https://yunjishop-public-test.oss-cn-shenzhen.aliyuncs.com/yunjishop-public-test/29db943b05b64202b2e5e5e55c792c301743586321246.png',
    //   url: '/installment',
    // }]
  })
  if (user.value) {
    store.dispatch('IntegralData') // 获取积分
    // 大卡会员标识
    const memberRess = await getMemberInfo({ vipType: 'VIP' })
    if (memberRess.data.length > 0) {
      memberRess.data.map((item) => {
        if (item.vipCust) {
          vipBig.value = true
        }
      })
    }
    await orderState().then((res) => {
      if (res.data) {
        orderData.value['PAYING'] = res.data.PAYING || 0
        orderData.value['WAIT_DELIVER'] = res.data.WAIT_DELIVER || 0
        orderData.value['WAIT_RECEIVING'] = res.data.WAIT_RECEIVING || 0
        orderData.value['VERIFY'] = res.data.VERIFY || 0
        orderData.value['WAIT_DELIVER'] = res.data.WAIT_DELIVER || 0
      } else {
        orderData.value = {
          PAYING: 0,
          WAIT_DELIVER: 0,
          WAIT_RECEIVING: 0,
        }
      }
    })
    couponsNumber().then((res) => {
      // 计算优惠券
      const coupons = res.data
      if (res.data?.length > 0) {
        coupons.map((item) => {
          if (item.state === 'UNUSED') {
            couponTotal.value = item.quantity
          }
        })
      }
    })
  }
  getDictData({ dictType: 'h5_test_white' }).then((res) => {
    let whiteList = []
    if (res.data?.length > 0) {
      res.data.map((item) => {
        whiteList.push(item.dictValue)
      })
    }
    testWhiteList.value = whiteList
  })
}
watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initData()
      }
    }
  }
)
onMounted(() => {
  initData()
})
onActivated(() => {
  if (user.value) {
    // 更新客户信息
    store.dispatch('GetInfo')
  }
})
// 判断登录
const loginValid = (callback) => {
  if (user.value) {
    callback()
  } else {
    proxy.appJS.appLogin()
  }
}
const handleSetting = () => {
  loginValid(() => {
    router.push('/system-setting')
  })
}
const onRefresh = async () => {
  await initData()

  myMoneyCard.value.refresh()
  // 刷新猜你喜欢列表
  if (goodsListRef.value) {
    goodsListRef.value.reload()
  }
  loading.value = false
}
</script>

<style lang="scss" scoped>
.my-page {
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  z-index: 99;
  background: #f4f6fa;

  &-header {
    // background: url('@/assets/images/my/my-bg.png') no-repeat;
    background: linear-gradient(
      175deg,
      #5581f6 20%,
      #aabefb 50%,
      #d6dffa 65%,
      #e8edfa 75%,
      #f4f6fa 100%
    );
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;

    .header-icon {
      position: fixed;
      top: 0;
      width: 100%;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      // 适配手机 stateBar
      padding-top: 25px;
      z-index: 999;

      .title {
        font-size: 18px;
        color: #101010;
      }
    }
  }

  .bannar-wrapper {
    margin: 15px 12px 0;

    .bannar-img {
      width: 100%;
      height: auto;
    }
  }

  .bannar-wrapper-2 {
    margin: 15px 12px 0;

    .bannar-img {
      width: 100%;
      height: auto;
    }

    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}
</style>
