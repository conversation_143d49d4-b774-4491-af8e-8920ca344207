<template>
  <div class="unpaid-bills-page">
    <navigation-bar
      pageName="我的账单 "
      :navBarStyle="{ backgroundColor: '#fff', color: '#333', fontSize: '16px', fontWeight: 600 }"
      @onLeftClick="onBackClick"
    ></navigation-bar>
    <div class="nav-list">
      <div
        :class="`nav-item ${item.value === queryParams.status ? 'active' : ''}`"
        v-for="(item, index) in navList"
        :key="index"
        @click="handleNav(item)"
      >
        {{ item.label }}{{ item.count > 0 ? item.count : ''}}
      </div>
    </div>
    <div class="unpaid-bills-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-pull-refresh
        v-if="list.length > 0"
        v-model="refreshing"
        class="list"
        @refresh="onRefresh"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="——没有更多了——"
          @load="getList"
          :immediate-check="false"
        >
        <!-- 账单列表 -->
          <div class="item" v-for="(item, index) in groupedBills" :key="index" >
            <div class="day">{{item.date}}</div>
            <div class="content_list">
              <div class="content" v-for="(billItem, billIndex) in item.list" :key="billIndex" @click="goRepayment(billItem)">
                <img :src="billItem.spuIcon" class="goods-img" />
                <div class="content-c">
                  <div class="title">{{billItem.spuName}}</div>
                   <div class="period">第{{billItem.billPlan.term}}期</div>
                </div>
                <div class="content-r">
                  <div class="content-r-box">
                    <div class="price">￥{{billItem.status=='SETTLE'?billItem.billPlan.actualPrincipalInterest:billItem.billPlan.repayAmount}}</div>
                    <div class="status red"  v-if="billItem.status=='OVERDUE'">已逾期</div>
                    <div class="status"  v-else-if="billItem.status=='WAIT'">待支付</div>
                    <div class="status" v-else-if="billItem.status=='SETTLE'"><img src="@/assets/images/my/unpaidbill-yzf.png" alt=""></div>
                  </div>
                  <!-- <van-icon style="color: #999DAD;width: 5px;height: 8.5px;margin-right: 28px;" size="18" name="arrow" /> -->
                  <img src="@/assets/images/my/right-icon.png" class="ringht-img"  alt="">
                </div>
              </div>

            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else>
        <van-empty :description="loading ? '加载中..' : '暂无数据'" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { listGoodsOrder } from '@/api/goods-order'
import { billList,billsTypeCount} from '@/api/bill'
import { showDialog } from 'vant'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const store = useStore()
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const navList = [
  { label: '全部', value: '',count: 0 },
  { label: '待支付', value: 'WAIT' ,count: 0 },
  { label: '已守约', value: 'SETTLE' ,count: 0 },
  { label: '已逾期', value: 'OVERDUE',count: 0  },
]
const data = reactive({
  queryParams: {
    pageNum: '1',
    pageSize: '10',
    status: '',
  },
})
const { queryParams } = toRefs(data)
// 用来修复切换分页后，数据不正确的问题，因为加载中时没有显示切换分类
// 且数据返回时间不一致，每次请求后id + 1，id大于最后请求的才能更新数据
const lastGetListRequestId = ref(0)
const state = ref('')

// 获取分类数量
const getBillsTypeCount =async () => {
  try {
    const {data} = await billsTypeCount()
    console.log("🚀 ~ getBillsTypeCount ~ data:", data)
    navList[0].count = data.allCount
    navList[1].count = data.waitCount
    navList[2].count = data.settleCount
    navList[3].count = data.overdueCount
  } catch (error) {
    console.log("🚀 ~ getBillsTypeCount ~ error:", error)
  }
}


// 响应式状态
const groupedBills = ref([])        // 分组后的账单
const dateMap = ref(new Map())      // 用于快速查找日期分组的映射表

const onBackClick = () => {
  router.go(-1)
}

const goRepayment = (item) => {
  router.push({ path: '/my/bill/repayment', query: {
    billId: item.billId,
    status: queryParams.value.status,
  } })
}
const handleNav = async (item) => {
  state.value = item.value
  queryParams.value.status = item.value
  loading.value = true
  resetQuery()
}
// 重置查询
const resetQuery = () => {
  queryParams.value.pageNum = 1
  list.value = []
  groupedBills.value = []
  dateMap.value = new Map()
  getList()
}
// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
}
const getList = () => {
  const requestId = lastGetListRequestId.value + 1
  lastGetListRequestId.value += 1
  // const data = {
  //   pageNum: 1,
  //   pageSize: 10,
  // }
  billList(queryParams.value)
    .then((res) => {
      if (requestId >= lastGetListRequestId.value) {
        loading.value = false
        list.value = [...list.value, ...res.data.CONSUME]

        // 处理分组
        processGrouping(res.data.CONSUME)

        if (list.value.length >= res.total) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      }
    })
    .catch(() => {
      if (requestId >= lastGetListRequestId.value) {
        loading.value = false
        finished.value = true // 防止死循环
      }
    })
}

/**
 * 处理账单分组逻辑
 * @param {Array} newBills 新获取的账单数据
 */
const processGrouping = (newBills) => {
  newBills.forEach(bill => {
    // 格式化日期：同年显示 MM-DD，跨年显示 YYYY-MM-DD
    const billDate = new Date(bill.repayDay)
    // const currentYear = new Date().getFullYear()
    const displayDate =`${billDate.getFullYear().toString()}年${(billDate.getMonth() + 1).toString().padStart(2, '0')}月${billDate.getDate().toString().padStart(2, '0')}日`

    // 检查是否已有该日期的分组
    if (dateMap.value.has(displayDate)) {
      const groupIndex = dateMap.value.get(displayDate)
      groupedBills.value[groupIndex].list.push(bill)
    } else {
      // 创建新分组
      groupedBills.value.push({
        date: displayDate,
        yDate: bill.repayDay,
        list: [bill]
      })
      // 更新映射表
      dateMap.value.set(displayDate, groupedBills.value.length - 1)
    }
  })

  // 按日期排序分组 (最新在前)
  groupedBills.value.sort((a, b) => {
    return new Date(b.yDate) - new Date(a.yDate)
  })
}




const onAfterSales = (data) => {
  sessionStorage.setItem(proxy.$global.PARAM_AFTERSALES, JSON.stringify(data))
  router.push('/my/after-sales-form')
}
const onFinish = (item) => {
  // 设置为超时
  item.outTime = 0
}
onMounted(() => {
  queryParams.value.status = route.query.status ? route.query.status : ''
  getBillsTypeCount()
  getList()
})
</script>

<style lang="scss" scoped>
.unpaid-bills-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .nav-bar {
    :deep(.left) {
      padding-left: 9px;
      color: #333;
    }
    :deep(.page-title) {
      font-size: 16px;
    }
  }
  .nav-list {
    height: 44px;
    display: flex;
    align-items: center;
    background: #ffffff;
    .nav-item {
      height: 44px;
      flex: 1;
      font-size: 14px;
      font-weight: 400;
      color: #999;
      line-height: 44px;
      text-align: center;
    }
    .nav-item.active {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      position: relative;
    }
    .nav-item.active::after {
      //主要是这个
      content: '';
      display: block;
      margin: 0 auto;
      width: 22px;
      height: 5px;
      background: #4671EB;
      border-radius: 3px;
      position: absolute;
      left: calc(50% - 11px);
      bottom: 6px;
    }
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    // display: flex;
    // flex-direction: column;
    background: #f9f9f9;
    .list {
      min-height: 100%;
      height: max-content;
      .item{
        margin: 10px;
        width: 355px;
        flex-shrink: 0;
        background: #fff;
        border-radius:10px ;
        color: #333;
        font-size: 20px;
        padding:7.5px  8.5px 0px 12px;
        box-sizing: border-box;
        overflow: hidden;
        .day{
          color: #999;
          font-size: 16px;
          font-weight: 400;
        }
        .content_list{
          .content{
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #CFCFCF;
            padding: 11px 0;
        overflow: hidden;
            &:last-child{
              border-bottom: none;
            }
            .goods-img{
              width: 55px;
              height: 55px;
              border-radius: 10px;
              margin-right: 5px;
            }
            .content-c{
              flex: 1;
              .title{
                color: #000;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: 22.5px;
                width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

              }
              .period{
                color: #999;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: 22.5px;
              }

            }
            .content-r{
              display: flex;
              align-items: center;
              .content-r-box{
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                justify-content: flex-start;

                .price{
                  color: #000;
                  text-align: right;
                  font-size: 15px;
                  font-weight: 500;
                  line-height: 22.5px;
                }
                .status{
                  color: #4D65DB;
                  text-align: right;
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 22.5px;
                  position: relative;
                  height: 22.5px;
                  &.red{
                    color: #F43727;
                  }
                  img{
                    width: 65px;
                    // height: 32.5px;
                    position: absolute;
                    top: 3px;
                    right: 0;
                  }
                }
              }
              .ringht-img{
                width: 4.5px;
                height: 8.5px;
                margin-left: 15px;
              }

            }
          }

        }
      }

    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    .pay-status {
      // width: 100%;
      font-size: 15px;
      font-weight: 400;
      color: #333;
      padding-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      .van-count-down {
        display: inline-block;
        color: #f43727;
      }
    }
  }
}
.nav-bar {
  border-bottom: none;
}
</style>
