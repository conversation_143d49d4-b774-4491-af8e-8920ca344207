<template>
  <div class="ad-banner">
    <van-skeleton :loading="loading">
      <template #template>
        <van-skeleton-image class="skeleton-swiper-img" />
      </template>
      <!-- 广告1 -->
      <div
        v-if="advertises['MY_QUOTA1']"
        class="bannar-wrapper"
        style="padding: 5px 10px"
      >
        <!-- <animation-img /> -->
        <my-swiper :swiperDatas="advertises['MY_QUOTA1']"></my-swiper>
      </div>
    </van-skeleton>
  </div>
</template>
<script setup>
  import { getCurrentInstance, onMounted, watch } from 'vue'
  import { getAdList } from '@/api/base'
  import MySwiper from '@/components/MySwiper'

  const { proxy } = getCurrentInstance()
  const store = useStore()

  const advertises = ref({})
  const loading = ref(false)

  const initPage = async () => {
    console.log('额度页广告 初始化')
    try {
      loading.value = true
      const res = await getAdList({
        regionType: ['MY_QUOTA1'],
      })
      advertises.value = res.data
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    await initPage()
  })

  watch(
    () => store.getters.userInfo,
    (newValue, oldValue) => {
      // 用户变更重新获取数据
      if (newValue) {
        if (
          !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
        ) {
          initPage()
        }
      }
    }
  )

  defineExpose({
    initPage
  })
</script>

<style lang="scss" scoped>
.ad-banner {
  // background: linear-gradient(180deg, #7e9bf8, #bcccfb);
  margin-top: 10px;
}

.skeleton-swiper-img {
  width: 100%;
  height: 142px;
}

.bannar-wrapper {
  margin-top: -1px;
  position: relative;
}
</style>
