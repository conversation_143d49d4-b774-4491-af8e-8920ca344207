<template>
  <section class="page">
    <main @scroll="onScrollChange">
      <div class="consume-uota-page">
        <quota-nav
          :scroll-top-value="scrollTopValue"
          :nav-bar-style="navBarStyle"
          @onLeftClick="onBackClick"
        />
        <navigation-bar
        pageName="额度页 "
        :navBarStyle="{ color: '#fff', fontSize: '16px', fontWeight: 600 }"
        @onLeftClick="onBackClick"
        ></navigation-bar>
        <van-pull-refresh v-model="refreshing" @refresh="refresh(0)">
        <div class="consume-uota-page-header">
            <img class="banner" src="@/assets/images/my/consume-quota-banner.png" alt="" />

            <MyMoneyCard  ref="myMoneyCardRef" />
          </div>

          <!-- 广告位 -->
          <AdConsumeQuota  ref="adConsumeQuotaRef"/>
          <!-- 大牌品牌周 -->
          <premium-recommend v-if="preimunList.length > 0" :list="preimunList" />

          <!-- 热销榜单 -->
          <GoodsList ref="goodsListRef" :listType="currentGoodsType" :isAdShow="true" />
        </van-pull-refresh>
        </div>
    </main>
  </section>
</template>

<script setup>
import MyMoneyCard from '@/components/MyMoneyCard/index.vue'
import AdConsumeQuota from './components/AdConsumeQuota.vue'
import QuotaNav from './components/QuotaNav.vue'
import {  regionGoodsPage } from '@/api/goods'
import { useAsyncState, useToggle, whenever } from '@vueuse/core'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const route = useRoute()
const store = useStore()
const loading = ref(false)
const scrollTopValue = ref(-1)
const ANCHOR_SCROLL_TOP = 64
const finished = ref(false)
// const refreshing = ref(false)
const lastGetListRequestId = ref(0)
const state = ref('')
const myMoneyCardRef = ref(null)
const adConsumeQuotaRef = ref(null)
const goodsListRef = ref(null)

const preimunList = ref([])

const data = reactive({
  navBarStyle: {
    backgroundColor: '#ffffff',
    position: 'fixed',
  },
})
const {  navBarStyle } = toRefs(data)


const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP
  navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
}
// 下拉刷新
const { execute: refresh, isLoading: refreshing } = useAsyncState(
  async () => {
    console.log('refresh')
    myMoneyCardRef.value.refresh()
    adConsumeQuotaRef.value.initPage()
    goodsListRef.value.reload()
  },
  null,
  {
    immediate: false,
  }
)

const currentGoodsType = ref({
  type: 'region',
  value: proxy.$global.GOODS_REGION_FOLLOW,
  name: '热销榜单',
})

const onBackClick = () => {
  router.push({ name: 'Main', params: { componentName: '/my' } })
}

const goRepayment = (item) => {
  router.push({ path: '/my/bill/repayment', query: { billId: item.billId } })
}


onMounted(() => {})
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 1.2;
  color: #000;
   main {
    flex: 1;
    height: auto;
    background: #f6f6f6;
    overflow-y: scroll;
    overflow-x: hidden;
    .van-pull-refresh {
      min-height: 100%;
      // :deep(.van-pull-refresh__track) {
      //   min-height: 100%;
      // }
    }
    padding-bottom: var(--safe-area-inset-bottom);
  }
}
.consume-uota-page {
  height: 100%;
  width: 100%;
  background-image: url('@/assets/images/my/my-edu-bg.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: contain;
  background-attachment: scroll;
  background-color: #f4f6fa;
  &-header {
    // background: url('@/assets/images/my/my-bg.png') no-repeat;
    // background: linear-gradient(
    //   180deg,
    //   #5581f6 20%,
    //   #aabefb 50%,
    //   #d6dffa 65%,
    //   #e8edfa 75%,
    //   #f4f6fa 100%
    // );
    // background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .banner {
      display: block;
      width: 291px;
      margin: 25px auto 0 auto;
    }
  }
  .nav-bar {
    :deep(.left) {
      padding-left: 9px;
      color: #fff;
    }
    :deep(.page-title) {
      font-size: 16px;
      color: #fff;
    }
  }
}
.nav-bar {
  border-bottom: none;
}
</style>
