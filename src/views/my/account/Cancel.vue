<template>
  <div class="account-cancel-page">
    <navigation-bar pageName="账号注销" @onLeftClick="onBackClick"></navigation-bar>
    <div class="account-cancel-page-content">
      <img src="@/assets/images/login-phone-big.png" />
      <div class="tips">当前账号</div>
      <div class="phone" v-if="user">
        {{ phoneFormat(user.phone) }}
      </div>
      <van-divider
        :style="{
          'font-size': '16px',
          color: '#CC7A2C',
          margin: '29px 26px 0 26px',
          'border-color': '#CC7A2C',
        }"
        >温馨提示</van-divider
      >
      <div class="decs">请确认您的账号中是否存在操作中/在途交易订单操作完成可注销账户</div>
      <div class="cancel-btn theme-text" @click="showConfirm = true">注销账号</div>
    </div>
    <van-popup v-model:show="showConfirm" :style="{ 'border-radius': '8px' }">
      <div class="confirm-wrapper">
        <img class="close-btn" src="@/assets/icons/close-icon.png" @click="showConfirm = false" />
        <div class="title text-center">注销手机号</div>
        <div class="tips text-center">确认注销吗</div>
        <div class="btn-wrapper">
          <div class="btn" @click="showConfirm = false">放弃注销</div>
          <div class="btn confirm theme-bg" @click="onConfirm">确认</div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="showCode" :style="{ 'border-radius': '8px' }">
      <div class="confirm-wrapper">
        <div class="code-title text-center">
          <div>为保障您的账号安全</div>
          <div>确保是您本人操作，请输入验证码</div>
        </div>
        <div class="code-text text-center">已发送验证码短信到</div>
        <div class="phone text-center">{{ phoneFormat(user.phone) }}</div>
        <div class="input-wrapper solid-bottom">
          <input
            ref="inputRef"
            type="tel"
            v-model="inputCode"
            @input="resetInputVal"
            autofocus
            placeholder="请输入短信验证码"
          />
          <div :class="`btn theme-text ${times > 0 ? 'disabled' : ''}`" @click="getCode">
            {{ codeText }}
          </div>
        </div>
        <div class="btn-wrapper">
          <div class="btn" @click="showCode = false">取消</div>
          <div class="btn confirm theme-bg theme-border" @click="onCancel">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { accountCancel } from '@/api/customer'
import { phoneFormat } from '@/utils/common'
import { smsCode } from '@/api/base'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const router = useRouter()
const store = useStore()
const showConfirm = ref(false)
const showCode = ref(false)
const inputCode = ref('')
const inputRef = ref(null)
const codeText = ref('获取验证码')
const times = ref(0)
const onBackClick = () => {
  router.go(-1)
}
const onConfirm = () => {
  showConfirm.value = false
  showCode.value = true
  // nextTick(()=>{
  //   inputRef.value.focus()
  // })
  getCode()
}
const getCode = () => {
  if (times.value === 0) {
    times.value = 60
    codeText.value = times.value + 's后重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's后重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '获取验证码'
      }
    }, 1000)
    smsCode({ phone: user.value.phone })
  }
}
const resetInputVal = () => {
  const code = '' + inputCode.value
  if (code.length > 6) {
    inputCode.value = code.substring(0, 6)
  }
}
const onCancel = () => {
  if (!inputCode.value) {
    showToast('请输入短信验证码')
    return
  }
  accountCancel({ verifyCode: inputCode.value }).then((res) => {
    store.dispatch('FedLogOut').then(() => {
      showToast('注销成功！')
      proxy.appJS.appLogout()
      router.push({ name: 'Main', params: { componentName: '/my' } })
    })
  })
}
</script>

<style lang="scss" scoped>
.account-cancel-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .nav-bar {
    // border: none;
  }
  &-content {
    flex-grow: 1;
    overflow: hidden;
    text-align: center;
    background: #ffffff;
    // background: #f9f9f9;
    padding-top: 54px;
    img {
      width: 50px;
      height: 50px;
    }
    .phone {
      font-size: 36px;
      font-weight: bold;
      color: #363636;
      line-height: 47px;
      margin-top: 8px;
    }
    .tips {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #a8a8a8;
      line-height: 20px;
      margin-top: 23px;
    }
    .cancel-btn {
      width: 320px;
      margin: 0 auto;
      height: 44px;
      border-radius: 22px;
      border: 1px solid #4671eb;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 44px;
      margin-top: 37px;
    }
    .decs {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #cc7a2c;
      line-height: 20px;
      margin: 13px 38px 0;
    }
  }
  .confirm-wrapper {
    padding: 25px;
    position: relative;
    background: #ffffff;
    width: 300px;
    box-sizing: border-box;
    .close-btn {
      position: absolute;
      right: 16px;
      top: 16px;
      width: 14px;
      height: 14px;
    }
    .title {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #363636;
      line-height: 25px;
    }
    .code-title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #363636;
      line-height: 22px;
    }
    .tips {
      margin-top: 8px;
      font-size: 15px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #363636;
      line-height: 21px;
    }
    .code-text {
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #747474;
      line-height: 20px;
      margin-top: 13px;
    }
    .phone {
      margin-top: 3px;
      font-size: 22px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #747474;
      line-height: 30px;
    }
    .input-wrapper {
      display: flex;
      padding: 8px 0;
      margin-top: 19px;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;
      input {
        font-size: 13px;
      }
      .btn {
        text-align: right;
        font-size: 13px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        white-space: nowrap;
      }
      .btn.disabled {
        color: #a8a8a8;
      }
    }
    .btn-wrapper {
      display: flex;
      justify-content: space-between;
      margin-top: 24px;
      .btn {
        width: 116px;
        height: 42px;
        border-radius: 21px;
        border: 1px solid #cccccc;
        line-height: 42px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #a8a8a8;
        text-align: center;
      }
      .btn.confirm {
        color: #ffffff;
      }
    }
  }
}
</style>
