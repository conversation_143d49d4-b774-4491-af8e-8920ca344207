<template>
  <div class="my-account-page">
    <navigation-bar pageName="账号与安全" @onLeftClick="onBackClick"></navigation-bar>
    <div class="my-account-page-content">
      <div class="fun-wrapper">
        <div class="my-cell">
          <div class="cell-content solid-bottom">
            <div class="cell-title">头像</div>
            <div class="cell-value">
              <img
                v-if="user"
                style="width: 26px; height: 26px; margin-right: 5px; border-radius: 5000px"
                :src="user && user.custInfo.avatar ? user.custInfo.avatar : avatar"
                @click="onAvatar"
              />
              <!-- <van-icon name="arrow" /> -->
            </div>
          </div>
        </div>
        <div class="my-cell">
          <div class="cell-content solid-bottom">
            <div class="cell-title">手机号码</div>
            <div class="cell-value">
              <span class="text">{{ user ? phoneFormat(user.phone) : '未知' }}</span>
              <!-- <van-icon name="arrow" /> -->
            </div>
          </div>
        </div>
        <div class="my-cell">
          <div class="cell-content solid-bottom">
            <div class="cell-title">实名认证</div>
            <div class="cell-value" @click="toRealName">
              <span class="text">{{
                user ? (user.flow === $global.USER_FLOW_IDCARD ? '未实名' : '已实名') : '未知'
              }}</span>
              <!-- <van-icon name="arrow" /> -->
            </div>
          </div>
        </div>
        <div class="my-cell" @click="onCancel">
          <div class="cell-content solid-bottom">
            <div class="cell-title">账号注销</div>
            <div class="cell-value">
              <span class="text">{{ user ? '' : '未登录' }}</span
              ><van-icon name="arrow" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <avatar-upload ref="avatarUploadRef" />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { phoneFormat } from '@/utils/common'
import AvatarUpload from '@/components/AvatarUpload'
import avatar from '@/assets/images/avatar.png'
import { showToast } from 'vant'
import { useAvatarAuth } from '../hooks/useAvatarAuth'
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const router = useRouter()
const store = useStore()
const avatarUploadRef = ref(null)
const onBackClick = () => {
  router.go(-1)
}
const onCancel = () => {
  if (user.value) {
    router.push('/my/account/cancel')
  } else {
    showToast('未登录')
  }
}
const onAvatar = () => {
  // if(user.value) {
  //   useAvatarAuth(avatarUploadRef.value)
  // } else {
  //   proxy.appJS.appLogin()
  // }
}
</script>

<style lang="scss" scoped>
.my-account-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border: none;
  }
  &-content {
    flex-grow: 1;
    overflow: hidden;
  }
}
</style>