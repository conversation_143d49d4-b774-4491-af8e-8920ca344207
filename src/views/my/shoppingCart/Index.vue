<template>
  <div class="shopping-cart-page">
    <navigation-bar pageName="购物车" @onLeftClick="onBackClick">
      <template #nav-right>
        <div @click="isManage=!isManage">
          {{ isManage ? '管理' : '完成' }}
        </div>
      </template>
    </navigation-bar>
    <cart-page-content :is-manage="isManage" />
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import CartPageContent from '@/views/shoppingCart/components/CartPageContent'
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const isManage = ref(true)
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .shopping-cart-page{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .nav-bar .right{
      font-size: 14px;
    }
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
</style>