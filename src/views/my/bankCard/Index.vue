<template>
  <div class="bankcard-page">
    <navigation-bar pageName="银行卡" @onLeftClick="onBackClick"></navigation-bar>
    <div class="bankcard-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="list" v-if="list.length > 0">
        <van-swipe-cell v-for="item in list" :key="item.id">
          <div :class="`item bank ${bankCode(item.bankName) ?? 'other'}`">
            <div class="name">{{ item.bankName }}</div>
            <div class="cardno">{{ item.cardNoShort }}</div>
            <div class="card-type" v-if="item.type === 'CREDIT'">分期还款</div>
          </div>
          <!-- <template #right>
            <van-button square text="删除" type="danger" class="delete-button" @click="onDelete(item.id)" />
          </template> -->
        </van-swipe-cell>
      </div>
      <van-empty v-else description="暂无绑卡数据" />
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { bankCardList, delBankCard, getBankListGroup } from '@/api/bankcard'
import { bankCode } from '@/constant/bank'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const router = useRouter()
const store = useStore()
const list = ref([])
onMounted(() => {
  getList()
})
// 删除
const onDelete = (id) => {
  delBankCard({ id }).then((res) => {
    proxy.onToastSucc(() => {
      getList()
    }, '删除成功')
  })
}
const getList = () => {
  getBankListGroup().then((res) => {
    list.value = res.data
  })
}
const onBackClick = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.bankcard-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  .nav-bar {
    border: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f9f9f9;
    .list {
      .item {
        margin: 0 15px;
        margin-top: 15px;
        position: relative;
        padding-left: 60px;
        padding-top: 17px;
        height: 115px;
        box-sizing: border-box;
        // background-color: #666;
        border-radius: 8px;
        &.other {
          background-image: linear-gradient(to right, #ef7d82, #dd5759);
        }
        .name {
          font-size: 16px;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
        }
        .cardno {
          font-size: 16px;
          font-weight: 500;
          color: #ffffff;
          line-height: 22px;
          margin-top: 7px;
        }
        .card-type {
          height: 16px;
          border-radius: 9px;
          border: 1px solid #ffffff;
          display: inline-block;
          font-size: 12px;
          transform: scale(0.8);
          transform-origin: left top;
          color: #ffffff;
          line-height: 14px;
          padding: 1px 5px;
        }
      }
    }
    .add {
      margin: 15px;
      height: 65px;
      background: #ffffff;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #a8a8a8;
      img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
      }
    }
  }
}
.delete-button {
  height: 100%;
}
</style>
