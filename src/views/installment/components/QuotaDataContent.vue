<template>
  <div class="content">
    <div class="p1">{{ quotaData.creditStatus === 'SUCC' ? '可用额度(元)' : '最高消费额度(元)' }}
      <div class="p1-desc">
        <img style="height: 13px; margin-left: 5px;" src="@/assets/icons/cashier/safe.png" />
        <span style="color: #6113004D; font-size: 12px; margin-left: 2px; margin-top: 2px;">安全保障中</span>
      </div>
    </div>
    <div class="features">
      <div>限时免息</div>
      <div>循环额度</div>
      <div>灵活借钱</div>
    </div>
    <div class="succ" v-if="quotaData.creditStatus === 'SUCC'">{{ formatMoney(quotaData.balance) }}</div>
    <!-- <div class="apply" v-else-if="quotaData.creditStatus === 'APPLY'">额度申请中</div> -->
    <div class="secret" v-else>******</div>
    <div class="btn" @click="onClickBtn">
      {{ (quotaData.creditStatus === 'SUCC') ? '查账单' : '立即申请额度' }}
    </div>
    <AgreementPersonalInfoAuth v-model="agree"/>
    <ProcessBar style="margin-top: 10px; margin-bottom: 10px;" v-if="quotaData.creditStatus !== 'SUCC'" :creditStatus="quotaData.creditStatus"/>
    <div v-else style="height: 10px; margin-bottom: 10px;"></div>
  </div>
</template>

<script setup>
import ProcessBar from './ProcessBar.vue'
import AgreementPersonalInfoAuth from '@/components/AgreementPersonalInfoAuth'
import { showToast } from 'vant';
import { ref, computed } from 'vue'

const props = defineProps({
  quotaData: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['toActive'])
const quotaData = computed(() => props.quotaData)
const agree = ref(false)

const onClickBtn = () => {
  if (quotaData.value.creditStatus === 'APPLY') {
    showToast('资方审核中，请耐心等待')
    return
  }
  if (!agree.value) {
    showToast('请阅读并同意协议')
    return
  }
  emit('toActive')
}
</script>

<style lang="scss" scoped>
.content {
  width: 355px;
  margin: 0 auto;
  margin-top: 15px;
  background: #FFFFFF;
  border-radius: 15px;
  overflow:hidden;
  position: relative;
  .p1{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px auto 10px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 18px;
    position: relative;

    .p1-desc {
      position: absolute;
      right: 40px;
      display: flex;
      align-items: center;
    }
  }
  .features {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    div {
      background: #F43B2C1A;
      border-radius: 4px;
      color: #B52502;
      font-size: 12px;
      text-align: center;
      font-weight: 400;
      padding: 4px 8px 3px;
    }
  }
  .succ{
    margin-top: 10px;
    font-size: 36px;
    font-weight: 600;
    color: #8A2107;
    letter-spacing: 5px;
    text-align: center;
  }
  // .apply{
  //   margin-top: 10px;
  //   font-size: 16px;
  //   color: #999;
  //   letter-spacing: 5px;
  //   text-align: center;
  // }
  .secret{
    margin-top: 10px;
    font-size: 45px;
    font-weight: 600;
    color: #8A2107;
    letter-spacing: 5px;
    text-align: center;
    height: 36px;
  }
  .button-tips {
    width: 210px;
    height: 31px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, rgba(244, 56, 40, 0.1) 0%, rgba(244, 56, 40, 0.01) 34.53%);
    border-radius: 10px;

    img {
      height: 36px;
      margin-top: -14px;
    }

    span {
      margin-left: 13px;
      margin-top: -7px;
      color: #F87E47;
      font-size: 12px;
      font-weight: 600;
    }
  }
  .btn {
    width: 257px;
    height: 36px;
    font-weight: bold;
    font-size: 16px;
    color: #FFFFFF;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #FF3927 0%, #FF8C64 100%);
    border-radius: 18px;
    position: sticky;
  }
}
</style>