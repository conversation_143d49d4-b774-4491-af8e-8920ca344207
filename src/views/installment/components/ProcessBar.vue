<script setup>
import { computed } from 'vue';

const props = defineProps({
  creditStatus: {
    type: String,
    default: ''
  }
})

const waiting = computed(() => {
  return props.creditStatus === 'APPLY' || props.creditStatus === 'VERIFY'
})
</script>

<template>
  <div class="process">
    <div class="left">
      <template v-if="waiting">
        <img class="process-bar" src="@/assets/images/cash-loan/process-bar-90.png" />
        <div :class="['cursor', 'cursor-waiting']">仅剩10%</div>
      </template>
      <template v-else>
        <img class="process-bar" src="@/assets/images/cash-loan/process-bar-70.png" />
        <div class="cursor">仅剩30%</div>
      </template>
    </div>
    <img class="right-icon" src="@/assets/images/cash-loan/right.png" />
    <div class="current">下载APP</div>
    <div class="end">{{ waiting ? '提交资料，等待审核' : '完善信息，获得额度' }}</div>
  </div>
</template>

<style lang="scss" scoped>
.process {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 25px auto;
  padding-bottom: 22px;
  position: relative;

  .left {
    border-radius: 7px;
    width: 286px;
    height: 13px;
    position: relative;
    margin-right: 2px;
    margin-left: 8px;

    .process-bar {
      position: absolute;
      left: 0;
      top: 0px;
      width: 286px;
      height: 13px;
    }

    .cursor {
      position: absolute;
      left: 180px;
      top: -2px;
      background-color: #F36B60;
      border-radius: 10px;
      width: 62px;
      height: 18px;
      line-height: 18px;
      text-align: center;

      font-size: 12px;
      font-weight: 600;
      color: #fff;
    }

    .cursor-waiting {
      left: 210px;
    }
  }

  .right-icon {
    width: 18px;
    height: 18px;
  }

  .current {
    position: absolute;
    top: 25px;
    left: 28px;
    font-size: 13px;
    color: #999;
  }

  .end {
    position: absolute;
    top: 25px;
    right: 22px;
    font-size: 13px;
    color: #999;
  }
}
</style>