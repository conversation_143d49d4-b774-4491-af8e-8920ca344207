<template>
  <div class="installment-page">
    <van-sticky class="header">
      <!-- 原版不带搜索功能 -->
      <navigation-bar
        pageName="极刻花"
        :navBarStyle="{ color: '#fff' }"
        @onLeftClick="onBackClick"
      />
      <!-- 新原型带搜索功能 -->
      <!-- <navigation-bar pageName="极刻花" :navBarStyle="{ color: '#fff' }" :isShowBack="false" @onLeftClick="onBackClick"/>
      <div class="search-wrapper">
        <van-icon size="22" name="arrow-left" style="padding: 6px 8px;" color="#fff" @click="onBackClick"/>
        <SearchBar class="search" disabled @click="routerSearch" />
      </div> -->
      <van-notice-bar
        v-if="quotaData.cashSuccCount > 0 || quotaData.applyVerifyCount > 0"
        left-icon="volume-o"
        :scrollable="false"
        color="#D69834"
      >
        <van-swipe
          vertical
          class="notice-swipe"
          :autoplay="3000"
          :touchable="false"
          :show-indicators="false"
        >
          <van-swipe-item v-if="quotaData.cashSuccCount > 0"
            >你有{{ quotaData.cashSuccCount }}笔消费分期订单使用中，请按时还款</van-swipe-item
          >
          <van-swipe-item v-if="quotaData.applyVerifyCount > 0"
            >您有{{ quotaData.applyVerifyCount }}笔消费分期订单审核中，请耐心等待</van-swipe-item
          >
        </van-swipe>
      </van-notice-bar>
    </van-sticky>
    <div class="installment-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="top">
        <quota-data-content :quota-data="quotaData" @toActive="toActive" />
      </div>
      <!-- <func-group
        class="icon-group"
        :list="funcGroupList"
        :item-style="{flex: '0 0 25%', margin: '18px 0'}"
        :text-style="{'font-size':'12px', 'margin-top': '10px', color: '#333333', transform: 'none','line-height': '17px'}"
      /> -->
      <!-- <installment-step /> -->
      <gift-gag-goods />
    </div>
    <credit-loading ref="loadingRef" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import QuotaDataContent from './components/QuotaDataContent'
import CreditLoading from '@/components/CreditLoading'
import GiftGagGoods from '@/components/GiftGagGoods'
import SearchBar from '@/components/SearchBar/SearchBar.vue'
import { getProductQuota, creditQuota } from '@/api/customer'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const loadingRef = ref(null)
const data = reactive({
  quotaData: {},
})
const { quotaData } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const toActive = () => {
  if (user.value) {
    if (user.value.flow === proxy.$global.USER_FLOW_FINISH) {
      if (
        quotaData.value.creditStatus === 'NON' ||
        (quotaData.value.creditStatus === 'FAIL' &&
          new Date().getTime() >= quotaData.value.allowTime)
      ) {
        // 授信
        loadingRef.value.show = true
        creditQuota({ requestType: 'credit', sceneCode: proxy.$global.CREDIT_SCENE_CONSUME }).then(
          (res) => {
            setTimeout(() => {
              loadingRef.value.show = false
              getProductQuota({ productType: proxy.$global.CREDIT_SCENE_CONSUME }).then((res) => {
                quotaData.value = res.data[0]
              })
            }, 1500)
          }
        )
      } else if (
        quotaData.value.creditStatus === 'FAIL' &&
        new Date().getTime() < quotaData.value.allowTime
      ) {
        refusePopupRef.value.show = true
      } else if (quotaData.value.creditStatus === 'SUCC') {
        router.push('/my/bill')
      } else {
        showToast('申请审核中！')
      }
    } else {
      const name =
        user.value.flow === proxy.$global.USER_FLOW_INFO
          ? proxy.$global.USER_FLOW_INFO_NAME
          : proxy.$global.USER_FLOW_IDCARD_NAME
      localStorage.setItem(proxy.$global.LOCAL_CREDIT_SCENE, proxy.$global.CREDIT_SCENE_CONSUME) // 实名授信通道
      router.push({ name, params: { backUrl: '/installment' } })
    }
  } else {
    proxy.appJS.appLogin()
  }
}

// 搜索
const routerSearch = () => {
  router.push('/base-search')
}

onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  if (user.value) {
    // 授信额度
    getProductQuota({ productType: 'CONSUME' }).then((res) => {
      // 测试数据
      // if (import.meta.env.VITE_APP_ENV === 'development') {
      //   res.data[0].creditStatus = 'APPLY'
      //   res.data[0].cashSuccCount = 10
      //   res.data[0].balance = 1000000
      // }
      quotaData.value = res.data[0]
    })
  } else {
    quotaData.value = {}
  }
})
</script>
<style scoped lang='scss'>
.installment-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #f6f6f6;
  :deep(.page-title) {
    font-size: 16px !important;
    color: #fff !important;
    font-weight: 600;
  }
  :deep(.bottom-line) {
    border: none;
  }
  .search-wrapper {
    display: flex;
    align-items: center;
    margin-left: 4px;
    margin-right: 20px;
    margin-top: 2px;
    margin-bottom: 12px;
  }
  .header {
    background: linear-gradient(180deg, #4671eb, #f58d84);
    color: #fff;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .top {
      background: linear-gradient(180deg, #f58d84, #f6f6f6);
    }
  }
}
.notice-swipe {
  height: 34px;
  line-height: 34px;
  font-size: 12px;
}
</style>