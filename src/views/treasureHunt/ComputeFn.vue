<template>
  <div class="page">
    <navigation-bar pageName="计算详情" @onLeftClick="onBackClick">
      <template #nav-right>
        <van-icon name="question-o" size="25" @click="show=true" />
      </template>
    </navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="main">
        <div class="result">
          <div class="number">{{ computeData.draNumber }}</div>
          <div class="text">最终结果</div>
        </div>
        <div class="compute-context">
          <div class="plus box">
            <div class="number">
              {{ computeData.calNumber }}
            </div>
            <div class="text">时间取值之和</div>
          </div>
          <div class="fixed box">
            <div class="number">
              {{ computeData.fixedValue }}
            </div>
            <div class="text">固定数值</div>
          </div>
          <div class="goods-integral box">
            <div class="number">
              {{ computeData.realPerson }}
            </div>
            <div class="text">商品总需次数</div>
          </div>
          <img src="@/assets/images/treasure-hunt/equals-img.png" class="equals-img">
          <img src="@/assets/images/treasure-hunt/percentage-img.png" class="percentage-img">
          <img src="@/assets/images/treasure-hunt/plus-img.png" class="plus-img">
        </div>
        <!-- <div class="list">
          <div class="item"></div>
          <div class="symbol text-white">%</div>
          <div class="item has">
            <div class="number">{{ computeData.draNumber }}</div>
            <div class="text">最终结果</div>
          </div>
          <div class="symbol text-white">+</div>
          <div class="item"></div>
        </div>
        <div class="equal">=</div>
        <div class="list">
          <div class="item has">
            <div class="number">{{ computeData.calNumber }}</div>
            <div class="text mini">时间取值之和</div>
          </div>
          <div class="symbol">%</div>
          <div class="item has">
            <div class="number">{{ computeData.realPerson }}</div>
            <div class="text mini">商品总需次数</div>
          </div>
          <div class="symbol">+</div>
          <div class="item has">
            <div class="number">{{ computeData.fixedValue }}</div>
            <div class="text mini">固定数值</div>
          </div>
        </div> -->
      </div>
      <div class="tips">
        时间取值之和：截止本商品最后参与换取时间（{{ formattedTime(list[0]?.exchangeTime) }},网站所有商品的最后100参与换取记录的时间取值只和
      </div>
      <div class="table-list">
        <div class="list-title solid-bottom">
          <div class="left">换取时间</div>
          <div class="center">转换数据</div>
          <div class="right">会员用户</div>
        </div>
        <div class="list-item" v-for="(item, index) in list" :key="index">
          <div class="left">{{ formattedTime(item.exchangeTime) }}</div>
          <div class="center">{{ item.drawNumber }}</div>
          <div class="right">{{ item.phone }}</div>
        </div>
      </div>
    </div>
    <div v-if="!finished" class="footer" @click="getList" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="btn solid-top">展开所有<van-icon name="arrow-down" /></div>
    </div>
    <van-popup 
      class="overflow-inherit"
      v-model:show="show"
      :close-on-click-overlay="false"
      closeable
      :style="{ width: $px2rem('284px'), 'border-radius': $px2rem('18px') }"
    >
      <div class="popup-content">
        <div class="title">如何计算</div>
        <div class="p">
          1、截取至该商品最后换取时间全平台最后 100条换取时间记录;
        </div>
        <div class="p">
          2、每条时间记录按时、分、秒、毫秒一次排列成一个数值
        </div>
        <div class="p">
          3、将100 个数值之间和除以商品所有等份后取余；
        </div>
        <div class="p">
          4、余数是指整数除以商品所有等份末初除尽部分如：7/3=2.....1.1是余数；
        </div>
        <div class="p">
          5、余数加上 10000001“幸运编号”
        </div>
        <div class="got-it" @click="show=false">知道了</div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { comcupteInfo, comcupteList } from '@/api/integral-treasure-hunt'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter()
const showAll = ref(false)
const sessionId = ref('')
const show = ref(false)
const finished = ref(false)
const list = ref([])
const data = reactive({
    queryParams: {
      pageNum: 0,
      pageSize: '10'
    },
    computeData: {}
  })
const { queryParams, computeData }  = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const getList = () => {
  queryParams.value.sessionId = sessionId.value
  queryParams.value.pageNum += 1
  comcupteList(queryParams.value).then(res => {
    list.value = [...list.value, ...res.data]
    if(list.value.length >= res.total || res.data.length === 0) {
      finished.value = true
    } else {
      queryParams.value.pageNum ++
    }
  }).catch(() => {
    loading.value = false
    finished.value = true // 防止死循环
  })
}
const formattedTime = (time) => {
  const dateObj = new Date(time);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  const milliseconds = String(dateObj.getMilliseconds()).padStart(3, '0');
  
  const formattedTime = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  return formattedTime
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  sessionId.value = route.query.id
  comcupteInfo({ sessionId: sessionId.value }).then(res => {
    computeData.value = res.data
  })
  getList()
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #F8F8F8;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      .main{
        background: #FFFFFF;
        border-radius: 0px 0px 8px 8px;
        padding:25px;
        padding-bottom: 10px;
        .result{
          background: linear-gradient(to right, #4E8BF2, #005AF4);
          border-radius: 8px;
          color: #FFFFFF;
          height: 99px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .number{
            font-size: 18px;
          }
          .text{
            font-size: 18px;
            margin-top: 3px;
          }
        }
        .compute-context{
          position: relative;
          height: 130px;
          .equals-img{
            width: 56px;
            height: 46px;
            position: absolute;
            left: 154px;
            top: 0;
          }
          .percentage-img{
            width: 36px;
            height: 41px;
            position: absolute;
            left: 70px;
            top: 66px;
          }
          .plus-img{
            width: 36px;
            height: 41px;
            position: absolute;
            right: 70px;
            top: 66px;
          }
          .goods-integral{
            left: 105px;
            top: 73px;
          }
          .box{
            position: absolute;
            width: 115px;
            height: 50px;
            background: linear-gradient(to right, #4E8BF2, #005AF4);
            border-radius: 8px;
            color: #FFFFFF;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .number{
              font-size: 13px;
            }
            .text{
              font-size: 13px;
              margin-top: 3px;
            }
          }
          .plus{
            top: 16px;
            left: 0;
            width: 140px;
          }
          .fixed{
            position: absolute;
            right: 0;
            top: 16px;
            width: 115px;
            height: 50px;
          }
        }
      }
      .tips{
        font-size: 12px;
        margin: 11px 25px;
        background: rgba(#005AF4,0.4);
        border-radius: 10px;
        white-space: nowrap;
        font-size: 12px;
        color: #FFFFFF;
        padding: 4px 18px;
        overflow-x: auto;
      }
      .equal{
        text-align: center;
      }
      .divider{
        height: 10px;
        background: #F5F5F5;
      }
      .list-title{
        display: flex;
        align-items: center;
        font-size: 13px;
        font-weight: bold;
        padding: 0 10px;
        height: 44px;
        .left{
          width: 33%;
        }
        .center{
          width: 33%;
          text-align: center;
        }
        .right{
          width: 33%;
          text-align: center;
        }
      }
      .table-list{
        background: #ffffff;
        border-radius: 8px 8px 0px 0px;
        overflow: hidden;
      }
      .list-item{
        padding: 15px 10px 10px 10px;
        display: flex;
        align-items: center;
        font-size: 13px;
        .left{
          width: 33%;
        }
        .center{
          width: 33%;
          text-align: center;
        }
        .right{
          width: 33%;
          text-align: center;
        }
      }
    }
    .footer{
      position: fixed;
      width: 100%;
      bottom: 0;
      background: #ffffff;
      .btn{
        height: 44px;
        font-size: 15px;
        text-align: center;
        line-height: 44px;
        color: #005AF4;
      }
    }
    .popup-content{
      padding: 20px;
      .title{
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
      }
      .p{
        font-size: 13px;
        line-height: 22px;
        margin-top: 5px;
      }
      .got-it{
        height: 44px;
        line-height: 44px;
        border-radius: 22px;
        background: linear-gradient(to right, #4E8BF2, #025BF4);
        font-size: 16px;
        text-align: center;
        color: #ffffff;
        margin-top: 20px;
      }
    }
  }
</style>