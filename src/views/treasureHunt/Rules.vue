<template>
  <div class="page" @scroll="onScrollChange">
    <comm-nav text="规格说明" :scroll-top-value="scrollTopValue" :nav-bar-style="navBarStyle" />
    <div class="page-context">
      <img v-if="scrollTopValue<30" class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick">
      <img class="main-img" src="@/assets/images/treasure-hunt/main-bg.jpg">
      <div class="box">
        <div class="box-title box-title1">积分夺宝</div>
        <div class="box-context">
          本次积分夺宝活动分为达成目标时间和达成目标次数两种开奖方式，每人每期每件商品最多下注100注，根据商品价值不同，下注所需积分不同
        </div>
      </div>
      <div class="box">
        <div class="box-title box-title2">积分获得与使用</div>
        <div class="box-context">
          <div class="p">1.新人送福利与注册会员</div>
          <div class="p-context">
            <div class="p1">
              轻享花限时福利，新人登录即送100积分，注册会员再赠200积分
            </div>
          </div>
          <div class="p">2.积分砸金蛋</div>
          <div class="p-context">
            <div class="p1">
              金蛋不断好运连连，轻享花推出金蛋活动，每次开奖消耗10点积分，有概率抽中30积分，50积分，100积分等奖项；更有隐藏大奖1000面值京东e卡（可直接抵扣多平台消费）
            </div>
          </div>
          <div class="p">3.积分可直接兑换商品</div>
          <div class="p-context">
            <div class="p1">
              获得的积分也可直接在积分兑换板块选择自己心仪的 产品，免费兑换包邮到家。
            </div>
          </div>
          <div class="p">4.官方正品</div>
          <div class="p-context">
            <div class="p1">
              本次活动商品三保障： 正品保障 极速发货 免费配送
            </div>
          </div>
        </div>
      </div>
      <div class="tips">
        <div class="zoom-text-10">
          注：轻享花商城商品由品牌商家提供，100%正品!作为一种全新的购物方式，轻享花商城购物与消费体验为一体，让您在优惠购物的同时体验到积分可换商品的乐趣与惊喜!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import CommNav from './components/CommNav'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const data = reactive({
  navBarStyle: {
    backgroundColor: '#ffffff',
    position: 'fixed'
  },
})
const { navBarStyle } = toRefs(data)
const scrollTopValue= ref(-1)
const ANCHOR_SCROLL_TOP = 64
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
  navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
}
const onBackClick = () => {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    overflow: hidden;
    overflow-y: auto;
    background: #57CFF7;
    &-context{
      padding-bottom: 44px;
      .main-img{
        width: 100%;
        height: auto;
        display: block;
      }
      .back-img{
        width: 32px;
        height: 32px;
        position: absolute;
        left: 16px;
        top: 44px;
        display: block;
      }
      .box +.box{
        margin-top: 15px;
      }
      .box{
        background: linear-gradient(to bottom, #ffffff, #DFF7FF);
        border-radius: 8px 8px 8px 8px;
        margin: 0 17px;
        position: relative;
        .box-title{
          height: 29px;
          color: #0569FF;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: -5px;
          text-align: center;
          line-height: 29px;
          font-size: 17px;
        }
        .box-title1{
          width: 141px;
          background: url('@/assets/images/treasure-hunt/title-bg.png') no-repeat;
          background-size: 100% 100%;
        }
        .box-title2{
          width: 161px;
          background: url('@/assets/images/treasure-hunt/title-bg.png') no-repeat;
          background-size: 100% 100%;
        }
        .box-context{
          font-size: 12px;
          line-height: 20px;
          padding: 35px 16px 15px 16px;
          .p{
            font-size: 13px;
            color: #000000;
            font-weight: bold;
            margin-top: 8px;
          }
          .p-context{
            padding-left: 1em;
          }
        }
      }
      .tips{
        margin: 10px 30px 0;
        line-height: 15px;
      }
    }
  }
</style>
