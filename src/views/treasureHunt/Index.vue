<template>
  <div class="treasure-hunt">
    <div class="main">
      <img class="main-bg" src="@/assets/images/treasure-hunt/main-bg.jpg">
      <img class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick">
      <img class="rules" src="@/assets/images/treasure-hunt/rules-text.png" @click="onRules">
      <div class="marquee-wrapper">
        <Vue3Marquee
          :vertical="true"
          :duration="20"
        >
          <div class="marquee-item" v-for="item in marqueeList">
            <div class="zoom-text-10">{{ item }}</div>
          </div>
        </Vue3Marquee>
      </div>
    </div>
    <div class="context">
      <tab-list :query-params="queryParams" @changeTab="changeTab"></tab-list>
      <my-integral></my-integral>
    </div>
    <div class="list-wrapper" :class="{ 'iphonex-bottom': isIphoneX }">
      <PrizeList
        v-if="list.length > 0"
        :list="list"
        :query-params="queryParams"
        @onRefresh="getList"
      ></PrizeList>
      <list-empty v-else :text="emptyText" :desc="emptyDesc"></list-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { treasureHuntList, prizeList, joinList } from '@/api/integral-treasure-hunt'
import { TabList, MyIntegral, PrizeList, ListEmpty } from './components/index'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const list = ref([])
const emptyText = ref('')
const emptyDesc = ref('')
const marqueeList = ref([])
const data = reactive({
  queryParams: {
    pageNum: '1',
    pageSize: '100',
    oldFlag: 'N'
  }
})
const { queryParams }  = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const onRules = () => {
  router.push('/treasure-hunt-rules')
}
// 切换tab
const changeTab = (item) => {
  queryParams.value.oldFlag = item.value
  getList()
  emptyText.value = ''
  emptyDesc.value = ''
  if (item.value === 'MY') {
    emptyText.value = '暂时没有奖品哦'
    emptyDesc.value = '好东西等待幸运的您'
  } else if (item.value === 'Y'){
    emptyText.value = '暂时没有往期活动'
  } else if (item.value === 'Y'){
    emptyText.value = '暂时没有进行中活动，敬请期待'
  }
}
const getList = () => {
  if (queryParams.value.oldFlag === 'MY') { // 我的奖品
    prizeList(queryParams.value).then(res => {
      list.value = res.data
    })
  } else {
    treasureHuntList(queryParams.value).then(res => {
      list.value = res.data
    })
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  const sessionNav = sessionStorage.getItem('treasure-nav')
  if (sessionNav) { // 记录跳转返回的nav
    queryParams.value.oldFlag = sessionNav
    sessionStorage.removeItem('treasure-nav')
  }
  getList()
  joinList().then(res => {
    marqueeList.value = res.data
  })
})
</script>
<style scoped lang='scss'>
  .treasure-hunt{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #57CFF7;
    .main{
      position: relative;
      z-index: 98;
      margin-bottom: -15px;
      .marquee-wrapper{
        position: absolute;
        top: 250px;
        height: 90px;
        left: 10px;
        .marquee-item{
          margin-top: 3px;
          background: url('@/assets/images/treasure-hunt/marquee-bg.png') no-repeat;
          background-size: 100% 100%;
          width: 84px;
          color: #ffffff;
          text-align: center;
          padding: 3px 0;
        }
      }
      // .marquee-wrapper::before{
      //   content: '';
      //   width: 84px;
      //   position: absolute;
      //   top: -1px;
      //   background: linear-gradient(to bottom, rgba(255,255,255, 1), rgba(255,255,255, 0));
      //   height: 15px;
      //   z-index: 100;
      // }
      // .marquee-wrapper::after{
      //   content: '';
      //   width: 84px;
      //   position: absolute;
      //   bottom: -1px;
      //   background: linear-gradient(to bottom, rgba(255,255,255, 1), rgba(255,255,255, 0));
      //   transform: rotate(180deg);
      //   height: 15px;
      //   z-index: 100;
      // }
      .main-bg{
        width: 100%;
        height: auto;
      }
      .back-img{
        width: 28px;
        height: 28px;
        position: absolute;
        left: 16px;
        top: 39px;
        display: block;
      }
      .rules{
        position: absolute;
        top: 109px;
        right: 0;
        width: 43px;
        height: 44px;
      }
    }
    .context{
      padding-bottom: 10px;
    }
    .list-wrapper{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      padding-bottom: 20px;
    }
  }
</style>