<template>
  <van-popup class="overflow-inherit popup-transparent"
    v-model:show="show"
    :style="{ width: $px2rem('287px'), height: $px2rem('109px'), 'border-radius': $px2rem('8px') }">
    <div class="content">
      <img class="result-img" src="@/assets/images/treasure-hunt/join-result.png">
      <div class="text">参与成功</div>
      <van-icon class="close" color="#cccccc" name="close" @click="show=false"></van-icon>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  :deep(.van-popup){
    overflow-y:inherit !important;
  }
  :deep(.overflow-inherit) {
    background: transparent !important;
  }
  .content{
    background: radial-gradient( 0% 0% at 0% 0%, #A0E7FF 0%, #E3F8FF 100%);
    border-radius: 8px 8px 8px 8px;
    position: relative;
    height: 109px;
    .result-img{
      width: 87px;
      height: 86px;
      position: absolute;
      top: -43px;
      left: 50%;
      transform: translateX(-50%);
    }
    .text{
      text-align: center;
      padding-top: 61px;
      color: #B84500;
      font-size: 18px;
      font-weight: bold;
    }
    .btn{
      width: 186px;
      height: 40px;
      border-radius: 20px;
      margin: 24px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .close{
      display: block;
      position: absolute;
      bottom: -40px;
      left: calc(50% - 10px);
    }
  }
</style>