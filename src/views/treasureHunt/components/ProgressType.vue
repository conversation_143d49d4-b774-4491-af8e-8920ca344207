<template>
  <div class="progress-padd">
    <div class="progress-type">
      <div class="progress-wrapper">
        <van-progress 
          :percentage="Number(prizeInfo.realPerson/prizeInfo.targetPerson*100)"
          :stroke-width="10"
          :show-pivot="false"
          track-color="#F8F8F8"
          color="linear-gradient(to right, #FF6414, #FF2400)" />
      </div>
      <div class="progress-text">
        <div class="join-num item">
          <div class="number">{{ prizeInfo.realPerson }}</div>
          <div class="number-text">已参次数</div>
        </div>
        <div class="target-num item">
          <div class="number">{{ prizeInfo.targetPerson }}</div>
          <div class="number-text">总需次数</div>
        </div>
        <div class="remaining-num item">
          <div class="number">{{ prizeInfo.targetPerson - prizeInfo.realPerson }}</div>
          <div class="number-text">剩余次数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .progress-padd{
    margin: 0 7px;
    padding-top: 7px;
  }
  .progress-type{
    height: 50px;
    background: #57CFF7;
    padding: 5px 8px 10px;
    border-radius: 7px;
  }
  .progress-text{
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    .item {
      text-align: center;
      font-size: 12px;
      color: #ffffff;
      .number{
        font-weight: bold;
      }
      .number-text{
        margin-top: 5px;
      }
    }
    .target-num{
      flex: 1;
      text-align: center;
    }
  }
</style>