<template>
  <van-popup class="overflow-inherit popup-transparent"
    v-model:show="show"
    :close-on-click-overlay="false"
    :style="{ width: $px2rem('320px') }">
  <div class="content" @click="onHide">
    <img v-if="prizeInfo.winFlag === 'Y'" class="result-bg" src="@/assets/images/treasure-hunt/winner-bg.png" />
    <div v-if="prizeInfo.winFlag === 'Y'" class="winner">
      <img class="prize-img" :src="prizeInfo.thumbPics">
      <div class="tips-text">你的奖品</div>
      <div class="tips-p">{{ prizeInfo.prizeName }}</div>
    </div>
    <img v-if="prizeInfo.winFlag === 'N'" class="result-bg" src="@/assets/images/treasure-hunt/no-winner-bg.png" />
    <div v-if="prizeInfo.winFlag === 'N'" class="no-winner">
      <img class="empty-box" src="@/assets/images/treasure-hunt/no-winner.png">
      <div class="empty-tips">很遗憾！未中奖</div>
      <div class="tips-p">差一点就中奖了~</div>
      <div class="tips-p">很可惜！与大奖擦肩而过</div>
      <div class="tips-p">再接再厉</div>
    </div>
    <img class="close" src="@/assets/images/treasure-hunt/close.png" @click="show=false">
  </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  }
})
const onHide = () => {
  show.value = false
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .content {
    position: relative;
    .result-bg{
      width: 320px;
      height: 455px;
      display: flex;
    }
    .close{
      display: block;
      position: absolute;
      width: 22px;
      height: 21px;
      bottom: -41px;
      left: 50%;
      transform: translateX(-50%);
    }
    .winner{
      position: absolute;
      width: 100%;
      text-align: center;
      top: 154px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .prize-img{
        width: 134px;
        height: 134px;
        display: block;
        border-radius: 8px;
      }
      .tips-text{
        font-size: 15px;
        color: #87260B;
        font-weight: bold;
        margin-top: 20px;
      }
    }
    .no-winner{
      position: absolute;
      width: 100%;
      text-align: center;
      top: 169px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .empty-box{
        width: 93px;
        height: 77px;
        display: block;
      }
    }
    .empty-tips{
      font-size: 15px;
      color: #87260B;
      font-weight: bold;
      margin-top: 20px;
      margin-bottom: 10px;
    }
    .tips-p{
      color: #87260B;
      font-size: 12px;
      zoom: 0.83;
      padding: 5px 0;
    }
  }
</style>