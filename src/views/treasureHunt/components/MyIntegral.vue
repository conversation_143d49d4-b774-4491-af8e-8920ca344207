<template>
  <div class="integral">
    <div class="integral-text">
      <div class="text">我的积分</div>
    </div>
    <div class="integral-context">
      <div class="left">
        <img class="integral-icon1" src="@/assets/images/treasure-hunt/integral-icon1.png">
        <div class="integral-number">
          <div class="integral-label">积分</div>
          <div class="integral-number-value">{{ integralValue }}</div>
        </div>
      </div>
      <div class="line"></div>
      <div class="right">
        <img class="integral-icon2" src="@/assets/images/treasure-hunt/integral-icon2.png">
        <div class="integral-label" @click="handleIntegral">
          领积分
          <van-icon name="arrow"></van-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { getIntegral } from '@/api/customer'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const integralValue = computed(() => store.getters.integralValue)

const handleIntegral = () => {
  router.push('/integral/sign')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  store.dispatch('IntegralData')
})
</script>
<style scoped lang='scss'>
  .integral{
    margin: 0 15px;
    border-radius: 10px;
    background: #FF693C;
    position: relative;
    z-index: 100;
    display: flex;
    align-items: center;
    .integral-text{
      width: 54px;
      height: 48px;
      display: flex;
      align-items: center;
      .text{
        font-size: 16px;
        text-align: center;
        padding-left: 15px;
        color: #ffffff;
      }
    }
    .integral-context{
      flex: 1;
      display: flex;
      justify-content: space-between;
      border-radius: 10px;
      background: #ffffff;
      margin-left: 10px;
      height: 48px;
      .left, .right{
        flex: 1;
        display: flex;
        align-items: center;
        margin-left: 21px;
        .integral-icon1{
          width: 32px;
          height: 40px;
          margin-right: 10px;
        }
        .integral-icon2{
          width: 24px;
          height: 40px;
          margin-right: 10px;
        }
        .integral-label{
          font-size: 15px;
        }
        .integral-number-value{
          color: #FF0000;
          font-size: 15px;
          margin-top: 5px;
        }
      }
      .line{
        width: 1px;
        background: #DEDEDE;
      }
    }
  }
  
</style>