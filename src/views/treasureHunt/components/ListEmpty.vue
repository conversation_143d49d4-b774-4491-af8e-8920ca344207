<template>
  <div class="empty-wrapper">
    <img class="empty-img" src="@/assets/images/treasure-hunt/empty-img.png">
    <div class="empty-text">{{ text }}</div>
    <div class="empty-decs">{{ desc }}</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  desc: {
    type: String,
    default: ''
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .empty-wrapper{
    margin-top: 50px;
    .empty-img{
      width: 149px;
      height: 110px;
      display: block;
      margin: 0 auto;
    }
    .empty-text, .empty-decs{
      color: #585858;
      font-size: 12px;
      margin-top: 13px;
      text-align: center;
    }
  }
</style>