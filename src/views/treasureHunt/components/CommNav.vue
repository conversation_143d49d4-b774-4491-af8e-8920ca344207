<template>
  <div v-if="scrollTopValue > 30" class="goods-nav" :class="{ 'iphonex-top': isIphoneX }" :style="navBarStyle">
    <div class="left" @click="onLeftClick">
        <van-icon size="22" name="arrow-left" />
    </div>
    <div class="center">
      <!-- 默认状态 -->
      <span class="page-title">{{ text }}</span>
    </div>
    <div class="right"></div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    scrollTopValue: {
        type: Number,
        defualt: 0
    },
    navBarStyle: {
        type: Object,
        defualt: () => {}
    },
    text: {
      type: String,
      default: ''
    }
})
const onLeftClick = () => {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .goods-nav{
        position: fixed;
        top: 0;
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // 适配手机 stateBar
        padding-top: 25px;
        z-index: 999;
        .left {
            padding-left: 6px;
            width: 32px;
            display: flex;
            align-items: center;
        }
        .right {
            display: flex;
            padding-right: 9px;
            align-items: center;
            width: 32px;
            position: relative;
            font-size: 14px;
        }
        .center {
            display: flex;
            height: 100%;
            flex-grow: 1;
            padding: 0 5px;
            .page-title {
                align-self: center;
                margin: 0 auto;
                font-size: 18px;
                color: #333333;
                white-space:nowrap;
            }
        }
    }
</style>