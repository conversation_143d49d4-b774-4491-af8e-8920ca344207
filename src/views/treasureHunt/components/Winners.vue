<template>
  <div class="winners">
    <div class="title">
      <div class="title-left">
        <span class="winner-text">获得者</span>
        <span class="winner-number">幸运编号：{{ prizeInfo.winnerInfo?.drawNumber }}</span>
      </div>
      <div class="title-right" @click="onComputeRules">查看计算规则</div>
    </div>
    <div class="main">
      <img class="winner-img" :src="prizeInfo.thumbPics">
      <div class="winner-info">
        <div class="winner-phone">{{ prizeInfo.winnerInfo?.winnerPhone }}</div>
        <div class="activity-time">开始时间：{{ parseTime(prizeInfo.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</div>
        <div class="winner-time">中奖时间：{{ parseTime(prizeInfo.winnerInfo?.activeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</div>
        <div class="use-integral">参与次数：{{ prizeInfo.winnerInfo.num }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  }
})
const onComputeRules = () => {
  router.push('/treasure-hunt-compute?id='+props.prizeInfo.id)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .winners{
    margin: 12px 13px 0;
    background: url('@/assets/images/treasure-hunt/cout-down-bg.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding-bottom: 10px;
    border-radius: 8px;
    .title{
      padding: 8px 19px 6px 19px;
      color: #1200FF;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left{
        display: flex;
        align-items: center;
        .winner-text{
          font-family: 'YouSheBiaoTiHei';
          font-size: 19px;
        }
        .winner-number{
          font-size: 12px;
          zoom: 0.93;
          margin-left: 13px;
        }
      }
      &-right{
        font-size: 12px;
        zoom: 0.83;
      }
    }
    .main{
      border-radius: 8px;
      background: #ffffff;
      padding: 12px 8px;
      display: flex;
      margin: 0 10px;
      .winner-img{
        width: 88px;
        height: 88px;
        flex-shrink: 0;
      }
      .winner-info{
        margin-left: 17px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .winner-phone{
          font-weight: bold;
          font-size: 16px;
        }
        .activity-time, .winner-time, .use-integral{
          color: #999999;
          font-size: 12px;
          margin-top: 6px;
        }
      }
    }
  }
</style>