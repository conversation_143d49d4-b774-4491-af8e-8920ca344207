<template>
  <van-popup class="overflow-inherit popup-transparent"
    v-model:show="show"
    :close-on-click-overlay="false"
    :style="{ width: $px2rem('366px'),'border-radius': '8px' }">
    <div class="content">
      <img class="result-img" src="@/assets/images/treasure-hunt/betting-img.png">
      <div class="title">选择投注数量</div>
      <div class="title-tips">
        <span class="theme-text">{{ prizeInfo?.integral }}积分</span>兑换<span class="theme-text">1个夺宝号</span>，单个宝贝最多可兑换<span class="theme-text">100个夺宝号</span>
      </div>
      <div class="stepper">
        <div :class="`stepper-minus ${ setpNum === 1 ?'disabled':''}`" @click="onAction('minus')">
          <van-icon name="minus" />
        </div>
        <div class="stepper-content" v-if="prizeInfo.integral">
          <div class="stepper-input">
            <input v-model="setpNum" class="van-stepper__input" inputmode="decimal" type="number" @change="onChange" />
            <div class="text">注</div>
          </div>
          <div class="consume-integral">
            消{{ setpNum * prizeInfo.integral }}积分
          </div>
        </div>
        <div :class="`stepper-plus ${ max > setpNum ? '' : 'disabled' }`" @click="onAction('plus')">
          <van-icon name="plus" />
        </div>
      </div>
      <div class="tips">您有 <span class="theme-text">{{ integralValue }}</span> 积分夺宝号越多中奖概率越大</div>
      <div class="betting-btn" @click="onBetting">夺宝兑换</div>
      <img class="close" src="@/assets/images/treasure-hunt/close.png" @click="show=false" />
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const setpNum = ref(1)
const integralValue = computed(() => store.getters.integralValue)
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  },
  max: {
    type: Number,
    default: 100
  }
})
const emit = defineEmits(['onJoin'])
const onAction = (type) => {
  if ((setpNum.value === 1 && type === 'minus') || (setpNum.value >= props.max && type === 'plus')) {
    return
  }
  if (type === 'minus') {
    setpNum.value = setpNum.value - 1
  } else {
    setpNum.value = setpNum.value + 1
  }
}
const onChange = () => {
  if (!setpNum.value) {
    setpNum.value = 1
  } else if(setpNum.value > 100){
    setpNum.value = 100
  }
}
const onBetting = () => {
  if ((setpNum.value * props.prizeInfo.integral) > integralValue) {
    showToast('您的积分不足')
  }
  emit('onJoin', setpNum.value)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  
})
defineExpose({ show, setpNum })
</script>
<style scoped lang='scss'>
  :deep(.van-popup){
    overflow-y:inherit !important;
  }
  :deep(.overflow-inherit) {
    background: transparent !important;
  }
  .content{
    background: linear-gradient(to bottom, #ffffff, #DFF7FF);
    border-radius: 8px;
    position: relative;
    padding-bottom: 34px;
    .result-img{
      width: 102px;
      height: 102px;
      position: absolute;
      top: -51px;
      left: 50%;
      transform: translateX(-50%);
    }
    .title{
      text-align: center;
      padding-top: 55px;
      color: #050709;
      font-size: 18px;
      font-weight: bold;
    }
    .title-tips{
      text-align: center;
      font-size: 12px;
      font-weight: bold;
      margin-top: 8px;
    }
    .stepper{
      margin: 25px auto 0;
      display: flex;
      justify-content: center;
      .stepper-minus, .stepper-plus{
        width: 79px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #57CFF7;
      }
      .stepper-minus.disabled, .stepper-plus.disabled{
        background: #E8E8E8;
      }
      .stepper-minus{
        border-radius: 8px 0 0 8px;
      }
      .stepper-plus{
        border-radius: 0px 8px 8px 0px;
      }
      .stepper-content{
        background: #ffffff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 20px;
        .stepper-input {
          display: flex;
          align-items: center;
          .text{
            font-size: 15px;
            font-weight: bold;
          }
          :deep(.van-stepper__input) {
            height: 35px;
            width: 40px;
            font-size: 18px;
            border-radius: 6px;
          }
        }
        .betting-num{
          color: #0D0907;
          font-size: 25px;
          .betting-text{
            font-size: 13px;
            display: inline-block;
            margin-left: 1px;
          }
        }
        .consume-integral{
          font-size: 12px;
          margin-top: 5px;
          font-weight: bold;
        }
      }
    }
    .tips{
      background: url('@/assets/images/treasure-hunt/tips-integral-bg.png') no-repeat;
      background-size: 100% 100%;
      width: 236px;
      height: 23px;
      font-size: 12px;
      text-align: center;
      margin: 20px auto 0;
      box-sizing: border-box;
      padding-top: 3px;
      color: #050709;
    }
    .betting-btn{
      width: 223px;
      height: 36px;
      background: linear-gradient(to right, #FC8648, #F75A43);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      margin: 5px auto 0;
      border-radius: 18px;
    }
    .btn{
      width: 186px;
      height: 40px;
      border-radius: 20px;
      margin: 24px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .close{
      display: block;
      position: absolute;
      bottom: -48px;
      left: 50%;
      transform: translateX(-50%);
      width: 26px;
      height: 26px;
    }
  }
</style>