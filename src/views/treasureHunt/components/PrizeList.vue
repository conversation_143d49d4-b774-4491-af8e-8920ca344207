<template>
  <div class="list">
    <div v-for="(item, index) in list" :key="index" class="list-item" @click="onPrizeDetail(item)">
      <div v-if="queryParams.oldFlag !== 'MY'" :class="`label ${ index%2 === 0 ? 'odd' : ''}`">
        <div class="zoom-text-10" v-if="item.startTime > new Date().getTime()">即将开始</div>
        <div v-else class="zoom-text-10">{{ item.realPerson }}人参与过</div>
      </div>
      <div class="item-left">
        <img :src="item.thumbPics" class="prize-img">
      </div>
      <div class="item-right">
        <div v-if="queryParams.oldFlag === 'MY'" class="phone">
          中奖号码：{{ item.winnerPhone }}
        </div>
        <div class="prize-name overflow-1">{{ item.prizeName }}</div>
        <div class="prize-period">
          <div class="zoom-text-11">{{ item.oldFlag === 'MY' ? '中奖' : '当前' }}期数：第{{ item.prizePeriod }}期</div>
        </div>
        <template v-if="queryParams.oldFlag === 'N' && item.startTime > new Date().getTime()">
          <div class="prize-down-times">
            <div class="down-times-label red">
              <div class="zoom-text-11">即将开始:</div></div>
            <div class="down-times-time yellow">
              <van-count-down
                :time="(item.startTime-new Date().getTime())"
                @finish="emit('onRefresh')" />
            </div>
          </div>
        </template>
        <template v-else>
          <div v-if="queryParams.oldFlag === 'N' && item.autoLottery=== 'Y'" class="prize-down-times">
            <div class="down-times-label">
              <div class="zoom-text-11">距离开奖:</div></div>
            <div class="down-times-time">
              <van-count-down
                v-if="item.endTime > new Date().getTime()"
                :time="(item.endTime-new Date().getTime())"
                @finish="emit('onRefresh')" />
              <div v-else class="van-count-down">开奖中</div>
            </div>
          </div>
          <div v-if="queryParams.oldFlag === 'N' && item.autoLottery=== 'N'" class="prize-progress">
            <div class="zoom-text-10">开奖<span>进度{{ formatValue(item.realPerson/item.targetPerson*100, 'price') }}%</span></div>
            <div class="progress-style">
              <van-progress
              :percentage="Number(item.realPerson/item.targetPerson*100)"
              :show-pivot="false"
              track-color="#B8EFFF"
              color="#3B86FF"
              />
            </div>
          </div>
        </template>
        
        <div v-if="queryParams.oldFlag === 'Y'" class="prize-time">
          <div class="time-text zoom-text-10">{{ parseTime(item.startTime, '{y}.{m}.{d} {h}:{i}:{s}') }} 开始</div>
          <div class="time-text zoom-text-10">{{ parseTime(item.drawTime, '{y}.{m}.{d} {h}:{i}:{s}') }} 开奖</div>
        </div>
        <div v-if="queryParams.oldFlag === 'N'" class="tips">
          <div class="zoom-text-10">
            <span>{{ item.integral }}积分</span>
            <template v-if="item.vipLevelNames.length > 0 && queryParams.oldFlag === 'N'">
              <span> 会员</span>
            </template>
          可进行参与抽奖</div>
        </div>
        <div v-if="queryParams.oldFlag !== 'MY'" :class="`price ${queryParams.oldFlag !== 'N' ? 'top' : ''}`">
          <div class="zoom-text-10">价值：<span class="theme-text">¥{{ item.price }}</span></div>
          <div v-if="queryParams.oldFlag === 'Y' && item.winnerPhone" class="zoom-text-10 price-person">
            恭喜 <span class="theme-text">{{ item.winnerPhone }}</span> 成功获奖
          </div>
        </div>
      </div>
      <div v-if="queryParams.oldFlag !== 'Y' && item.prizeFlag !== 'EXCHANGE'" class="btn-wrapper" @click.stop="onClick(item)">
        <span v-if="queryParams.oldFlag === 'MY' && item.prizeFlag === 'WIN'">立即领取</span>
        <span v-if="queryParams.oldFlag === 'N' && item.startTime > new Date().getTime()">暂未开始</span>
        <span v-else-if="queryParams.oldFlag === 'N' && item.joinFlag === 'N'">立即参加</span>
        <span v-else-if="queryParams.oldFlag === 'N' && item.joinFlag === 'Y'">正在参加</span>
        <span v-else-if="queryParams.oldFlag === 'MY' && item.prizeFlag === 'EXPIRE'">已失效</span>
      </div>
      <img v-if="item.prizeFlag === 'EXCHANGE'" class="receive-img" src="@/assets/images/treasure-hunt/receive-img.png">
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { multiply, divide } from '@/utils/common'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  queryParams: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['onRefresh'])
const onPrizeDetail = (item) => {
  sessionStorage.setItem('treasure-nav', props.queryParams.oldFlag)
  const id = props.queryParams.oldFlag === 'MY' ? item.prizeId : item.id
  router.push('/treasure-hunt-detail?id=' + id)
}
const onClick = (item) => {
  if (props.queryParams.oldFlag === 'MY' && item.prizeFlag === 'WIN') { // 奖品领取
    localStorage.setItem(proxy.$global.PARAM_ORDERCONFIRM, JSON.stringify({
      shoppingFlag: 'P',
      participateId: item.logId,
      msOrderSpus: {
        spuId: item.spuId,
        skuId: item.skuId,
        quantity: 1
      }
    }))
    sessionStorage.setItem('treasure-nav', props.queryParams.oldFlag)
    router.push('/order-confirm')
  } else {
    onPrizeDetail(item)
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .list{
    padding: 0 15px;
    &-item{
      background: url('@/assets/images/treasure-hunt/list-item-bg.png') no-repeat;
      background-size: 100% 100%;
      height: 119px;
      display: flex;
      box-sizing: border-box;
      padding-left: 16px;
      padding-right: 9px;
      position: relative;
      .label{
        position: absolute;
        top: 0;
        left: 0;
        height: 16px;
        padding-left: 10px;
        padding-right: 6px;
        background: #005AF4;
        border-radius: 12px 0 6px 0;
        display: flex;
        align-items: center;
        color: #ffffff;
      }
      .btn-wrapper{
        position: absolute;
        bottom: 7px;
        right: 9px;
        width: 91px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FF5700;
        border-radius: 12px;
        z-index: 100;
        span{
          font-family: 'YouSheBiaoTiHei';
          color: #ffffff;
          font-size: 18px;
        }
      }
      .receive-img{
        position: absolute;
        right: 0;
        top: 10px;
        width: 93px;
        height: 93px;
        z-index: 99;
      }
      .item-left{
        display: flex;
        align-items: center;
        .prize-img{
          width: 71px;
          height: auto;
          display: block;
        }
      }
      .item-right{
        margin-left: 30px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .phone{
          font-size: 12px;
          font-weight: bold;
          color: #A0A0A1;
          margin-bottom: 7px;
        }
        .prize-name{
          color: #08090D;
          font-weight: bold;
          font-size: 15px;
          line-height: 16px;
        }
        .prize-period{
          margin-top: 7px;
          color: #8F8F8F;
        }
        .prize-progress{
          margin-top: 7px;
          .zoom-text-10{
            span{
              color: #FF0000;
            }
          }
          .progress-style{
            margin-top: 4px;
            width: 160px;
          }
        }
        .prize-down-times{
          margin-top: 7px;
          display: flex;
          align-items: center;
          .down-times-label{
            height: 19px;
            padding: 0px 4px 0px 6px;
            display: flex;
            align-items: center;
            border-radius: 5px  0px  0px  5px;
            background: #005AF4;
            color: #ffffff;
          }
          .down-times-label.red{
            background: #FF0000;
          }
          .down-times-time{
            height: 19px;
            display: flex;
            align-items: center;
            background: #89B5FF;
            border-radius: 0px  5px  5px  0px;
            padding: 0px 6px 0px 4px;
          }
          .down-times-time.yellow{
            background: #FF9C00;
          }
          :deep(.van-count-down) {
            font-size: 12px;
            zoom: 0.93;
            color: #ffffff;
          }
        }
        .prize-time{
          margin-top: 8px;
          .time-text{
            color: #8F8F8F;
          }
          .time-text +.time-text{
            margin-top: 5px;
          }
        }
        .tips{
          margin-top: 7px;
          span{
            color:#FF0000;
          }
        }
        .price{
          margin-top: 3px;
          display: flex;
          justify-content: space-between;
        }
        .price.top{
          margin-top: 10px;
        }
      }
    }
    .list-item .label.odd {
      background: #FF5700;
    }
    .list-item +.list-item{
      margin-top: 8px;
    }
  }
</style>