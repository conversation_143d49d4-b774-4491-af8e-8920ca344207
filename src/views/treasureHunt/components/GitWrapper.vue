<template>
  <div class="git">
    <img class="git-img" src="@/assets/images/treasure-hunt/git-icon.png">
    <div class="text">您还没有换取记录哦  试试吧！</div>
    <div class="git-btn" @click="emit('onReceive')">立即领取<van-icon name="arrow"></van-icon></div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const emit = defineEmits(['onReceive'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .git{
    position: relative;
    display: flex;
    margin: 11px 13px 0;
    height: 21px;
    border-radius: 21px;
    align-items: center;
    background: linear-gradient(to right, #57F0F7, #57D5F7);
    padding: 0 13px;
    padding-right: 11px;
    .git-img{
      width: 18px;
      height: 16px;
    }
    .text{
      font-size: 12px;
      color: #08090D;
      flex: 1;
      margin-left: 7px;
    }
    .git-btn{
      font-size: 12px;
      padding: 3px 9px 3px 11px;
      background: #ffffff;
      border-radius: 12px;
      font-weight: bold;
    }
  }
</style>