<template>
  <div class="goods-info">
    <div class="goods-base">
      <div class="goods-name">
        <div class="period">
          <div class="zoom-text-11">第{{ prizeInfo.prizePeriod }}期</div>
        </div>
        <div>{{ prizeInfo.prizeName }}</div>
      </div>
      <div class="goods-number">数量：<span class="theme-text">1</span></div>
    </div>
    <div class="number-price">
      <div class="join-number">
        单次参与需 <span class="theme-text">{{ prizeInfo.integral }}积分</span>
      </div>
      <div class="price">
        价值：<span>¥</span>{{ prizeInfo.price }}
      </div>
    </div>
    <div class="service">
      <div class="service-item">
        <img src="@/assets/images/treasure-hunt/service-icon.png">
        <div>正品保证</div>
      </div>
      <div class="service-item">
        <img src="@/assets/images/treasure-hunt/service-icon.png">
        <div>极速发货</div>
      </div>
      <div class="service-item">
        <img src="@/assets/images/treasure-hunt/service-icon.png">
        <div>免费配送</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const numberToChinese = (num) => {
  if (num) {
    const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千'];

    if (num === 0) {
        return '零';
    }

    let numStr = num.toString();
    let result = '';
    let len = numStr.length;

    for (let i = 0; i < len; i++) {
        let digit = parseInt(numStr[i]);
        if (digit!== 0) {
            result += chineseNumbers[digit] + units[len - i - 1];
        } else {
            // 处理零的情况，避免连续的零
            if (i < len - 1 && parseInt(numStr[i + 1])!== 0) {
                result += chineseNumbers[digit];
            }
        }
    }
    return result;
  }
}
</script>
<style scoped lang='scss'>
  .goods-info{
    padding: 13px 17px 14px;
    .goods-base{
      display: flex;
      justify-content: space-between;
      .goods-name{
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
        .period{
          height: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient( to right, #B3F9FF 0%, #13C4FF 100%);
          border-radius: 16px 6px 13px 3px;
          margin-right: 6px;
          padding: 0 6px;
          .zoom-text-11{
            color: #1200FF;
            font-weight: 'Alimama';
            font-weight: bold;
          }
        }
        span{
          margin-left: 7px;
          font-size: 18px;
        }
      }
      .goods-number{
        flex-shrink: 0;
        margin-left: 10px;
        font-size: 12px;
      }
    }
    .number-price{
      margin-top: 7px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .join-number{
        font-size: 12px;
        color: #08090D;
        font-weight: bold;
      }
      .price{
        font-size: 15px;
        span{
          font-size: 12px;
          zoom: 0.83;
        }
      }
    }
    .service{
      height: 30px;
      display: flex;
      align-items: center;
      background: #E8F9FF;
      border-radius: 8px;
      margin-top: 9px;
      .service-item{
        flex: 1;
        font-size: 12px;
        color: #FF6000;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        img{
          width: 13px;
          height: 13px;
          margin-right: 5px;
        }
      }
    }
  }
</style>