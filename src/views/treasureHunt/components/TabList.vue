<template>
  <div :class="`list ${queryParams.oldFlag}`">
    <div
      class="list-item"
      v-for="(item, index) in list"
      :key="index"
      @click="handleTab(item)"
    >
      <div :class="`text-item ${item.value} ${queryParams.oldFlag === item.value ? 'active': ''}`">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const list = [
  { label: '抽奖进行中', value: 'N' },
  { label: '往期奖品', value: 'Y' },
  { label: '我的奖品', value: 'MY' }
]
const props = defineProps({
  queryParams: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['changeTab'])
const handleTab = (item) => {
  emit('changeTab', item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .list{
    display: flex;
    position: relative;
    z-index: 99;
    height: 63px;
    margin-bottom: -15px;
    .list-item{
      flex: 1;
      text-align: center;
      .text-item{
        font-family: 'YouSheBiaoTiHei';
        color: #ffffff;
        font-size: 18px;
        margin-top: 12px;
      }
      .text-item.active {
        font-size: 25px;
      }
    }
    .text-item.active, .text-item.active{
      display: block;
      margin-top: 9px;
      width: 150px;
    }
  }
  .list.Y{
    background: url('@/assets/images/treasure-hunt/tab-previous-bg.png') no-repeat;
    background-size: 100% 63px;
  }
  .list.N{
    background: url('@/assets/images/treasure-hunt/tab-progress-bg.png') no-repeat;
    background-size: 100% 63px;
  }
  .list.MY{
    background: url('@/assets/images/treasure-hunt/tab-my-bg.png') no-repeat;
    background-size: 100% 63px;
  }
</style>