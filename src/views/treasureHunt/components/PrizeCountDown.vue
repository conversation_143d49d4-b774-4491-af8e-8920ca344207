<template>
  <div class="count-down">
    <div class="title">
      <div class="title-left">
        {{ prizeInfo.startTime > new Date().getTime() ? '即将开始' : '即将揭晓' }}
      </div>
      <div class="title-right">这次会是谁呢？</div>
    </div>
    <div class="main">
      <van-count-down v-if="prizeInfo" :time="formTimes()" @finish="emit('onRefresh')">
        <template #default="timeData">
          <span class="block" v-if="timeData.days > 0">{{ timeData.hours + timeData.days*24 }}</span>
          <span class="block" v-else>{{ timeData.hours > 9 ? timeData.hours : '0' + timeData.hours }}</span>
          <span class="colon">:</span>
          <span class="block">{{ timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes }}</span>
          <span class="colon">:</span>
          <span class="block">{{ timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds }}</span>
        </template>
      </van-count-down>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  prizeInfo: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['onRefresh'])
const formTimes = () => {
  if (props.prizeInfo.startTime > new Date().getTime()) {
    return props.prizeInfo.startTime - new Date().getTime()
  } else {
    return props.prizeInfo.endTime - new Date().getTime()
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .count-down{
    height: 106px;
    margin: 11px 13px 0;
    background: url('@/assets/images/treasure-hunt/cout-down-bg.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    .title{
      padding: 8px 19px 6px 19px;
      color: #1200FF;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left{
        font-family: 'YouSheBiaoTiHei';
        font-size: 19px;
      }
      &-right{
        font-size: 12px;
        zoom: 0.83;
      }
    }
    .main{
      height: 67px;
      border-radius: 8px;
      background: #ffffff;
      margin: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      :deep(.van-count-down){
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .block{
        width: 42px;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        background: #57CFF7;
        border-radius: 8px;
        color: #F74848;
        font-weight: bold;
      }
      .colon{
        margin: 0 7px;
        font-size: 20px;
        color: #F74848;
      }
    }
  }
</style>