<template>
  <div class="betting-number">
    <div class="number-context">
      <div class="title">
        您已参与：<span class="theme-text">{{ drawNumbers.length }} 次</span>
      </div>
      <van-text-ellipsis :content="text" expand-text="展开" collapse-text=" 收起" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  drawNumbers: {
    type: Array,
    default: () => []
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .betting-number{
    margin: 12px 13px 0;
    background: url('@/assets/images/treasure-hunt/cout-down-bg.png') no-repeat;
    background-size: 100% 100%;
    padding: 5px;
    border-radius: 8px;
    .number-context{
      background: #ffffff;
      border-radius: 8px;
      padding: 10px;
      padding-right: 5px;
      font-size: 16px;
      font-size: 16px;
      font-weight: bold;
      color: #000000;
      .title{
        padding: 5px 0;
      }
    }
    :deep(.van-text-ellipsis) {
      color: #646060;
      font-weight: 400;
    }
  }
</style>