<template>
  <div class="page" @scroll="onScrollChange" ref="goodsRef">
    <detail-nav :scroll-top-value="scrollTopValue" :nav-bar-style="navBarStyle" @onLeftClick="onBackClick" />
    <div class="page-context">
      <img v-if="scrollTopValue<30" class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick">
      <img class="prize-img" :src="prizeInfo.thumbPics">
      <git-wrapper v-if="prizeInfo.joinFlag === 'N' && prizeInfo.status === 'LISTED' 
      && prizeInfo.isProcess === 'N' && prizeInfo.startTime < new Date().getTime()" @onReceive="onReceive"></git-wrapper>
      <div class="main">
        <progress-type v-if="prizeInfo.autoLottery === 'N'" :prize-info="prizeInfo"></progress-type>
        <goods-info :prize-info="prizeInfo"></goods-info>
      </div>
      <prize-count-down
        v-if="(prizeInfo.autoLottery === 'Y' && prizeInfo.endTime > new Date().getTime()) 
        || prizeInfo.startTime > new Date().getTime()"
        :prize-info="prizeInfo"
        @onRefresh="getInfo"
      ></prize-count-down>
      <betting-number v-if="drawNumbers" :text="drawNumbers" :draw-numbers="prizeInfo.drawNumbers"></betting-number>
      <winners v-if="prizeInfo.winnerInfo" :prize-info="prizeInfo"></winners>
      <div class="details" v-if="prizeInfo.desc">
        <div class="details-title">
          <div class="text">抽奖规则</div>
        </div>
        <div v-html="prizeInfo.desc"></div>
      </div>
      <div style="height: 100px;"></div>
    </div>
    <div
      v-if="((prizeInfo.autoLottery === 'N' && prizeInfo.targetPerson > prizeInfo.realPerson) || 
      (prizeInfo.autoLottery === 'Y' && prizeInfo.endTime > new Date().getTime()) 
      || prizeInfo.isProcess === 'Y') && new Date().getTime() > prizeInfo.startTime"
      class="footer" :class="{ 'footer-bottom': isIphoneX, 'disabled': prizeInfo.isProcess === 'Y' }"
      @click="onReceive"
    >
      {{ prizeInfo.isProcess === 'Y' ? '开奖中...' : '参与兑换夺宝号' }}
    </div>
    <join-success-popup ref="joinSuccessRef"></join-success-popup>
    <betting-popup
      ref="bettingRef"
      :prize-info="prizeInfo"
      :max="prizeInfo.autoLottery === 'N' ? (prizeInfo.targetPerson - prizeInfo.realPerson) : 100"
      @onJoin="onJoin"
    ></betting-popup>
    <betting-result
      :prize-info="prizeInfo"
      ref="bettingResultRef"
    ></betting-result>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { prizeDetail, joinSubmit } from '@/api/integral-treasure-hunt'
import { JoinSuccessPopup, GitWrapper, DetailNav, 
  ProgressType, GoodsInfo, PrizeCountDown, BettingPopup, 
  BettingNumber, Winners, BettingResult } from './components/index'
import { showDialog } from 'vant'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const id = ref('')
const joinSuccessRef = ref(null)
const bettingRef = ref(null)
const bettingResultRef = ref(null)
const drawNumbers = ref('')
const data = reactive({
  prizeInfo: {},
  navBarStyle: {
    backgroundColor: '#ffffff',
    position: 'fixed'
  },
})
const { prizeInfo, navBarStyle } = toRefs(data)
const scrollTopValue= ref(-1)
const ANCHOR_SCROLL_TOP = 64
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
  navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
}
const onBackClick = () => {
  router.go(-1)
}
const onReceive = ()=> {
  if (prizeInfo.value.isProcess !== 'Y') {
    bettingRef.value.show = true
    bettingRef.value.setpNum = 1
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
// 下注
const onJoin = (bettingNum) => {
  joinSubmit({ num: bettingNum, sessionId: id.value }).then(res => {
    joinSuccessRef.value.show = true
    bettingRef.value.show = false
    setTimeout(() => {
      getInfo()
    }, 500)
    store.dispatch('IntegralData')
  }).catch(() => {
    getInfo()
  })
}
// 开奖提示
const winningResults = () => {
  const winningIds = localStorage.getItem('winning-list')
  const winningIdArr = winningIds ? winningIds.split(',') : []
  if (winningIdArr.length === 0 || !winningIdArr.includes(id.value)) { // 未提示过
    winningIdArr.push(id.value)
    localStorage.setItem('winning-list', winningIdArr.join(','))
    nextTick(() => {
      bettingResultRef.value.show = true
    })
  }
}
const getInfo  = () => {
  prizeDetail({ sessionId: id.value }).then(res => {
    prizeInfo.value = res.data
    if (prizeInfo.value.drawNumbers?.length > 0) {
      drawNumbers.value = prizeInfo.value.drawNumbers.join('、')
    }
    joinSuccessRef.value.show = false
    if (prizeInfo.value.status === 'PROCESSED' && drawNumbers.value) { // 已开奖并有参与
      winningResults()
    }
  })
}
onMounted(() => {
  id.value = route.query.id
  getInfo()
  store.dispatch('IntegralData')
})
</script>
<style scoped lang='scss'>
  .page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    overflow: hidden;
    overflow-y: auto;
    background: #FAFAFA;
    &-context{
      .prize-img{
        width: 100%;
        height: auto;
        display: block;
      }
      .back-img{
        width: 32px;
        height: 32px;
        position: absolute;
        left: 16px;
        top: 44px;
        display: block;
      }
      .main{
        margin: 12px 13px 0;
        background: #ffffff;
        border-radius: 8px;
      }
      .details{
        margin: 12px 13px 0;
        background: #ffffff;
        border-radius: 8px;
        padding: 0 15px 15px;
        &-title{
          display: flex;
          justify-content: center;
          .text{
            font-size: 19px;
            color: #08090D;
            padding: 8px 25px;
            background: #E8F9FF;
            border-radius: 0px 0px 11px 11px;
          }
        }
        :deep(p){
          font-size: 12px;
          line-height: 20px;
          color: #08090D;
        }
        :deep(img){
          max-width: 100%;
        }
      }
    }
    .footer{
      position: fixed;
      bottom: 0;
      width: calc(100% - 22px);
      margin: 0 11px;
      height: 44px;
      line-height: 44px;
      background: #FF6000;
      text-align: center;
      font-size: 18px;
      color: #ffffff;
      box-sizing: border-box;
      border-radius: 22px;
    }
    .footer.disabled {
      background: #E8E8E8;
      height: 44px;
      border-radius: 22px;
      color: #AFAFAF;
    }
    .footer.footer-bottom{
      bottom: 34px;
    }
  }
</style>