<template>
  <div class="info-result-page">
    <navigation-bar pageName="激活额度" @onLeftClick="onBackClick"></navigation-bar>
    <div class="info-result-page-context" id="show_words" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="title">待获取借款额度</div>
      <div class="title-decs">资料补充完成即可提交授信</div>
      <div class="tips">
        <img src="@/assets/icons/cust-info-credit.png" />
        <div class="text">智能匹配贷款方案，超20000万已放款，请真实填写信息</div>
      </div>
      <div class="item">
        <div class="item-left">
          <div class="img">
            <img src="@/assets/icons/cust-info-real-name.png" />
          </div>
          <div class="text">
            <div class="p1">身份证认证</div>
            <div class="p2">保证您的账号安全</div>
          </div>
        </div>
        <div class="item-right close">已实名</div>
      </div>
      <div class="item">
        <div class="item-left">
          <div class="img">
            <img src="@/assets/icons/cust-info.png" />
          </div>
          <div class="text">
            <div class="p1">基本信息</div>
            <div class="p2">完善资料 提高信誉</div>
          </div>
        </div>
        <div class="item-right theme-linear-gradient" @click="handleInfo">去补充</div>
      </div>
      <!-- <div class="agreement">
          <van-checkbox v-model="agreement" icon-size="17px" :checked-color="$global.THMEM"></van-checkbox>
          <div class="text">
            <span>我已阅读并同意</span>
            <span class="deepen">
              <span @click="onAgreement">《授信额度服务协议》</span></span>
          </div>
        </div> -->
    </div>
    <footer-fiexd>
      <div class="apply-btn theme-linear-gradient">继续申请</div>
    </footer-fiexd>
    <agreement-popup
      ref="agreementPopupRef"
      url="/agreement/product-service.htm"
      title="《授信额度服务协议》"
    />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import AgreementPopup from '@/components/AgreementPopup'
const { proxy } = getCurrentInstance()
const router = useRouter()
const isIphoneX = window.isIphoneX
const agreement = ref(true)
const agreementPopupRef = ref(null)
const onBackClick = () => {
  router.go(-1)
}
const onAgreement = () => {
  agreementPopupRef.value.show = true
}
const handleInfo = () => {
  router.replace('/cust-info')
}
</script>

<style lang="scss" scoped>
.info-result-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    position: relative;
    padding-left: 25px;
    .title {
      font-size: 22px;
      font-weight: 500;
      color: #363636;
      line-height: 30px;
      margin-top: 13px;
    }
    .title-decs {
      font-size: 13px;
      font-weight: 400;
      color: #868686;
      line-height: 18px;
      margin-top: 2px;
    }
    .tips {
      display: flex;
      align-items: center;
      margin-top: 10px;
      margin-bottom: 24px;
      img {
        width: 21px;
        height: 22px;
        z-index: 10;
      }
      .text {
        background: #ecfcfc;
        border-radius: 0px 10px 10px 0px;
        margin-left: -10px;
        padding-left: 15px;
        padding-right: 9px;
        font-size: 12px;
        color: #0b564c;
        line-height: 17px;
        z-index: 9;
      }
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0px 0px 8px 2px #f4f4f4;
      border-radius: 2px;
      padding: 0 18px;
      margin-right: 25px;
      height: 81px;
      .item-left {
        display: flex;
        align-items: center;
        .img {
          img {
            width: 24px;
            height: 24px;
            margin-right: 13px;
          }
        }
        .p1 {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #363636;
          line-height: 22px;
        }
        .p2 {
          font-size: 12px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #999999;
          line-height: 17px;
          margin-top: 1px;
        }
      }
      .item-right {
        width: 59px;
        height: 27px;
        border-radius: 18px;
        line-height: 27px;
        font-size: 13px;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
      }
      .item-right.close {
        background: #abb5c4;
      }
    }
    .item + .item {
      margin-top: 14px;
    }
  }
}
.agreement {
  display: flex;
  margin: 10px 0 10px 36px;
  align-items: center;
  .text {
    margin-left: 3px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #a8a8a8;
    line-height: 17px;
    .deepen {
      color: #363636;
    }
  }
}
.apply-btn {
  width: 100%;
  margin: 0 auto;
  height: 49px;
  border-radius: 25px;
  line-height: 49px;
  color: #ffffff;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  text-align: center;
}
</style>