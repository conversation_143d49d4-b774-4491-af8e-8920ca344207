<template>
  <div class="info-page" id="info-page">
    <navigation-bar pageName="基本信息" @onLeftClick="onBackClick"></navigation-bar>
    <div class="info-page-context" id="show_words" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="title">
        <img src="@/assets/images/credit/credit-percent80.png">
        <div class="title-tips">
          <div class="big-title">
            <span>填写本人资料</span>
            <span class="hightlight">获最高20万额度</span>
          </div>
          <div class="small-title">准确填写有效信息，有助于额度审批</div>
        </div>
      </div>
      <van-form class="margin-top-sm" ref="formRef" @submit="onSubmit">
        <van-cell-group>
          <van-field
            v-model="form.educationName"
            label="学历"
            is-link
            readonly
            placeholder="请选择您的学历"
            @click="showEducation = true"
            :rules="[{ required: true, message: '请选择您的学历' }]"
          />
          <van-field
            v-model="form.companyPositionName"
            label="职业"
            is-link
            readonly
            placeholder="请选择您的职业"
            @click="showCompanyPosition = true"
            :rules="[{ required: true, message: '请选择您的职业' }]"
          />
        </van-cell-group>
        <van-cell-group class="margin-top-sm">
          <van-field
            v-model="form.company"
            label="公司名称"
            placeholder="如：***市*****有限公司"
            :rules="[{ required: true, message: '请填写公司名字' }]"
          />
          <van-field
            v-model="form.companyTele"
            label="办公电话"
            placeholder="010-请填电话或手机号"
            maxlength="13"
            :rules="[{ required: true, message: '请填电话或手机号' }]"
          />
          <van-field
            v-model="form.companyAreaData"
            is-link
            readonly
            label="公司地址"
            placeholder="点击选择省市区"
            :rules="[{ validator: validCompanyArea, message: '请选择省市区' }]"
            @click="showCompanyArea = true"
          />
          <van-field
            v-model="form.companyAddr"
            placeholder="请补充详细地址 如：XX路/街道XX号XX小区XX栋XX"
            type="textarea"
            :rules="[{ required: true, message: '请填写详细地址' }]"
          />
        </van-cell-group>
        <van-cell-group class="margin-top-sm">
          <van-field
            v-model="form.contacts[0].name"
            label="家庭联系人"
            @focus="onFocus"
            @blur="onBlur"
            placeholder="请填写联系人姓名"
            :rules="[{ required: true, message: '请填写联系人姓名' }]"
          />
          <van-field
            v-model="form.contacts[0].phone"
            label="联系人电话"
            placeholder="请填写联系人电话"
            maxlength="11"
            @focus="onFocus"
            @blur="onBlur"
            type="tel"
            :rules="[{ required: true, validator: validatorPhone0, message: '请填写联系人电话' }]"
          />
          <van-field
            v-model="form.contacts[0].relationshipName"
            label="关系"
            is-link
            readonly
            placeholder="请选择联系人关系"
            @click="showRelationship1 = true"
            :rules="[{ required: true, message: '请选择联系人关系' }]"
          />
        </van-cell-group>
        <van-cell-group class="margin-top-sm">
          <van-field
            v-model="form.contacts[1].name"
            label="其他联系人"
            @focus="onFocus"
            @blur="onBlur"
            placeholder="请填写联系人姓名"
            :rules="[{ required: true, message: '请填写联系人姓名' }]"
          />
          <van-field
            v-model="form.contacts[1].phone"
            label="联系人电话"
            placeholder="请填写联系人电话"
            type="tel"
            @focus="onFocus"
            @blur="onBlur"
            maxlength="11"
            :rules="[{ required: true, validator: validatorOtherPhone, message: '请填写联系人电话' }]"
          />
          <van-field
            v-model="form.contacts[1].relationshipName"
            label="关系"
            is-link
            readonly
            placeholder="请选择联系人关系"
            @click="showRelationship2 = true"
            :rules="[{ required: true, message: '请选择联系人关系' }]"
          />
        </van-cell-group>
      </van-form>
      <div class="andriod-empty" ref="andriodRef"></div>
      <footer-fiexd>
        <div class="footer-btn theme-linear-gradient" @click="handleSubmit">提交</div>
      </footer-fiexd>
    </div>
    <van-popup v-model:show="showEducation" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="educationOptions"
        @confirm="onConfirmEducation"
        @cancel="showEducation = false"
      />
    </van-popup>
    <van-popup v-model:show="showCompanyPosition" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="companyPositionOptions"
        @confirm="onConfirmCompanyPosition"
        @cancel="showCompanyPosition = false"
      />
    </van-popup>
    <van-popup v-model:show="showLiveStatus" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="liveStatusOptions"
        @confirm="onConfirmLiveStatus"
        @cancel="showLiveStatus = false"
      />
    </van-popup>
    <van-popup v-model:show="showLiveArea" position="bottom">
      <van-area
        :value="form.LiveDistrictCode"
        :area-list="areaList"
        @confirm="onLiveAreaConfirm"
        @cancel="showLiveArea = false"
      />
    </van-popup>
    <van-popup v-model:show="showMarriage" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="marriageOptions"
        @confirm="onConfirmMarriage"
        @cancel="showMarriage = false"
      />
    </van-popup>
    <van-popup v-model:show="showCompanyArea" position="bottom">
      <van-area
        :value="form.companyDistrictCode"
        :area-list="areaList"
        @confirm="onCompanyAreaConfirm"
        @cancel="showCompanyArea = false"
      />
    </van-popup>
    <van-popup v-model:show="showRelationship1" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="relationship1Options"
        @confirm="onConfirmRelationship1"
        @cancel="showRelationship1 = false"
      />
    </van-popup>
    <van-popup v-model:show="showRelationship2" position="bottom" :safe-area-inset-bottom="true">
      <van-picker
        :columns="relationship2Options"
        @confirm="onConfirmRelationship2"
        @cancel="showRelationship2 = false"
      />
    </van-popup>
  </div>
</template>

<script setup name="CustInfo">
  import { saveCustInfo } from '@/api/customer'
  import NavigationBar from '@/components/NavigationBar'
  import { areaList } from '@vant/area-data'
  import { validPhone } from '@/utils/common'
  const { proxy } = getCurrentInstance()
  const  isIphoneX = window.isIphoneX
  const store = useStore()
  const route = useRoute()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const formRef = ref(null)
  const andriodRef = ref(null)
  const data = reactive({
    form: {
      education: '',
      educationName: '',
      liveStatus: '',
      liveStatusName: '',
      liveProvince: '',
      liveCity: '',
      liveDistrict: '',
      liveAddr: '',
      liveAreaData: '',
      liveDistrictCode: '',
      marriage: '',
      marriageName: '',
      spouseIdCard: '',
      spouseName: '',
      spousePhone: '',
      spouseCompany: '',
      companyPosition: '',
      companyPositionName: '',
      company: '',
      companyTele: '',
      companyAreaData: '',
      companyDistrict: '',
      companyDistrictCode: '',
      companyAddr: '',
      contacts: [
        {
         index: 1,
         name: '',
         phone: '',
         relationship: '' 
        },
        {
         index: 2,
         name: '',
         phone: '',
         relationship: '' 
        }
      ]
    }
  })
  let params = {}
  const { form } = toRefs(data)
  const validLiveArea = (val) => val ? true : false
  const validCompanyArea = (val) => val ? true : false
  const validatorPhone0 = (val) => {
    if(!validPhone(val)) {
      return '请填写正确的手机号码'
    }else if(val === user.value.phone) {
      return '联系电话不能是本人的手机号'
    } else if(val === form.value.contacts[1].phone){
      return '家庭联系人电话和其他联系人电话不能是同一个'
    }
  }
  const validatorOtherPhone = (val) => {
    if(!validPhone(val)) {
      return '请填写正确的手机号码'
    } else if(val === user.value.phone) {
      return '联系电话不能是本人的手机号'
    } else if(val === form.value.contacts[0].phone){
      return '其他联系人电话和家庭联系人电话不能是同一个'
    }
  }
  // 学历
  const showEducation = ref(false)
  const educationOptions = proxy.$global.EDUCATION_OPTIONS
  const onConfirmEducation = (val) => {
    const { selectedOptions } = val
    form.value.education = selectedOptions[0].value
    form.value.educationName = selectedOptions[0].text
    showEducation.value = false
  }
  // 职业
  const showCompanyPosition = ref(false)
  const companyPositionOptions = proxy.$global.COMPANY_POSITION_OPTIONS
  const onConfirmCompanyPosition = (val) => {
    const { selectedOptions } = val
    form.value.companyPosition = selectedOptions[0].value
    form.value.companyPositionName = selectedOptions[0].text
    showCompanyPosition.value = false
  }

  // 居住情况
  const showLiveStatus = ref(false)
  const liveStatusOptions = proxy.$global.LIVE_STATUS_OPTIONS
  const onConfirmLiveStatus = (val) => {
    const { selectedOptions } = val
    form.value.liveStatus = selectedOptions[0].value
    form.value.liveStatusName = selectedOptions[0].text
    showLiveStatus.value = false
  }
  // 婚姻情况
  const showMarriage = ref(false)
  const marriageOptions = proxy.$global.MARRIAGE_OPTIONS
  const onConfirmMarriage = (val) => {
    const { selectedOptions } = val
    form.value.marriage = selectedOptions[0].value
    form.value.marriageName = selectedOptions[0].text
    showMarriage.value = false
  }
  // 居住区域选择
  const showLiveArea = ref(false)
  const onLiveAreaConfirm = (areaValues) => {
    form.value.liveAreaData = areaValues.selectedOptions
        .filter((item) => !!item)
        .map((item) => item.text)
        .join('/')
    form.value.liveProvince = areaValues.selectedOptions[0].text
    form.value.liveCity = areaValues.selectedOptions[1].text
    form.value.liveDistrict = areaValues.selectedOptions[2].text
    form.value.liveDistrictCode = areaValues.selectedOptions[2].code
    showLiveArea.value = false
  }
  // 公司区域选择
  const showCompanyArea = ref(false)
  const onCompanyAreaConfirm = (areaValues) => {
    form.value.companyAreaData = areaValues.selectedOptions
        .filter((item) => !!item)
        .map((item) => item.text)
        .join('/')
    form.value.companyProvince = areaValues.selectedOptions[0].text
    form.value.companyCity = areaValues.selectedOptions[1].text
    form.value.companyDistrict = areaValues.selectedOptions[2].text
    form.value.companyDistrictCode = areaValues.selectedOptions[2].code
    showCompanyArea.value = false
  }
  const showRelationship1 = ref(false)
  const relationship1Options = proxy.$global.RELATIONSHOP1_OPTIONS
  const onConfirmRelationship1 = (val) => {
    const { selectedOptions } = val
    form.value.contacts[0].relationship = selectedOptions[0].value
    form.value.contacts[0].relationshipName = selectedOptions[0].text
    showRelationship1.value = false
  }
  const showRelationship2 = ref(false)
  const relationship2Options = proxy.$global.RELATIONSHOP2_OPTIONS
  const onConfirmRelationship2 = (val) => {
    const { selectedOptions } = val
    form.value.contacts[1].relationship = selectedOptions[0].value
    form.value.contacts[1].relationshipName = selectedOptions[0].text
    showRelationship2.value = false
  }
  const onBackClick = () => {
    router.go(-1)
  }
  const handleSubmit = () => {
    formRef.value.submit()
  }
  const onSubmit = () => {
    saveCustInfo({ custInfo: form.value }).then(res => {
      store.dispatch('GetInfo').then(res => {
        proxy.onToastSucc(() => {
          if(params.whole === '1') {
            router.replace({path: '/product/whole-detail', query: {productId: params.productId}})
          } else {
            router.replace({ name: 'ActiveLoading' })
          }
        },'提交成功')
      })
    })
  }
  const  isAndroid = () => {
    let u = navigator.userAgent;
    let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; // android终端或者uc浏览器
    return isAndroid === true;
  }
  const onFocus = (e) => {
    if(isAndroid()){
      andriodRef.value.style.display = 'block'
      setTimeout(() => {
        andriodRef.value.scrollIntoView(false)
      },300)
    }
  }
  const onBlur = () => {
    andriodRef.value.style.display = 'none'
  }
</script>

<style lang="scss" scoped>
  .info-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      .van-form{
        padding-bottom: 100px;
      }
      .title{
        background: #ffffff;
        display: flex;
        padding: 33px 23px 23px;
        align-items: center;
        img{
          width: 44px;
          height: 44px;
          margin-right: 11px;
        }
        .title-tips{
          display: flex;
          flex-direction: column;
          .big-title{
            display: flex;
            font-weight: 500;
            font-size: 19px;
            color: #373844;
            line-height: 26px;
            .hightlight{
              color: #FF551A;
              margin-left: 8px;
            }
          }
          .small-title{
            font-size: 13px;
            color: #B3B3B3;
            line-height: 18px;
          }
        }
      }
    }
  }
  .form-btn{
    margin: 46px 30px 23px;
  }
  .form-btn.android{
    margin-top: 200px;
  }
  :deep(.van-cell){
    padding: 12px 16px;
  }
  .andriod-empty{
    height: 320px;
    display: none;
  }
</style>