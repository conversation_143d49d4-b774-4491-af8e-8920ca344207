<template>
  <div class="message-notify-page">
    <navigation-bar pageName="消息通知" @onLeftClick="onBackClick"></navigation-bar>
    <div class="message-notify-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="list">
        <div class="list-item" v-for="item in list" :key="item.id">
          <div class="list-item-time">
            <img src="@/assets/icons/time.png">
            <span>{{ parseTime(item.noticeTime || item.createTime) }}</span>
          </div>
          <div class="list-item-info">
            <div class="title solid-bottom">
              {{item.title}}
            </div>
            <div class="content">
              {{item.content}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { getMessageList } from '@/api/home'
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const list = ref([])
  const onBackClick = () => {
    router.go(-1)
  }
  const getList = () => {
    getMessageList({pageNum: 1, pageSize: 100}).then(res => {
      list.value = res.data
      // 已读
      let msgRead = []
      if(res.data.length > 0) {
        res.data.map(item => {
          msgRead.push(item.id)
        })
      }
      localStorage.setItem('msg-read', msgRead.join(','))
    })
  }
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .message-notify-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      background: #F4F4F4;
      .list{
        margin: 0 16px;
        &-item{
          margin-top: 10px;
          &-time{
            display: flex;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 18px;
            padding: 10px 0;
            align-items: center;
            img{
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
          }
          &-info{
            padding: 16px;
            background: #ffffff;
            border-radius: 4px;
            .title{
              padding-bottom: 10px;
              font-size: 15px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 22px;
            }
            .content{
              padding-top: 10px;
              font-size: 13px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 18px;
            }
          }
        }
      }
    }
  }
</style>