<template>
  <section class="page">
    <header>
      <NavigationBar title="额度记录" />
    </header>
    <main>
      <van-pull-refresh v-model="refreshing" @refresh="refresh(0)">
        <!-- <div>ConsumeAmountRecord</div> -->
        <div class="main-card">
          <div class="main-card__title">总额度</div>
          <div class="main-card__amount">
            <sub>￥</sub>
            <span>{{
              formatNumber({
                padRight: 2,
              })(quotaInfo?.ultimaConsum ?? 0)
            }}</span>
          </div>
        </div>
        <van-list
          :immediate-check="false"
          :loading="listIsLoading"
          :error="listError"
          :finished="isLastPage"
          :error-text="listError?.message"
          finished-text="—— 没有更多了 ——"
          @load="onLoad"
        >
          <div class="list-card">
            <van-cell-group :border="false" title="">
              <van-cell
                v-for="(item, index) in list"
                :key="index"
                :label="dayjs(item.accountTime).format('YYYY-MM-DD HH:mm:ss')"
                :title="item.abstractInfo"
                :value="
                  formatNumber({
                    padRight: 2,
                    prefix: (item.crdrFlag === 'DEBIT' ? '+' : '-') + '￥',
                  })(item.amount)
                "
              />
            </van-cell-group>
          </div>
        </van-list>
      </van-pull-refresh>
    </main>
  </section>
</template>

<script setup>
defineOptions({
  name: 'CreditQuotaRecord',
})
import NavigationBar from '@/components/NavigationBar/index2.vue'
import { useAsyncState, useToggle, whenever } from '@vueuse/core'
import { creditQuota, saveContacts, getCreditQuotaRecord } from '@/api/customer'
import global from '@/constant/Global.js'
import formatNumber from 'format-number'
import { useRouteParams, useRouteQuery } from '@vueuse/router'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()
const store = useStore()
const user = computed(() => store.getters.userInfo)

const sceneCode = useRouteParams('sceneCode', '')

import request from '@/plugins/request'
// import { useRequest, usePagination } from 'alova/client'

const {
  data: quotaInfo,
  loading: quotaIsLoading,
  send: refreshQuota,
} = useRequest(
  () =>
    creditQuota({
      requestType: 'query',
      sceneCode: sceneCode.value,
    }),
  {
    immediate: true,
    async middleware(_, next) {
      const { data } = await next()
      return data
    },
  }
)

const {
  data: list,
  page,
  isLastPage,
  error: listError,
  loading: listIsLoading,
  reload: reloadList,
} = usePagination(
  (pageNum, pageSize) =>
    request.Post('/gateway', {
      apiCode: 'SF015',
      pageNum,
      pageSize,
      productId: quotaInfo.value?.productId,
    }),
  {
    initialData: {
      total: 0,
      data: [],
    },
    immediate: false,
    watchingStates: [() => quotaInfo.value?.productId],
    append: true,
  }
)

const { execute: refresh, isLoading: refreshing } = useAsyncState(
  async () => {
    refreshQuota()
    reloadList()
  },
  null,
  {
    immediate: false,
  }
)

async function onLoad() {
  page.value++
}
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 1.2;
  color: #000;
  header {
    flex: none;
    background: #fff;
  }
  main {
    flex: 1;
    height: auto;
    background: #f6f6f6;
    overflow-y: scroll;
    overflow-x: hidden;
    .van-pull-refresh {
      min-height: 100%;
      // :deep(.van-pull-refresh__track) {
      //   min-height: 100%;
      // }
    }
    padding-bottom: var(--safe-area-inset-bottom);
  }
}

.main-card {
  border-radius: 12px;
  border: 5px solid #fff;
  background: linear-gradient(1deg, rgba(255, 255, 255, 0) 50.16%, rgba(70, 113, 235, 0.2) 99.48%);
  // width: 355px;
  // height: 174px;
  margin: 20px 10px 0;
  padding: 20px 24px;
  box-sizing: border-box;
  // position: sticky;
  // top: 20px;
  &__title {
    font-size: 18px;
  }
  &__amount {
    margin-top: 4px;
    font-size: 34px;
    font-weight: 700;
    font-family: 'DIN';
    sub {
      font-size: 0.8em;
    }
    * {
      font: inherit;
    }
  }
}
.list-card {
  background: #fff;
  border-radius: 12px;
  margin: 12px 10px;
  overflow: hidden;
  // padding: 12px;
  .van-cell {
    padding: 10px 20px;
    :deep(.van-cell__title) {
      color: #000;
      font-size: 15px;
    }
    :deep(.van-cell__label) {
      font-size: 14px;
      color: #00000066;
    }
    :deep(.van-cell__value) {
      width: 100px;
      flex: none;
      color: #000;
    }
  }
}
</style>
