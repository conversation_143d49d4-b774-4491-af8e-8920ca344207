<template>
  <div v-if="!loading" class="detail-page" :style="{ background: isVip ? '#F3F6FF' : '#FFF5F4' }">
    <div class="menu-overlay" v-show="subMenuShow" @click="subMenuShow=false"></div>
    <div class="nav-wrapper">
      <img v-if="isVip" class="nav-right-bg" src="@/assets/images/member/nav-right-vip.png" />
      <img v-if="!isVip" class="nav-right-bg" src="@/assets/images/member/nav-right-discount.png" />
      <div class="left" @click="onBackClick">
        <van-icon size="22" name="arrow-left" />
      </div>
      <div class="center">
        <span class="page-title">订单详情</span>
      </div>
      <div class="right" @click="handleSubMenu">
        <van-icon size="27" name="ellipsis" />
        <div class="sub-menu" v-if="subMenuShow">
          <div class="menu-item" @click="$customerService">联系客服</div>
          <div
            v-if="orderData.orderStatus === 'PAIED' || orderData.orderStatus === 'FINISH' || orderData.orderStatus === 'VERIFY_NOPASS'"
            class="menu-item solid-top"
            @click="onApplyReturn"
          >申请售后</div>
        </div>
      </div>
    </div>
    <div class="order-status">
      <img v-if="orderData.orderStatus === 'PAIED'" src="@/assets/images/member/success.png">
      <img v-if="orderData.orderStatus === 'REFUND'" class="fail-img" src="@/assets/images/member/pay-fail.png">
      <img v-if="orderData.orderStatus === 'REFUND_VERIFY'" src="@/assets/images/member/success.png">
      <div class="status-decs">
        <div class="p1">
          {{ orderData.orderStatus === 'PAIED' ? '激活成功' :
          orderData.orderStatus === 'REFUND' ? '已退款' :
          orderData.orderStatus === 'REFUND_VERIFY' ? '退款审核中' :
          '' }}
        </div>
        <div class="p2">
          <span v-if="orderData.orderStatus === 'PAIED'">恭喜您，权益领取成功</span>
          <span v-if="orderData.orderStatus === 'REFUND'">很遗憾，您的权益已取消</span>
          <span v-if="orderData.orderStatus === 'REFUND_VERIFY'">您的申请已提交，请等待审核</span>
          <span v-if="orderData.orderStatus === 'REFUNDING'">您的申请已提交，退款处理中</span>
        </div>
      </div>
    </div>
    <detail-member-info :member-info="memberInfo" :is-vip="isVip"></detail-member-info>
    <detail-order-info :order-data="orderData" :is-vip="isVip">
      <div
        class="bill-btn"
        :class="{ 'is-vip': isVip }"
        v-if="(billData.status === 'WAIT' || billData.status === 'OVERDUE') &&
        orderData.orderStatus !== 'REFUND_VERIFY'" @click="handleRepay"
      >提前支付</div>
      <div v-if="memberInfo.vipCustInfo && memberInfo.vipCustInfo.renew ==='Y'" class="subscription">
        <div class="text" @click="handleSubscription">取消订阅</div>
        <div class="line"></div>
        <div class="index" @click="handleIndex">返回首页</div>
      </div>
      <div class="customer-service" :class="{ 'is-vip': isVip }" @click="$customerService">
        对此单有疑问?去咨询 >
      </div>
    </detail-order-info>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { billDetailByOrderId } from '@/api/bill'
import { memberCustInfo, renewalSwitch, getEuqityOrder } from '@/api/member'
import { showConfirmDialog } from 'vant';
import DetailMemberInfo from './components/detail/DetailMemberInfo'
import DetailOrderInfo from './components/detail/DetailOrderInfo';
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const user = computed(() => store.getters.userInfo)
const { orderId } = route.query
const subMenuShow = ref(false)
const data = reactive({
    billData: {},
    orderData: {},
    memberInfo: {}
  })
const { billData, orderData, memberInfo } = toRefs(data)
const loading = ref(true)
const isVip = computed(() => {
  return memberInfo.value.vipPriceInfo?.vipType === 'VIP'
})
const onBackClick = () => {
    router.go(-1)
}
const handleRepay = () => {
  router.push({ path: '/my/bill/repayment', query: { billId: billData.value.billId } })
}
const handleIndex = () => {
  proxy.$menuRouter('/')
}
const handleSubMenu = () => {
  subMenuShow.value = true
}
// 退款
const onApplyReturn = () => {
  getEuqityOrder({ orderId }).then(res => {
    if(!res.data.vipInvalidTime || new Date().getTime() > res.data.vipInvalidTime) {
      showToast('会员已过期!')
    } else {
      sessionStorage.setItem('equityOrderDetail', JSON.stringify(res.data))
      router.push('/member/order-return?orderId=' + orderId)
    }
  })
}
const handleSubscription = async () => {
  showConfirmDialog({
    title: '提示',
    message: '确认关闭自动续费吗',
  })
  .then(() => {
    renewalSwitch({ id: memberInfo.value.vipCustInfo.id, renewFlag: 'N' }).then(res => {
      proxy.onToastSucc(() => {
        memberCustInfo({ id: orderData.value.productId }).then(res => {
          memberInfo.value = res.data
        })
      },'关闭成功')
    })
  })
  .catch(() => {
    console.log('close')
  });
}
onMounted(() => {
  orderData.value = JSON.parse(sessionStorage.getItem('equityOrderDetail'))
  console.log('orderData.value>>>', orderData.value);
  if (orderData.value.payType === 'PAYLATER') {
    billDetailByOrderId({ orderId }).then(res => {
      billData.value = res.data
    })
  }
  memberCustInfo({ id: orderData.value.productId }).then(res => {
    memberInfo.value = res.data
    loading.value = false
  })
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
</script>
<style scoped lang='scss'>
  .detail-page{
    width: 100%;
    height: 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    // background: #F3F6FF;
    background-size: 100% 377px;
    .nav-wrapper{
      width: 100%;
      height: 44px;
      display: flex;
      justify-content: space-between;
      // 适配手机 stateBar
      // padding-top: 25px;
      padding-top: var(--safe-area-inset-top);
      align-items: center;
      flex-shrink: 0;
      .left {
        padding-left: 12px;
        width: 32px;
        display: flex;
        align-items: center;
      }
      .center {
        display: flex;
        height: 100%;
        flex-grow: 1;
        padding: 0 5px;
        .page-title {
          align-self: center;
          margin: 0 auto;
          font-size: 16px;
          font-weight: 600;
          color: #000000;
          white-space:nowrap;
        }
      }
      .right{
        width: 32px;
        position: relative;
        display: flex;
        padding-right: 12px;
        .sub-menu{
          background: #ffffff;
          position: absolute;
          right: 14px;
          border-radius: 6px;
          padding: 0 10px;
          top: 30px;
          box-shadow:4px 4px 15px #cccccc;
          z-index: 999;
          .menu-item {
            width: 80px;
            text-align: center;
            font-size: 15px;
            height: 44px;
            line-height: 44px;
          }
        }
        .sub-menu::after{
          position: absolute;
          content: ' ';
          width: 0;
          height: 0;
          top: -18px;
          right: 6px;
          border: 10px solid transparent;
          border-bottom: 10px solid #ffffff;
        }
      }
      .nav-right-bg {
        position: absolute;
        width: 121px;
        height: auto;
        top: 0;
        right: 0;
      }
    }
    .order-status{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 10px 0;
      img{
        width: 64px;
        height: 64px;
        display: block;
      }
      img.fail-img{
        width: 54px;
        height: 54px;
      }
      .status-decs{
        margin-left: 10px;
        .p1{
          font-size: 17px;
          color: #000000;
          line-height: 22px;
        }
        .p2{
          font-size: 14px;
          color: #666666;
          line-height: 22px;
          margin-top: 2px;
        }
      }
    }
    .customer-service{
      margin: 14px auto 0;
      text-align: center;
      font-size: 13px;
      color: #FF3324;
      // transform: scale(0.9);
      // transform-origin: center center;
    }
    .customer-service.is-vip {
      color: #5E85FB;
    }
    .bill-btn{
      background: var(--primary-linear-to-bottom);
      margin: 35px 47px 0;
      width: 276px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      font-size: 17px;
      color: #FFFFFF;
      border-radius: 50px;
    }
    .bill-btn.is-vip {
      background: #5E85FB;
    }
    .subscription{
      margin-top: 9px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #000000;
      opacity: .5;
      line-height: 22px;
      .line{
        width: 1px;
        height: 14px;
        background: #000000;
        opacity: .5;
        margin: 0 10px;
      }
    }
  }
  .menu-overlay{
    height: 100vh;
    height: 100dvh;
    width: 100%;
    position: absolute;
    z-index: 998;
  }
</style>