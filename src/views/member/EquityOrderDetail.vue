<template>
  <div class="detail-page">
    <navigation-bar pageName="订单详情" @onLeftClick="onBackClick" />
    <div class="detail-page-content">
      <div class="order-info">
        <div class="goods" v-if="orderData.goodsList">
          <img class="goods-img" :src="orderData.goodsList[0]?.thumbPics">
          <div class="goods-info">
            <div class="name-price">
              <div class="name overflow-2">{{ orderData.goodsList[0]?.spuTitle }}</div>
              <div class="price">¥{{ formatMoney(orderData.goodsList[0]?.salesPrice) }}</div>
            </div>
            <div class="goods-spec">
              <div class="spec">{{ orderData.goodsList[0]?.skuName }}</div>
              <div class="number">x{{ orderData.goodsList[0]?.quantity }}</div>
            </div>
            <div v-if="orderData.orderType ==='ECARD'" class="tips theme-text">请勿将卡密复制截图给他人，谨防诈骗！</div>
          </div>
        </div>
        <div class="order-detail">
          <div class="group-cell">
            <div class="label">商品总价</div>
            <div class="value">¥{{ formatMoney(orderData.totalAmount) }}</div>
          </div>
          <div class="group-cell" v-if="orderData.mbAmount">
            <div class="label">会员优惠</div>
            <div class="value theme-text">-￥{{ formatMoney(orderData.mbAmount) }}</div>
          </div>
          <div class="group-cell">
            <div class="label">合计</div>
            <div class="value theme-text">¥{{ formatMoney(orderData.payAmount) }}</div>
          </div>
          <div class="group-cell">
            <div class="label">订单编号</div>
            <div class="value flex">
              <div class="text">{{ orderData.orderId }}</div>
              <div class="copy" v-clipboard:copy="orderData.orderId"
              v-clipboard:success="clipboardSuccess">复制</div>
            </div>
          </div>
          <div class="group-cell">
            <div class="label">订单状态</div>
            <div class="value theme-text">{{ $global.GOODS_ORDER_STATUS[orderData.status] }}</div>
          </div>
          <div class="group-cell">
            <div class="label">下单时间：</div>
            <div class="value">
              {{ orderData.createTime }}
            </div>
          </div>
          <div class="group-cell" v-if="orderData.orderType ==='TELE' || orderData.orderType ==='DIRECT_ADD'">
            <div class="label">直充账号：</div>
            <div class="value">
              {{ orderData.virtualMsg.chargeAccount }}
            </div>
          </div>
        </div>
      </div>
      <div v-if="orderData.addressInfo" class="address-info">
        <div class="title">物流信息</div>
        <div class="name-phone">
          <img src="@/assets/images/goods/address-mini-icon.png">
          <div class="name">{{ orderData.addressInfo?.contactName }}</div>
          <div class="phone">{{ orderData.addressInfo?.contactTel }}</div>
        </div>
        <div class="address">{{ orderData.addressInfo?.province + orderData.addressInfo?.city + orderData.addressInfo?.district + orderData.addressInfo?.address }}</div>
      </div>
      <div class="ecard-info" v-if="orderData.orderType ==='ECARD' && orderData.ecardInfo">
        <div class="group-cell">
          <div class="label">卡号</div>
          <div class="value flex align-center">
            <div class="text">{{ orderData.ecardInfo?.cardNo }}</div>
            <div
              class="copy-btn"
              v-clipboard:copy="orderData.ecardInfo?.cardNo"
              v-clipboard:success="clipboardSuccess">复制</div>
          </div>
        </div>
        <div class="group-cell h">
          <div class="label">卡密</div>
          <div class="value long-value">
            <div v-if="pwdShow" class="pwd">{{ orderData.ecardInfo?.password }}</div>
            <div v-else class="pwd">{{ maskPwd(orderData.ecardInfo?.password)}}</div>
            <div v-if="pwdShow" class="copy-btn" v-clipboard:copy="orderData.ecardInfo?.password"
              v-clipboard:success="clipboardSuccess">复制</div>
            <div v-else class="copy-btn" @click="viewPwd">查看卡密</div>
          </div>
        </div>
        <div class="group-cell">
          <div class="label">有效期至</div>
          <div class="value">
            {{ parseTime(orderData.ecardInfo?.invalidTime, '{y}-{m}-{d}') }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="getAppVersion() !== 'VERIFY'" class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="kefu-btn" @click="$customerService">联系客服</div>
    </div>
  </div>
</template>

<script setup>
import { getAppVersion } from '@/utils/auth'
import { showToast } from 'vant';
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const { orderId } = route.query
const pwdShow = ref(false)
const data = reactive({
  orderData: {}
})
const { orderData } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const clipboardSuccess = () => {
  showToast('复制成功！')
  pwdShow.value = false
}
const viewPwd = () => {
  showToast('请勿将卡密复制截图给他人，谨防诈骗！')
  pwdShow.value = true
}
const maskPwd = (str) => {
  const firstFourChar = str.substring(0, 4)
  const lastFourChar = str.substring(str.length - 4)
  const senistiveInfo = str.substring(4, str.length - 4)
  const mask = '*'
  return firstFourChar + senistiveInfo.replace(/[^-]/g, mask) + lastFourChar
}
onMounted(() => {
  orderData.value = JSON.parse(sessionStorage.getItem('equityOrderDetail'))
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
</script>
<style scoped lang='scss'>
  .detail-page{
    width: 100%;
    height: 100%;
    position: absolute;
    &-content{
      overflow-y: auto;
      flex: 1;
      .order-info{
        margin: 10px 15px 0;
        padding: 15px;
        background: #ffffff;
        border-radius: 8px;
        .goods{
          display: flex;
          .goods-img{
            width: 90px;
            height: 90px;
            display: block;
            margin-right: 10px;
          }
          .name-price{
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            line-height: 22px;
            font-weight: bold;
            .name{
              font-size: 14px;
              color: #333333;
            }
            .price{
              flex-shrink: 0;
              color: #000000;
              margin-left: 20px;
            }
          }
          .goods-spec{
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #A6A6A6;
            line-height: 22px;
            .number{
              font-size: 12px;
              color: #000000;
              line-height: 22px;
            }
          }
          .tips{
            font-size: 12px;
            line-height: 22px;
            margin-top: 2px;
          }
        }
        .order-detail{
          margin-top: 20px;
        }
      }
      .ecard-info{
        margin: 10px 15px;
        background: #ffffff;
        border-radius: 8px;
        padding: 7px 15px;
      }
      .address-info{
        margin: 10px;
        padding: 10px;
        padding-bottom: 13px;
        background: #ffffff;
        border-radius: 8px;
        .title{
          font-size: 13px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #222222;
          line-height: 18px;
        }
        .name-phone{
          margin-top: 7px;
          margin-left: 3px;
          display: flex;
          align-items: center;
          font-size: 13px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #222222;
          line-height: 18px;
          img{
            width: 12px;
            height: 13px;
            margin-right: 5px;
          }
          .name{
            margin-right: 8px;
          }
        }
        .address{
          margin-top: 4px;
          margin-left: 20px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 16px;
        }
      }
    }
  }
  .group-cell{
    display: flex;
    justify-content: space-between;
    height: 38px;
    align-items: center;
    font-size: 13px;
    color: #000000;
    .label{
      flex-shrink: 0;
      min-width: 50px;
    }
    .value{
      font-weight: bold;
      .copy {
        color: #333333;
        padding-left: 10px;
        border-left: 1px solid #333333;
        margin-left: 5px;
      }
      .copy-btn{
        height: 20px;
        background: #FFF1ED;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #FF571A;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF571A;
        margin-left: 10px;
        padding: 0 9px;
        flex-shrink: 0;
      }
    }
    .long-value{
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .text{
        color: #666666;
        max-width: 150px;
      }
      .pwd{
        color: #666666;
        font-weight: 400;
      }
      .copy-btn{
        font-weight: 400;
        margin-top: 5px;
      }
    }
    .value.flex{
      font-weight: 400;
      .text{
        color: #666666;
        max-width: 150px;
      }
    }
  }
  .group-cell.h{
    height: unset;
    align-items: flex-start;
    padding: 10px 0;
  }
  .footer.iphonex-bottom{
    // padding-bottom: 50px;
    padding-bottom: calc(15px + var(--safe-area-inset-bottom));
  }
  .footer{
    position: fixed;
    bottom: 0;
    background: #ffffff;
    padding: 15px;
    width: calc(100% - 30px);
    display: flex;
    justify-content: flex-end;
    .kefu-btn{
      width: 84px;
      height: 36px;
      border-radius: 20px;
      border: 1px solid #B2B2B2;
      font-size: 14px;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>