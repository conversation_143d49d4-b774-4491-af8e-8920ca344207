<template>
  <div class="benefit" v-if="checkedMember.benefitList">
    <div class="benefit-item" v-for="(item, index) in checkedMember.benefitList" :key="index">
      <img class="img" :src="item.benefitImg">
      <div class="benefit-name">{{ item.benefitName }}</div>
      <div class="benefit-decs">{{ item.benefitDesc }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .benefit{
    display: flex;
    margin-top: 24px;
    padding: 0 10px;
    &-item{
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .img{
        width: 36px;
        height: 36px;
        display: block;
      }
      .benefit-name{
        font-size: 12px;
        transform: scale(0.9);
        color: #523E21;
        line-height: 16px;
        margin-top: 9px;
      }
      .benefit-decs{
        margin-top: 1px;
        font-size: 12px;
        transform: scale(0.9);
        color: #523E21;
        line-height: 16px;
      }
    }
  }
</style>