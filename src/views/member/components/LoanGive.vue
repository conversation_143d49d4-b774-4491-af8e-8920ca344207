<template>
  <div v-if="benefit?.packageType === 'LOAN_GIVE'" class="benefit-item loan-give">
    <div class="title1">
      {{ benefit.title1 }}
    </div>
    <div class="title2">
      {{ benefit.title2 }}
    </div>
    <div class="title3">
      {{ benefit.title3 }}
    </div>
    <div ref="swiper" class="swiper list-swiper">
      <div class="list swiper-wrapper">
        <div v-for="goodsInfo in benefit.vipBenefitGoodsInfo" class="item swiper-slide">
          <!-- {{ goodsInfo.name }} -->
          <div class="image-wrapper">
            <img :src="goodsInfo.imgUrl" alt="" class="img" />
          </div>
          <div class="content">
            <div class="name">
              {{ goodsInfo.name }}
            </div>
            <div class="price">
              <span class="label">价值</span><span class="symbol">￥</span
              ><span class="value">{{ goodsInfo.marketPrice }}</span>
            </div>
            <button class="btn" @click="handleGoods(goodsInfo)">
              {{
                benefit.receiveNum === 0
                  ? '免费领取'
                  : goodsInfo.receiveNum > 0
                  ? '已领取'
                  : '限领一件'
              }}
            </button>
          </div>
          <!-- <img src="" alt="" /> -->
        </div>
      </div>
      <div class="swiper-pagination"></div>
    </div>
  </div>
</template>

<script setup>
import { useTemplateRef } from 'vue'
import Swiper from 'swiper'
import { Navigation, Pagination, EffectCoverflow, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-coverflow'
import { unrefElement } from '@vueuse/core'
const props = defineProps({
  benefit: {
    type: Object,
    required: true,
  },
})
const emit = defineEmits(['handleGoods'])

const swiperRef = useTemplateRef('swiper')
let swiperInstance = null
onMounted(() => {
  swiperInstance = new Swiper(unrefElement(swiperRef), {
    modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
    slidesPerView: 'auto',
    spaceBetween: 15,
    loop: true,
    centeredSlides: true,
    effect: 'coverflow',
    coverflowEffect: {
      depth: 100,
      modifier: 1,
      rotate: 0,
      scale: 0.9,
      slideShadows: false,
      stretch: 4,
    },
    speed: 800,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: '.swiper-pagination',
    },
  })
  onUnmounted(() => {
    swiperInstance?.destroy()
  })
})
watch(
  () => props.benefit?.vipBenefitGoodsInfo,
  (val) => {
    swiperInstance?.update()
  }
)
const router = useRouter()
function handleGoods(goodsInfo) {
  // console.log(props.benefit, goodsInfo)
  emit('handleGoods', props.benefit, goodsInfo)
  // if (!props.benefit.vipCust) {
  //   emit('buyTips')
  //   return
  // }
  // router.push({
  //   path: '/goods-detail',
  //   query: {
  //     goodsId: goodsInfo.spuId,
  //     skuId: goodsInfo.skuId,
  //     packageId: props.benefit.id,
  //     shoppingFlag: 'V',
  //   },
  // })
}
</script>

<style lang="scss" scoped>
.loan-give {
  margin: -13px -13px 0;
  // padding: 6px;
  border: 6px solid #fff;
  border-radius: 20px;
  background: linear-gradient(180deg, rgba(88, 129, 250, 0.3) 0%, rgba(88, 129, 250, 0) 15.84%);
  // height: 300px;
  // padding: 13px;
  padding-top: 12px;
  .title1 {
    margin: 0 auto;
    background-image: url('@/assets/images/member/main-card-title-bg.png');
    width: calc(368px / 2);
    height: calc(58px / 2);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 17px;
    font-weight: 600;
  }
  .title2 {
    font-size: 14px;
    color: #cfcfcf;
    font-weight: 600;
    text-align: center;
    margin-top: 6px;
  }
  .title3 {
    font-size: 14px;
    color: #cfcfcf;
    font-weight: 600;
    text-align: center;
    margin-top: 6px;
  }
  .list-swiper {
    // padding-bottom: 40px;
    margin-top: 20px;
    height: calc(560px / 2);
    .swiper-wrapper {
      .swiper-slide {
        width: calc(272px / 2);
        // height: calc(400px / 2);
        // background-color: red;
        .image-wrapper {
          background-image: url('@/assets/images/member/loan-give-bg.png');
          background-size: 100% 100%;
          width: calc(272px / 2);
          height: calc(323px / 2);
          box-sizing: border-box;
          padding: 30px 5px 5px;
          border-radius: 10px;
          overflow: hidden;
          .img {
            width: 100%;
            height: 100%;
            display: block;
            border-radius: 10px;
            background-color: #fff;
          }
        }
        .content {
          margin-top: 4px;
          .name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.2;
          }
          .price {
            color: #bebcbc;
            font-size: 15px;
            margin-top: 4px;
          }
          .btn {
            width: calc(220px / 2);
            height: calc(60px / 2);
            background-color: var(--primary-color);
            border-radius: 15px;
            border: none;
            color: #fff;
            font-size: 15px;
            font-weight: 400;
            border-radius: 999vw;
            display: block;
            margin: 4px auto 0;
          }
        }
      }
    }
    .swiper-pagination {
      :deep() {
        .swiper-pagination-bullet {
          width: 5px;
          height: 5px;
          // background-color: #fff;
          // opacity: 1;
          border-radius: 999vw;
          margin: 0 2px;
        }
        .swiper-pagination-bullet-active {
          background-color: #5881fa;
          width: 20px;
        }
      }
    }
  }
}
</style>
