<template>
  <div class="member-header" :style="rootStyle">
    <div class="menu-overlay" v-show="subMenuShow" @click="subMenuShow = false"></div>
    <div class="nav-wrapper" :style="{ background: background }">
      <div class="left" :style="{ color: color }" @click="onBackClick">
        <van-icon size="22" name="arrow-left" />
      </div>
      <div class="center">
        <span class="page-title" :style="{ color: color }">{{ title }}</span>
      </div>
      <div class="right" @click="subMenuShow = true">
        <van-icon size="22" name="ellipsis" :style="{ color: color }" />
        <div class="sub-menu" v-if="subMenuShow">
          <div class="menu-item" v-if="isMember" @click="routerMemberOrder">会员订单</div>
          <div class="menu-item solid-top" @click="$customerService">在线客服</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const subMenuShow = ref(false)
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  isMember: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => {},
  },
  background: {
    type: String,
    default: '#ffffff',
  },
  color: {
    type: String,
    default: '#333333',
  },
  rootStyle: {
    type: Object,
    default() {
      return {}
    },
  },
})

const emit = defineEmits(['back'])
// 会员订单详情
const routerMemberOrder = () => {
  console.log('props.orderInfo>>>', props.orderInfo)
  if (props.orderInfo.orderId) {
    sessionStorage.setItem('equityOrderDetail', JSON.stringify(props.orderInfo))
    router.push('/member/order-detail?orderId=' + props.orderInfo.orderId)
  }
}

function onBackClick() {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang="scss">
.member-header {
  background: linear-gradient(180deg, #f43828, #f57165);
}
.nav-wrapper {
  width: 100%;
  height: 44px;
  display: flex;
  justify-content: space-between;
  // 适配手机 stateBar
  padding-top: 25px;
  padding-top: var(--safe-area-inset-top);
  align-items: center;
  flex-shrink: 0;
  .left {
    padding-left: 6px;
    width: 32px;
    display: flex;
    align-items: center;
  }
  .center {
    display: flex;
    height: 100%;
    flex-grow: 1;
    padding: 0 5px;
    .page-title {
      align-self: center;
      margin: 0 auto;
      font-size: 18px;
      color: #333333;
      white-space: nowrap;
    }
  }
  .right {
    width: 32px;
    position: relative;
    display: flex;
    .sub-menu {
      background: #ffffff;
      position: absolute;
      right: 5px;
      border-radius: 6px;
      padding: 0 10px;
      top: 30px;
      box-shadow: 4px 4px 15px #cccccc;
      z-index: 999;
      .menu-item {
        width: 80px;
        text-align: center;
        font-size: 15px;
        height: 44px;
        line-height: 44px;
      }
    }
    .sub-menu::after {
      position: absolute;
      content: ' ';
      width: 0;
      height: 0;
      top: -18px;
      right: 6px;
      border: 10px solid transparent;
      border-bottom: 10px solid #ffffff;
    }
  }
}
.menu-overlay {
  height: 100vh;
  height: 100dvh;
  width: 100%;
  position: absolute;
  z-index: 998;
}
</style>
