<template>
  <div class="member-info" v-if="memberInfo.vipPriceInfo">
    <div class="member-data">
      <div class="member-name">轻享花如意卡 · {{ memberInfo.vipPriceInfo.vipName }}</div>
      <div class="member-price">
        <span>会员优惠价：{{ memberInfo.vipPriceInfo.discountPrice }}</span>
        <span v-if="memberInfo.vipPriceInfo.discountPrice != memberInfo.vipPriceInfo.price" class="discount">原价：{{ memberInfo.vipPriceInfo.price }}</span>
      </div>
      <div class="valid-time">{{ parseTime(memberInfo.vipCustInfo.invalidTime, '{y}/{m}/{d}') }} 到期</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  memberInfo: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .member-info{
    background: linear-gradient( 93deg, #FBEEDF 0%, #FDFAF1 100%);
    border-radius: 4px;
    padding: 13px 0 13px 20px;
    margin: 0 52px;
    display: flex;
    position: relative;
    .member-name{
      font-weight: bold;
      font-size: 14px;
      color: #84714D;
      line-height: 16px;
    }
    .member-price{
      font-weight: bold;
      font-size: 12px;
      color: #84714D;
      line-height: 12px;
      zoom: 0.85;
      margin-top:4px;
      .discount{
        margin-left: 8px;
        text-decoration: line-through;
      }
    }
    .valid-time{
      margin-top: 4px;
      font-weight: bold;
      font-size: 12px;
      color: #84714D;
      zoom: 0.9;
    }
  }
</style>
