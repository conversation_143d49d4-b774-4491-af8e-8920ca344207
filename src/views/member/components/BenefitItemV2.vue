<template>
  <div class="benefit-list">
    <template v-for="(benefit, index) in benefitList">
      <!-- 开卡礼 -->
      <loan-give
        v-if="benefit.packageType === 'LOAN_GIVE'"
        :benefit="benefit"
        @handleGoods="handleGoods"
      ></loan-give>

      <!-- 商品权益 -->
      <div v-else-if="benefit.packageType === 'GOODS'" class="benefit-item goods">
        <div v-if="benefit.title1 || benefit.title2" class="title-wrapper">
          <span v-if="benefit.title1" class="title1 title-box">
            {{ benefit.title1 }}
          </span>
          <span v-if="benefit.title2" class="title2">
            {{ benefit.title2 }}
          </span>
        </div>
        <div class="content">
          <div class="title-wrapper">
            <img src="@/assets/images/member/goods-title-icon.png" alt="" class="icon" />
            <span class="title3">
              {{ benefit.title3 }}
            </span>
          </div>
          <div class="list-wrapper">
            <div class="list">
              <div
                v-for="goodsInfo in benefit.vipBenefitGoodsInfo"
                class="item"
                :key="goodsInfo.id"
                @click="handleGoods(benefit, goodsInfo)"
              >
                <div class="image-wrapper">
                  <img :src="goodsInfo.imgUrl" alt="" class="img" />
                </div>
                <div class="price">
                  <span class="label">仅需</span><span class="symbol">￥</span
                  ><span class="value">{{ goodsInfo.price }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="title2">
          {{ benefit.title2 }}
        </div>
        <div class="title3">
          {{ benefit.title3 }}
        </div> -->
      </div>

      <!-- 优惠券权益 -->
      <div v-else-if="benefit.packageType === 'COUPON'" class="benefit-item coupon">
        <div class="title-wrapper">
          <span class="title1 title-box">
            {{ benefit.title1 }}
          </span>
          <span class="title2">
            {{ benefit.title2 }}
          </span>
        </div>
        <div class="content">
          <div class="list-wrapper">
            <div class="list">
              <div v-for="coupon in benefit.vipBenefitGoodsInfo" class="item">
                <img
                  v-if="coupon.receiveNum > 0"
                  src="@/assets/images/member/coupon-receive.png"
                  alt=""
                  class="receive-icon"
                />

                <div class="body">
                  <div class="discount">
                    <span class="symbol">￥</span>
                    <span class="value">{{ coupon.ticketDefine.ticketDiscount.value }}</span>
                  </div>
                  <div class="desc">
                    <span
                      v-if="
                        coupon.ticketDefine.ticketSpuRel.type === 'NONE' &&
                        coupon.ticketDefine.ticketThreshold.type === 'NONE'
                      "
                      >无门槛</span
                    >
                    <span v-else-if="coupon.ticketDefine.ticketSpuRel.type === 'NONE'">
                      满{{ coupon.ticketDefine.ticketThreshold.amount }}元可用
                    </span>
                    <span v-else-if="coupon.ticketDefine.ticketSpuRel.type === 'SPU'">
                      指定商品可用
                    </span>
                    <span v-else>
                      {{ coupon.name }}
                    </span>
                  </div>
                </div>
                <div class="footer">
                  <button
                    class="receive-btn"
                    :disabled="coupon.receiveNum > 0"
                    @click="handleGoods(benefit, coupon)"
                  >
                    {{ coupon.receiveNum > 0 ? '已领取' : '领取' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 优惠券礼包权益 -->
      <div v-else-if="benefit.packageType === 'COUPON_PACKAGE'" class="benefit-item coupon-package">
        <div class="title-wrapper">
          <span class="title1 title-box">
            {{ benefit.title1 }}
          </span>
          <span class="title2">
            {{ benefit.title2 }}
          </span>
        </div>
        <div v-for="goodsInfo in benefit.vipBenefitGoodsInfo" class="content">
          <div class="list-wrapper">
            <div class="list">
              <div v-for="coupon in goodsInfo.ticketList" class="item">
                <img
                  v-if="goodsInfo.receiveNum > 0"
                  src="@/assets/images/member/coupon-receive.png"
                  alt=""
                  class="receive-icon"
                />
                <div class="body">
                  <div class="discount">
                    <span class="symbol">￥</span>
                    <span class="value">{{ coupon.ticketDiscount.value }}</span>
                  </div>
                  <div class="desc">
                    <span
                      v-if="
                        coupon.ticketSpuRel.type === 'NONE' &&
                        coupon.ticketThreshold.type === 'NONE'
                      "
                      >无门槛</span
                    >
                    <span v-else-if="coupon.ticketSpuRel.type === 'NONE'">
                      满{{ coupon.ticketThreshold.amount }}元可用
                    </span>
                    <span v-else-if="coupon.ticketSpuRel.type === 'SPU'"> 指定商品可用 </span>
                    <span v-else>
                      {{ coupon.name }}
                    </span>
                  </div>
                </div>
                <div class="footer">
                  <button
                    class="receive-btn"
                    :disabled="goodsInfo.receiveNum > 0"
                    @click="handleGoods(benefit, goodsInfo)"
                  >
                    {{ goodsInfo.receiveNum > 0 ? '已领取' : '领取' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button
            class="one-receive-btn"
            :disabled="goodsInfo.receiveNum > 0"
            @click="handleGoods(benefit, goodsInfo)"
          >
            一键领取
          </button>
        </div>
      </div>
      <!-- 试用商品 -->
      <div v-else-if="benefit.packageType === 'TRYOUT_GOODS'" class="benefit-item tryout-goods">
        <div class="title-wrapper">
          <span class="title1 title-box">
            {{ benefit.title1 }}
          </span>
          <span class="title2">
            {{ benefit.title2 }}
          </span>
        </div>
        <div class="content">
          <div class="list-wrapper">
            <div class="list">
              <div
                v-for="goodsInfo in benefit.vipBenefitGoodsInfo"
                class="item"
                @click="handleGoods(benefit, goodsInfo)"
              >
                <div class="img-wrapper">
                  <img :src="goodsInfo.imgUrl" alt="" class="img" />
                </div>
                <div class="info-wrapper">
                  <div class="name">
                    {{ goodsInfo.name }}
                  </div>
                  <div class="tag-wrapper">
                    <div class="tag">
                      <img src="@/assets/images/member/VIP-icon.png" alt="" class="icon" />
                      <span>会员尝鲜区</span>
                    </div>
                  </div>
                  <div class="price-wrapper">
                    <div class="price-wrapper-item">
                      <div class="price">
                        <span class="symbol">￥</span>
                        <span class="value">{{ goodsInfo.price }}</span>
                      </div>
                      <!-- <div class="price price-2">
                        <span class="symbol">￥</span>
                        <span class="value">{{ goodsInfo.salePrice }}</span>
                      </div> -->
                    </div>
                    <button class="btn">
                      <img src="@/assets/images/member/cart.png" alt="" class="icon" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 虚拟商品 -->
      <div v-else-if="benefit.packageType === 'VIRTUAL_GOODS'" class="benefit-item virtual-goods">
        <div class="title-wrapper">
          <span class="title1 title-box">
            {{ benefit.title1 }}
          </span>
          <span class="title2">
            {{ benefit.title2 }}
          </span>
        </div>
        <div class="content">
          <!-- {{ benefit.title3 }} -->

          <div class="list-wrapper">
            <div class="list">
              <div v-for="goodsInfo in benefit.vipBenefitGoodsInfo" class="item">
                <div class="img-wrapper">
                  <div class="img-wrapper-item">
                    <img src="@/assets/images/logo.png" alt="" class="img" />
                    <div class="name">极享VIP</div>
                  </div>
                  <span class="interval">+</span>
                  <div class="img-wrapper-item">
                    <img :src="goodsInfo.imgUrl" alt="" class="img" />
                    <div class="name">{{ goodsInfo.name }}</div>
                  </div>
                </div>
                <div class="price">
                  <span>仅需</span><span class="symbol">￥</span
                  ><span class="value">{{ goodsInfo.price }}</span
                  ><span>/月</span>
                </div>

                <button
                  class="receive-btn"
                  :disabled="goodsInfo.receiveNum > 0"
                  @click="handleGoods(benefit, goodsInfo)"
                >
                  {{ goodsInfo.receiveNum > 0 ? '我已领取' : '我选它' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他 -->
      <div v-else class="benefit-item" :data-type="benefit.packageType" hidden></div>

      <div v-if="index === 0 && adList?.length > 0" class="ad-banner">
        <div class="header">
          <div class="title-box">
            <span>抵扣劵仅会员可抢</span>
          </div>
        </div>
        <div class="body">
          <van-swipe class="ad-swipe" :autoplay="3000">
            <van-swipe-item v-for="ad in adList">
              <img :src="ad.pic" alt="" @click="$adRouter(ad)" />
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
    </template>

    <div class="customer-service benefit-item">
      <div class="title-wrapper">
        <div class="title-box">
          <span class="title1"> 专属客服 </span>
          <span class="title2"> </span>
        </div>
      </div>
      <div class="content" @click="data?.vipCust ? $customerService() : $emit('buyTips')">
        <img src="@/assets/images/member/customer-service.png" alt="" class="img" />
        <div class="text-wrapper">
          <div class="title">专属客服优先服务 优先接待</div>
          <button v-if="!!data?.vipCust" class="btn">
            <span>立即咨询</span><van-icon name="down" class="icon" />
          </button>
          <div v-else class="sub-title">开通会员即可享受</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useTemplateRef, watch } from 'vue'
import { Toast, showLoadingToast, closeToast, showSuccessToast, showDialog } from 'vant'
import { useAsyncState, whenever } from '@vueuse/core'

import { memberBenefitList, receiveSubmit } from '@/api/member'
import LoanGive from './LoanGive.vue'

const props = defineProps({
  data: {
    type: Object,
  },
  benefitList: {
    type: Array,
    default: () => [],
  },
  adList: {
    type: Array,
    default: () => [],
  },
})
const emit = defineEmits(['buyTips', 'handleGoods', 'refresh'])

// const {
//   state: benefitList,
//   isLoading: benefitListIsLoading,
//   execute: getBenefitList,
// } = useAsyncState(
//   async () =>
//     (
//       await memberBenefitList({
//         id: props.data?.vipPrice?.id,
//       })
//     )?.data ?? [],
//   [],
//   {
//     immediate: !!props.data?.vipPrice?.id,
//     resetOnExecute: false,
//   }
// )

// watch(
//   () => props.data,
//   () => {
//     if (props.data?.vipPrice?.id) {
//       getBenefitList()
//     } else {
//       benefitList.value = []
//     }
//   }
// )
function buyTips() {
  emit('buyTips')
}
const router = useRouter()
async function handleGoods(benefit, goodsInfo) {
  console.log(benefit, goodsInfo)
  emit('handleGoods', benefit, goodsInfo)

  // if (!props.data?.vipCust) {
  //   emit('buyTips')
  //   return
  // }

  // if (benefit.periodNum === benefit.receiveNum) {
  //   showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
  //   return
  // }

  // if (benefit.packageType === 'COUPON') {
  //   // if (benefit.periodNum === benefit.receiveNum) {
  //   //   showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
  //   //   return
  //   // }
  //   if (goodsInfo.receiveNum === 0) {
  //     await receiveSubmit({ id: goodsInfo.id }).then((res) => {
  //       showSuccessToast('领取成功')
  //     })
  //     // getBenefitList()
  //     emit('refresh')
  //   }

  //   return
  // }

  // if (benefit.packageType === 'COUPON_PACKAGE') {
  //   // if (benefit.periodNum === benefit.receiveNum) {
  //   //   showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
  //   //   return
  //   // }
  //   if (goodsInfo.receiveNum === 0) {
  //     await receiveSubmit({ id: goodsInfo.id }).then((res) => {
  //       showSuccessToast('领取成功')
  //     })
  //     // getBenefitList()
  //     emit('refresh')
  //   }

  //   return
  // }

  // // if (benefit.packageType === 'TRYOUT_GOODS') {
  // // return
  // // }

  // if (goodsInfo.categoryGroup === 'COUPON') {
  //   router.push({
  //     path: '/goods-coupon',
  //     query: {
  //       goodsId: goodsInfo.spuId,
  //       skuId: goodsInfo.skuId,
  //       packageId: benefit.id,
  //       shoppingFlag: 'V',
  //     },
  //   })
  //   return
  // }

  // router.push({
  //   path: '/goods-detail',
  //   query: {
  //     goodsId: goodsInfo.spuId,
  //     skuId: goodsInfo.skuId,
  //     packageId: benefit.id,
  //     shoppingFlag: 'V',
  //   },
  // })
}

function handleCustomerService() {
  console.log('handleCustomerService')
}

function handleAd(ad) {
  console.log(ad)
}
</script>

<style lang="scss" scoped>
.benefit-list {
  font-size: 14px;
  padding: 13px;
  // margin-bottom: 20px;
  .benefit-item {
    + .benefit-item {
      margin-top: 10px;
    }
  }
}
.title-box {
  border-image: url('@/assets/images/member/goods-title-bg.png') 30 30 26 30 fill;
  border-width: 6px 15px 4px 15px;
  border-style: solid;
  border-image-width: 15px 15px 13px 15px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  color: #2e3a58;
  // margin-bottom: 8px;
}

.goods,
.coupon,
.coupon-package,
.tryout-goods,
.virtual-goods,
.customer-service {
  .title-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
  }
  .title1 {
    // background-image: url('@/assets/images/member/goods-title-bg.png');
    // border-image: url('@/assets/images/member/goods-title-bg.png') 30 30 26 30 fill;
    // border-width: 6px 15px 4px 15px;
    // border-style: solid;
    // border-image-width: 15px 15px 13px 15px;
    // display: inline-block;
    // font-size: 15px;
    // font-weight: 600;
    // color: #2e3a58;
    margin-bottom: 8px;
  }
  .title2 {
    font-size: 15px;
    color: #2e3a5880;
    margin-left: auto;
  }
}
.goods {
  .content {
    display: flex;
    height: calc(243px / 2);
    background: linear-gradient(97deg, #eff3fe 18.22%, #fff 99.38%);
    .title-wrapper {
      flex: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: calc(102px / 2);
      background-color: #dce5ff;
      border-radius: 10px 0px 0px 10px;
      gap: 0px;
      margin-bottom: 0px;
      .icon {
        width: calc(51px / 2);
        height: calc(48px / 2);
        margin-bottom: 4px;
      }
      .title3 {
        font-size: 12px;
        color: #638af8;
        width: 24px;
      }
    }
    .list-wrapper {
      flex: 1;
      overflow-x: scroll;
      overflow-y: hidden;
      .list {
        display: flex;
        padding: 10px;
        // padding-right: 0;
        gap: 8px;
        width: max-content;
        .item {
          flex: none;
          width: calc(162px / 2);
          // height: calc(162px / 2);
          background-color: #eff4ff;
          border-radius: 10px;
          overflow: hidden;
          .image-wrapper {
            height: calc(162px / 2);
            // background-color: #fff;
            overflow: hidden;
            border-radius: 4px;
            .img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              display: block;
            }
          }
          .price {
            // margin-top: 4px;
            padding: 4px;
            text-align: center;
            color: var(--primary-color);
            // font-size: 15px;
            font-weight: 600;
            .label {
              font-size: 12px;
            }
            .symbol {
              font-size: 11px;
            }
            .value {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

.coupon,
.coupon-package {
  .content {
    background: linear-gradient(180deg, #c8d6ff 64.45%, rgba(216, 226, 255, 0) 107.33%);
    border-radius: 10px;
    overflow: hidden;
    padding: 6px;
    + .content {
      margin-top: 8px;
    }
    .list-wrapper {
      overflow-x: scroll;
      overflow-y: hidden;
      .list {
        display: flex;
        width: max-content;
        gap: 8px;
        .item {
          flex: none;
          position: relative;
          height: calc(215px / 2);
          width: calc(195px / 2);
          // background-color: #eff4ff;
          border-radius: 10px;
          background-image: url('@/assets/images/member/coupon-bg.png');
          background-size: 100% 100%;
          display: flex;
          flex-direction: column;
          .body {
            flex: none;
            height: 66px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
          }
          .footer {
            flex: 1;
            height: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .discount {
            text-align: center;
            font-weight: 600;
            font-size: 25px;
            // margin-top: 8px;
            .symbol {
              font-size: 16px;
            }
          }
          .desc {
            text-align: center;
            font-size: 15px;
            color: #2e3a584d;
            margin-top: 4px;
          }
          .receive-icon {
            position: absolute;
            width: calc(132px / 2);
            height: calc(132px / 2);
            right: -8px;
            bottom: -8px;
            pointer-events: none;
          }
          .receive-btn {
            width: calc(136px / 2);
            height: calc(50px / 2);
            background-color: var(--primary-color);
            color: #fff;
            border-radius: 999vw;
            font-size: 15px;
            font-weight: 400;
            display: block;
            border: none;
            // margin: 23px auto 0;
            &:disabled {
              background-color: #4671eb33;
            }
          }
        }
      }
    }
    .one-receive-btn {
      width: calc(675px / 2);
      height: calc(70px / 2);
      border: none;
      margin: 8px auto 0;
      border-radius: 999vw;
      color: #fff;
      font-size: calc(34px / 2);
      font-weight: 500;
      background: linear-gradient(2deg, #ff8c64 1.66%, #ff2419 98.94%);
      &:disabled {
        background: #4671eb33;
      }
    }
  }
}
.virtual-goods {
  .content {
    background: linear-gradient(180deg, #c8d6ff 64.45%, rgba(216, 226, 255, 0) 107.33%);
    border-radius: 10px;
    overflow: hidden;
    padding: 6px;
    .list-wrapper {
      .list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        .item {
          padding: 10px;
          background-color: #fff;
          border-radius: 15px;
          // display: flex;
          // flex-direction: column;
          // align-items: center;
          .img-wrapper {
            display: flex;
            align-items: start;
            // align-items: center;
            // justify-content: center;
            justify-content: space-between;
            margin-top: 5px;
            .img-wrapper-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              flex: none;
              width: calc(120px / 2);
              .img {
                border-radius: calc(35px / 2);
                background: #eef3ff;
                width: calc(110px / 2);
                height: calc(110px / 2);
                display: block;
                margin: 0 auto;
                padding: 6px;
                box-sizing: border-box;
              }
              .name {
                font-size: 15px;
                color: #0e439c;
                margin-top: 6px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                height: 30px;
              }
            }
            .interval {
              font-size: 15px;
              color: #000;
              flex: none;
              // margin: 8px;
              margin-top: 10px;
            }
          }
          .price {
            margin-top: 4px;
            text-align: center;
            color: #0e439c4d;
            font-size: 14px;
            .symbol {
              font-size: 14px;
              color: #0e439c;
            }
            .value {
              font-size: 20px;
              color: #0e439c;
            }
          }
          .receive-btn {
            width: calc(220px / 2);
            height: calc(60px / 2);
            background-color: var(--primary-color);
            color: #fff;
            border-radius: 999vw;
            font-size: 16px;
            font-weight: 400;
            display: block;
            border: none;
            margin: 10px auto 0;
            &:disabled {
              background-color: #4671eb33;
            }
          }
        }
      }
    }
  }
}
.tryout-goods {
  .content {
    background: linear-gradient(180deg, #c8d6ff 64.34%, rgba(216, 226, 255, 0) 107.34%);
    border-radius: 10px;
    overflow: hidden;
    padding: 6px;
    .list-wrapper {
      .list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        .item {
          overflow: hidden;
          // padding: 10px;
          background-color: #fff;
          border-radius: 8px;
          .img-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            .img {
              width: 100%;
              height: calc(213px / 2);
              display: block;
              // margin: 0 auto;
              // padding: 6px;
              box-sizing: border-box;
            }
          }
          .info-wrapper {
            padding: 4px;
            .name {
              font-size: 14px;
              color: #2e3a58;
              font-weight: 600;
              line-height: 1.2;
              margin-top: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .tag-wrapper {
              margin-top: 4px;
            }
            .tag {
              border-radius: 999vw;
              color: #4671eb;
              border: 1px solid #4671eb;
              font-size: 13px;
              font-weight: 400;
              padding: 4px 6px;
              display: flex;
              align-items: center;
              .icon {
                width: calc(32px / 2);
                height: calc(32px / 2);
                margin-right: 4px;
              }
            }
            .price-wrapper {
              display: flex;
              align-items: center;
              margin-top: 4px;

              .price-wrapper-item {
                // display: flex;
                flex: 1;
              }
              .price {
                font-size: calc(36px / 2);
                color: var(--primary-color);
                font-weight: 700;
                .symbol {
                  font-size: calc(26px / 2);
                }
              }
              .price-2 {
                font-size: calc(28px / 2);
                color: #bebebe;
                // font-weight: 500;
                font-weight: normal;
                text-decoration: line-through;
                .symbol {
                  // font-size: calc(28px / 2);
                }
                .value {
                }
              }
              .btn {
                // margin-left: auto;
                flex: none;
                width: calc(52px / 2);
                height: calc(52px / 2);
                background-color: var(--primary-color);
                border-radius: 999vw;
                color: #fff;
                border: none;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0;
                .icon {
                  width: calc(34px / 2);
                  height: calc(34px / 2);
                  display: block;
                }
              }
            }
          }
        }
      }
    }
  }
}
.customer-service {
  .content {
    background: linear-gradient(
      180deg,
      rgba(216, 226, 255, 0.4) 64.34%,
      rgba(216, 226, 255, 0) 107.34%
    );
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0 6px;
    .img {
      width: calc(180px / 2);
      height: calc(180px / 2);
      display: block;
      margin-right: 20px;
    }
    .text-wrapper {
      .title {
        color: #3259cc;
        font-size: 17px;
        font-weight: 600;
      }
      .btn {
        color: #3259cc80;
        font-size: 13px;
        height: 22px;
        border-radius: 999vw;
        border: 1px solid #91a6e6;
        margin-top: 4px;
        background-color: transparent;
        // padding: 0;
        .icon {
          transform: rotate(-90deg);
          margin-left: 2px;
        }
      }
      .sub-title {
        color: #3259cc80;
        font-size: 15px;
        font-weight: 600;
        margin-top: 4px;
      }
    }
  }
}

.ad-banner {
  margin: 10px 0;
  .header {
    margin-bottom: 10px;
  }
  .body {
    // background: linear-gradient(180deg, #c8d6ff 64.45%, rgba(216, 226, 255, 0) 107.33%);
    border-radius: 10px;
    overflow: hidden;
    // padding: 6px;
    .ad-swipe {
      width: 100%;
      height: auto;
      img {
        width: 100%;
        height: auto;
        display: block;
        border-radius: 10px;
      }
      :deep() {
      }
    }
  }
}
</style>
