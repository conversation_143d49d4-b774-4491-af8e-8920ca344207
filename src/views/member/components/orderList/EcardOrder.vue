<template>
  <div class="list-item" v-for="(item, index) in list" :key="index" @click="handleDetail(item)">
    <img class="goods-img" :src="item.goodsList[0]?.thumbPics">
    <div class="goods-info">
      <div class="name-status">
        <div class="goods-name overflow-1">{{ item.goodsList[0]?.spuTitle }}</div>
        <div class="status">{{ $global.GOODS_ORDER_STATUS[item.status] }}</div>
      </div>
      <div class="account">{{ item.goodsList[0]?.skuName }}</div>
      <div class="time">订单时间: {{ item.createTime }}</div>
    </div>
  </div>
</template>

<script setup>
  const router = useRouter()
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })
  const handleDetail = (item) => {
    sessionStorage.setItem('equityOrderDetail', JSON.stringify(item))
    router.push('/member/equity-order-detail?orderId='+item.orderId)
  }
</script>

<style lang="scss" scoped>
  .list-item {
    margin: 0 16px 10px;
    background: #ffffff;
    padding: 12px;
    border-radius: 12px;
    display: flex;
    .goods-img{
      display: block;
      width: 90px;
      height: 90px;
      margin-right: 5px;
    }
    .goods-info{
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .name-status{
        display: flex;
        justify-content: space-between;
        .goods-name{
          font-size: 14px;
          color: #333333;
          font-weight: bold;
          line-height: 22px;
        }
        .status{
          font-size: 14px;
          flex-shrink: 0;
          line-height: 22px;
          margin-left: 10px;
          color: #A6A6A6;
        }
      }
      .time, .account{
        font-size: 13px;
        color: #A6A6A6;
      }
    }
  }
  .list-item:first-child{
    margin-top: 15px;
  }
</style>