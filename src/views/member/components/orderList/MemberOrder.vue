<template>
  <div class="member list-item" v-for="(item, index) in list" :key="index">
    <div class="title">
      <div class="title-left flex align-center">会员权益礼品</div>
      <div class="title-status">{{ $global.GOODS_ORDER_STATUS[item.status] }}</div>
    </div>
    <div class="content solid-top" @click="handleDetail(item)">
      <img class="goods-img" :src="item.goodsList[0].thumbPics">
      <div class="order-info">
        <div class="info-text overflow-2">{{ item.goodsList[0].spuTitle }}</div>
        <div class="number-time">
          <div class="number">
            <div>X{{ item.goodsList[0].quantity }}</div>
            <div class="price theme-text">¥{{ formatMoney(item.totalAmount) }}</div>
          </div>
          <div class="time">{{ item.createTime }}</div>
        </div>
      </div>
      
    </div>
  </div>
</template>

<script setup>
  import { getEuqityOrder } from '@/api/member'
  import { showToast } from 'vant';
  const router = useRouter()
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })
  const handleDetail = (item) => {
    sessionStorage.setItem('equityOrderDetail', JSON.stringify(item))
    router.push('/member/equity-order-detail?orderId='+item.orderId)
  }
</script>

<style lang="scss" scoped>
  .list-item {
    margin: 12px 16px 0;
    background: #ffffff;
    border-radius: 8px;
    .title{
      display: flex;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      height: 47px;
      font-weight: bold;
      color: #222222;
      padding: 0 15px;
      img{
        width: 17px;
        height: 17px;
        margin-right: 6px;
      }
      .title-status{
        font-size: 13px;
        color: #A6A6A6;
        font-weight: 400;
      }
    }
  }
  .member .content{
    padding: 15px;
    display: flex;
    justify-content: space-between;
    position: relative;
    .goods-img{
      width: 90px;
      height: 90px;
      display: block;
      border-radius: 12px;
    }
    .order-info {
      margin-left: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
      .info-text{
        font-size: 14px;
        color: #333333;
        line-height: 22px;
      }
    }
    .number-time{
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .number{
        font-size: 13px;
        color: #A6A6A6;
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        .price{
          font-size: 14px;
          color: #FF571A;
          font-weight: bold;
        }
      }
      .time{
        font-size: 12px;
        color: #A6A6A6;
        line-height: 22px;
        margin-top:10px;
      }
    }
  }
  .service{
    display: flex;
    justify-content: flex-end;
    padding: 10px 15px;
    &-btn{
      height: 28px;
      border-radius: 15px;
      border: 1px solid #D5D5D5;
      font-size: 12px;
      color: #999999;
      padding: 0 11px;
      line-height: 28px;
    }
  }
</style>