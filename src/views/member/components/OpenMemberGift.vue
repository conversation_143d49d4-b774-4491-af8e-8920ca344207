<template>
  <div class="gift">
    <div class="gift-item" v-for="item in benefit.vipBenefitGoodsInfo" @click="handleGoods(item)">
      <img :src="item.imgUrl" class="goods-img">
      <div class="title overflow-1">{{ item.name }}</div>
      <div class="goods-price">原价 ¥ {{ formatMoney(item.salePrice) }}</div>
      <div :class="`btn ${ (benefit.receiveNum > 0 && item.receiveNum === 0)? 'disabled': ''}`">
        <img v-if="benefit.receiveNum === 0" src="@/assets/images/member/gift-inactive.png">
        <img v-else-if="checkedMember.vipCust && benefit.receiveNum > 0 && item.receiveNum === 0" src="@/assets/images/member/gift-disabled.png">
        <img v-else-if="checkedMember.vipCust && benefit.receiveNum > 0 && item.receiveNum > 0" src="@/assets/images/member/gift-active.png">
        <div class="text">
          {{(benefit.receiveNum > 0 && item.receiveNum > 0)? '已领取' : '待领取' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const props = defineProps({
  benefit: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleGoods'])
const handleGoods = (item) => {
  if (checkedMember.value.vipCust && props.benefit.receiveNum > 0) {
    return
  } 
  emit('handleGoods', props.benefit, item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .gift{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 10px 16px 0;
    .gift-item{
      width: 90px;
      margin-bottom: 18px;
      .goods-img{
        width: 90px;
        height: auto;
        display: block;
      }
      .title{
        font-size: 13px;
        color: #000000;
        line-height: 18px;
        text-align: center;
        margin-top: 10px;
      }
      .goods-price{
        text-align: center;
        font-size: 12px;
        zoom: 0.83;
        line-height: 18px;
        color: rgba(153,153,153,0.6);
      }
      .btn{
        width: 80px;
        height: 30px;
        margin: 6px auto 0;
        padding-left: 10px;
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #5F5B68;
        background: linear-gradient( 180deg, #FEECDC 0%, #F8D2AC 100%);
        border-radius: 20px;
        box-sizing: border-box;
        img{
          width: 16px;
          height: 16px;
          display: block;
          margin-right: 4px;
        }
      }
      .btn.disabled{
        color: #AFAFAF;
        background: #E8E8E8;
      }
    }
  }
</style>