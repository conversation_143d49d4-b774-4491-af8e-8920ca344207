<template>
  <van-popup :show="isRevealed" class="success-dialog" @close="cancel('close')">
    <div class="head"></div>
    <div class="body">
      <!-- <img src="@/assets/images/member/success-dialog.png" alt="" class="main-img"/> -->
    </div>
    <div class="footer">
      <button class="btn cancel-btn" @click="cancel('cancel')">稍后领</button>
      <button class="btn confirm-btn" @click="confirm('confirm')">前往领礼品</button>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
defineExpose({
  isRevealed,
  reveal,
  confirm,
  cancel,
})
</script>

<style lang="scss" scoped>
.success-dialog {
  // width: 100%;
  // height: 100%;
  background-color: transparent;
  .body {
    width: calc(534px / 2);
    height: calc(649px / 2);
    background-image: url('@/assets/images/member/success-dialog.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    // padding: 0 20px;
    gap: 10px;
    margin-top: 16px;
    .btn {
      width: calc(254px / 2);
      height: calc(80px / 2);
      border-radius: 999vw;
      font-size: calc(36px / 2);
      font-weight: 500;
      border: none;
      box-sizing: border-box;
      color: #fff;
      &.cancel-btn {
        background-color: transparent;
        border: 1px solid #fff;
      }
      &.confirm-btn {
        background-color: #5881fa;
        font-weight: 600;
      }
    }
  }
}
</style>
