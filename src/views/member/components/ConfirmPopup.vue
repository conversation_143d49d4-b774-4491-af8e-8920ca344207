<template>
  <van-popup
    :show="isRevealed"
    position="bottom"
    round
    closeable
    safe-area-inset-bottom
    class="confirm-popup"
    @close="cancel('close')"
  >
    <div class="header">
      <!-- <button @click="cancel('close')">关闭</button> -->
      <!-- <span>开通可享权益福利</span> -->
      <img src="@/assets/images/member/confirm-popup-title.png" alt="" class="title-img" />
    </div>
    <div class="body">
      <div v-if="adList?.length" class="ad">
        <van-swipe class="ad-swipe" :autoplay="3000">
          <van-swipe-item v-for="ad in adList">
            <img :src="ad.pic" alt="" />
          </van-swipe-item>
        </van-swipe>
      </div>
      <div v-if="couponBenefit" class="box">
        <!-- {{ couponBenefit }} -->
        <div class="coupon-list">
          <div
            v-for="coupon in (couponBenefit.packageType === 'COUPON'
              ? couponBenefit.vipBenefitGoodsInfo
              : couponBenefit.vipBenefitGoodsInfo[0].ticketList
            ).slice(0, 3)"
            class="item"
          >
            <div class="ticketDiscount">
              <span class="symbol">￥</span>
              <span class="value">{{ getCouponInfo(coupon).ticketDiscount.value }}</span>
            </div>
            <div class="desc">
              <span
                v-if="
                  getCouponInfo(coupon).ticketSpuRel.type === 'NONE' &&
                  getCouponInfo(coupon).ticketThreshold.type === 'NONE'
                "
                >无门槛</span
              >
              <span v-else-if="getCouponInfo(coupon).ticketSpuRel.type === 'NONE'">
                满{{ getCouponInfo(coupon).ticketThreshold.amount }}元可用
              </span>
              <span v-else-if="getCouponInfo(coupon).ticketSpuRel.type === 'SPU'">
                指定商品可用
              </span>
              <span v-else>
                {{ coupon.name }}
              </span>
            </div>
            <button class="btn">领取</button>
          </div>
        </div>
        <div class="title">
          <span>开通极享VIP会员享7项特权</span>
        </div>
        <div class="sub-title">
          <div class="item"><van-checkbox :modelValue="true" />会员尊享礼</div>
          <div class="item"><van-checkbox :modelValue="true" />9.9抢100抵扣券</div>
          <div class="item"><van-checkbox :modelValue="true" />联合视听会员</div>
          <div class="item"><van-checkbox :modelValue="true" />98元会员商品等</div>
        </div>
      </div>

      <button class="confirm-btn" @click="handleConfirm()">
        <!-- <strong>0</strong>元享受权益，收到礼品后再付¥<strong>{{
          checkedMember?.vipPrice?.discountPrice
        }}</strong> -->
        立即开通
      </button>
      <div class="agreement">
        <van-checkbox v-model="checked" />
        <div class="agreement-text">
          我已阅读并同意<strong @click="emit('showAgreement')">《权益服务协议》</strong
          >、理解并接受相关条款并同意
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'
import { showToast } from 'vant'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

const props = defineProps({
  checkedMember: {
    type: Object,
    default: () => {},
  },
  benefitList: {
    type: Array,
    default: () => [],
  },
  adList: {
    type: Array,
    default: () => [],
  },
})

const checked = defineModel('checked')
defineExpose({ isRevealed, reveal, cancel })

const couponBenefit = computed(() => {
  return props.benefitList?.find((item) => item.packageType === 'COUPON_PACKAGE')
})

function getCouponInfo(coupon) {
  if (couponBenefit.value === 'COUPON') {
    return coupon.ticketDefine
  }
  return coupon
}

function handleConfirm() {
  if (!checked.value) {
    showToast('请先阅读并同意《权益服务协议》')
    return
  }
  confirm()
}
</script>

<style lang="scss" scoped>
.confirm-popup {
  background: linear-gradient(149deg, #e2eaff 10.38%, #fff 57.93%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-size: 14px;
}
.header {
  text-align: center;
  font-size: 25px;
  color: #3e68e7;
  font-weight: 400;
  padding-top: 16px;
  .title-img {
    display: inline-block;
    width: auto;
    height: calc(47px / 2);
  }
}
.box {
  background: linear-gradient(180deg, #c8d6ff 10.64%, rgba(216, 226, 255, 0) 101.73%);
  background-size: 100% calc(213px / 2);
  background-repeat: no-repeat;
  border-radius: 15px;
  overflow: hidden;
  padding: 6px;
  box-sizing: border-box;
}
.body {
  padding: 10px 10px 20px;
  .ad {
    width: 100%;
    height: auto;

    .ad-swipe {
      width: 100%;
      height: auto;
      img {
        border-radius: 10px;
        width: 100%;
        height: auto;
      }
    }
  }
}
.confirm-btn {
  border-radius: 999vw;
  background: linear-gradient(180deg, #5881fa 0%, #91b7fd 100%);
  display: block;
  width: 100%;
  height: calc(85px / 2);
  font-size: 15px;
  color: #fff;
  border: none;
  margin-top: 20px;
  strong {
    font-size: 20px;
  }
}
.coupon-list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  .item {
    position: relative;
    border-radius: 10px;
    background-image: url('@/assets/images/member/coupon-bg.png');
    background-size: 100% 100%;
    height: calc(215px / 2);
    width: calc(210px / 2);
    padding: 4px;
    box-sizing: border-box;

    .ticketDiscount {
      color: var(--primary-color);
      font-size: calc(54px / 2);
      font-weight: 400;
      font-family: DIN;
      margin-top: 4px;
      text-align: center;
      * {
        font-family: DIN;
      }
      .symbol {
        font-size: calc(36px / 2);
      }
    }
    .desc {
      color: #2e3a584d;
      font-size: 15px;
      font-weight: 400;
      text-align: center;
      margin-top: 4px;
    }
    .btn {
      background-color: var(--primary-color);
      border: none;
      display: block;
      margin: 20px auto 0;
      width: calc(136px / 2);
      height: 25px;
      border-radius: 999vw;
      color: #fff;
      font-size: 15px;
      font-weight: 400;
    }
  }
}

.title {
  text-align: center;
  font-size: 15px;
  color: #213c8d80;
  margin: 10px 0;
  span {
    display: inline-block;
    position: relative;
    padding: 0 40px;
    &::before {
      content: '';
      display: block;
      width: 33px;
      height: 1px;
      background-image: url('@/assets/images/member/confirm-popup-title-border.png');
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    &::after {
      content: '';
      display: block;
      width: 33px;
      height: 1px;
      background-image: url('@/assets/images/member/confirm-popup-title-border.png');
      background-size: 100% 100%;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%) rotate(180deg);
    }
  }
}
.sub-title {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 0 20px;
  .item {
    display: flex;
    color: #6088fa;

    .van-checkbox {
      margin-right: 4px;
      --van-checkbox-checked-icon-color: #6088fa;
      --van-checkbox-size: 16px;
    }
  }
}
.agreement {
  display: flex;
  font-size: 12px;
  // line-height: 16px;
  font-weight: 400;
  color: #0000004d;
  margin-top: 4px;
  align-items: center;
  .van-checkbox {
    margin-right: 4px;
    --van-checkbox-size: 16px;
    --van-checkbox-checked-icon-color: #6088fa;
    flex: none;
  }
  .agreement-text {
    overflow-y: hidden;
    overflow-x: scroll;
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    strong {
      color: var(--primary-color);
      font-weight: inherit;
    }
  }
}
</style>
