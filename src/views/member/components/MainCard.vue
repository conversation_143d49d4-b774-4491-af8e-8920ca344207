<template>
  <div class="main-card">
    <div class="card-1">
      <div class="title">
        <img src="@/assets/images/member/VIP-icon.png" alt="" class="icon" />
        <span>{{ checkedMember?.vipPrice?.vipName }}</span>
      </div>
      <div class="sub-title">
        <template v-if="checkedMember?.vipCust">
          到期时间 {{ dayjs(checkedMember.vipCust.invalidTime).format('YYYY-MM-DD') }}

          <button class="order" @click="handleMemberOrder">权益订单<van-icon name="arrow" /></button>
        </template>

        <template v-else>
          {{ checkedMember?.vipPrice?.title }}
        </template>
      </div>
      <div class="tip">
        {{ checkedMember?.vipCust ? '当前已开通会员' : '当前未开通会员' }}
      </div>
    </div>
    <div class="card-2">
      <div class="title">会员卡精选特权</div>
      <div ref="benefitListSwiper" class="benefit-list swiper">
        <div class="list swiper-wrapper">
          <div v-for="item in checkedMember?.benefitList" class="item swiper-slide">
            <img :src="item.benefitImg" alt="" class="icon" />
            <div class="name">
              {{ item.benefitName }}
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
      </div>
      <button class="call-service">
        <img src="@/assets/images/home/<USER>" alt="" class="icon" />
        <span @click="checkedMember?.vipCust ? $customerService() : $emit('handleBuy')"
          >召唤专属你的会员客服</span
        >
      </button>
    </div>
  </div>
</template>

<script setup>
import { onMounted, useTemplateRef } from 'vue'
import Swiper from 'swiper'
import { Navigation, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { unrefElement } from '@vueuse/core'
import dayjs from 'dayjs'
const benefitListSwiperRef = useTemplateRef('benefitListSwiper')
// const store = useStore()
// const user = computed(() => store.getters.userInfo)

const props = defineProps({
  checkedMember: {
    type: Object,
    // default: () => {},
  },
  orderInfo: {
    type: Object,
  },
})

const emit = defineEmits(['handleBuy'])

const router = useRouter()

let swiperInstance = null

onMounted(() => {
  swiperInstance = new Swiper(unrefElement(benefitListSwiperRef), {
    modules: [Navigation, Pagination],
    slidesPerView: 4,
    slidesPerGroup: 4,
    pagination: {
      el: '.swiper-pagination',
    },
  })
  onUnmounted(() => {
    swiperInstance?.destroy()
  })
})

watch(
  () => props.checkedMember,
  async (newVal) => {
    await nextTick()
    swiperInstance?.update()
  }
)

const handleMemberOrder = () => {
  router.push('/member/equity-order')
}
</script>

<style lang="scss" scoped>
.main-card {
  font-size: 14px;
  margin: 13px 13px 0;
  .card-1 {
    margin: 0 9px;
    border-radius: 15px;
    background: linear-gradient(
      177deg,
      #9cb5ff -39.05%,
      #f4f7ff 15.48%,
      #fff 26.46%,
      #fcfcff 36.87%,
      #d3deff 97.21%
    );
    height: 146px;
    padding: 20px 15px;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    .title {
      .icon {
        width: 22px;
        height: 23px;
        margin-right: 4px;
      }
      font-size: 25px;
      font-weight: 600;
      color: #2151c8;
    }
    .sub-title {
      font-size: 15px;
      font-weight: 400;
      color: #2151c880;
      margin-top: 4px;
      display: flex;
      align-items: center;
      .order {
        margin-left: auto;
        border: none;
        background: transparent;
        padding: 0;
      }
    }
    .tip {
      font-size: 13px;
      color: #fff;
      background: linear-gradient(
        -90deg,
        rgba(33, 81, 200, 0.5) 0%,
        rgba(113, 144, 220, 0.32) 67.07%,
        rgba(255, 255, 255, 0) 100%
      );
      position: absolute;
      right: 0;
      top: 0;
      padding: 4px 10px;
      // border-radius: 10px;
    }
  }
  .card-2 {
    margin-top: -70px;
    height: 228px;
    border-radius: 15px;
    background: linear-gradient(179deg, #eff3ff 1.12%, #fff 17.77%);
    position: relative;
    padding: 10px;
    box-sizing: border-box;
    .title {
      background-image: url('@/assets/images/member/main-card-title-bg.png');
      background-size: 100% 100%;
      width: 174px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 17px;
      font-weight: 600;
      color: #333;
      margin: 0 auto;
    }
    .benefit-list {
      margin: 10px 8px 0px;
      padding-bottom: 30px;

      .list {
        // display: flex;
        // align-items: center;
        // justify-content: space-between;
        .item {
          .icon {
            width: 45px;
            height: 45px;
            display: block;
            margin: 0 auto;
          }
          .name {
            font-size: 13px;
            font-weight: 400;
            color: #1b67ea;
            text-align: center;
            margin-top: 5px;
          }
        }
      }
      .swiper-pagination {
        :deep() {
          .swiper-pagination-bullet {
            border-radius: 999vw;
            width: 10px;
            margin: 0 2px;
            height: 4px;
          }
          .swiper-pagination-bullet-active {
            width: 16px;
          }
        }
        // bottom: initial;
        // top: calc(100% + 10px);
      }
    }
    .call-service {
      width: 284px;
      height: 42px;
      border-radius: 999vw;
      background: #5881fa;
      border: none;
      color: #fff;
      font-size: 15px;
      font-weight: 400;
      margin: 10px auto 0;
      display: block;
      .icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
        vertical-align: middle;
      }
    }
  }
}
</style>
