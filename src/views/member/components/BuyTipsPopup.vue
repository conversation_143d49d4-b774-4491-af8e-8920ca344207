<template>
  <van-popup class="overflow-inherit" round v-model:show="show" :style="{ width: $px2rem('301px') }">
    <div class="content">
      <div class="title">
        立即开通享用
      </div>
      <div class="tips" v-if="checkedMember.vipPrice">确认即同意《用户服务协议》，并同意以{{ checkedMember.vipPrice.discountPrice }}元购买，可随时取消</div>
      <div class="btn theme-linear-gradient" @click="handleBuy">确认并购买</div>
      <van-icon class="close" color="#cccccc" name="close" @click="show=false"></van-icon>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleBuy'])
const handleBuy = () => {
  show.value = false
  emit('handleBuy')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  :deep(.van-popup){
    overflow-y:inherit !important;
  }
  .content{
    padding: 34px 20px 26px;
    position: relative;
    .title{
      font-weight: 500;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
      text-align: center;
    }
    .tips{
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-top: 18px;
    }
    .benefit{
      margin-top: 17px;
      display: flex;
      justify-content: space-between;
      &-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        img{
          width: 40px;
          height: 40px;
          display: block;
        }
        .text{
          font-size: 12px;
          color: #666666;
          line-height: 17px;
          margin-top: 6px;
        }
      }
    }
    .btn{
      width: 186px;
      height: 40px;
      border-radius: 20px;
      margin: 24px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .close{
      display: block;
      position: absolute;
      bottom: -40px;
      left: calc(50% - 10px);
    }
  }
</style>