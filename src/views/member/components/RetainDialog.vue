<template>
  <van-popup :show="isRevealed" class="retain-dialog" round>
    <div class="header">
      <img src="@/assets/images/member/retain-dialog-title.png" alt="" class="title" />
    </div>
    <div class="body">
      <img src="@/assets/images/member/retain-dialog-card.png" alt="" class="card" />
      <div class="title">
        <span>开通极享VIP会员享7项特权</span>
      </div>
      <div class="sub-title">
        <div class="item"><van-checkbox :modelValue="true" />会员尊享礼</div>
        <div class="item"><van-checkbox :modelValue="true" />9.9抢100抵扣券</div>
        <div class="item"><van-checkbox :modelValue="true" />联合视听会员</div>
        <div class="item"><van-checkbox :modelValue="true" />88元会员商品等</div>
      </div>
    </div>
    <div class="footer">
      <button class="confirm-btn" @click="confirm('confirm')">确认领取</button>
      <button class="cancel-btn" @click="cancel('cancel')">暂不开通</button>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
defineExpose({
  isRevealed,
  reveal,
  confirm,
  cancel,
})
</script>

<style lang="scss" scoped>
.retain-dialog {
  width: calc(556px / 2);
  height: calc(799px / 2);
  background-color: transparent;
  background-image: url('@/assets/images/member/retain-dialog-bg.png');
  background-size: 100% 100%;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  --van-popup-close-icon-margin: 8px;

  // :deep(){
  //   .van-popup__close-icon{

  //   }
  // }
  .header {
    padding-top: calc(60px / 2);
    flex: none;
    .title {
      display: block;
      margin: 0 auto;
      width: calc(502px / 2);
      height: calc(43px / 2);
    }
  }
  .body {
    padding-top: 20px;
    flex: 1;
    min-height: 0;
    .card {
      width: calc(455px / 2);
      height: calc(235px / 2);
      display: block;
      margin: 0 auto;
    }
    .title {
      text-align: center;
      font-size: 15px;
      color: #213c8d80;
      margin: 20px 0 16px;
      span {
        display: inline-block;
        position: relative;
        padding: 0 40px;
        &::before {
          content: '';
          display: block;
          width: 33px;
          height: 1px;
          background-image: url('@/assets/images/member/confirm-popup-title-border.png');
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }

        &::after {
          content: '';
          display: block;
          width: 33px;
          height: 1px;
          background-image: url('@/assets/images/member/confirm-popup-title-border.png');
          background-size: 100% 100%;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
    .sub-title {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px 0px;
      padding: 0 10px 0 20px;
      .item {
        display: flex;
        color: #6088fa;
        line-height: normal;

        .van-checkbox {
          margin-right: 4px;
          --van-checkbox-checked-icon-color: #6088fa;
          --van-checkbox-size: 16px;
        }
      }
    }
  }
  .footer {
    padding: 12px 12px 16px;
    flex: none;
    .confirm-btn {
      display: block;
      width: calc(500px / 2);
      height: calc(86px / 2);
      font-size: calc(34px / 2);
      color: #fff;
      font-weight: 500;
      border-radius: 999vw;
      border: none;
      background: linear-gradient(180deg, #5881fa 0%, #91b7fd 100%);
      margin: 0 auto 0;
    }
    .cancel-btn {
      display: block;
      font-size: 15px;
      color: #213c8d33;
      font-weight: 400;
      border: none;
      background-color: transparent;
      margin: 12px auto 0;
    }
  }
}
</style>
