<template>
  <div class="order-info" v-if="orderData" :style="getStyle">
    <div class="order-info-title">订单信息</div>
    <div class="info-group">
      <div class="group-label">订单编号</div>
      <div class="group-value">
        {{ orderData.orderId }}
        <div
          class="copy-btn"
          v-clipboard:copy="orderData.orderId"
          v-clipboard:success="clipboardSuccess">复制</div>
      </div>
    </div>
    <div class="info-group">
      <div class="group-label">激活时间</div>
      <div class="group-value">
        {{ `${parseTime(orderData.createTime, '{y}.{m}.{d}')}` }}
      </div>
    </div>
    <div class="info-group">
      <div class="group-label">支付方式</div>
      <div class="group-value">
        {{ $global.ORDER_PAY_TYPE[orderData.payType] }}
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { showToast } from 'vant';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  orderId: {
    type: String,
    default: ''
  },
  orderData: {
    type: Object,
    default: () => {}
  },
  isVip: {
    type: Boolean,
    default: false
  }
})
const { isVip } = props
const getStyle = computed(() => {
  return {
    boxShadow: isVip ? '0 0 20px rgb(226, 233, 254)' : '0 0 20px rgb(252, 205, 202)'
  }
})
const clipboardSuccess = () => {
  showToast('复制成功')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .order-info{
    flex: 1;
    background: #FFFFFF;
    border-radius: 12px 12px 0px 0px;
    padding: 19px 0 40px 0;
    &-title{
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      margin-left: 25px;
    }
    .info-group{
      display: flex;
      height: 50px;
      justify-content: space-between;
      align-items: center;
      margin: 10px 25px 0;
      font-size: 14px;
      .group-label{
        color: #666666;
      }
      .group-value{
        color: #000000;
        display: flex;
        align-items: center;
        .copy-btn {
          width: 42px;
          height: 20px;
          background: #F2F2F2;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #DADADA;
          font-size: 12px;
          text-align: center;
          line-height: 20px;
          margin-left: 10px;
        }
      }
    }
  }
</style>