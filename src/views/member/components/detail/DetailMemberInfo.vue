<template>
  <div class="member-info" v-if="memberInfo.vipPriceInfo" :class="{ 'is-vip': isVip }">
    <!-- <div class="right-label">
      会员价
    </div> -->
    <!-- <img class="ji-img" src="@/assets/images/member/ji-logo1.png"> -->
    <div class="member-info-header">
      <div class="member-name">
      【{{ memberInfo.vipPriceInfo.vipName }}】
      </div>
      <div class="member-status" :class="{ 'is-vip': isVip }">已开通</div>
    </div>
    <div class="member-price">
      <span>会员优惠价：¥{{ memberInfo.vipPriceInfo.discountPrice }}</span>
      <span v-if="memberInfo.vipPriceInfo.discountPrice != memberInfo.vipPriceInfo.price" class="discount">¥{{ memberInfo.vipPriceInfo.price }}</span>
    </div>
    <div class="valid-time" v-if="memberInfo.vipCustInfo" :class="{ 'is-vip': isVip }">
      有效期至：{{ `${parseTime(memberInfo.vipCustInfo.validTime, '{y}.{m}.{d}')} - ${parseTime(memberInfo.vipCustInfo.invalidTime, '{y}.{m}.{d}')}` }} 
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  memberInfo: {
    type: Object,
    default: () => {}
  },
  isVip: {
    type: Boolean,
    default: false
  }
})
const { isVip } = props
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .member-info{
    width: 324px;
    height: 115px;
    position: relative;
    background: url('@/assets/images/member/order-detail-discount.png') no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    .right-label {
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient( 93deg, #583D10 0%, #201502 100%);
      border-radius: 0px 12px 0px 12px;
      width: 60px;
      height: 24px;
      line-height: 24px;
      display: flex;
      text-align: center;
      justify-content: center;
      font-size: 12px;
      color: #D7C6AC;
    }
    // .ji-img{
    //   width: 86px;
    //   height: 99px;
    //   display: block;
    //   position: absolute;
    //   top: 32px;
    //   right: 17px;
    // }
    .member-info-header {
      display: flex;
      align-items: center;
      margin: 15px 0 2px 5px;
    }
    .member-name{
      display: inline-block;
      // margin: 15px 0 0 5px;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 25px;
      font-weight: bold;
      // font-family: HelloFont WenYiHei;
    }
    .member-status {
      display: inline-block;
      background: #CD625D;
      text-align: center;
      width: 55px;
      height: 20px;
      line-height: 20px;
      border-radius: 10px;
      font-size: 13px;
      color: #ffffff;
      // margin-top: -10px;
    }
    .member-status.is-vip {
      background: #5E85FB;
    }
    .member-price{
      font-size: 15px;
      color: #FFFFFF;
      line-height: 21px;
      font-weight: 600;
      // margin-top: 45px;
      margin-left: 15px;
      .discount{
        color: #FFFFFF;
        opacity: .5;
        text-decoration: line-through;
        margin-left: 4px;
      }
    }
    .valid-time{
      margin-top: 22px;
      font-size: 14px;
      color: #CD625D;
      opacity: .5;
      line-height: 20px;
      margin-left: 15px;
    }
    .valid-time.is-vip {
      color: #5881FA;
      // opacity: .5;
    }
  }
  .member-info.is-vip {
    background: url('@/assets/images/member/order-detail-vip.png') no-repeat;
    background-size: 100% 100%;
  }
</style>