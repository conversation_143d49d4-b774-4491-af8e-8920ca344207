<template>
  <div class="benefit-goods">
    <div class="goods-item" v-for="item in benefit.vipBenefitGoodsInfo" @click="handleGoods(item)">
      <img :src="item.imgUrl" class="goods-img">
      <div class="title overflow-1">{{ item.name }}</div>
      <div class="sale-price">
        <div class="text">原价 ¥{{ formatMoney(item.salePrice) }}</div>
      </div>
      <div class="price">
        <div class="tag">
          <div class="zoom-text-11">会员</div></div>
        <div class="price-text"><div class="zoom-text-11"><span>¥</span>{{ formatMoney(item.price, 0) }}</div></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const props = defineProps({
  benefit: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleGoods'])
const handleGoods = (item) => {
  emit('handleGoods', props.benefit, item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .benefit-goods{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 15px 16px 0;
    .goods-item{
      width: 90px;
      margin-bottom: 18px;
      .goods-img{
        width: 50px;
        height: auto;
        display: block;
        margin: 0 auto;
      }
      .title{
        font-size: 13px;
        color: #000000;
        line-height: 18px;
        text-align: center;
        margin-top: 10px;
      }
      .sale-price{
        text-align: center;
        color: rgba(153,153,153,0.6);
        line-height: 18px;
        margin-top: 2px;
        .text{
          font-size: 12px;
          zoom: 0.83;
        }
      }
      .price {
        display: flex;
        align-items: center;
        height: 26px;
        margin-top: 4px;
        padding-right: 7px;
        background: #fffaf5;
        border-radius: 20px;
        border: 1px solid #f1dcbf;
        font-weight: bold;
        color: #202337;
        width: 77px;
        box-sizing: border-box;
        margin: 4px auto 0;
        .price-text span{
          margin-right: 2px;
        }
        .tag{
          width: 36px;
          height: 100%;
          padding-right: 2px;
          margin-right: 2px;
          background: #573C10;
          border-radius: 15px 0 0 15px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-weight: 400;
          clip-path: polygon(0% 0%, 100% 0%, 85% 100%, 0% 100%);
        }
      }
    }
  }
</style>