<template>
  <van-popup
    :show="isRevealed"
    round
    position="bottom"
    closeable
    class="loan-give-dialog"
    @close="cancel('close')"
  >
    <div class="dialog-content">
      <div class="header">
        <div class="title">
          <span class="title__text">
            <span>会员入会礼</span>
            <div class="title__line"></div>
          </span>
        </div>
        <div class="sub-title">
          可{{
            '零一二三四五六七八九十'[benefit?.vipBenefitGoodsInfo?.length]
          }}选一，免费领取包邮到家
        </div>
      </div>
      <div class="body">
        <div ref="swiper" class="swiper list-swiper">
          <div class="list swiper-wrapper">
            <div v-for="goodsInfo in benefit?.vipBenefitGoodsInfo" class="item swiper-slide">
              <!-- {{ goodsInfo.name }} -->
              <div class="image-wrapper">
                <img :src="goodsInfo.imgUrl" alt="" class="img" />
              </div>
              <div class="content">
                <div class="name">
                  {{ goodsInfo.name }}
                </div>
                <div class="price">
                  <span class="label">价值</span><span class="symbol">￥</span
                  ><span class="value">{{ goodsInfo.marketPrice }}</span>
                </div>
                <button class="btn" @click="handleGoods(goodsInfo)">
                  {{
                    benefit.receiveNum === 0
                      ? '免费领取'
                      : goodsInfo.receiveNum > 0
                      ? '已领取'
                      : '限领一件'
                  }}
                </button>
              </div>
              <!-- <img src="" alt="" /> -->
            </div>
          </div>
          <div class="swiper-pagination"></div>
        </div>
        <div class="desc">左右滑动选择即可</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog, unrefElement, whenever } from '@vueuse/core'
import { computed, shallowRef, useTemplateRef, watch } from 'vue'
import Swiper from 'swiper'
import { Navigation, Pagination, EffectCoverflow, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-coverflow'

const props = defineProps({
  // checkedMember: {
  //   type: Object,
  // },
  benefitList: {
    type: Array,
  },
})

const benefit = computed(() => props.benefitList.find((item) => item.packageType === 'LOAN_GIVE'))
const swiperRef = useTemplateRef('swiper')
let swiperInstance = null

whenever(swiperRef, (el, oldEl, onCleanup) => {
  swiperInstance = new Swiper(el, {
    modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
    slidesPerView: 'auto',
    spaceBetween: 15,
    loop: true,
    centeredSlides: true,
    effect: 'coverflow',
    coverflowEffect: {
      depth: 100,
      modifier: 1,
      rotate: 0,
      scale: 0.9,
      slideShadows: false,
      stretch: 4,
    },
    speed: 800,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination: {
      el: '.swiper-pagination',
    },
  })

  onCleanup(() => {
    swiperInstance?.destroy()
  })
  // onUnmounted(() => {
  //   swiperInstance?.destroy()
  // })
})

onMounted(() => {
  // swiperInstance = new Swiper(unrefElement(swiperRef), {
  //   modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
  //   slidesPerView: 'auto',
  //   spaceBetween: 15,
  //   loop: true,
  //   centeredSlides: true,
  //   effect: 'coverflow',
  //   coverflowEffect: {
  //     depth: 100,
  //     modifier: 1,
  //     rotate: 0,
  //     scale: 0.9,
  //     slideShadows: false,
  //     stretch: 4,
  //   },
  //   speed: 800,
  //   autoplay: {
  //     delay: 3000,
  //     disableOnInteraction: false,
  //   },
  //   pagination: {
  //     el: '.swiper-pagination',
  //   },
  // })
  // onUnmounted(() => {
  //   swiperInstance?.destroy()
  // })
})

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  isRevealed,
  reveal,
  cancel,
})
watch(
  () => props.benefitList,
  async () => {
    if (isRevealed.value) {
      await nextTick()
      swiperInstance?.update()
    }
  }
)
onReveal(async () => {
  await nextTick()
  swiperInstance?.update()
})

function handleGoods(goodsInfo) {
  console.log(goodsInfo)
  confirm({
    benefit: benefit.value,
    goodsInfo,
  })
}
</script>

<style lang="scss" scoped>
.loan-give-dialog {
  background-color: #fff;
  --van-popup-round-radius: 40px;
  --van-popup-close-icon-color: #8793b7;
  font-size: 14px;
  :deep(.van-popup__close-icon--top-right) {
    top: 30px;
    right: 20px;
  }
  .dialog-content {
    background-image: linear-gradient(
      180deg,
      rgba(88, 129, 250, 0.3) 0%,
      rgba(88, 129, 250, 0) 15.84%
    );
    margin: 8px 6px 0;
    border-radius: 40px 40px 0 0;
    // overflow: hidden;
    overflow: visible;
  }
  .header {
    // padding: 16px;
    padding-top: calc(36px / 2);
    text-align: center;
    .title {
      font-size: calc(38px / 2);
      font-weight: 600;
      .title__text {
        background: linear-gradient(99deg, #a12828 27.26%, #333 52.63%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: inline-block;
        position: relative;
      }
      .title__line {
        position: absolute;
        width: calc(168px / 2);
        height: calc(20px / 2);
        display: block;
        background-image: url('@/assets/images/member/loan-give-dialog-title-line.png');
        background-size: 100% 100%;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        z-index: -1;
      }
    }
    .sub-title {
      font-size: calc(30px / 2);
      color: #afa9a9;
      margin-top: 7px;
      font-weight: 400;
    }
  }
  .body {
    // height: 400px;
    margin: 0 -6px;
    padding-bottom: 20px;
    .desc {
      text-align: center;
      font-size: 15px;
      font-weight: 400;
      color: #cfcfcfcc;
      margin-top: 14px;
    }
  }
  .list-swiper {
    // padding-bottom: 40px;
    margin-top: 20px;
    height: calc(560px / 2);
    .swiper-wrapper {
      .swiper-slide {
        width: calc(272px / 2);
        // height: calc(400px / 2);
        // background-color: red;
        .image-wrapper {
          background-image: url('@/assets/images/member/loan-give-bg.png');
          background-size: 100% 100%;
          width: calc(272px / 2);
          height: calc(323px / 2);
          box-sizing: border-box;
          padding: 30px 5px 5px;
          border-radius: 10px;
          overflow: hidden;
          .img {
            width: 100%;
            height: 100%;
            display: block;
            border-radius: 10px;
            background-color: #fff;
          }
        }
        .content {
          margin-top: 4px;
          .name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.2;
          }
          .price {
            color: #bebcbc;
            font-size: 15px;
            margin-top: 4px;
          }
          .btn {
            width: calc(220px / 2);
            height: calc(60px / 2);
            background-color: var(--primary-color);
            border-radius: 15px;
            border: none;
            color: #fff;
            font-size: 15px;
            font-weight: 400;
            border-radius: 999vw;
            display: block;
            margin: 4px auto 0;
          }
        }
      }
    }
    .swiper-pagination {
      :deep() {
        .swiper-pagination-bullet {
          width: 5px;
          height: 5px;
          // background-color: #fff;
          // opacity: 1;
          border-radius: 999vw;
          margin: 0 2px;
        }
        .swiper-pagination-bullet-active {
          background-color: #5881fa;
          width: 20px;
        }
      }
    }
  }
}
</style>
