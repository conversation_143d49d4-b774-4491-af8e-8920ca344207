<template>
  <div class="more-benefit">
    <div class="title">更多权益</div>
    <div class="more-list">
      <div class="more-item" @click="handleItem('loan')">
        <div class="p1">专属VIP通道</div>
        <div class="p1">审核快人一步</div>
        <div class="p2"></div>
        <img class="more-img" src="@/assets/images/member/more-vip.png">
      </div>
      <div class="more-item" @click="handleItem('egg')">
        <div class="p1">会员返积分</div>
        <div class="p1">天天砸金蛋</div>
        <div class="p2">小积分砸出千元E卡</div>
        <img class="more-img" src="@/assets/images/member/more-egg.png">
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const emit = defineEmits(['buyTips'])
const handleItem = (type) => {
  if (checkedMember.value.vipCust) {
    if (type === 'loan') {
      router.push('/cash-loan')
    }else if (type === 'egg') {
      router.push('/integral/draw')
    }
  } else {
    emit('buyTips')
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .more-benefit{
    margin-top: 15px;
    .title{
      font-size: 18px;
      color: #333333;
      font-weight: bold;
      text-align: center;
    }
    .more-list{
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      .more-item{
        width: 165px;
        padding: 15px 0;
        background: #FFFFFF;
        border-radius: 12px;
        text-align: center;
        .p1{
          font-size: 14px;
          color: #333333;
          line-height: 18px;
          font-weight: bold;
          margin-left: 14px;
          text-align: left;
        }
        .p2{
          height: 18px;
          font-size: 12px;
          color: #666666;
          line-height: 18px;
          margin-top: 6px;
          margin-left: 14px;
          text-align: left;
        }
        .more-img{
          width: 137px;
          height: auto;
          margin: 2px auto 0;
        }
      } 
    }
  }
</style>