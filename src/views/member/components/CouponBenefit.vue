<template>
  <div class="coupon">
    <div class="coupon-list">
      <div
        v-if="benifit.packageType === 'COUPON'"
        class="coupon-item"
        v-for="item in benifit.vipBenefitGoodsInfo">
        <coupon
          :item="item.ticketDefine"
          :reactiveNum="item.receiveNum"
          @handleReceive="handleReceive(item)"
        />
      </div>
      <div v-else class="coupon-item" v-for="item in benifit.vipBenefitGoodsInfo[0].ticketList">
        <coupon
          :item="item"
          :reactiveNum="benifit.vipBenefitGoodsInfo[0].receiveNum"
          @handleReceive="handleReceive(benifit.vipBenefitGoodsInfo[0])"
        />
      </div>
    </div>
    <div
      v-if="benifit.packageType === 'COUPON_PACKAGE'"
      :class="`package-receive-btn ${ benifit.vipBenefitGoodsInfo[0].receiveNum > 0 ? 'disabeld' : ''}`"
      @click="handleReceive(benifit.vipBenefitGoodsInfo[0])"
    >一键领取</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import Coupon from './Coupon'
const { proxy } = getCurrentInstance()
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  list: {
    type:Array,
    default: () => []
  },
  benifit: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleReceive'])
const handleReceive = (item) => {
  if (item.receiveNum > 0) {
    return
  }
  emit('handleReceive', props.benifit, item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .coupon-list{
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    padding-bottom: 15px;
    padding-right: 10px;
    .coupon-item{
      min-width: 98px;
      flex: 1;
      height: 136px;
      box-sizing: border-box;
      margin-left: 10px;
      margin-top: 10px;
      position: relative;
      background: linear-gradient( 180deg, #FFF3D9 0%, #FEE5B5 100%);
      border-radius: 12px;
      overflow: hidden;
    }
    .coupon-item::before{
      content: ' ';
      position: absolute;
      left: -5px;
      top: 81px;
      background: #FFFAF5;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      z-index: 99;
    }
    .coupon-item::after{
      content: ' ';
      position: absolute;
      right: -5px;
      top: 81px;
      background: #FFFAF5;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      z-index: 99;
    }
  }
  .package-receive-btn {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #FFFFFF;
    background: linear-gradient( 180deg, #FF8338 0%, #FF6322 100%);
    border-radius: 40px;
    margin: 0 10px 15px;
  }
  .package-receive-btn.disabeld{
    background: #E8E8E8;
    color: #A7A7A7;
  }
</style>