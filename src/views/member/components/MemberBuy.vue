<template>
  <div class="buy-wrapper" v-if="!checkedMember.vipCust">
    <div class="title">
      <img src="@/assets/images/member/title-l.png">
      <div class="text">选择会员套餐</div>
      <img src="@/assets/images/member/title-r.png">
    </div>
    <div class="buy-tips" v-if="checkedMember.vipPrice && !checkedMember.vipCust">
      购买权益有效期增加{{ checkedMember.vipPrice.durationUnit === 'MONTH' ?
      checkedMember.vipPrice.duration * 30 : checkedMember.vipPrice.duration }}天，数量有限，先买先得</div>
    <membership-level v-if="!checkedMember.vipCust" :data="data"></membership-level>
    <!-- <div class="cust-tips" v-if="checkedMember.vipCust">
      <div class="p theme-text">当前会员已购买 <span class="order" @click="handleOrder">《我的订单》</span></div>
      <div class="p">会员有效时间至：{{ parseTime(checkedMember.vipCust.invalidTime) }}</div>
    </div> -->
    <div class="buy-fiexd" v-if="!checkedMember.vipCust" :style="buyStyle">
      <div class="buy-btn" @click="emit('handleBuy')">立即开通</div>
      <div class="agreement">
        <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
        <span class="agree">我已阅读并知晓
          <span class="highlight theme-text" @click="agreementRef.show=true">《会员服务协议》</span>
        </span>
      </div>
    </div>
    <agreement-popup url="/agreement/member-service.htm" title="轻享花如意会员服务协议" ref="agreementRef"></agreement-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance,inject } from 'vue'
import AgreementPopup from '@/components/AgreementPopup'
import MembershipLevel from './MembershipLevel'
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const checked = ref(true)
const agreementRef = ref(false)
const porps = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  buyStyle: {
    type: Object,
    default: () => {}
  }
})
const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleBuy'])
const handleOrder = () => {
  // 不再支持直接进入会员订单列表
  //router.push('/member/equity-order?vipCode=VIP')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {

})
</script>
<style scoped lang='scss'>
  .buy-wrapper{
    padding-top: 22px;
    .title{
      display: flex;
      align-items: center;
      padding: 0 48px;
      img{
        width: 49px;
        height: 6px;
        display: block;
      }
      .text{
        font-weight: bold;
        font-size: 17px;
        color: #000000;
        line-height: 24px;
        flex: 1;
        text-align: center;
      }
    }
    .buy-tips{
      font-size: 13px;
      color: #666666;
      line-height: 18px;
      text-align: center;
      margin-top: 7px;
    }
    .buy-fiexd{
      padding: 28px 12px 10px;
      width: calc(100% - 24px);
    }
    .buy-btn{
      height: 50px;
      background: linear-gradient( 180deg, #FF671A 0%, #FF4019 100%);
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50px;
    }
    .agreement{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      .agree{
        font-weight: 400;
        font-size: 12px;
        transform: scale(0.9);
        color: #999999;
        transform-origin: left center;
        margin-left: 8px;
      }
    }
  }
  .cust-tips{
    margin: 12px;
    font-size: 12px;
    .p +.p{
      margin-top: 5px;
    }
  }
  .order{
    color: blue;
    display: inline-block;
    text-decoration: underline;
  }
</style>
