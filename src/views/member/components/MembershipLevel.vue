<template>
  <div class="level">
    <swiper
      class="swiperBox"
      @slideChange="onSlideChange"
      :slides-per-view="data.length > 2 ? 3 : data.length === 2 ? 2 : 1"
      :space-between="10"
    >
      <swiper-slide v-for="(item, index) in data" :key="index">
        <div
          class="level-item"
          :class="{ 'active': item.vipPrice.id === checkedMember.vipPrice.id }"
          @click="handleChecked(item)"
        >
          <div class="name">{{ item.vipPrice.vipName }}</div>
          <div class="price"><span>¥</span>{{ item.vipPrice.discountPrice }}</div>
          <div class="tips">折合约{{ formatMoney(divide(item.vipPrice.discountPrice,
          item.vipPrice.durationUnit === 'MONTH' ? item.vipPrice.duration * 30 : item.vipPrice.duration)) }}元每天</div>
          <img v-if="item.vipPrice.id === checkedMember.vipPrice.id" class="checked" src="@/assets/images/member/buy-checked.png">
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue'
import { divide } from '@/utils/common'
import 'swiper/css'
import { inject } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const porps = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
const updateMember = inject('updateMember')
const onSlideChange = (checked) => {
  console.log(checked)
}
const handleChecked = (item) => {
  updateMember(item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .level{
    margin-top: 22px;
    .swiperBox{
      padding: 0 12px;
    }
    display: flex;
    .swiper-slide:last-child{
      padding-right: 12px;
    }
    .level-item{
      background: linear-gradient( 180deg, #FCFCFC 0%, #FFFFFF 100%);
      border: 1px solid #D8D8D8;
      border-radius: 8px;
      flex: 1;
      height: 140px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      .checked{
        width: 27px;
        height: 27px;
        position: absolute;
        right: -1px;
        bottom: -1px;
      }
      .name{
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
      .price{
        font-weight: 600;
        font-size: 24px;
        color: #FF4019;
        line-height: 42px;
        margin-top: 6px;
        span{
          font-weight: 400;
          font-size: 12px;
          transform: scale(0.85);
          color: #FF4019;
          line-height: 14px;
          margin-right: 1px;
        }
      }
      .tips{
        margin-top: 10px;
        font-size: 12px;
        transform: scale(0.85);
        color: #999999;
        line-height: 14px;
      }
    }
    .level-item.active{
      background: linear-gradient( 180deg, #FFEFDD 0%, #FFFFFF 100%);
      box-shadow: 0px 0px 6px 0px rgba(153,153,153,0.16);
      border: 1px solid #FF671A;
    }
    .level-item +.level-item{
      margin-left: 10px;
    }
  }
</style>
