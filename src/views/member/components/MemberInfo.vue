<template>
  <div class="member-info">
    <div class="right-top">
      <div class="open-btn" v-if="!checkedMember.vipCust" @click="emit('handleBuy')">立即开通</div>
      <div v-else class="period-valid">
        <!-- <img class="vip-label" src="@/assets/images/member/vip-label.png"> -->
        <div class="order-icon" @click="handleMemberOrder">
          权益订单
          <van-icon name="arrow"></van-icon>
        </div>
        <div class="time">{{ parseTime(checkedMember.vipCust.invalidTime, '{y}/{m}/{d}') }}到期</div>
      </div>
    </div>
    <img class="member-name" src="@/assets/images/member/member-name.png">
    <div class="decs" v-if="checkedMember.vipPrice">
      <span>{{ checkedMember.vipPrice.title }}</span>
    </div>
    <member-info-benefit />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance,inject } from 'vue'
import MemberInfoBenefit from './MemberInfoBenefit';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleBuy'])
const handleOrder = () => {
  // 不再支持直接进入会员订单列表
  //router.push('/member/equity-order?vipCode=VIP')
}
const handleMemberOrder = () => {
  router.push('/member/equity-order')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .member-info{
    background: linear-gradient( 179deg, #FFE6B5 0%, #FFD895 49%);
    margin: 0 15px;
    position: relative;
    padding-top: 15px;
    padding-bottom: 20px;
    box-sizing: border-box;
    border-radius: 12px 12px 0 0;
    .right-top{
      position: absolute;
      right: 15px;
      top: 23px;
      .open-btn{
        width: 80px;
        height: 32px;
        background: #C28962;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        color: #FFFFFF;
      }
      .period-valid{
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .order-icon{
          text-align: right;
          font-size: 14px;
          color: #5F422E;
          line-height: 16px;
          font-weight: bold;
        }
        .vip-label{
          width: 44px;
          height: 16px;
          display: block;
        }
        .time{
          margin-top: 7px;
          font-size: 12px;
          color: #5F422E;
          line-height: 22px;
        }
      }
    }
    .member-name{
        display: block;
        width: 175px;
        height: 25px;
        margin-left: 15px;
    }
    .decs{
      font-size: 13px;
      color: #5B4538;
      line-height: 18px;
      margin-left: 15px;
      margin-top: 8px;
      display: flex;
      align-items: center;
      .order-text{
        text-decoration: underline;
        margin-left: 10px;
      }
    }
  }
</style>