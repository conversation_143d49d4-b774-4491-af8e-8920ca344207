<template>
  <div>
    <div v-for="(benefit,index) in benefitList" :key="index">
      <div :class="`benefit-item`" v-if="BenefitTypes.includes(benefit.packageType)">
        <div :class="`clamp`">
          <div class="content">{{benefit.name}}</div>
          <div class="diagonal-half"></div>
        </div>
        <div class="item-content">
          <div class="title1">
            <div class="text">{{benefit.title1}}</div>
          </div>
          <div class="title2">{{benefit.title3}}</div>
          <goods-benefit
            v-if="benefit.packageType === 'GOODS'"
            :benefit="benefit"
            @handleGoods="handleGoods"
          ></goods-benefit>
          <open-member-gift
            v-if="benefit.packageType === 'LOAN_GIVE'"
            :benefit="benefit"
            @handleGoods="handleGoods"
          ></open-member-gift>
          <div v-if="benefit.packageType === 'LOAN_GIVE'" class="order-btn" @click="handleOrder">
            查看订单
          </div>
          <coupon-benefit
            v-else-if="benefit.packageType === 'COUPON' || benefit.packageType === 'COUPON_PACKAGE'"
            :benifit="benefit"
            @handleReceive="handleReceive"
          />
        </div>
      </div>
      <div v-if="benefit.packageType === 'VOUCHER'" class="quota-content">
        <div class="title">{{benefit.title1}}</div>
        <div class="quota-list">
          <div class="quota-item" v-for="item in benefit.vipBenefitGoodsInfo">
            <template v-if="item.receiveNum > 0">
              <img v-if="item.voucherUseType ==='CASH_QUOTA'" class="bg" src="@/assets/images/member/quota-bg-cash.png">
              <img v-else class="bg" src="@/assets/images/member/quota-bg-consume.png">
              <img v-if="item.voucherUseType ==='CASH_QUOTA'" class="receive-sign" src="@/assets/images/member/quota-receive-cash.png">
              <img v-else class="receive-sign" src="@/assets/images/member/quota-receive-consume.png">
              <div :class="`text vip ${item.voucherUseType}`">
                <div class="p1">{{ item.voucherUseType === 'CASH_QUOTA' ? '极享金' : '极刻花' }}</div>
                <div class="p2">轻松提额度</div>
              </div>
              <div :class="`btn list ${item.voucherUseType}`" @click="handleVoucher(item)">去提额</div>
            </template>
            <template v-else>
              <img class="bg" v-if="item.voucherUseType ==='CASH_QUOTA'" src="@/assets/images/member/quota-cash.png">
              <img class="bg" v-else src="@/assets/images/member/quota-consume.png">
              <div class="text">
                <div class="p1">{{ item.voucherUseType === 'CASH_QUOTA' ? '极享金' : '极刻花' }}</div>
                <div class="p2">轻松提额度</div>
              </div>
              <div :class="`btn ${item.voucherUseType}`" @click="handleReceive(benefit, item)">立即领取</div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { memberBenefitList, receiveSubmit } from '@/api/member'
import Sku from './Sku.vue'
import CouponBenefit from './CouponBenefit';
import GoodsBenefit from './GoodsBenefit';
import OpenMemberGift from './OpenMemberGift'
import { Toast, showLoadingToast, closeToast, showSuccessToast, showDialog } from 'vant';
const { proxy } = getCurrentInstance();
const BenefitTypes = ['GOODS', 'COUPON', 'COUPON_PACKAGE', 'LOAN_GIVE']
const store = useStore();
const route = useRoute();
const router = useRouter();
const vipId = ref(0)
const benefitList = ref([])
const checkedMember = inject('checkedMember')
watch(checkedMember, (val) => {
  vipId.value = val.vipPrice.id
  memberBenefitList({ id: val.vipPrice.id }).then(res => {
    benefitList.value = res.data || []
  })
})
const emit = defineEmits(['buyTips'])
const handleGoods = (benefit, item) => {
  if(checkedMember.value.vipCust) {
    const params = { goodsId: item.spuId, 
    skuId: item.skuId, packageId: benefit.id, shoppingFlag: 'V' }
    if (item.categoryGroup === 'COUPON') {
      router.push({path: '/goods-coupon', query: params })
    } else {
      router.push({path: '/goods-detail', query: params })
    }
  } else {
    emit('buyTips')
  }
}
const handleVoucher = () => {
  router.push('/my/voucher?tab=QUOTA')
}
const handleOrder = () => {
  router.push('/member/equity-order')
}
// 领取
const handleReceive = (benefit, item) => {
  if(checkedMember.value.vipCust) {
    if (benefit.periodNum === benefit.receiveNum) {
      showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
      return
    }
    if (item.receiveNum === 0) {
      receiveSubmit({ id: item.id }).then(res => {
        showSuccessToast('领取成功')
        memberBenefitList({ id: vipId.value }).then(res => {
          benefitList.value = res.data || []
        })
      })
    }
  } else {
    emit('buyTips')
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .benefit-item.first{
    margin-top: 15px;
  }
  .benefit-item{
    background: #ffffff;
    border-radius: 12px;
    position: relative;
    padding-top: 26px;
    padding-bottom: 10px;
    margin-top: 30px;
    .clamp.first{
      top: 0px;
    }
    .clamp{
      position: absolute;
      left: -5px;
      top: -13px;
      background: linear-gradient( 270deg, #291B04 0%, #573C10 100%);
      border-radius: 12px 2px 12px 2px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content:center;
      font-size: 13px;
      color: #D7C6AC;
      padding: 0 12px;
      min-width: 60px;
    }
    .item-content{
      .title1{
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        .text{
          text-align: center;
        }
        .more{
          font-size: 13px;
          font-weight: 400;
        }
      }
      .title2{
        font-size: 12px;
        color: #A6A6A6;
        line-height: 18px;
        margin-top: 6px;
        text-align: center;
      }
      .title3{
        font-size: 12px;
        color: #4A280C;
        line-height: 17px;
        text-align: center;
        margin-top: 6px;
      }
      .box-list{
        display: flex;
        flex-wrap: wrap;
        margin: 38px 10px 0 10px;
        .item {
          margin: 0 7px 24px;
        }
      }
      .order-btn{
        text-align: center;
        font-size: 13px;
        color: #333333;
        line-height: 18px;
      }
    }
  }
  .quota-content{
    padding-top: 22px;
    padding-bottom: 10px;
    .title{
      font-weight: bold;
      font-size: 17px;
      color: #000000;
      line-height: 24px;
      text-align: center;
    }
    .quota-list{
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      .quota-item {
        width: 164px;
        height: 120px;
        position: relative;
        .bg {
          position: absolute;
          width: 164px;
          height: 120px;
          display: block;
        }
        .receive-sign{
          width: 71px;
          height: 71px;
          position: absolute;
          top: 10px;
          right: 6px;
        }
        .text{
          position: absolute;
          top: 19px;
          left: 18px;
          color: #ffffff;
          .p1{
            font-size: 18px;
            line-height: 25px;
            font-weight: bold;
          }
          .p2{
            font-size: 12px;
            line-height: 16px;
            zoom:0.9;
          }
        }
        .text.vip{
          color: #FF4419;
        }
        .text.CONSUME_QUOTA{
          color: #1E4EFC;
        }
        .btn{
          position: absolute;
          width: 130px;
          height: 30px;
          background: linear-gradient( 180deg, #FFFFFF 0%, #FFE8DB 100%);
          border-radius: 16px;
          border: 1px solid #FF9C9C;
          font-size: 13px;
          font-weight: bold;
          color: #FF5723;
          line-height: 30px;
          text-align: center;
          bottom: 14px;
          left: 19px;
        }
        .btn.list{
          left: 12px;
          bottom: 15px;
          width: 80px;
        }
        .btn.CONSUME_QUOTA{
          background: linear-gradient( 180deg, #FFFFFF 0%, #DBFCFF 100%);
          border-radius: 16px;
          border: 1px solid #1A3FFF;
          color: #1A3FFF;
        }
      }
    }
  }
</style>