<template>
  <div class="item">
    <div class="cover">
      <img :src="data.imgUrl" />
    </div>
    <div class="name">{{ data.name }}</div>
    <s class="oPrice">原价¥{{ data.salePrice }}</s>
    <div class="price">
      <div class="tag">会员</div>
      <span class="unit">¥</span>
      <span class="num">{{ data.price }}</span>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    data: { type: Object, default: () => ({}) },
  });
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 93px;
  height: 135px;
  padding-top: 49px;
  background: #ffffff;
  border-radius: 8px;
  box-sizing: border-box;
  .cover {
    position: absolute;
    top: -17px;
    left: 50%;
    margin-left: -25px;
    width: 50px;
    height: 50px;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px #fbeee1;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .name {
    max-width: 6em;
    font-size: 13px;
    font-weight: 500;
    color: #4a280c;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .oPrice {
    margin-top: 7px;
    font-size: 10px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
  .price {
    display: flex;
    align-items: center;
    height: 20px;
    margin-top: 10px;
    padding-right: 7px;
    background: #fffaf5;
    border-radius: 11px;
    border: 2px solid #f1dcbf;
    font-size: 14px;
    font-weight: 600;
    color: #202337;
  }
  .unit {
    font-size: 8px;
  }
  .tag {
    width: 36px;
    height: 100%;
    padding-right: 2px;
    margin-right: 2px;
    background: #202337;
    border-radius: 11px 0 0 11px;
    line-height: 18px;
    text-align: center;
    font-size: 11px;
    font-weight: 400;
    color: #f9e0c3;
    clip-path: polygon(0% 0%, 100% 0%, 85% 100%, 0% 100%);
  }
}
</style>
