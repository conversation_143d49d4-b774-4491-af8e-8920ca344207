<template>
  <div v-if="show" class="bankcard-add-page">
    <van-popup
      class="main-popup"
      :close-on-click-overlay="false"
      round
      v-model:show="show"
      closeable
      position="bottom"
      teleport="body"
    >
      <div v-if="bindType === 'cash'" class="title margin-bottom">请确认您的收款账户</div>
      <div v-else class="title margin-bottom">绑定银行卡（储蓄卡）</div>
      <div class="addcard">
        <img class="card" src="@/assets/images/member/addcard-card.png" />
        <img class="bg" src="@/assets/images/member/addcard-bg.png" />
      </div>
      <div class="form" v-if="form.custIdCard">
        <div class="form-title">银行卡信息</div>
        <van-form ref="formSubmit" @submit="onSubmit">
          <van-cell-group>
            <van-field
              v-if="user.flow !== $global.USER_FLOW_IDCARD"
              v-model="form.custIdCard.shieldName"
              label="持卡人"
              readonly
            />
            <van-field
              v-else
              v-model="form.custIdCard.name"
              label="持卡人"
              placeholder="请填写本人姓名"
              :rules="[{ required: true, message: '请填写本人姓名' }]"
            />
            <van-field
              v-model="form.bankCard.phone"
              label="手机号码"
              placeholder="请填写银行预留手机号码"
              type="tel"
              maxlength="11"
              :rules="[{ required: true, message: '请填写银行预留手机号码' }]"
            >
              <template #right-icon>
                <van-icon name="warning-o" @click="phoneShow = true" />
              </template>
            </van-field>
            <van-field
              v-if="user.flow === $global.USER_FLOW_IDCARD"
              v-model="form.custIdCard.idcard"
              label="身份证号"
              placeholder="请填写持卡人身份证"
              :rules="[{ required: true, message: '请填写持卡人身份证' }]"
            />
            <van-field
              v-model="form.bankCard.cardNo"
              label="银行卡号"
              type="number"
              pattern="[0-9]*"
              placeholder="请填写银行卡号"
              :formatter="formatter"
              maxlength="25"
              @blur="onBankBlur"
              :rules="[{ required: true, message: '请填写银行卡号' }]"
            >
              <template v-if="form.bankCard.cardNo" #right-icon>
                <van-icon name="close" @click="clearCardNo" />
              </template>
            </van-field>
            <van-field v-model="form.bankName" label="银行名称" placeholder="银行名称" readonly>
              <template #right-icon>
                <van-icon name="warning-o" @click="supportingBanksRef.show = true" />
              </template>
            </van-field>
          </van-cell-group>
        </van-form>
      </div>
      <div class="agreement">
        <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
        <span class="agree"
          >已同意阅读<span
            class="highlight theme-text"
            @click="
              agreementPopupRef.reveal({
                title: '账户委托扣款授权书',
                src: '/agreement/account_auth.htm',
              })
            "
            >《账户委托扣款授权书》</span
          >、<span
            class="highlight theme-text"
            @click="
              agreementPopupRef.reveal({
                title: '数字证书服务协议',
                src: '/agreement/digital-certificate-protocol.htm',
              })
            "
            >《数字证书协议》</span
          ></span
        >
      </div>
      <div class="submit-btn" @click="handleSubmit">确定</div>
    </van-popup>
    <van-popup v-model:show="smsShow" round teleport="body" :close-on-click-overlay="false">
      <div class="sms-popup">
        <div class="phone-text">填写验证码</div>
        <div v-if="bindType === 'cash'" class="sms-popup-tip">
          <img class="sms-popup-tip-image" src="@/assets/images/member/redpack.png" />
          <span class="sms-popup-tip-text">仅差一步即可领取现金</span>
        </div>
        <van-cell
          class="sms-popup-phone-number"
          title="手机号码"
          :title-style="{ fontSize: '17px', color: '#333', lineHeight: '34px' }"
        >
          <span style="color: #333; font-size: 17px; line-height: 34px">{{
            phoneFormat(form.bankCard.phone) || '***********'
          }}</span>
        </van-cell>
        <van-field v-model="code" placeholder="请输入验证码" type="number" @input="resetInputVal">
          <template #button>
            <van-button
              v-if="times === 0"
              class="sms-btn"
              size="small"
              round
              type="text"
              @click="getCode"
              >获取验证码</van-button
            >
            <van-button v-else class="sms-btn text-gray" size="small" round type="text"
              >{{ times }}s</van-button
            >
          </template>
        </van-field>
        <div class="tips">已发送验证码短信到</div>
        <div class="btn-wrapper">
          <div class="cancel-btn btn" @click="smsClose">取消</div>
          <div class="confirm-btn btn" @click="onBindCard">确认</div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="phoneShow" teleport="body" round closeable>
      <div class="phone-popup">
        <div class="title">预留手机号</div>
        <div class="tips">
          该手机号码是您办理该银行卡时在银行填写的号码，如有疑问请联系该银行客服
        </div>
        <div class="btn" @click="phoneShow = false">确认</div>
      </div>
    </van-popup>
    <!-- <van-popup v-model:show="bankShow" :style="{'width': '90%'}" closeable>
      <img class="support-bank-img" src="@/assets/images/my/support-bank.png">
    </van-popup> -->
    <supporting-banks ref="supportingBanksRef"></supporting-banks>
    <iframe-popup ref="agreementPopup" />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { saveBankCard, saveBindCard, getBankName, getBankSmsCode } from '@/api/bankcard'
import { showFirstName, phoneFormat, plusXing } from '@/utils/common'
import SupportingBanks from '@/components/SupportingBanks'
import { showToast } from 'vant'
import { useTemplateRef } from 'vue'
import IframePopup from '@/components/IframePopup.vue'
const props = defineProps({
  bindParams: {
    type: Object,
    default: () => {},
  },
  bindType: {
    type: String,
    default: '',
  },
})
const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const route = useRoute()
const user = computed(() => store.getters.userInfo)
const checked = ref(true)
const formSubmit = ref(null)
const code = ref('')
const show = ref(false)
const smsShow = ref(false)
const bankShow = ref(false)
const phoneShow = ref(false)
const newCardId = ref(0)
const bankName = ref('')
const times = ref(0)
const supportingBanksRef = ref(null)
// const cheight = ref('600px')
const agreementPopupRef = useTemplateRef('agreementPopup')
// 格式化银行卡
const formatter = (value) => {
  return value.replace(/\s/, '').replace(/([0-9]{4})(?=[0-9])/g, '$1 ')
}
let timer = null
const data = reactive({
  form: {},
})
const { form } = toRefs(data)
const reset = () => {
  form.value = {
    bankCard: {
      channelId: '',
      phone: '',
      cardNo: '',
    },
    custIdCard: {
      name: '',
      idcard: '',
    },
    bankName: '',
  }
}
const resetForm = () => {
  form.value.custIdCard.name = user.value.custIdCard.name
  form.value.custIdCard.shieldName = showFirstName(user.value.custIdCard.name)
  form.value.custIdCard.idcard = user.value.custIdCard.idcard
  form.value.custIdCard.shieldId = plusXing(user.value.custIdCard.idcard, 14, 0)
}
// 监听参数
watch(
  () => props.bindParams,
  (val) => {
    form.value.productId = val.productId || undefined
    form.value.bankCard.channelId = val.bankChannelId
    form.value.orderId = val.orderId || undefined
    form.value.bankCard.cardNo = val.cardNo || undefined
    form.value.cmId = val.cmId || undefined
  },
  { deep: true }
)
watch(
  () => store.getters.userInfo,
  (newValue) => {
    // 重新刷新
    if (newValue) {
      resetForm()
    }
  }
)
const emit = defineEmits(['bindSuccess'])
const handleSubmit = () => {
  formSubmit.value.submit()
}
// 提交绑定
const onSubmit = () => {
  form.value.bankCard.cardNo = form.value.bankCard.cardNo.replace(/\s/g, '')
  form.value.returnUrl = route.query.returnUrl
  saveBankCard(form.value).then((res) => {
    if (res.data.status === 'VALID') {
      proxy.onToastSucc(() => {
        router.go(-1)
      }, '绑卡成功')
    } else {
      if (res.data.signMode === 'API') {
        // 内部验证码
        countDown()
        smsShow.value = true
        newCardId.value = res.data.id
      } else if (res.data.signMode === 'PAGE') {
        // 需要去通道绑卡
        proxy.appJS.appOtherWebView(res.data.submitUrl)
      }
    }
  })
}
const resetInputVal = () => {
  const newCode = '' + code.value
  if (newCode > 6) {
    code.value = newCode.substring(0, 6)
  }
}
// 重新获取验证码
const getCode = () => {
  if (times.value === 0) {
    countDown()
    getBankSmsCode({ id: newCardId.value })
  }
}
const countDown = () => {
  times.value = 60
  timer = setInterval(function () {
    times.value--
    if (times.value === 0) {
      clearInterval(timer)
    }
  }, 1000)
}
const smsClose = () => {
  smsShow.value = false
  clearInterval(timer)
}
const clearCardNo = () => {
  form.value.bankCard.cardNo = ''
  form.value.bankName = ''
}
// 验证码确认
const onBindCard = () => {
  if (code.value) {
    saveBindCard({ verifyCode: code.value, id: newCardId.value }).then((res) => {
      proxy.onToastSucc(() => {
        show.value = false
        smsShow.value = false
        emit('bindSuccess', res.data.id)
      }, '绑定成功')
    })
  } else {
    showToast('请填写验证码！')
  }
}
const onBankBlur = () => {
  const _cardNo = form.value.bankCard.cardNo
  if (_cardNo && _cardNo.length > 15) {
    getBankName({ bankCard: { cardNo: _cardNo.replace(/\s/g, '') } }).then((res) => {
      form.value.bankName = res.data.bankName
    })
  } else {
    form.value.bankName = ''
  }
}
// 设置默认银行信息
const defaultBankCard = (bankCard) => {
  if (bankCard) {
    form.value.bankCard.cardNo = bankCard.cardNo
    form.value.bankName = bankCard.bankName
    form.value.bankCard.phone = bankCard.wholePhone
  } else {
    form.value.bankCard.cardNo = undefined
    form.value.bankName = undefined
    form.value.bankCard.phone = undefined
  }
}
onMounted(() => {
  reset()
  if (user.value && user.value.flow !== proxy.$global.USER_FLOW_IDCARD) {
    resetForm()
  }
  // if (window.screen.height > 667) {
  //   cheight.value = '700px'
  // }
})
defineExpose({ show, defaultBankCard })
</script>

<style lang="scss" scoped>
.main-popup {
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    text-align: center;
    padding-top: 18px;
  }

  :deep(.van-popup) {
    margin: 0;
    max-width: 100%;
  }

  .addcard {
    padding: 10px 0;
    height: 100px;
    text-align: center;
    overflow: hidden;
    .card {
      height: 185px;
    }
    .bg {
      height: 319px;
      margin-top: -110px;
    }
  }

  .form {
    margin: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 10px;

    .form-title {
      font-size: 15px;
      font-weight: 600;
      color: #000;
      text-align: left;
      margin-bottom: 10px;
    }
  }
  --van-cell-font-size: 15px;
  --van-cell-value-font-size: 15px;
  .van-cell-group {
    background-color: #f9f9f9;
  }
  .van-cell {
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    margin-bottom: 10px;
  }
}
.agreement {
  display: flex;
  font-size: 12px;
  align-items: center;
  line-height: 22px;
  margin-top: 10px;
  padding: 0 20px;
  color: #333;
  .agree {
    margin-left: 3px;
  }
  .highlight {
    display: inline-block;
    color: #fd511b;
  }
}
.submit-btn {
  text-align: center;
  height: 40px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  line-height: 40px;
  margin: 5px 20px 20px;
  background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
}
.bottom-confirm.iphonex {
  // padding-bottom: 34px;
  padding-bottom: var(--safe-area-inset-bottom);
}
.phone-popup {
  width: 300px;
  .title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #363636;
    line-height: 22px;
    text-align: center;
    margin-top: 22px;
  }
  .tips {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #a8a8a8;
    line-height: 18px;
    margin: 12px 20px 0;
  }
  .btn {
    width: 252px;
    height: 40px;
    line-height: 40px;
    border-radius: 21px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    margin: 0 auto;
    margin-top: 38px;
    margin-bottom: 38px;
    text-align: center;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
  }
}
.tips-bank {
  background: #ffffff;
  padding-left: 120px;
  font-size: 13px;
  padding-top: 5px;
}
.support-bank-img {
  width: 100%;
  height: 100%;
}
</style>
