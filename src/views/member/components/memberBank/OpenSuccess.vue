<template>
  <van-popup 
    class="overflow-inherit"
    round
    v-model:show="show"
    :style="{ width: $px2rem('301px') }"
    :close-on-click-overlay="false"
  >
    <div class="content">
      <div class="title">
        {{ title }}
      </div>
      <div class="tips1">{{ tips }}</div>
      <div class="tips2" @click="handleOrder">详细请查看 <span class="theme-text">权益服务订单</span>，海量权益等你使用。</div>
      <div v-if="showBtn" class="card-btn theme-linear-gradient" @click="onConfirm">
        {{ preFlag ? `请继续完成办理${times}S` : '确认' }}
      </div>
      <van-icon class="close" color="#cccccc" name="close" @click="onConfirm"></van-icon>
    </div>
  </van-popup>  
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checked = ref(true)
const agreementRef = ref(null)
const show = ref(false)
const times = ref(3)
const timerId = ref(null);
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  tips: {
    type: String,
    default: ''
  },
  showBtn: {
    type: Boolean,
    default: false
  },
  preFlag: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['onConfirm'])
const onConfirm = () => {
  show.value = false
  if (props.preFlag) {
    clearInterval(timerId.value)
  }
  emit('onConfirm')
}
const startTimes = () => {
  times.value = 3
  timerId.value = setInterval(() => {
    times.value --
    if(times.value === 0) {
      onConfirm()
    }
  }, 1000);
}
const handleOrder = () => {
  show.value = false
  router.push('/member/equity-order')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show, startTimes })
</script>
<style scoped lang='scss'>
  :deep(.van-popup){
    overflow-y:inherit !important;
  }
  .content{
    padding: 34px 20px 26px;
    position: relative;
    .title{
      font-weight: 500;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
      text-align: center;
    }
    .tips1{
      font-weight: bold;
      font-size: 13px;
      color: #333333;
      line-height: 15px;
      text-align: center;
      margin-top: 14px;
    }
    .tips2{
      font-size: 12px;
      color: #666666;
      line-height: 14px;
      text-align: center;
      margin-top: 7px;
    }
    .card-btn{
      height: 40px;
      border-radius: 20px;
      margin: 34px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .close{
      display: block;
      position: absolute;
      bottom: -40px;
      left: calc(50% - 10px);
      font-size: 26px;
    }
  }
</style>