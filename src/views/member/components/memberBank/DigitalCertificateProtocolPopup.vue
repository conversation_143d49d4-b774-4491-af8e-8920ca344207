<template>
  <van-popup :show="isRevealed" class="digital-certificate-protocol-popup" @close="cancel('close')">
    <navigation-bar :isShowBack="false" pageName="数字证书服务协议">
      <template #nav-left>
        <van-icon name="cross" size="22" class="text-gray" @click="cancel('close')" />
      </template>
    </navigation-bar>
    <!-- <div class="header">
      <div class="title">数字证书协议</div>
    </div> -->
    <div class="body">
      <iframe class="iframe" src="/agreement/digital-certificate-protocol.htm"></iframe>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel } = useConfirmDialog()
defineExpose({
  isRevealed,
  reveal,
  confirm,
  cancel,
})
</script>

<style lang="scss" scoped>
.digital-certificate-protocol-popup {
  width: 100%;
  height: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  // .header {
  //   flex: none;
  //   height: 44px;
  //   padding-top: var(--safe-area-inset-top);
  //   .title {
  //     font-size: 16px;
  //     font-weight: 600;
  //     color: #333;
  //   }
  // }
  .body {
    flex: 1;
    min-height: 0;
    .iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}
</style>
