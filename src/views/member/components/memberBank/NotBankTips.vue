<template>
  <van-popup :close-on-click-overlay="false" round v-model:show="show" :style="{ width: $px2rem('300px') }">
    <div class="content">
      <div class="title">{{ title }}</div>
      <div class="btn-wrapper">
        <div class="btn cancel" @click="show=false">取消</div>
        <div class="btn bind theme-linear-gradient" @click="handleBind">我要绑卡享受权益</div>
      </div>
    </div>
  </van-popup>  
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checked = ref(true)
const agreementRef = ref(null)
const show = ref(false)
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['onNext','showMemberAgreement', 'add-bank'])
const handleBind = ()=> {
  show.value = false
  emit('add-bank')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  :deep(.van-popup){
    overflow-y:inherit !important;
  }
  .content{
    padding: 34px 20px 26px;
    position: relative;
    .title{
      font-weight: 500;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
      text-align: center;
    }
    .btn-wrapper{
      display: flex; 
      justify-content: space-between;
      margin-top: 30px;
      .btn{
        text-align: center;
        font-size: 16px;
      }
      .btn.cancel {
        color: #cccccc;
        background: #ffffff;
        border: 2px solid #cccccc;
        border-radius: 22px;
        width: 80px;
        height: 36px;
        line-height: 36px;
        margin-right: 20px;
      }
      .btn.bind {
        color: #ffffff;
        flex: 1;
        height: 40px;
        line-height: 40px;
      }
    }
  }
</style>