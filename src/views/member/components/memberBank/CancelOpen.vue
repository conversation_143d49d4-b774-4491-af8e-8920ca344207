<template>
  <van-popup :close-on-click-overlay="false" round v-model:show="show">
    <div class="content">
      <div class="title">确定要放弃以下特权吗？</div>
      <div class="list">
        <div class="item">
          <div class="item-title">
            <div class="item-title-inside">优先放款特权</div>
          </div>
          <div class="special-title theme-text">VIP专属放款特权</div>
          <div class="p">会员有效期内每个月加速一次</div>
          <div class="p">放款成功则扣除机会，放款失败则返回机会</div>
        </div>
        <div class="item">
          <div class="item-title">
            <div class="item-title-inside">优先提额特权</div>
          </div>
          <div class="special-title"></div>
          <div class="p">会员开通期间持续专享提额特权</div>
          <div class="p">月末月初不定期提额，最高可<span class="theme-text">提额58000元</span></div>
        </div>
        <div class="item">
          <div class="item-title">
            <div class="item-title-inside">优先审核特权</div>
          </div>
          <div class="special-title"></div>
          <div class="p">开通会员期间<span class="theme-text">审核提速到 80%</span></div>
        </div>
      </div>
      <div class="btn-wrapper">
        <div class="btn cancel" @click="show=false">狠心离开</div>
        <div class="btn bind theme-linear-gradient" @click="handleBuy">我要开通特权</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
const emit = defineEmits(['handleBuy'])
const handleBuy = () => {
  show.value = false
  emit('handleBuy')
}
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .content {
    padding: 34px 15px 26px;
    position: relative;
    .title {
      font-weight: 500;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
      text-align: center;
    }
    .list{
      border: 2px solid #F1DCBF;
      border-radius: 22px;
      margin-top: 20px;
      padding: 0 10px 20px;
      text-align: center;
      .item{
        &-title::after{
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 0;
          height: 0;
          border-top: 16px solid transparent;
          border-right: 8px solid #ffffff;
          border-bottom: 16px solid transparent;
        }
        &-title::before{
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-top: 16px solid transparent;
          border-left: 8px solid #ffffff;
          border-bottom: 16px solid transparent;
          z-index: 11;
        }
        &-title{
          display: inline-block;
          color: #F1DCBF;
          font-size: 16px;
          padding: 3px 7px;
          background: #eddec8;
          position: relative;
          margin-top: 20px;
          &-inside {
            background: #22223b;
            padding: 5px 15px;
            position: relative;
          }
          &-inside:after{
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-top: 14px solid transparent;
            border-right: 7px solid #eddec8;
            border-bottom: 14px solid transparent;
          }
          &-inside:before{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-top: 14px solid transparent;
            border-left: 7px solid #eddec8;
            border-bottom: 14px solid transparent;
          }
        }
        .special-title{
          font-size: 14px;
          margin-top: 15px;
        }
        .p{
          font-size: 14px;
          margin-top: 7px;
        }
      }
    }
    .btn-wrapper{
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      .btn{
        text-align: center;
        font-size: 16px;
        height: 40px;
        line-height: 40px;
      }
      .btn.cancel{
        width: 100px;
        color: #999999;
      }
      .btn.bind{
        flex: 1;
        color: #ffffff;
      }
    }
  }
</style>