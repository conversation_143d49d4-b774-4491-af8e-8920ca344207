<template>
  <div class="member-info">
    <img class="member-name" src="@/assets/images/member/member-name.png">
    <div class="decs">
      <span>{{ checkedMember.vipPrice?.title }}</span>
    </div>
    <member-info-benefit />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import MemberInfoBenefit from './MemberInfoBenefit';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .member-info{
        width: 355px;
        height: 248px;
        background: url('@/assets/images/member/info-bg.png') no-repeat;
        background-size: 100% 100%;
        margin: 0 10px -30px;
        position: relative;
        padding-top: 30px;
        box-sizing: border-box;
        .member-name{
            display: block;
            width: 190px;
            height: 31px;
            margin-left: 28px;
        }
        .decs{
            font-size: 13px;
            color: #5B4538;
            line-height: 18px;
            margin-left: 28px;
            margin-top: 11px;
        }
    }
</style>