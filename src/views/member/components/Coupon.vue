<template>
  <div v-if="item">
    <div v-if="item.ticketThreshold && item.ticketThreshold.type === 'MIN_AMOUNT'" class="item-label">
      <div class="zoom-text">平台满减券</div>
    </div>
    <div v-else class="empty-label"></div>
    <div class="item-price"><span class="unit">¥</span>{{ item.ticketDiscount && item.ticketDiscount.value }}</div>
    <div class="item-filter">
      <div class="zoom-text-11" v-if="item.ticketSpuRel.type ==='NONE'">
        {{ item.ticketThreshold.type === 'NONE' ? '无门槛' : '满' + item.ticketThreshold.amount + '元可用' }}
      </div>
      <div class="zoom-text-11" v-else>
        {{ item.ticketSpuRel.type ==='SPU' ? '指定商品可用': item.ticketSpuRel.type ==='CATEGORY' ? ('指定' + item.ticketSpuRel.relName.join(',')+ '可用') : '' }}
      </div>
    </div>
    <div class="dashed-line"></div>
    <div :class="`receive-btn ${ reactiveNum > 0 ? 'receive' : '' }`" @click="emit('handleReceive')">
      {{ reactiveNum > 0 ? '已领取' : '领取' }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  item: {
    type: Object,
    default: () => {}
  },
  reactiveNum: {
    type: Number,
    default: 0
  }
})
const emit = defineEmits(['handleReceive'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .empty-label{
    height: 18px;
  }
  .item-label{
    width: 70px;
    height: 18px;
    margin: 0 auto;
    background: #FEE5B6;
    border-radius: 0px 0px 10px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9D7951;
    .zoom-text{
      font-size: 12px;
      zoom: 0.83px;
    }
  }
  .item-price{
    font-weight: bold;
    font-size: 32px;
    color: #5B3E11;
    text-align: center;
    margin-top: 12px;
  }
  .item-price > .unit{
    font-size: 16px;
    margin-right: 1px;
  }
  .item-filter{
    text-align: center;
    line-height: 18px;
    color: #5B3E11;
  }
  .dashed-line{
    height: 1px;
    background: linear-gradient(to left,transparent 0%,transparent 50%,#F0CBA1 50%,#F0CBA1 100%);
    background-size: 2px 1px;
    background-repeat: repeat-x;
    position: absolute;
    top: 86px;
    left: 0;
    width: 100%;
  }
  .receive-btn{
    width: 70px;
    height: 24px;
    font-size: 12px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FF571A;
    border-radius: 20px;
    margin-top: 10px;
    position: absolute;
    bottom: 13px;
    left: calc(50% - 35px);
  }
  .receive-btn.receive{
    background: #E8E8E8;
    color: #A7A7A7;
  }
</style>