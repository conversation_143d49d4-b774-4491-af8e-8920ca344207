<template>
  <div class="order-return-page">
    <navigation-bar pageName="申请退款" @onLeftClick="onBackClick"></navigation-bar>
    <div class="order-return-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="order-info">
        <div class="order-goods">
          <div class="goods-name">{{ orderInfo.smstitle }}</div>
          <div class="order-id">订单号：{{ orderInfo.orderId }}</div>
        </div>
        <div class="order-price">￥{{ orderInfo.realAmount }}</div>
      </div>
      <div class="return-wrapper">
        <div class="flex justify-between">
          <span class="left">售后方式</span><span class="right">退款</span>
        </div>
        <div class="remark-label">售后原因</div>
        <van-field
          v-model="remark"
          rows="2"
          autosize
          type="textarea"
          show-word-limit
          maxlength="100"
          placeholder="请您填写具体的退款原因，以便更快的为您办理退款，不少于20 字"
        />
      </div>
      <div :class="`bottom-confirm ${isIphoneX ? 'iphonex' : ''}`">
        <div class="submit-btn theme-linear-gradient" @click="handleSubmit">提交申请</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { getEuqityOrder, submitReturn } from '@/api/member'
import { showToast } from 'vant';
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const route = useRoute()
  const router = useRouter()
  const remark = ref('')
  const data = reactive({
    orderInfo: {}
  })
  const { orderInfo } = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  const handleSubmit = () => {
    if (remark.value.length >= 20) {
      submitReturn({ orderId: orderInfo.value.orderId, desc: remark.value }).then(res => {
        proxy.onToastSucc(() => {
          onBackClick()
        }, '提交成功')
      })
    } else {
      showToast('请填写不少于20字退款原因')
    }
  }
  onMounted(() => {
    orderInfo.value = JSON.parse(sessionStorage.getItem('equityOrderDetail'))
  })
</script>

<style lang="scss" scoped>
  .order-return-page{
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #F5F5F5;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .order-info{
        margin: 10px;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 20px 16px 18px 16px;
        display: flex;
        justify-content: space-between;
        .order-goods{
          .goods-name{
            font-size: 16px;
            font-weight: 500;
            color: #363636;
          }
          .order-id{
            margin-top: 10px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #A8A8A8;
            line-height: 20px;
          }
        }
        .order-price{
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #F2334E;
          line-height: 22px;
        }
      }
      .return-wrapper{
        margin: 10px;
        padding: 20px 16px;
        background: #FFFFFF;
        border-radius: 8px;
        .left{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #363636;
          line-height: 20px;
        }
        .right{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #A8A8A8;
          line-height: 20px;
        }
        .remark-label{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #363636;
          line-height: 20px;
          margin-top: 20px;
          margin-bottom: 5px;
        }
        .van-cell{
          padding: 0;
        }
      }
      .bottom-confirm{
        width:calc(100% - 32px);
        position: fixed;
        bottom: 0;
        background: #FFFFFF;
        padding: 0 16px;
        .submit-btn{
          text-align: center;
          height: 40px;
          border-radius: 20px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 40px;
          margin-top: 5px;
          margin-bottom: 10px;
        }
        .submit-btn.disabled{
          background: #dddddd;
        }
      }
      .bottom-confirm.iphonex{
        // padding-bottom: 34px;
        padding-bottom: var(--safe-area-inset-bottom)
      }
    }
  }
</style>