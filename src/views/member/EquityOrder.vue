<template>
  <div class="equity-order-page">
    <navigation-bar pageName="权益订单" @onLeftClick="onBackClick" />
    <div class="equity-order-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="my-tabs">
        <div
          class="tab-item"
          v-for="(item, index) in tabs"
          :key="index"
          :class="{ 'active theme-text': item.key === curTab }"
          @click="onChange(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="tab-content">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="——没有更多了——"
          @load="getList"
          :immediate-check="false"
        >
          <!-- 会员订单 -->
          <member-order v-if="curTab === 'LOAN_GIVE'" :list="list" />
          <!-- 卡券订单 -->
          <ecard-order v-if="curTab === 'ECARD'" :list="list" />
          <!-- 直充订单 -->
          <charge-order v-if="curTab === 'DIRECT_ADD'" :list="list" />
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import MemberOrder from './components/orderList/MemberOrder'
import EcardOrder from './components/orderList/EcardOrder'
import ChargeOrder from './components/orderList/ChargeOrder'
import { memberGiftOrder, listOrder } from '@/api/member'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const router = useRouter()
const route = useRoute()
const loading = ref(false)
const finished = ref(false)
const list = ref([])
const tabs = [
  { key: 'LOAN_GIVE', name: '入会礼品' },
  { key: 'ECARD', name: '卡券类' },
  { key: 'DIRECT_ADD', name: '直充类' },
]
const curTab = ref('LOAN_GIVE')
const container = ref(null)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    virtualMsg: {
      productGroups: ['ECARD'],
    },
    categoryGroups: [],
    shoppingFlags: ['V'],
    type: 'LOAN_GIVE',
  },
})
const { queryParams } = toRefs(data)
const getList = () => {
  loading.value = true
  if (curTab.value === 'LOAN_GIVE') {
    memberGiftOrder(queryParams.value)
      .then((res) => {
        loading.value = false
        list.value = [...list.value, ...res.data]
        if (list.value.length >= res.total || res.data.length === 0) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  } else {
    listOrder(queryParams.value)
      .then((res) => {
        loading.value = false
        list.value = [...list.value, ...res.data]
        if (!res.total) {
          finished.value = true
          return
        }
        if (list.value.length >= res.total) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  }
}
const onChange = (item) => {
  list.value = []
  curTab.value = item.key
  if (item.key === 'LOAN_GIVE') {
    queryParams.value.type == 'LOAN_GIVE'
  } else if (item.key === 'ECARD') {
    queryParams.value.categoryGroups = ['ECARD']
  } else if (item.key === 'DIRECT_ADD') {
    queryParams.value.categoryGroups = ['DIRECT_ADD', 'TELE']
  }
  queryParams.value.pageNum = 1
  finished.value = false
  loading.value = true
  getList()
}
const onBackClick = () => {
  router.go(-1)
}
onMounted(() => {
  const queryTab = route.query.tabs
  const vipCode = route.query.vipCode
  if (queryTab) {
    curTab.value = queryTab === 'TELE' ? 'DIRECT_CHARGE' : queryTab
    queryParams.value.virtualMsg.productGroups =
      queryTab === 'TELE' ? ['DIRECT_CHARGE', 'TELE'] : [queryTab]
  } else {
    queryParams.value.categoryGroups = ['ECARD']
  }
  if (vipCode) {
    queryParams.value.virtualMsg.vipType = vipCode
  }
  getList()
})
</script>

<style lang="scss" scoped>
.equity-order-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .my-tabs {
      height: 50px;
      display: flex;
      background: #ffffff;
      .tab-item {
        line-height: 50px;
        flex: 1;
        font-size: 16px;
        text-align: center;
        color: #000000;
        font-family: PingFangSC-Regular, PingFang SC;
      }
      .tab-item.active::after {
        //主要是这个
        content: '';
        display: block;
        margin: 0 auto;
        margin-top: -3px;
        width: 32px;
        height: 3px;
        background: #4671eb;
        border-radius: 2px;
      }
    }
  }
}
.nav-bar {
  border-bottom: none;
}
</style>