<template>
  <div class="page" ref="pageRef" @scroll="scrollChange">
    <div class="page-content">
      <button class="rule-btn" @click="showRulePopup()">规则</button>
      <member-header
        :title="`${checkedMember?.vipPrice?.vipName ?? ''}·享受${
          '零一二三四五六七八九十'[checkedMember?.benefitList?.length] ?? ''
        }大权益`"
        :is-member="checkedMember?.vipCust ? true : false"
        background="transparent"
        color="#ffffff"
        :order-info="orderInfo"
      />
      <van-pull-refresh v-model="isLoading" class="main-pull-refresh" @refresh="refresh()">
        <main-card
          :checkedMember="checkedMember"
          :order-info="orderInfo"
          @handleBuy="showConfirmPopup()"
        />

        <!-- <img class="semicircle" src="@/assets/images/member/member-semicircle.png"> -->
        <!-- <member-info v-if="checkedMember?.vipCust" @handleBuy="handleBuy" />
        <not-member-info v-else /> -->
        <div class="buy-benefit">
          <div class="benefit-wrapper">
            <benefit-item
              :data="checkedMember"
              :benefitList="benefitList"
              :adList="adList"
              @refresh="getBenefitList()"
              @handleGoods="handleGoods"
              @buyTips="showConfirmPopup()"
            ></benefit-item>
            <!-- <more-benefit @buyTips="buyTipsPopupRef.show = true" /> -->
            <!-- <div
              v-if="!checkedMember.vipCust"
              :style="{ height: isIphoneX ? '110px' : '60px' }"
            ></div> -->
          </div>
        </div>
        <buy-tips-popup ref="buyTipsPopupRef" @handleBuy="handleBuy"></buy-tips-popup>
      </van-pull-refresh>
    </div>

    <div v-if="!checkedMember.vipCust" class="footer">
      <button class="buy-btn" @click="showConfirmPopup()">
        <!-- <strong>0</strong>元享受权益，收到礼品后再付¥<strong>{{
          checkedMember?.vipPrice?.discountPrice
        }}</strong> -->
        立即开通
      </button>
      <div class="agreement">
        <van-checkbox v-model="checked" icon-size="16px" />
        <div class="agreement-text">
          我已阅读并同意<strong @click="showAgreement()">《权益服务协议》</strong
          >、理解并接受相关条款并同意
        </div>
      </div>
    </div>
    <agreement-popup
      url="/agreement/member-service.htm"
      title="轻享花会员服务协议"
      ref="agreement"
    ></agreement-popup>
    <confirm-popup
      v-model:checked="checked"
      :checkedMember="checkedMember"
      :benefitList="benefitList"
      :adList="adList"
      @showAgreement="showAgreement()"
      ref="confirmPopup"
    ></confirm-popup>
    <success-dialog ref="successDialog" />

    <van-popup
      :show="rulePopup.isRevealed"
      class="rule-popup"
      round
      closeable
      @close="rulePopup.cancel('close')"
    >
      <div class="header"></div>
      <div class="body">
        <div class="content" v-html="checkedMember?.vipPrice?.vipDesc"></div>
      </div>
    </van-popup>

    <!-- 挽留弹窗 -->
    <retain-dialog ref="retainDialog"></retain-dialog>
    <loan-give-dialog
      :checkedMember="checkedMember"
      :benefitList="benefitList"
      ref="loanGiveDialog"
    ></loan-give-dialog>
  </div>
</template>

<script setup name="Member">
import MainCard from './components/MainCard.vue'
import ScrollNav from '@/components/ScrollNav'
import MemberInfo from './components/MemberInfo'
import NotMemberInfo from './components/NotMemberInfo'
import MemberHeader from './components/MemberHeader'
import MemberBuy from './components/MemberBuy'
import BenefitItem from './components/BenefitItemV2'
import BuyTipsPopup from './components/BuyTipsPopup'
import MoreBenefit from './components/MoreBenefit'
import {
  getMemberInfo,
  submitMemberOrder,
  memberOrderInfo,
  memberBenefitList,
  receiveSubmit,
} from '@/api/member'
import { onMounted, provide, useTemplateRef } from 'vue'
import {
  showConfirmDialog,
  Toast,
  showLoadingToast,
  closeToast,
  showSuccessToast,
  showDialog,
} from 'vant'
import { isMenuPath } from '@/utils/common'
import { until, useAsyncState, useConfirmDialog } from '@vueuse/core'
import AgreementPopup from '@/components/AgreementPopup'
import ConfirmPopup from './components/ConfirmPopup.vue'
import { showToast } from 'vant'
import SuccessDialog from './components/SuccessDialog.vue'
import RetainDialog from './components/RetainDialog.vue'
import LoanGiveDialog from './components/LoanGiveDialog.vue'

const agreementRef = useTemplateRef('agreement')
const confirmPopupRef = useTemplateRef('confirmPopup')
const successDialogRef = useTemplateRef('successDialog')
const retainDialogRef = useTemplateRef('retainDialog')
const loanGiveDialogRef = useTemplateRef('loanGiveDialog')
const store = useStore()
const user = computed(() => store.getters.userInfo)
const { proxy } = getCurrentInstance()
if (!user.value) {
  proxy.appJS.appLogin()
}
const router = useRouter()
const isIphoneX = window.isIphoneX
const memberList = ref([])
const buyTipsPopupRef = ref(null)
const data = reactive({
  checkedMember: {},
  buyStyle: {},
  orderInfo: {},
})
const { checkedMember, buyStyle, orderInfo } = toRefs(data)
const updateMember = (item) => {
  checkedMember.value = item
}
const checked = ref(false)
provide('checkedMember', checkedMember)
provide('updateMember', updateMember)

const onBackClick = async () => {
  // if (!isMenuPath()) {
  //   router.go(-1)
  // }

  const { isCanceled } = await retainDialogRef.value.reveal()
  if (isCanceled) {
    router.go(-1)
    return
  }
}
const {
  execute: refresh,
  isLoading,
  isReady,
} = useAsyncState(
  async () => {
    const res = await getMemberInfo({ vipType: 'VIP' })
    memberList.value = res.data
    checkedMember.value = res.data?.[0] // 默认第一条会员
    if (checkedMember.value.vipCust) {
      orderInfo.value = (await memberOrderInfo({ id: checkedMember.value.vipPrice.id })).data
    }

    // if (route.query.success) {
    //   successDialogRef.value.reveal()
    // }
  },
  undefined,
  {
    immediate: !!user.value,
    onSuccess() {},
  }
)

async function showConfirmPopup() {
  // if (!checked.value) {
  //   showToast('请先阅读并同意《权益服务协议》')
  //   return
  // }
  const { data, isCanceled } = await confirmPopupRef.value.reveal()
  if (isCanceled) {
    const { isCanceled: isCanceled2 } = await retainDialogRef.value.reveal()
    if (isCanceled2) {
      return
    }
  }
  handleBuy()
}

// const memnberInfoFn = () => {
//   getMemberInfo({ vipType: 'VIP' }).then((res) => {
//     // 会员集合
//     memberList.value = res.data
//     checkedMember.value = res.data?.[0] // 默认第一条会员
//     if (checkedMember.value.vipCust) {
//       memberOrderInfo({ id: checkedMember.value.vipPrice.id }).then((res) => {
//         orderInfo.value = res.data
//       })
//     }
//   })
// }
watch(
  () => store.getters.userInfo,
  (newValue) => {
    // 重新刷新
    if (newValue) {
      refresh()
    }
  }
)
// 购买按钮
const scrollChange = ($e) => {
  const val = $e.target.scrollTop
  if (val > 0) {
    buyStyle.value = {
      background: '#ffffff',
      position: 'fixed',
      bottom: 0,
      'z-index': 998,
      'padding-top': proxy.$px2rem('10px'),
      'padding-bottom': isIphoneX ? proxy.$px2rem('44px') : 0,
    }
  } else {
    buyStyle.value = {}
  }
}
const handleBuy = () => {
  if (checkedMember.value.vipCust) {
    showConfirmDialog({
      title: '标题',
      message: '您已购买该会员卡，确认再次购买吗？',
    })
      .then(() => {
        submitOrder()
      })
      .catch(() => {})
  } else {
    submitOrder()
  }
}

const route = useRoute()
const submitOrder = () => {
  submitMemberOrder({
    virtualMsg: { vipPriceId: checkedMember.value.vipPrice.id },
  }).then((res) => {
    router.push({
      path: '/cashier',
      query: {
        orderId: res.data.orderId,
        orderSource: proxy.$global.CASHIER_VIP_BUY,
        redirect: route.fullPath,
        // back: true,
      },
    })
  })
}

const {
  state: benefitList,
  isLoading: benefitListIsLoading,
  execute: getBenefitList,
} = useAsyncState(
  async () =>
    (
      await memberBenefitList({
        id: checkedMember.value?.vipPrice?.id,
      })
    )?.data ?? [],
  [],
  {
    immediate: !!checkedMember.value?.vipPrice?.id,
    resetOnExecute: false,
  }
)
watch(checkedMember, () => {
  if (checkedMember.value?.vipPrice?.id) {
    getBenefitList()
  } else {
    benefitList.value = []
  }
})

// onMounted(() => {
//   if (!user.value) {
//     proxy.appJS.appLogin()
//   }
//   refresh()
// })

import { getAdList } from '@/api/base'

const { state: adList } = useAsyncState(
  async () =>
    (
      await getAdList({
        regionType: ['MEMBER'],
      })
    ).data?.['MEMBER'] ?? [],
  [],
  {
    // immediate: false,
  }
)
function showAgreement() {
  agreementRef.value.show = true
}
onMounted(async () => {
  // 等待会员信息加载完成
  await until(isReady).toBe(true)

  // 如果已经购买了会员，并且有成功标志，则显示成功弹窗
  if (checkedMember.value.vipCust && route.query.success) {
    const { isCanceled } = await successDialogRef.value.reveal()
    if (isCanceled) {
      return
    }
    const { isCanceled: isCanceled2, data: loanGiveData } = await loanGiveDialogRef.value.reveal()
    if (isCanceled2) {
      return
    }
    handleGoods(loanGiveData.benefit, loanGiveData.goodsInfo)
  }
})

const rulePopup = reactive(useConfirmDialog())

function showRulePopup() {
  rulePopup.reveal()
}

async function handleGoods(benefit, goodsInfo) {
  console.log(benefit, goodsInfo)
  if (!checkedMember.value?.vipCust) {
    // emit('buyTips')
    showConfirmPopup()
    return
  }

  if (benefit.periodNum === benefit.receiveNum) {
    showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
    return
  }

  if (benefit.packageType === 'COUPON') {
    // if (benefit.periodNum === benefit.receiveNum) {
    //   showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
    //   return
    // }
    if (goodsInfo.receiveNum === 0) {
      await receiveSubmit({ id: goodsInfo.id }).then((res) => {
        showSuccessToast('领取成功')
      })
      getBenefitList()
      // emit('refresh')
    }

    return
  }

  if (benefit.packageType === 'COUPON_PACKAGE') {
    // if (benefit.periodNum === benefit.receiveNum) {
    //   showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
    //   return
    // }
    if (goodsInfo.receiveNum === 0) {
      await receiveSubmit({ id: goodsInfo.id }).then((res) => {
        showSuccessToast('领取成功')
      })
      getBenefitList()
      // emit('refresh')
    }

    return
  }

  // if (benefit.packageType === 'TRYOUT_GOODS') {
  // return
  // }

  if (goodsInfo.categoryGroup === 'COUPON') {
    router.push({
      path: '/goods-coupon',
      query: {
        goodsId: goodsInfo.spuId,
        skuId: goodsInfo.skuId,
        packageId: benefit.id,
        shoppingFlag: 'V',
      },
    })
    return
  }

  router.push({
    path: '/goods-detail',
    query: {
      goodsId: goodsInfo.spuId,
      skuId: goodsInfo.skuId,
      packageId: benefit.id,
      shoppingFlag: 'V',
    },
  })
}

onMounted(async () => {
  // if (import.meta.env.DEV) {
  //   await new Promise((res) => setTimeout(res, 1000))
  //   const { isCanceled } = await successDialogRef.value.reveal()
  //   if (isCanceled) {
  //     return
  //   }
  //   const { isCanceled: isCanceled2, data: loanGiveData } = await loanGiveDialogRef.value.reveal()
  //   if (isCanceled2) {
  //     return
  //   }
  //   console.log(loanGiveData)
  // }
})
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  overflow-y: scroll;
  // background: linear-gradient(179deg, #5881fa 4.27%, #ddf5de 26.15%, #cce9fa 45.55%);
  // background-size: 100% 3427px;
  .member-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: none;
    background-image: url('@/assets/images/member/page-bg.png');
    background-size: 100% auto;
    background-position: 0 0;
    background-repeat: no-repeat;
  }
}

.page-content {
  background-image: url('@/assets/images/member/page-bg.png');
  background-size: 100% auto;
  background-position: 0 0;
  background-repeat: no-repeat;
  // background: linear-gradient(179deg, #5881fa 4.27%, #ddf5de 26.15%, #cce9fa 45.55%);
  // background-size: 100% 1714px;
  // background: linear-gradient( 180deg, #FF671A 0%, #FD7D13 100%) no-repeat;
  // background-size: 100% 289px;
  background-position: 0 0;
  background-repeat: no-repeat;
  position: relative;
  min-height: 100vh;
  min-height: 100dvh;
  flex: none;
  position: relative;
  // padding-top: 13px;
  // overflow-y: auto;
  // margin-top: -1px;
  // .semicircle{
  //   position: absolute;
  //   top: 2px;
  //   width: 103px;
  //   height: 170px;
  // }
  .buy-benefit {
    // padding: 13px;
    margin-top: -10px;
    background: #fff;
    // border: ;
    border-radius: 19px 19px 0px 0px;
    position: relative;
    // padding-bottom: 44px;
    // border: 1px solid #fff;
  }
  .benefit-wrapper {
    // margin: 5px 16px 0;
  }
}
.footer {
  position: sticky;
  bottom: 0;
  z-index: 10;
  left: 0;
  right: 0;
  border-radius: 10px 10px 0px 0px;
  background: #fff;
  box-shadow: 0px -2px 17px 0px rgba(136, 122, 109, 0.16);
  padding: 20px 10px;
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
  .buy-btn {
    border-radius: 999vw;
    background: linear-gradient(180deg, #5881fa 0%, #91b7fd 100%);
    display: block;
    width: 100%;
    height: calc(85px / 2);
    font-size: 15px;
    color: #fff;
    border: none;
    strong {
      font-size: 20px;
    }
  }
  .agreement {
    display: flex;
    font-size: 12px;
    // line-height: 16px;
    font-weight: 400;
    color: #0000004d;
    margin-top: 4px;
    align-items: center;
    .van-checkbox {
      margin-right: 4px;
      flex: none;
    }
    .agreement-text {
      overflow-y: hidden;
      overflow-x: scroll;
      flex: 1;
      min-width: 0;
      white-space: nowrap;

      strong {
        color: var(--primary-color);
        font-weight: inherit;
      }
    }
  }
  // width: 100%;
}
.rule-btn {
  position: fixed;
  top: 200px;
  right: 0px;
  background: #0d0d0d4d;

  z-index: 10;
  font-size: 14px;
  border-radius: 999vw 0 0 999vw;
  border: none;
  color: #fff;
  padding: 4px 4px 4px 8px;
}
.rule-popup {
  font-size: 14px;
  // border-radius: 16px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // margin-top: var(--safe-area-inset-top) !important;
  // margin-bottom: var(--safe-area-inset-bottom) !important;
  // margin-bottom: 88px !important;
  // max-height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-top));
  max-height: calc(80dvh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
  // max-height: 50vh;
  .header {
    height: 40px;
    flex: none;
  }
  .body {
    flex: 1;
    // height: 0;
    min-height: 0;
    padding: 8px;
    font-size: 14px;
    overflow-x: hidden;
    overflow-y: scroll;
    .content {
      // padding: 8px;
      font-size: 14px;
      line-height: 1.2;
      // height: 200vh;
    }
  }
}
.main-pull-refresh {
  --van-pull-refresh-head-text-color: #fff;
  --van-loading-text-color: #fff;
  --van-loading-spinner-color: #fff;
}
</style>
