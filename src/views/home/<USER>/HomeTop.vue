<script setup>
import SearchBar from './SearchBar.vue'
import { useRouter } from 'vue-router'
import { getAdList } from '@/api/base'
import { onMounted, watch } from 'vue'
import { appVersion } from '@/utils/auth'

const emit = defineEmits(['kefu'])

const { proxy } = getCurrentInstance()
const router = useRouter()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const advertises = ref({})

// 跳转消息页面
const toMessageNofiy = () => {
  emit('kefu')
}

const initPage = async () => {
  console.log('HomeTop 初始化')
  // 广告
  await getAdList({
    regionType: [proxy.$global.AD_POSITION_HOME_TOP],
  }).then((res) => {
    advertises.value = res.data
  })
}

onMounted(() => {
  initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

defineExpose({
  initPage,
})
</script>

<template>
  <van-sticky>
    <div class="home-header">
      <div class="bar">
        <img class="logo" src="@/assets/images/home/<USER>" />
        <!-- <SearchBar @click="routerSearch" style="margin-right: 5px" /> -->
        <search-bar />
        <!-- <img
          v-if="appVersion !== 'VERIFY' || user?.creditIntentionFlag === 'Y'"
          class="customer-service"
          src="@/assets/images/home/<USER>"
          @click="toMessageNofiy"
        /> -->
        <!-- <div
         class="home-header-button"
          v-if="advertises[$global.AD_POSITION_HOME_TOP]"
          @click="$adRouter(advertises[$global.AD_POSITION_HOME_TOP][0])"
        >
          <img
            :src="advertises[$global.AD_POSITION_HOME_TOP][0].pic"
          />
        </div> -->
        <div class="home-header-button" @click="toMessageNofiy">
          <img src="@/assets/images/home/<USER>" />
          <div class="badge theme-text" v-if="msgNum">{{ msgNum }}</div>
        </div>
      </div>
      <div class="static">
        <div class="static-item">
          <img src="@/assets/images/home/<USER>" alt="" />正品好物
        </div>
        <div class="static-item">
          <img src="@/assets/images/home/<USER>" alt="" />隐私保护
        </div>
        <div class="static-item">
          <img src="@/assets/images/home/<USER>" alt="" />极速送达
        </div>
        <div class="static-item">
          <img src="@/assets/images/home/<USER>" alt="" />售后无忧
        </div>
      </div>
    </div>
  </van-sticky>
</template>

<style lang="scss" scoped>
.home-header {
  height: 68px;
  padding: 44px 10px 4px 14px;
  background: linear-gradient(180deg, #5581f6, #7e9bf8);
  .bar {
    display: flex;
    align-items: center;
    padding: 6px 0;
    .logo {
      width: 59px;
      height: auto;
      margin-right: 6px;
    }
    .customer-service {
      width: 25px;
      height: auto;
      margin-left: 6px;
    }
  }
  .static {
    display: flex;
    justify-content: space-between;
    .static-item {
      font-size: 13px;
      color: #fff;
      font-weight: 400;
      line-height: 17px;
      display: flex;
      align-items: center;
      img {
        height: 17px;
        width: auto;
        margin-right: 5px;
      }
    }
  }

  .home-header-button {
    padding: 5px;
    display: flex;
    align-items: center;

    img {
      height: 21px;
      cursor: pointer;
    }
  }
}
</style>
