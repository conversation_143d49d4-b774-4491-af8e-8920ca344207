import { getMessageList } from '@/api/home'
export function useMessage() {
  const msgNum = ref(0)
  // 跳转消息页面
  const toMessageNofiy = () => {
    router.push('/message-notify')
  }
  onMounted(() => {
    // 消息信息
    getMessageList({pageNum: 1, pageSize: 100}).then(res => {
      const localMsg = localStorage.getItem('msg-read')
      let num = 0
      if(localMsg) {
        let msgIds = localMsg.split(',')
        if(res.data.length > 0) {
          res.data.map(item => {
            if(!msgIds.includes(''+item.id)) {
              ++ num
            }
          })
        }
      } else {
        res.data.map(item => {
          ++ num
        })
      }
      msgNum.value = num
    })
  })
  return { msgNum: msgNum.value, toMessageNofiy }
}