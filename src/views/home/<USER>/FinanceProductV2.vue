<template>
  <div class="product" v-if="visible">
    <my-swiper :swiperDatas="advertises[$global.AD_POSITION_HOME_FINANCE]"></my-swiper>
    <div class="notice-warp">
      <van-notice-bar
        class="notice-bar"
        :delay="2"
        color="rgba(255,255,255,0.5)"
        left-icon="volume-o"
        :scrollable="false"
      >
        <van-swipe vertical="" class="notice-swipe" :autoplay="3000" :touchable="false" :show-indicators="false">
          <van-swipe-item class="flex justify-between" v-for="(item, index) in activeList" :key="index">
            <span>{{ item.phone }}</span>
          </van-swipe-item>
        </van-swipe>
      </van-notice-bar>
    </div>
  </div>
</template>

<script setup>
import MySwiper from '@/components/MySwiper'
import { computed, onMounted, watch } from 'vue'
import { getAdList } from '@/api/base'
// import bgPath from '@/assets/images/home/<USER>'

const { proxy } = getCurrentInstance()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const activeList = proxy.$global.ACTIVE_LIST
const loading = ref(false)
const advertises = ref({})
const visible = computed(() => {
  if (!advertises.value[proxy.$global.AD_POSITION_HOME_FINANCE]) {
    return false
  }
  if (!user.value) {
    return false
  }
  if (user.value.creditIntentionFlag !== 'Y') {
    return false
  }
  return true
})

const initPage = async () => {
  console.log('FinanceProductV2 初始化')
  // 广告
  await getAdList({
    regionType: [
      proxy.$global.AD_POSITION_HOME_FINANCE,
    ],
  }).then((res) => {
    advertises.value = res.data
    // advertises.value[proxy.$global.AD_POSITION_HOME_FINANCE][0].pic = bgPath
    loading.value = false
  })
}

onMounted(async () => {
  await initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  },
)

defineExpose({
  initPage
})
</script>

<style lang="scss" scoped>
.product {
  margin: 12px 10px;
  position: relative;

  .notice-warp {
    position: absolute;
    top: -4px;
    right: 2px;
    width: 207px;

    .notice-bar {
      background: none;
      padding: 0 8px;
    }

    .notice-swipe {
      height: 24px;
      line-height: 24px;

      .van-swipe-item {
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
}
</style>