<template>
  <van-popup class="ad-popup" v-model:show="show" teleport="body">
    <img @click="handleClick" class="popup-img" :src="list?.[0]?.pic" />
    <img src="@/assets/images/home/<USER>" @click="show = false" class="ad-popup-close" />
  </van-popup>
</template>

<script setup>
import { ref, watch, onMounted, onActivated, getCurrentInstance } from 'vue'
import { getToday } from '@/utils/date'
import { getAdList } from '@/api/base'

const { proxy } = getCurrentInstance()
const store = useStore()
const user = computed(() => store.getters.userInfo)

const loading = ref(false)
const list = ref([])

const show = ref(false)
const adpopupKey = 'yunji-adpopup'
const scene = ref('')
const isMounted = ref(false)

onActivated(() => {
  if (!isMounted.value) {
    // 规避首次多次执行
    visibleReset()
  } else {
    isMounted.value = false
  }
})

const visibleReset = () => {
  if (user.value && user.value.creditIntentionFlag === 'Y') {
    // 广告弹框
    adPopupShow()
  }
}

const initPage = async () => {
  try {
    loading.value = true
    const res = await getAdList({
      regionType: [proxy.$global.AD_POSITION_HOME_POPUP],
    })
    const arr = res.data[proxy.$global.AD_POSITION_HOME_POPUP]
    list.value = Array.isArray(arr) ? arr : []
  } finally {
    loading.value = false
  }

  visibleReset()
}

onMounted(() => {
  initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

const adPopupShow = () => {
  if (list.value.length === 0) {
    // 无广告
    return
  }
  const popup = localStorage.getItem(adpopupKey)
  const today = getToday()
  if (popup) {
    const popupNumber = Number(popup.split(',')[1])
    if (popup.split(',')[0] !== today) {
      localStorage.setItem(adpopupKey, today + ',' + 1)
      show.value = true
    } else {
      // 默认 1 次、后台备注配置每天次数
      const maxNumber = !isNaN(parseFloat(list.value[0].remark)) ? Number(list.value[0].remark) : 1
      if (popupNumber >= maxNumber) {
        return
      }
      localStorage.setItem(adpopupKey, today + ',' + (popupNumber + 1))
      show.value = true
    }
  } else {
    localStorage.setItem(adpopupKey, today + ',' + 1)
    show.value = true
  }
}

// 跳转
const handleClick = () => {
  if (scene.value) {
    proxy.commQiaKe(scene.value)
  } else {
    proxy.$adRouter(list.value[0])
  }
}
</script>

<style scoped lang="scss">
.ad-popup {
  // position: relative;
  width: 260px !important;
  font-size: 0;
  .popup-img {
    max-width: 100%;
    height: auto;
  }
  .ad-popup-close {
    display: block;
    width: 27px;
    height: 27px;
    position: absolute;
    bottom: -43px;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 26px;
  }
}
</style>
