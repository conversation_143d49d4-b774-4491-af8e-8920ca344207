<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    teleport="body"
  >
    <div class="kefu" :class="{ 'iphonex' : isIphoneX }">
      <div v-if="getAppVersion() !== 'VERIFY'" class="inline" @click="handleInline">在线客服</div>
      <div class="phone" @click="callNumber">
        <img src="@/assets/images/home/<USER>">
        呼叫 4000047719
      </div>
      <div class="cancel" @click="show=false">取消</div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { getAppVersion } from '@/utils/auth'
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const handleInline = () => {
  show.value = false
  proxy.$customerService()
}
const callNumber = () => {
  show.value = false
  window.location.href = 'tel:4000138638';
}
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .kefu.iphonex{
    // padding-bottom: 68px;
    padding-bottom: calc(44px + var(--safe-area-inset-bottom));
  }
  .kefu{
    padding-top: 20px;
    padding-bottom: 44px;
    .inline{
      font-size: 17px;
      color: #2F80ED;
      line-height: 25px;
      text-align: center;
      font-weight: bold;
    }
    .phone{
      margin-top: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 17px;
      color: #2F80ED;
      line-height: 25px;
      font-weight: bold;
      img{
        width: 20px;
        height: 20px;
        margin-right: 23px;
      }
    }
    .cancel{
      margin-top: 30px;
      font-size: 20px;
      color: #2F80ED;
      line-height: 25px;
      text-align: center;
      font-weight: bold;
    }
  }
</style>