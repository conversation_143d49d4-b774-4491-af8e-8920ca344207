<script setup>
import { getFunGroup } from '@/api/home'
import { onMounted, watch } from 'vue'

const { proxy } = getCurrentInstance()
const store = useStore()

const loading = ref(false)
const list = ref([])

const initPage = async () => {
  console.log('HomeIconList2 初始化')
  try {
    loading.value = true
    const res = await getFunGroup({ funcRegion: proxy.$global.ICON_FUN_APP_HOME })
    list.value = Array.isArray(res.data) ? res.data : []
  } finally {
    loading.value = false
  }
}

const frontList = computed(() => {
  return list.value.slice(0, 5)
})

onMounted(async () => {
  await initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

defineExpose({
  initPage,
})
</script>

<template>
  <!-- icon 2 -->
  <div class="home-icon-list2">
    <van-skeleton :row="5" class="margin" :loading="loading">
      <template #template>
        <van-skeleton-image v-for="item in 5" />
      </template>
      <div class="list">
        <div class="list-item" v-for="(item, index) of list">
          <img
            class="list-item-image"
            :class="`${frontList.includes(item) ? 'big' : ''}`"
            :src="item.icon"
            @click="$funcRouter(item)"
          />
          <div class="list-item-text">{{ item.name }}</div>
        </div>
      </div>
    </van-skeleton>
  </div>
</template>

<style lang="scss" scoped>
.home-icon-list2 {
  padding: 5px 10px 7px 10px;
  margin-top: -1px;
  background: linear-gradient(180deg, #bcccfb, #f4f6fa);
  border: 0;

  .van-skeleton {
    padding: 0;
  }
  .van-skeleton-image {
    width: 20%;
    height: 43px;
    margin: 0 6px;
    border-radius: 8px;
  }
}

.list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px 0;

  .list-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }

  .list-item-image {
    width: auto;
    height: 43px;
  }

  .list-item-image.big {
    width: auto;
    height: 47px;
  }

  .list-item-text {
    font-size: 13px;
    color: #333;
    line-height: 18px;
  }
}
</style>