<template>
  <div class="goods-category-content" :class="{ 'iphonex-bottom': isIphoneX }">
    <div class="sub-category">
      <van-grid :column-num="5" :gutter="10" :border="false">
        <van-grid-item class="custom-grid-item" v-for="item in subCategoryList" :key="item.id" @click="toCategory(item.categoryId)">
          <img class="category-img" :src="item.icon">
          <div class="category-name">{{ item.categoryName }}</div>
        </van-grid-item>
      </van-grid>
    </div>
    <div class="margin-top-sm">
      <goods-recommend :goods-list="categoryGoods"></goods-recommend>
    </div>
  </div>
</template>

<script setup>
  import GoodsRecommend from './GoodsRecommend'
  import { subCategoryGoods } from '@/api/goods'
  const  isIphoneX = window.isIphoneX
  const router = useRouter()
  const props = defineProps({
    modelValue: {
      type: Number,
      default: null
    }
  })
  const subCategoryList = ref([])
  const categoryGoods = ref([])
  const getSubcategory = () => {
    subCategoryList.value = []
    categoryGoods.value = []
    subCategoryGoods({ categoryId: props.modelValue }).then(res=> {
      subCategoryList.value = res.data.categorys
      categoryGoods.value = res.data.spuList
    })
  }
  const toCategory = (categoryId) => {
    router.push({path: '/goods-list', query: {categoryId}})
  }
  defineExpose({ getSubcategory })
</script>

<style lang="scss" scoped>
  .goods-category-content{
    width: 375px;
    .sub-category{
      background: #ffffff;
      border-radius: 8px;
      margin: 0 10px;
      padding: 10px 0;
      .category-img{
        width: 46px;
        height: 46px;
      }
      .category-name{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 17px;
        margin-top: 3px;
      }
    }
  }
  
</style>