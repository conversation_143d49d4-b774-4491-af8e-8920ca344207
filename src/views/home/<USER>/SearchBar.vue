<template>
  <div class="search-bar">
    <img class="icon" src="@/assets/images/home/<USER>" />
    <van-notice-bar :delay="2" :scrollable="false" @click="routerSearch">
      <van-swipe
        vertical
        class="notice-swipe"
        :autoplay="3000"
        :show-indicators="false"
        :touchable="false"
        @change="onSwiperChange"
      >
        <van-swipe-item v-for="(item, index) in list" :key="index">
          <img class="hot-icon" v-if="item.icon" :src="item.icon" />
          <span class="hot-label">{{ item.label }}</span>
        </van-swipe-item>
      </van-swipe>
    </van-notice-bar>
    <span class="search-btn" @click="onSearch()">搜索</span>
  </div>
</template>

<script setup>
import { getHotSearch } from '@/api/base'
import { useRouter } from 'vue-router'
const router = useRouter()
const list = ref([])
const currentIndex = ref(0)
const routerSearch = () => {
  router.push('/base-search')
}
const getSearchWords = async () => {
  const res = await getHotSearch()
  list.value = res
}
const onSwiperChange = (index) => {
  currentIndex.value = index
}
const onSearch = () => {
  const value = list.value[currentIndex.value].value
  // 跳转至商品列表页
  router.push({ path: '/goods-list', query: { keywords: value } })
}

onMounted(() => {
  getSearchWords()
})
</script>

<style lang="scss" scoped>
.search-bar {
  width: 100%;
  height: 35px;
  border-radius: 18px;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  padding: 4px;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  .icon {
    display: block;
    width: 19px;
    height: 19px;
    margin-left: 9px;
  }
  .notice-swipe {
    height: 35px;
    line-height: 35px;
  }
  .van-swipe-item {
    font-size: 16px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: flex;
    align-items: center;
    .hot-icon {
      width: 20px;
      height: auto;
      margin: 0px 2px;
    }
    .hot-label {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .van-notice-bar {
    background: none;
    flex: 1;
    padding: 0 4px;
    font-weight: 400;
    color: #fff;
  }
  .search-btn {
    height: 100%;
    line-height: 27px;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 16px;
    background: #4671eb;
    font-weight: 400;
    color: #fff;
  }
}
</style>