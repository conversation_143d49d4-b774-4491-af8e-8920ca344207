<template>
  <div class="product" v-if="product">
    <div class="product-info">
      <div class="product-info-left">
        <div class="product-text">贷款方最高额度（元）</div>
        <div class="product-amount">{{ formatMoney(product.quota ? product.quota : 20000, 0) }}</div>
      </div>
      <div class="product-info-right">
        <img class="tips-img" src="@/assets/images/home/<USER>">
        <div class="product-btn" @click="borrowMoney">去借钱</div>
      </div>
    </div>
    <van-notice-bar :delay="2" style="background: linear-gradient( 180deg, #FBEADD 0%, #FFFAED 100%);" color="#4F392D"
      left-icon="volume-o" :scrollable="false">
      <van-swipe vertical class="notice-swipe" :autoplay="3000" :show-indicators="false">
        <van-swipe-item class="flex justify-between" v-for="(item, index) in activeList" :key="index">
          <span>{{ item.phone }}</span>
        </van-swipe-item>
      </van-swipe>
    </van-notice-bar>
  </div>
</template>

<script setup>
import { listProduct } from '@/api/product'
import { ref, watch, onMounted, computed, watchEffect } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const activeList = proxy.$global.ACTIVE_LIST

const mainProductList = ref([])
const product = ref(null)

const initPage = async () => {
  // 获得主产品列表
  const res = await listProduct({ regionType: proxy.$global.REGION_TYPE_HOME_MAIN_PRODUCR })
  if (res.data.topProductList.length > 0) {
    mainProductList.value = res.data.topProductList
  } else {
    mainProductList.value = res.data.productList
  }
}

watchEffect(() => {
  product.value = (mainProductList.value.length > 0 && user.value) ? mainProductList.value[0] : null
})

onMounted(async () => {
  await initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  },
)

const borrowMoney = () => {
  if (user.value) {
    if (user.value.flow === proxy.$global.USER_FLOW_FINISH) {
      router.push('/cash-loan')
    } else {
      const pushName = user.value.flow === proxy.$global.USER_FLOW_INFO ? 'CustInfoResult' : 'CreditFacilities'
      router.push({ name: pushName })
    }
  } else {
    proxy.appJS.appLogin()
  }
}
</script>

<style lang="scss" scoped>
.product {
  border-radius: 8px;
  margin: 15px 12px;

  .notice-swipe {
    height: 46px;
    line-height: 46px;

    .van-swipe-item {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }

  .product-info {
    padding: 28px 21px 25px 23px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;
    margin-bottom: -5px;

    &-left {
      .product-text {
        font-size: 12px;
        color: #FFFFFF;
        line-height: 17px;
      }

      .product-amount {
        font-size: 34px;
        font-family: DIN, DIN;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 40px;
        margin-top: 10px;
      }
    }

    &-right {
      .tips-img {
        width: 102px;
        height: 22px;
        display: block;
      }

      .product-btn {
        width: 94px;
        height: 32px;
        background: linear-gradient(180deg, #FAEECB 0%, #F8D494 100%);
        border-radius: 19px;
        line-height: 32px;
        text-align: center;
        font-weight: 600;
        font-size: 15px;
        color: #5C463C;
        margin-top: 10px;
      }
    }
  }
}
</style>