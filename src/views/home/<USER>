<template>
  <div class="home-page" @scroll="onScrollChange" ref="homeRef">
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="theme-text">
      <HomeTop ref="homeTopRef" @kefu="toMessageNofiy" />
      <!-- <HomeIconList ref="homeIconListRef" /> -->
      <AdBanner ref="adBannerRef" />
      <HomeIconList2 ref="homeIconList2Ref" />
      <!-- <FinanceProductV2 ref="financeProductRef" /> -->
      <!-- <TimeLimitCard ref="timeLimitCardRef" /> -->
      <!-- <MySwiper ref="mySwiperRef" /> -->
      <MoFangV2 ref="moFangRef" />
      <CarouselActives ref="carouselActivesRef"></CarouselActives>
      <!-- <PremiumRecommendV2 ref="premiumRecommendRef" /> -->
      <GoodsListV2 ref="goodsListRef" />
    </van-pull-refresh>
    <AdPopupV2 />
    <MemberGiftV2 />
    <customer-service ref="customerServiceRef" />
  </div>
</template>

<script setup name="Home">
import { onActivated } from 'vue'
import CustomerService from '@/components/CustomerService'
import MySwiper from './components/MySwiper.vue'
import CarouselActives from './components/CarouselActives.vue'
import HomeTop from './components/HomeTop.vue'
import HomeIconList from './components/HomeIconList.vue'
import AdBanner from './components/AdBanner.vue'
import HomeIconList2 from './components/HomeIconList2.vue'
import FinanceProductV2 from './components/FinanceProductV2.vue'
import MoFangV2 from './components/MoFangV2.vue'
import AdPopupV2 from './components/AdPopup.vue'
import MemberGiftV2 from './components/MemberGiftV2.vue'
import PremiumRecommendV2 from './components/PremiumRecommendV2.vue'
import GoodsListV2 from './components/GoodsListV2.vue'
import TimeLimitCard from './components/TimeLimitCard'

const homeRef = ref()
const homeTopRef = ref()
const homeIconListRef = ref()
const adBannerRef = ref()
const homeIconList2Ref = ref()
const financeProductRef = ref()
const moFangRef = ref()
const premiumRecommendRef = ref()
const goodsListRef = ref()
const customerServiceRef = ref()
const timeLimitCardRef = ref()
const mySwiperRef = ref()
const carouselActivesRef = ref()
const data = reactive({
  navBarStyle: {
    backgroundColor: '',
    position: 'fixed',
  },
})
const { navBarStyle } = toRefs(data)
// 滚动值
const scrollTopValue = ref(-1)
const ANCHOR_SCROLL_TOP = 64

onActivated(() => {
  homeRef.value.scrollTop = scrollTopValue.value
})

// 滚动
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  const opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP
  navBarStyle.value.backgroundColor = 'rgba(255,103,26, ' + opacity + ')'
}

// 下拉刷新
const loading = ref(false)
const onRefresh = () => {
  homeTopRef.value?.initPage()
  homeIconListRef.value?.initPage()
  adBannerRef.value?.initPage()
  homeIconList2Ref.value?.initPage()
  financeProductRef.value?.initPage()
  moFangRef.value?.initPage()
  premiumRecommendRef.value?.initPage()
  goodsListRef.value?.initPage()
  timeLimitCardRef.value?.reload()
  mySwiperRef.value?.initPage()
  carouselActivesRef.value?.initPage()
  loading.value = false
}

// 跳转客服页面
const toMessageNofiy = () => {
  customerServiceRef.value.show = true
}
</script>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  background: #f4f6fa;
}
</style>
