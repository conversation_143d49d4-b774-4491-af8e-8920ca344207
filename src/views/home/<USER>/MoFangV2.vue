<script setup>
import { getCurrentInstance, onMounted, watch, computed } from 'vue'
import { getAdList } from '@/api/base'
import { filter } from '@/utils/adIsShow'
import { add } from '@/utils/adEventRecord.js'

const { proxy } = getCurrentInstance()
const store = useStore()

const adList = ref([])
const list = ref({})
const loading = ref(false)

const initPage = async () => {
  console.log('MoFangV2 初始化')
  try {
    loading.value = true
    const res = await getAdList({
      regionType: [proxy.$global.AD_POSITION_HOME_MOFANG],
    })
    const data = res.data[proxy.$global.AD_POSITION_HOME_MOFANG]
    list.value = Array.isArray(data) ? data : []
    // adList的值为list数组移除后两位
    adList.value = await filter(list.value)
    adList.value.forEach((item) => {
      add(item, 'EXPOSURE')
    })
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

defineExpose({
  initPage,
})
</script>

<template>
  <div v-if="adList.length" class="mofang">
    <img
      v-for="(item, index) of adList"
      class="mofang-item"
      :key="index"
      :src="item.pic"
      @click="$adRouter(item)"
    />
  </div>
</template>

<style lang="scss" scoped>
.mofang {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 11px;
  column-gap: 0;
  margin: 12px 10px;

  .mofang-item {
    width: 100%;
    height: 146px;
  }
}
</style>
