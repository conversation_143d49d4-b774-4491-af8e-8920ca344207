<template>
  <div class="swiper">
    <my-swiper :swiperDatas="swiperDatas"></my-swiper>
  </div>
</template>
<script setup>
import MySwiper from '@/components/MySwiper'
import { getAdList } from '@/api/base'

const { proxy } = getCurrentInstance()
const swiperDatas = ref([])

const initPage = async () => {
  console.log('首页广告位 初始化')
}

onMounted(async () => {
  await initPage()
})

defineExpose({
  initPage,
})
</script>
<style lang="scss" scoped>
.swiper {
  width: 355px;
  height: 115px;
  margin: 10px auto;
  background: #fff;
  border-radius: 15px;
}
</style>