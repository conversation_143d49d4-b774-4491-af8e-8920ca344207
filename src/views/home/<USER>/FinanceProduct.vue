<template>
  <div class="product">
    <div class="product-ad">
      <my-swiper :swiperDatas="list"></my-swiper>
    </div>
    <div class="notice-warp">
      <van-notice-bar
        :delay="2"
        style="background: linear-gradient( 180deg, #FBEADD 0%, #FFFAED 100%);border-radius: 0 0 8px 8px;"
        color="#4F392D"
        left-icon="volume-o"
        :scrollable="false"
      >
        <van-swipe
          vertical
          class="notice-swipe"
          :autoplay="3000"
          :show-indicators="false"
        >
          <van-swipe-item class="flex justify-between" v-for="(item, index) in activeList" :key="index">
            <span>{{item.phone}}</span>
          </van-swipe-item>
        </van-swipe>
      </van-notice-bar>
    </div>
  </div>
</template>

<script setup>
  import MySwiper from '@/components/MySwiper'
  const { proxy } = getCurrentInstance()
  const activeList = proxy.$global.ACTIVE_LIST
  const props = defineProps({
    product: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  })
</script>

<style lang="scss" scoped>
  .product{
    border-radius: 8px;
    margin: 15px 12px;
    .product-ad{
      margin-bottom: -20px;
      position: relative;
      z-index: 1;
    }
    .notice-warp{
      padding-top: 20px;
      background: #FBEADD;
      border-radius: 0 0 8px 8px;
    }
    .notice-swipe{
      height: 40px;
      line-height: 40px;
      .van-swipe-item{
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
</style>