<template>
  <div v-if="list.length > 0" class="swiper-container">
    <div ref="swiper" class="swiper">
      <div class="swiper-wrapper">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="swiper-slide"
          :class="`${index % 2 !== 0 ? 'odder-slide' : ''}`"
          :data-id="item.id"
          @click="adRouter(item)"
        >
          <img :src="item.pic" class="slide-image" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance, onMounted, watch, ref, useTemplateRef, nextTick } from 'vue'
import { Navigation, Pagination, EffectCoverflow, Autoplay } from 'swiper/modules'
import Swiper from 'swiper'
import { unrefElement, watchArray, whenever } from '@vueuse/core'
import 'swiper/css'
import 'swiper/css/pagination'
import { getAdList } from '@/api/base'
import { filter } from '@/utils/adIsShow'
import { adRouter } from '@/utils/toRouter'
import { add as addAdEventRecord } from '@/utils/adEventRecord'
const { proxy } = getCurrentInstance()
const store = useStore()
const list = ref({})
const loading = ref(false)
// let swiperInstance = null
const swiperRef = useTemplateRef('swiper')
const initPage = async () => {
  try {
    // 获取广告位列表
    const res = await getAdList({ regionType: [proxy.$global.AD_POSITION_HOME_MIDDLE] })
    const items = await filter(res.data?.[proxy.$global.AD_POSITION_HOME_MIDDLE] ?? [])
    list.value = items
    loading.value = true
  } finally {
    loading.value = false
  }
}
watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

const visibleIds = ref([])
function updateVisibleIds(swiper) {
  const ids = swiper?.visibleSlides?.map((slide) => +slide.dataset.id) ?? []
  visibleIds.value = ids
}

watchArray(visibleIds, (newList, oldList, added, removed) => {
  added.forEach((id) => {
    const ad = list.value.find((item) => item.id === id)
    if (!ad) return
    addAdEventRecord(ad, 'EXPOSURE')
  })
})

whenever(swiperRef, (el, oldEl, onCleanup) => {
  // console.log('swiper init', el)
  const modifier = window.innerWidth / 375

  const swiper = new Swiper(unrefElement(swiperRef), {
    modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
    slidesPerView: 'auto',
    spaceBetween: 8 * modifier,
    loop: true,
    // loopAdditionalSlides: 1,
    direction: 'horizontal',
    speed: 800,
    watchSlidesProgress: true,
    slidesOffsetBefore: 10 * modifier,
    slidesOffsetAfter: 10 * modifier,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
    on: {
      init(swiper) {
        updateVisibleIds(swiper)
      },
      realIndexChange: (swiper) => {
        updateVisibleIds(swiper)
      },
    },
  })
  onCleanup(
    watch(
      list,
      () => {
        swiper.update()
      },
      {
        deep: true,
        flush: 'post',
      }
    )
  )
  onCleanup(() => {
    swiper.destroy()
  })
})

onMounted(async () => {
  await initPage()
  // swiperInstance = new Swiper(unrefElement(swiperRef), {
  //   modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
  //   slidesPerView: 'auto',
  //   spaceBetween: 8,
  //   loop: true,
  //   // loopAddBlankSlides: true,
  //   loopAdditionalSlides: 6,
  //   direction: 'horizontal',
  //   speed: 800,
  //   watchSlidesProgress: true,
  //   slidesOffsetBefore: 10,
  //   slidesOffsetAfter: 10,

  //   // autoplay: {
  //   //   delay: 5000,
  //   //   disableOnInteraction: false,
  //   // },
  //   on: {
  //     init(swiper) {
  //       updateVisibleIds(swiper)
  //     },
  //     realIndexChange: (swiper) => {
  //       updateVisibleIds(swiper)
  //     },
  //   },
  // })
  // onUnmounted(
  //   watch(
  //     list,
  //     (newList) => {
  //       swiperInstance.update()
  //     },
  //     {
  //       deep: true,
  //     }
  //   )
  // )
  // onUnmounted(() => {
  //   swiperInstance?.destroy()
  // })
})
defineExpose({
  initPage,
})
</script>

<style scoped>
.swiper-container {
  width: 100%;
  padding: 0px 0px 10px 0px;
  height: 120px;
}
.swiper-wrapper {
  /* width: 100%; */
  display: flex;
  align-items: center;
}
.swiper-wrapper,
.swiper-slide {
  width: 109px;
  height: auto;
  /*  background-color: red; */
}
.swiper-slide.odder-slide {
  width: 98px;
}
.swiper-slide .slide-image {
  width: 100%;
  display: block;
}
</style>
