<template>
  <div v-if="receiveFlag">
    <img
      class="receive-img"
      :class="{ 'iphonex': isIphoneX }"
      src="@/assets/images/home/<USER>"
      @click="handleMember"
    >
    <div v-if="show" class="activity-wrapper" :class="{ 'iphonex': isIphoneX }">
      <div class="activity-block">
        <img class="colse" src="@/assets/images/close.png" @click="show=false">
        <div class="content">
          <span>您有一份入会好礼待领取，快来挑选<br>请正确填写收货地址，以便我们快速送达</span>
          <div class="login-btn" @click="handleMember">包邮免费送</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onActivated } from 'vue';
import { getMemberInfo, memberBenefitInfo } from '@/api/member'

const isIphoneX = window.isIphoneX
const user = computed(() => store.getters.userInfo)
const store = useStore()
const router = useRouter()
const show = ref(true)
const receiveFlag = ref(false)
const isMounted = ref(false)

const handleMember = () => {
  router.push('/member')
}

const memberGiftQuery = async () => {
  const memberRess = await getMemberInfo({ vipType: 'VIP' }, false)
  if (memberRess.data.length > 0 && memberRess.data[0].vipCust) {
    const benefitRes = await memberBenefitInfo({ id: memberRess.data[0].vipPrice.id, packageType: 'LOAN_GIVE' })
    receiveFlag.value = benefitRes.data.receiveNum ? false : true
  } else {
    receiveFlag.value = false
  }
}

//  visible可见执行
const visibleReset = () => {
  if (user.value) {
    // 会员未领取显示
    memberGiftQuery()
  } else {
    receiveFlag.value = false
  }
}

onActivated(() => {
  if (!isMounted.value) {
    // 规避首次多次执行
    visibleReset()
  } else {
    isMounted.value = false
  }
})
</script>
<style scoped lang='scss'>
  .receive-img.iphonex{
    // bottom: 165px;
    bottom: calc(121px + var(--safe-area-inset-bottom));
  }
  .receive-img{
    display: block;
    position: fixed;
    bottom: 121px;
    width: 60px;
    height: 66px;
    right: 10px;
  }
  .activity-wrapper.iphonex{
    // bottom: 103px;
    bottom: calc(59px + var(--safe-area-inset-bottom));
  }
  .activity-wrapper{
    position: fixed;
    bottom: 59px;
    left: 10px;
    z-index: 999;
    box-sizing: border-box;
    .activity-block{
      width: 355px;
      margin: 0 auto;
      position: relative;
      background: rgba(0,0,0,0.75);
      padding: 10px;
      box-sizing: border-box;
      border-radius: 8px;
      .colse{
        position: absolute;
        left: -4px;
        top: -4px;
        width: 14px;
        height: 14px;
      }
      .content{
        display: flex;
        justify-content: space-between;
        align-items: center;
        img{
          width: 20px;
          height: 20px;
          display: block;
          margin-left: 17px;
          margin-right: 14px;
        }
        span{
          font-size: 12px;
          color: #FFFFFF;
          line-height: 18px;
          flex: 1;
        }
        .login-btn{
          width: 82px;
          height: 32px;
          font-size: 12px;
          background: linear-gradient(180deg, #f43828, #f57e74);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 16px;
          color: #fff;
        }
      }
    }
  }
</style>