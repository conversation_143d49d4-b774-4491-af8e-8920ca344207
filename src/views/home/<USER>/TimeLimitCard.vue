<template>
  <div class="card" v-if="getAppVersion() !== 'VERIFY' && user" v-show="seckillStore.currentSession">
    <div class="header">
      <div class="left">
        <img class="title-image" src="@/assets/images/time-limit/会员秒杀卡片标题.png" />
        <time-limit-timer/>
      </div>
      <div class="right" @click="onClickMore">
        <span>所有商品低至4折起</span>
        <van-icon name="arrow" size="10" />
      </div>
    </div>
    <div class="goods-list">
      <van-skeleton :loading="loading">
        <template #template>
          <div class="skeleton-goods-item" v-for="n in 5">
            <van-skeleton-image class="skeleton-goods-image"></van-skeleton-image>
            <van-skeleton-paragraph class="skeleton-goods-paragraph"></van-skeleton-paragraph>
          </div>
        </template>
        <div class="goods" v-for="item of list" @click="() => onClickGoods(item)">
          <img class="goods-image" :src="item.thumbPics" />
          <div class="buy-button">
            <span class="currency">￥</span>
            <span class="price">{{ item.seckillPrice }}</span>
            <img class="tip" src="@/assets/images/time-limit/loot.png" />
          </div>
          <div class="origin">
            <span class="origin-currency">￥</span>
            <span class="origin-price">{{ item.price }}</span>
          </div>
        </div>
      </van-skeleton>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import TimeLimitTimer from '@/components/TimeLimit/TimeLimitTimer'
import { useSeckillStore } from '@/store/seckill'
import { getSeckillGoodsList } from '@/api/seckill'
import { getAppVersion } from '@/utils/auth'

const store = useStore()
const user = computed(() => store.getters.userInfo)
const router = useRouter()
const seckillStore = useSeckillStore()

const data = reactive({
  queryParams: {
    pageNum: '1',
    pageSize: '10'
  }
})
const { queryParams } = toRefs(data)
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const myRoutePath = ref('')

watch(() => seckillStore.currentSession, async () => {
  if (router.currentRoute.value.path !== myRoutePath.value) {
    return
  }
  if (seckillStore.currentSession) {
    // console.log('秒杀卡片监听到当前场次变化', seckillStore.currentSession.id)
    await onCurrentSessionChanged()
  } else {
    list.value = []
    loading.value = false
    finished.value = true
  }
})

onMounted(() => {
  myRoutePath.value = router.currentRoute.value.path
  if (user.value) {
    reload()
  }
})

const reload = async () => {
  list.value = []
  queryParams.value = {
    pageNum: '1',
    pageSize: '10'
  }
  await seckillStore.reload()
}

const onCurrentSessionChanged = async () => {
  list.value = []
  queryParams.value = {
    pageNum: '1',
    pageSize: '10'
  }
  await getList()
}

const getList = async () => {
  try {
    loading.value = true
    const session = seckillStore.currentSession
    const res = await getSeckillGoodsList({
      ...queryParams.value,
      seckillSessionId: session.seckillSessionId,
    })
    const data = Array.isArray(res.data) ? res.data : []
    list.value = [...list.value, ...data]
    if (list.value.length >= res.total) {
      finished.value = true
    } else {
      queryParams.value.pageNum++
    }
  } catch(err) {
    finished.value = true // 防止死循环
    throw err
  } finally {
    loading.value = false
  }
}

const onClickMore = () => {
  router.push('/time-limit')
}

const onClickGoods = async (item) => {
  const session = seckillStore.currentSession
  // 跳转到商品详情页面，是秒杀活动
  router.push({
    path: '/goods-detail-seckill',
    query: {
      goodsId: item.id, sessionId: session.id,
      timeLimit: '1',
      isVip: session.isVip,
    }
  })
}

defineExpose({
  reload,
})
</script>

<style lang="scss" scoped>
.card {
  border-radius: 8px;
  background-color: #fff;
  margin: 0 12px;
  background: linear-gradient(174.94deg, #FF5243 -10.32%, rgba(255, 82, 67, 0.455399) 2.04%, rgba(255, 82, 67, 0.246449) 9.98%, #fff 21.71%);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;

    .left {
      display: flex;
      align-items: center;
      gap: 4px;

      .title-image {
        height: 18px;
      }
    }

    .right {
      font-size: 12px;
      color: #1D0200;
      opacity: 50%;
      padding: 4px 0;
    }
  }

  .goods-list {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding: 0 10px 10px 10px;

    .van-skeleton {
      padding: 0;
      gap: 8px;
    }

    .skeleton-goods-item {
      padding-bottom: 18px;

      .skeleton-goods-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
      }

      .skeleton-goods-paragraph {
        border-radius: 12px;
        height: 20px;
        margin-top: 7px;
      }
    }

    .goods {
      .goods-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        // border: 1px solid #eee;
        background-color: #fff;
      }

      .buy-button {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fde9dd;
        border-radius: 12px;
        overflow: hidden;

        .currency {
          font-size: 10px;
          color: #f03927;
          margin-left: 6px;
        }

        .price {
          font-size: 14px;
          font-weight: bold;
          color: #f03927;
          flex: 1;
        }

        .tip {
          height: 20px;
        }
      }

      .origin {
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: line-through #ccc;
        margin-top: 4px;

        .origin-currency {
          font-size: 10px;
          color: #ccc;
        }

        .origin-price {
          font-size: 13px;
          color: #ccc;
        }
      }
    }
  }
}
</style>