<template>
    <div class="animation-wrapper">
        <img class="animation-1" src="@/assets/images/home/<USER>">
        <img class="animation-2" @click="handleIntegral" src="@/assets/images/home/<USER>">
        <img class="animation-3" src="@/assets/images/home/<USER>">
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
const handleIntegral = () => {
    proxy.$menuRouter('/integral')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .animation-wrapper{
        position: absolute;
        width: 100%;
        display: flex;
        justify-content: space-between;
        z-index: 99;
        img{
            display: block;
        }
        .animation-1 {
            width: 65px;
            height: 65px;
            margin-top: 35px;
            margin-left: 20px;
            animation: animatio1 2s infinite; /* 应用跳动动画 */
        }
        .animation-2 {
            width: 111px;
            height: 31px;
            margin-top:85px;
            animation: animatio2 2.5s infinite; /* 应用跳动动画 */
        }
        .animation-3 {
            width: 60px;
            height: 60px;
            margin-top: 55px;
            margin-right: 16px;
            animation: animatio1 3s infinite; /* 应用跳动动画 */
        }
        @keyframes animatio1 {
            0%, 100% { transform: translateY(0); } /* 起始状态和结束状态，图片在原始位置 */
            50% { transform: translateY(-10px); } /* 图片向上移动10像素 */
        }
        @keyframes animatio2 {
            0%, 100% { transform: translateY(0); } /* 起始状态和结束状态，图片在原始位置 */
            50% { transform: translateY(-5px); } /* 图片向上移动10像素 */
        }
    }
</style>