<template>
  <div class="welfare">
    <div class="welfare-title">
      <img src="@/assets/images/title-left.png">
      <span>APP专享福利</span>
      <img src="@/assets/images/title-right.png">
    </div>
    <div class="welfare-item" >
      <img 
        :src="item.pic"
        v-for="item in list"
        :key="item.id"
        @click="$adRouter(item)"
      >
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })
</script>

<style lang="scss" scoped>
  .welfare{
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
    border-radius: 8px;
    padding: 0 15px;
    padding-bottom: 15px;
    margin: 15px 12px;
    background: #ffffff;
    .welfare-title {
      font-size: 15px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: bold;
      color: #000000;
      line-height: 21px;
      padding-top: 19px;
      padding-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        width: 50px;
        height: 7px;
      }
      span{
        margin: 0 15px;
      }
    }
    .welfare-item{
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      img{
        width: 157px;
        height: 90px;
        margin-bottom: 10px;
      }
    }
  }
</style>