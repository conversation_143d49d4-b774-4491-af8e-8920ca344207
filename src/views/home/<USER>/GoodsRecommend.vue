<template>
  <van-list
    v-model:loading="loading"
    :finished="finished"
    finished-text="——没有更多了——"
    @load="getList"
    :immediate-check="false"
  >
      <goods-row :goods-list="list" :round="true"></goods-row>
  </van-list>
</template>

<script setup>
  import GoodsRow from '@/components/GoodsRow'
  import { regionGoodsPage } from '@/api/goods'
  const { proxy } = getCurrentInstance()
  const data = reactive({
    queryParams: {
      pageNum: '1',
      pageSize: '10'
    }
  })
  const { queryParams } = toRefs(data)
  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const getList = () => {
    regionGoodsPage({ 'regionType': proxy.$global.GOODS_REGION_RECOMMEND, 
    ...queryParams.value }).then(res => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .goods-list-style-item{
    margin: 10px;
  }
  .goods-price{
    margin-top: 6px;
    .sale-price{
      font-size: 12px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #E9362E;
      .big-number{
        font-size: 18px;
      }
    }
    .market-price{
      margin-left: 5px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      text-decoration: line-through;
    }
  }
  .goods-info{
    margin-left: 14px;
  }
  .goods-info-top .goods-label{
    display: flex;
    .scale{
      border-radius: 2px;
      border: 1px solid #E9362E;
      font-size: 12px;
      transform: scale(0.8);
      transform-origin: left top;
      padding: 1px 3px;
      color: #E9362E;
      margin-top: 4px;
    }
  }
  .goods-sales-price{
    .big-number{
      font-size: 18px !important;
    }
  }
  .goods-title{
    .member-price{
      display: inline-block;
      font-size: 12px;
      transform: scale(0.8);
      transform-origin: left center;
      font-weight: 400;
      color: #FFFFFF;
      background: #E9362E;
      border-radius: 2px;
      padding: 1px 3px;
    }
  }
</style>