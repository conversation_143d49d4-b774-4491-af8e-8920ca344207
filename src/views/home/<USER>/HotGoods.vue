<template>
  <div class="hot-goods">
    <div class="title">
      <span class="title-name">爆款专区</span><span class="red-tips">热榜</span>
    </div>
    <div class="list">
      <div class="item" v-for="(item, index) in goodsList" :key="index" @click="$goodsRouter(item)">
        <img :src="item.image">
        <div class="goods-name overflow-1">{{ item.spuTitle }}</div>
        <div class="goods-price">￥{{ item.priceDown }}</div>
        <div class="goods-market">￥{{ item.price }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    goodsList: {
      type: Array,
      default: () => []
    }
  })
</script>

<style lang="scss" scoped>
  .hot-goods{
    background: #FFFFFF;
    border-radius: 6px;
    padding: 21px 10px;
    margin: 15px 12px;
    .title{
      display: flex;
      align-items: center;
      &-name{
        font-size: 16px;
        font-weight: bold;
        line-height: 22px;
      }
      .red-tips{
        width: 36px;
        height: 21px;
        background: #FF4941;
        border-radius: 4px;
        font-size: 12px;
        margin-left: 9px;
        line-height: 21px;
        color: #ffffff;
        text-align: center;
      }
    }
    .list{
      display: flex;
      margin-top: 16px;
      .item{
        width: 74px;
        text-align: center;
        img{
          width: 74px;
          height: 74px;
        }
        .goods-name{
          margin-top: 13px;
          font-size: 12px;
          font-family: PingFang-SC-Regular, PingFang-SC;
          font-weight: 400;
          color: #222222;
          line-height: 17px;
        }
        .goods-price{
          margin-top: 5px;
          font-size: 13px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: bold;
          color: #CD3330;
          line-height: 18px;
        }
        .goods-market{
          font-family: PingFang-SC-Regular, PingFang-SC;
          font-weight: 400;
          color: #A8A8A8;
          font-size: 12px;
          transform: scale(0.85);
          transform-origin: center top;
          text-decoration: line-through;
          margin-top: 2px;
        }
      }
      .item +.item{
        margin-left: 14px;
      }
    }
  }
</style>