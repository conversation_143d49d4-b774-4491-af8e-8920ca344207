<template>
  <van-popup
    :show="isRevealed"
    class="ad-popup"
    teleport="body"
    closeable
    :close-on-click-overlay="false"
    @click-overlay="cancel('close')"
    @click-close-icon="cancel('close')"
  >
    <div class="body">
      <img :src="currentAd?.pic" alt="" @click="confirm(currentAd)" />
    </div>
  </van-popup>
</template>

<script setup>
import { useAsyncState, useConfirmDialog, useLocalStorage } from '@vueuse/core'
import { nextTick, reactive, unref } from 'vue'
import dayjs from 'dayjs'

import $global from '@/constant/Global.js'
import { adRouter } from '@/utils/toRouter'
import { getAdList } from '@/api/base'
import { creditQuota } from '@/api/customer'
import { listOrder } from '@/api/cashloan'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

const store = useStore()
const user = computed(() => store.getters.userInfo)

const adHistory = useLocalStorage('CJAdHistory', new Map())

const showedIds = reactive(new Set())

// 当前广告
const currentAd = ref(null)

const {
  state: quota,
  isLoading: quotaIsLoading,
  isReady: quotaIsReady,
  execute: getQuota,
} = useAsyncState(
  async () =>
    (
      await creditQuota({
        requestType: 'query',
        sceneCode: $global.CREDIT_SCENE_CASH,
      })
    ).data,
  undefined,
  {
    immediate: false,
    throwError: true,
  }
)
const {
  state: order,
  isLoading: orderIsLoading,
  execute: getOrder,
} = useAsyncState(
  async () =>
    (
      await listOrder({
        pageSize: 1,
        pageNum: 1,
        productId: quota.value.productId,
      })
    ).data,
  undefined,
  { immediate: false, throwError: true }
)

// 广告列表
const {
  execute: refreshAdList,
  isLoading: adListIsLoading,
  state: adList,
} = useAsyncState(
  async () => {
    if (user.value?.creditIntentionFlag === 'Y') {
      await getQuota()
      await getOrder()
    }

    const { AD_POSITION_HOME_POPUP } = $global

    const adList =
      (
        await getAdList({
          regionType: [AD_POSITION_HOME_POPUP],
        })
      ).data?.[AD_POSITION_HOME_POPUP] ?? []

    return adList
      .map((item) => ({
        ...item,
        remarkObj: (() => {
          try {
            return JSON.parse(item.remark)
          } catch (error) {
            return {}
          }
        })(),
      }))
      .filter((ad) => {
        const history = adHistory.value.get(ad.id)
        // 每日最大次数
        if (
          history &&
          history.day === dayjs().format('YYYY-MM-DD') &&
          ad.remarkObj?.maxCount &&
          history.count >= ad.remarkObj?.maxCount
        ) {
          return false
        }

        // 登录状态
        if (
          ad.remarkObj.isLogin?.length &&
          !ad.remarkObj.isLogin.includes(user.value?.id ? 'Y' : 'N')
        ) {
          return false
        }

        // 金融属性
        if (
          ad.remarkObj.creditIntentionFlag?.length &&
          (!user.value?.id ||
            !ad.remarkObj.creditIntentionFlag.includes(user.value?.creditIntentionFlag))
        ) {
          return false
        }

        // 授信状态
        if (
          ad.remarkObj.creditStatus?.length &&
          (!user.value?.id || !ad.remarkObj.creditStatus.includes(quota.value?.creditStatus))
        ) {
          return false
        }

        // 用信状态
        if (
          ad.remarkObj.orderStatus?.length &&
          (!user.value?.id || !ad.remarkObj.orderStatus.includes(order.value?.status))
        ) {
          return false
        }

        return true
      })
  },
  [],
  {
    immediate: false,
    resetOnExecute: false,
    throwError: true,
  }
)

// const adListFiltered = computed(() => {
//   return adList.value.map((item) => ({
//     ...item,
//     remarkObj: (() => {
//       try {
//         return JSON.parse(item.remark)
//       } catch (error) {
//         return {}
//       }
//     })(),
//   }))
// })

onReveal((data) => {
  showedIds.add(data.id)
  currentAd.value = data

  const history = adHistory.value.get(data.id)

  const count = history && history.day === dayjs().format('YYYY-MM-DD') ? history.count + 1 : 1

  adHistory.value.set(data?.id, {
    count,
    day: dayjs().format('YYYY-MM-DD'),
  })
})
onConfirm((ad) => {
  console.log('confirm')

  // next()
  adRouter(ad)
})
onCancel(async () => {
  console.log('cancel')
  await nextTick()
  await new Promise((resolve) => setTimeout(resolve, 300))
  next()
})

function next() {
  const ad = adList.value.find((item) => !showedIds.has(item.id))
  console.log(ad)

  if (!ad) {
    showedIds.clear()
    return
  }

  reveal(ad)
}

onActivated(async () => {
  console.log('onActivated')
  if (showedIds.size === 0) {
    await refreshAdList()
  }
  next()
  // reveal()
})
</script>

<style lang="scss" scoped>
.ad-popup {
  font-size: 14px;
  max-width: 70%;
  // .header {
  //   font-size: 16px;
  //   font-weight: bold;
  // }
  --van-popup-close-icon-size: 18px;
  --van-popup-close-icon-color: #fff;
  :deep(.van-popup__close-icon) {
    top: unset;
    right: unset;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    border: 2px solid var(--van-popup-close-icon-color);
    border-radius: 999vw;
    padding: 4px;
    font-weight: 600;
  }
  .body {
    img {
      width: 100%;
      // height: 100%;
      height: auto;
      display: block;
    }
  }
}
</style>
