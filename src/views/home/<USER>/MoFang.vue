<template>
  <div v-if="list.length >= 3" class="mofang">
    <div class="left">
      <my-swiper :swiperDatas="adList" :autoplay="3500"></my-swiper>
    </div>
    <div class="right">
      <div class="right-top">
        <img :src="list[list.length-2]?.pic" @click="$adRouter(list[list.length-2])">
      </div>
      <div class="right-bottom">
        <img :src="list[list.length-1]?.pic" @click="$adRouter(list[list.length-1])">
      </div>
    </div>
  </div>
</template>

<script setup>
  import MySwiper from '@/components/MySwiper'
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })
  const adList = ref([])
  watch(() => props.list, (newValue, oldValue) => { // 用户变更重新获取数据
    let newArr = []
    if (newValue?.length > 0) {
      newValue.map((item, index) => {
        if (index < newValue.length - 2) {
          newArr.push(item)
        }
      })
      adList.value = newArr
    }
  },{deep: true, immediate: true});
</script>

<style lang="scss" scoped>
  .mofang{
    margin: 10px 12px;
    display: flex;
    justify-content: space-between;
    font-size: 0;
    .left{
      width: 161px;
      height: auto;
      :deep(.van-swipe-item){
        width: 172px;
        height: 215px;
      }
    }
    .right{
      width: 182px;
      height: 215px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .right img{
      height: 103px;
      display: block;
    }
  }
</style>