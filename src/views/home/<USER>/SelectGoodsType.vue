<script setup>
import { onMounted } from 'vue'
import { categoryList } from '@/api/goods'

const emit = defineEmits(['change'])

const props = defineProps({
  regions: {
    type: Array,
    default() {
      return []
    },
  },
})
// 一级分类
const categorys = ref(JSON.parse(JSON.stringify(props.regions)))
// 二级分类
const categoryItems = ref([])
// 当前一级分类的已选项
const activeValue = ref(null)

const current = defineModel({
  default: {
    type: '',
    value: '',
    name: '',
  },
})

const reload = async () => {
  // 获取商品分类
  const res = await categoryList({ parentId: '0', navStatus: 'Y', querySub: 'Y' })
  const arr = (Array.isArray(res.data) ? res.data : []).filter((t) => {
    return true
  })
  categorys.value = props.regions.concat(
    arr.map((t) => {
      return {
        type: 'category',
        name: t.categoryName,
        value: t.categoryId,
        sub: t.subCategoryList,
      }
    })
  )
  // if (
  //   categorys.value.length > 0 &&
  //   !categorys.value.find((t) => t.type === current.value.type && t.value === current.value.value)
  // ) {
  //   current.value =
  //     categorys.value.length > 0 ? categorys.value[0] : { type: '', value: '', name: '' }
  // }
}

const onClickItem = (item) => {
  if (
    (item.type === current.value.type && item.value === current.value.value) ||
    item.value === activeValue.value
  ) {
    return
  }
  if (item.sub) {
    categoryItems.value = item.sub.map((t) => {
      return {
        type: 'category',
        name: t.categoryName,
        value: t.categoryId,
      }
    })
    current.value = categoryItems.value[0]
  } else {
    categoryItems.value = []
    current.value = item
  }
  activeValue.value = item.value
  emit('change', item)
}

const onClickcategoryItemsItem = (item) => {
  if (item.type === current.value.type && item.value === current.value.value) {
    return
  }
  current.value = item
  emit('change', item)
}

onMounted(() => {
  reload()
})

defineExpose({
  reload,
})
</script>

<template>
  <div class="select-goods-type">
    <div
      :class="[
        'list-item',
        (item.type === current.type && item.value === current.value && 'list-item-activated') ||
          (item.value === activeValue && 'list-item-activated'),
      ]"
      v-for="(item, index) of categorys"
      :key="index"
      @click="() => onClickItem(item)"
    >
      {{ item.name }}
    </div>
  </div>
  <div class="select-good-items-type">
    <div
      :class="[
        'select-good-items-type-item',
        item.type === current.type &&
          item.value === current.value &&
          'select-good-items-type-item-activated',
      ]"
      v-for="(item, index) of categoryItems"
      :key="index"
      @click="() => onClickcategoryItemsItem(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-goods-type,
.select-good-items-type {
  display: flex;
  align-categorys: center;
  gap: 12px;
  margin: 0px 15px;
  overflow-x: scroll;
  .list-item {
    font-size: 16px;
    color: #333;
    // font-weight: 600;
    line-height: 23px;
    white-space: nowrap;
    user-select: none;
    cursor: pointer;
    padding-top: 1px;
  }
  .list-item-activated {
    color: #ff1830;
    // &::after {
    //   content: '';
    //   position: absolute;
    //   left: 50%;
    //   transform: translateX(-50%);
    //   bottom: -16px;
    //   width: 20px;
    //   height: 16px;
    //   background: url('@/assets/images/goods/当前商品分类.png') no-repeat center;
    //   background-size: contain;
    // }
  }
  .select-good-items-type-item {
    padding: 0 10px;
    height: 20px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 400;
    color: #999;
    line-height: 20px;
    margin-top: 5px;
    white-space: nowrap;
    user-select: none;
    cursor: pointer;
    // padding-bottom: 2px;
  }
  .select-good-items-type-item-activated {
    font-size: 14px;
    color: #fff;
    background-color: #ff1830;
  }
}
</style>
