<script setup>
import { onMounted, watch } from 'vue';
import { getFunGroup } from '@/api/home'

const { proxy } = getCurrentInstance()
const store = useStore()

const loading = ref(false)
const list = ref([])

const initPage = async () => {
  console.log('HomeIconList 初始化')
  try {
    loading.value = true
    const res = await getFunGroup({ funcRegion: proxy.$global.ICON_FUN_APP_HOME_TOP })
    list.value = (Array.isArray(res.data) ? res.data : []).slice(0, 4)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await initPage()
})

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  },
)

defineExpose({
  initPage
})
</script>

<template>
  <div class="home-icon-list">
    <van-skeleton :row="4" class="margin" :loading="loading">
      <template #template>
        <van-skeleton-image v-for="item in 4" />
      </template>
      <div class="list">
        <div class="list-item" v-for="item of list">
          <img class="list-item-image" :src="item.icon" @click="$funcRouter(item)">
          <div class="list-item-text">{{ item.name }}</div>
        </div>
      </div>
    </van-skeleton>
  </div>
</template>

<style lang="scss" scoped>
.home-icon-list {
  padding: 12px 20px 20px 20px;
  background: linear-gradient(180deg, #f57267, #f5afa9);
  margin-top: -1px;

  .van-skeleton {
    padding: 0;
  }

  .van-skeleton-image {
    width: 20%;
    height: auto;
    margin: 0 10px;
    border-radius: 500px;
  }
}

.list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px 0;
  
  .list-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }
  
  .list-item-image {
    width: auto;
    height: 35px;
  }

  .list-item-text {
    font-size: 14px;
    color: #fff;
  }
}
</style>