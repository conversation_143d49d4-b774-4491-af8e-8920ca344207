<template>
  <div class="product">
    <div class="product-info">
      <div class="product-info-left">
        <div class="product-text">贷款方最高额度（元）</div>
        <div class="product-amount">{{ formatMoney(product.quota ? product.quota : 20000, 0) }}</div>
      </div>
      <div class="product-info-right">
        <img class="tips-img" src="@/assets/images/home/<USER>">
        <div class="product-btn" @click="borrowMoney">去借钱</div>
      </div>
    </div>
    <van-notice-bar :delay="2" style="background: linear-gradient( 180deg, #FBEADD 0%, #FFFAED 100%);" color="#4F392D" left-icon="volume-o" :scrollable="false">
      <van-swipe
        vertical
        class="notice-swipe"
        :autoplay="3000"
        :show-indicators="false"
      >
        <van-swipe-item class="flex justify-between" v-for="(item, index) in activeList" :key="index">
          <span>{{item.phone}}</span>
        </van-swipe-item>
      </van-swipe>
    </van-notice-bar>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance()
  const activeList = proxy.$global.ACTIVE_LIST
  const props = defineProps({
    product: {
      type: Object,
      default: () => {}
    }
  })
  const emit = defineEmits(['borrowMoney'])
  const borrowMoney = () => {
    emit('borrowMoney')
  }
</script>

<style lang="scss" scoped>
  .product{
    border-radius: 8px;
    margin: 15px 12px;
    .notice-swipe{
      height: 46px;
      line-height: 46px;
      .van-swipe-item{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
    .product-info{
      padding: 28px 21px 25px 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: url('@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
      margin-bottom: -5px;
      &-left{
        .product-text{
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
        }
        .product-amount{
          font-size: 34px;
          font-family: DIN, DIN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 40px;
          margin-top: 10px;
        }
      }
      &-right{
        .tips-img{
          width: 102px;
          height: 22px;
          display: block;
        }
        .product-btn{
          width: 94px;
          height: 32px;
          background: linear-gradient( 180deg, #FAEECB 0%, #F8D494 100%);
          border-radius: 19px;
          line-height: 32px;
          text-align: center;
          font-weight: 600;
          font-size: 15px;
          color: #5C463C;
          margin-top: 10px;
        }
      }
    }
  }
</style>