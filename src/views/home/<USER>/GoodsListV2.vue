<script setup>
import { getCurrentInstance, onMounted, watch } from 'vue'
import SelectGoodsType from './SelectGoodsType.vue'
import GoodsList from '@/components/GoodsList/GoodsList.vue'

const { proxy } = getCurrentInstance()
const selectGoodsTypeRef = ref()
const currentGoodsType = ref({
  type: 'region',
  value: proxy.$global.GOODS_REGION_FOLLOW,
  name: '热销榜单',
})
const regions = ref([
  {
    type: 'region',
    value: proxy.$global.GOODS_REGION_FOLLOW,
    name: '热销榜单',
  },
  // {
  //   type: 'region',
  //   value: proxy.$global.GOODS_REGION_HOT_LIST,
  //   name: '爆品榜单',
  // },
])
const goodsListRef = ref()

const initPage = async () => {
  selectGoodsTypeRef.value.reload()
  goodsListRef.value.reload()
}

onMounted(() => {
  initPage()
})

defineExpose({
  initPage,
})
</script>

<template>
  <div class="goods-list">
    <SelectGoodsType ref="selectGoodsTypeRef" v-model="currentGoodsType" :regions="regions" />
    <GoodsList ref="goodsListRef" :listType="currentGoodsType" :isAdShow="true" />
  </div>
</template>

<style lang="scss" scoped>
.goods-list {
  .goods-list {
    margin: 12px 10px;
  }

  .goods-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
</style>
