<template>
  <div class="premiun" v-if="visible">
    <div class="title">
      <div class="flex align-center">
        <img class="img" src="@/assets/images/home/<USER>">
        <span class="title-text">极刻花推荐</span>
      </div>
      <div class="flex align-center" @click="handleRecommend">
        <span class="text-all">查看更多</span>
        <van-icon name="arrow" size="16" class="text-gray" />
      </div>
    </div>
    <div class="goods-list" v-if="list.length > 0">
      <template v-for="(item, index) in list" :key="item.spuId">
        <div v-if="index < 10" class="goods-list-item" @click="$goodsRouter(item)">
          <img class="goods-img" :src="item.image">
          <div class="goods-name overflow-1">{{ item.spuTitle }}</div>
          <div class="price-wrapper">
            <div class="price">
              预计{{ formatMoney(item.priceDown / 2, 0) }}元*2
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, computed } from 'vue'
import { regionGoodsPage } from '@/api/goods'

const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const visible = ref(false)

const list = ref([])

const handleRecommend = () => {
  router.push({ path: '/installment' })
}

const initPage = async () => {
  // 暂时隐藏
  // console.log('PremiumRecommendV2 初始化')
  // 精品
  // if (user.value && user.value.flow === proxy.$global.USER_FLOW_FINISH) {
  //   const res = await regionGoodsPage({
  //     regionType: proxy.$global.GOODS_REGION_PREMIUM_RECOMMEND,
  //     pageNum: '1',
  //     pageSize: '10',
  //   })
  //   list.value = Array.isArray(res.data) ? res.data : []
  //   visible.value = true
  // }
}

onMounted(() => {
  initPage()
})

defineExpose({
  initPage
})
</script>
<style scoped lang='scss'>
.premiun {
  background: linear-gradient(180deg, #FFEDEB 0%, #FFF6F6 100%);
  border-radius: 8px;
  padding: 20px 10px;
  margin: 12px;

  .title {
    display: flex;
    justify-content: space-between;

    .img {
      width: 24px;
      height: 24px;
      display: block;
      margin-right: 11px;
    }

    &-text {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      display: block;
    }

    .text-all {
      font-size: 12px;
      color: #333333;
      line-height: 16px;
      margin-right: 1px;
    }
  }

  .goods-list {
    margin-top: 28px;
    display: flex;
    overflow-x: auto;

    /* 水平滚动条 */
    .goods-list-item+.goods-list-item {
      margin-left: 10px;
    }

    &-item {
      width: 940px;
      text-align: center;

      .goods-img {
        width: 94px;
        height: 94px;
        display: block;
        border-radius: 5px;
      }

      .goods-name {
        margin-top: 9px;
        font-size: 13px;
        color: #333333;
        line-height: 18px;
      }

      .price-wrapper {
        padding: 3px 5px;
        background: #FFDCE1;
        border-radius: 11px;
        transform-origin: center;
        /* 确保从中心缩放 */
        display: inline-block;
        margin-top: 9px;
      }

      .price {
        font-size: 12px;
        transform: scale(0.9);
        color: #E04444;
        line-height: 16px;
        text-align: center;
      }
    }
  }
}
</style>