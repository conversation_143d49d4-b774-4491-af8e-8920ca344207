<template>
  <srcoll-nav :pageName="quotaData.productName" @onLeftClick="onBackClick">
    <div class="detail-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="confirm-amount-card">
        <confirm-amount :amount="amount" :maxAmount="maxAmount" :quotaList="quotaData.quotaList" @changed="onAmountChanged"/>
      </div>
      <!-- <borrow-money v-model="amount" :quota-data="quotaData" @updateAmount="checkAmount" /> -->
      <member-content
        ref="memberContentRef"
        v-model="memberOpenFlag"
        v-if="memberInfo.jskFlag === 'Y' || memberInfo.jskFlag === 'FORCE'"
        :memberInfo="memberInfo"
        @showMemberAgreement="memberAgreementRef.show = true"
      />
      <div style="height: 200px"></div>
    </div>
    <footer-fiexd :footer-style="{ background: '#f6f6f6' }">
      <div class="confirm-btn" @click="onConfirmAmount">确认</div>
    </footer-fiexd>
    <member-apply-popup
      ref="memberApplyRef"
      :data="memberInfo"
      @showMemberAgreement="memberAgreementRef.show = true"
    ></member-apply-popup>
    <open-success
      ref="openSuccessRef"
      title="激活成功"
      v-if="memberInfo.vipPrice"
      :tips="`成功开通如意卡·${memberInfo.vipPrice.vipName}，享优先推荐特权`"
      :show-btn="true"
      @onConfirm="nextPage"
    ></open-success>
    <agreement-popup
      url="/agreement/member-service.htm"
      title="权益服务协议"
      ref="memberAgreementRef"
    ></agreement-popup>
    <not-bank-tips
      ref="notBankTipsRef"
      @add-bank="memberAddBankRef.show = true"
      title="您还未绑定开通权益扣款卡"
    ></not-bank-tips>
    <member-add-bank
      ref="memberAddBankRef"
      :bind-params="payInfo"
      @bindSuccess="launchPay"
    ></member-add-bank>
    <!-- <retain-dialog
      ref="retainDialogRef"
      :data="memberInfo"
      @showMemberAgreement="memberAgreementRef.show = true"
    /> -->
  </srcoll-nav>
</template>

<script setup name="cashLoanDetail">
import SrcollNav from '@/components/ScrollNavV2'
import { creditQuota } from '@/api/customer'
import { submitMemberOrder, payProduct, submitPay, isNeedBindCard } from '@/api/member-bank'
import { acceleratorCardConfig } from '@/api/member'
import MemberContent from './components/MemberContent'
import ConfirmAmount from '../confirm/components/ConfirmAmount.vue'
import BorrowMoney from './components/BorrowMoney'
import MemberApplyPopup from './components/MemberApplyPopup'
import AgreementPopup from '@/components/AgreementPopup'
import OpenSuccess from './components/OpenSuccessDialog.vue'
import NotBankTips from '@/views/member/components/memberBank/NotBankTips'
import MemberAddBank from '@/views/member/components/memberBank/AddBank'
import { useAcceleratorCard } from '@/hooks/useAcceleratorCard'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { onBeforeRouteLeave } from 'vue-router'
import { onMounted, unref } from 'vue'
// import RetainDialog from './components/RetainDialog/index.vue'
// import { until } from '@vueuse/core'
import goodsCheckStore from '@/store/goodsCheck.js'

const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const isIphoneX = window.isIphoneX
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const amount = ref(undefined)
const maxAmount = ref(0)
const memberOpenFlag = ref(false)
const memberApplyRef = ref(null)
const memberContentRef = ref(null)
const memberAgreementRef = ref(null)
const openSuccessRef = ref(null)
const notBankTipsRef = ref(null)
const memberAddBankRef = ref(null)
const data = reactive({
  quotaData: {},
  memberInfo: {},
  payInfo: {},
})
const { quotaData, memberInfo, payInfo } = toRefs(data)

onMounted(async () => {
  await creditQuota({
    requestType: 'query',
    sceneCode: proxy.$global.CREDIT_SCENE_CASH,
  }).then((res) => {
    // 测试代码
    // if (import.meta.env.VITE_APP_ENV === 'development') {
    //   res.data.ultimaCash = 2500
    // }
    quotaData.value = res.data
    // 最大额度
    maxAmount.value = quotaData.value.ultimaCash ? quotaData.value.ultimaCash : 0
    // 未设置数量时，使用第一个预设数量
    if (!amount.value && quotaData.value.quotaList.length > 0) {
      amount.value = quotaData.value.quotaList[0]
    }
  })
  await useAcceleratorCard(
    {
      stage: 'APPLY',
      productId: quotaData.value.productId,
      amount: amount.value || 0,
    },
    (data) => {
      memberInfo.value = data
    }
  )
})
const onBackClick = () => {
  router.go(-1)
}
const onAmountChanged = (val) => {
  amount.value = val
  useAcceleratorCard(
    {
      stage: 'APPLY',
      productId: quotaData.value.productId,
      amount: amount.value || 0,
    },
    (data) => {
      memberInfo.value = data
    }
  )
}
// 选择金额
const checkAmount = (index) => {
  amount.value = quotaData.value.quotaList[index]
  useAcceleratorCard(
    {
      stage: 'APPLY',
      productId: quotaData.value.productId,
      amount: amount.value || 0,
    },
    (data) => {
      memberInfo.value = data
    }
  )
}
// 用信下一页
const nextPage = async () => {
  retainDialogIsDisable.value = true
  if (amount.value <= quotaData.value.ultimaCash) {
    // 先享后付是否存在待绑卡
    const bindRes = await isNeedBindCard({ sceneCode: 'APPLY' }, true)
    if (bindRes.data.supplement === 'Y') {
      // 需要绑卡
      router.push({
        path: '/my/bankcard/add',
        query: {
          channelId: bindRes.data.chnlId,
          orderId: bindRes.data.orderId,
          cmId: bindRes.data.cmId,
          replace: router.resolve({
            path: '/cash-loan-confirm',
            query: {
              amount: amount.value,
            },
          }).fullPath,
        },
      })
    } else {
      localStorage.removeItem('checkedPeriod') // 删除记录所选期数
      router.replace({
        path: '/cash-loan-confirm',
        query: { amount: amount.value },
      })
    }
  } else {
    showToast('您当前可用现金额度不足' + amount.value)
  }
}
// 提交支付
const launchPay = async () => {
  const payRes = await submitPay({
    cashierId: payInfo.value.id,
    orderId: payInfo.value.orderId,
    productId: payInfo.value.trials[0].productResponse.productId,
    subProductId: payInfo.value.trials[0].productResponse.subProductId,
  }).catch((error) => {
    console.log(error)
  })
  if (payRes) {
    openSuccessRef.value.reveal().then(({ isCanceled }) => {
      if (!isCanceled) nextPage()
    })
  }
}
// 取消开通
const cancelOpen = () => {
  if (memberInfo.value.jskFlag === 'Y') {
    nextPage()
  }
}
// 先发起会员开通
const memberOpenFn = async () => {
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  try {
    const configDataRes = await acceleratorCardConfig({
      stage: 'APPLY',
      productId: quotaData.value.productId,
      amount: amount.value,
    })
    if (configDataRes.data.jskFlag !== 'N') {
      // 再次校验是否已开通
      const orderRes = await submitMemberOrder({
        sceneCode: proxy.$global.CREDIT_SCENE_CASH,
        stage: 'APPLY',
        scenePage: 'LOAN',
        virtualMsg: { vipPriceId: memberInfo.value.vipPrice.id },
      })
      const orderId = orderRes.data.orderId
      // 获取支付产品信息
      const payRes = await payProduct({ orderId })
      payInfo.value = payRes.data.cashiers[0]
      payInfo.value.orderId = orderId
      // 提交支付
      await launchPay()
    } else {
      nextPage()
    }
    closeToast() // 清除加载中
  } catch (e) {
    closeToast() // 清除加载中
  }
}

// 确认金额
const onConfirmAmount = async () => {
  if (amount.value <= 0) {
    showToast('取现金额不能为0')
    return
  }
  // 会员判断
  if (memberInfo.value.jskFlag === 'Y' || memberInfo.value.jskFlag === 'FORCE') {
    if (memberOpenFlag.value) {
      memberOpenFn()
    } else {
      goodsCheckStore.value = null
      const { isCanceled, data } = await memberApplyRef.value.reveal()
      if (!isCanceled) {
        if (data?.gift) {
          goodsCheckStore.value = data.gift
        }
        await memberOpenFn()
        return
      }
      // data==='close' 表示关闭弹窗
      // data === 'cancel' 表示不开会员，但还可以继续申请额度
      if (data === 'cancel') {
        cancelOpen()
      }
    }
  } else {
    nextPage()
  }
}

// const answer = ref(false)
const retainDialogRef = ref()
const retainDialogIsDisable = ref(false)

onBeforeRouteLeave(async (to, from) => {
  // console.log({ to, from })
  // 挽留弹窗已禁用，允许离开页面
  // if (retainDialogIsDisable.value) {
  //   return true
  // }
  // // 不需要开通会员，允许离开页面
  // if (['N'].includes(memberInfo.value?.jskFlag)) {
  //   return true
  // }
  // // 开始挽留
  // const { isCanceled, data } = await retainDialogRef.value.reveal()
  // // 没挽留住，允许离开页面
  // if (isCanceled) {
  //   return true
  // }
  // // 开通
  // // memberOpenFn().then(() => {})
  // console.log(data)
  // return false
})

onMounted(async () => {
  // await retainDialogRef.value.reveal()
  // await until(openSuccessRef).toBeTruthy()
  // openSuccessRef.value.reveal()
})
</script>

<style lang="scss" scoped>
.detail-page-content {
  margin-top: -1px;
}
.confirm-btn {
  width: 100%;
  border-radius: 20px;
  height: 40px;
  line-height: 40px;
  color: #ffffff;
  background: linear-gradient(180deg, #FF3927 0%, #FF8C64 100%);
  font-weight: 500;
  font-size: 15px;
  text-align: center;
}
.confirm-amount-card {
  background-color: #fff;
  border-radius: 15px;
  margin: 10px;

  .borrow-money-amount {
    padding: 12px 16px;
  }
}
</style>
