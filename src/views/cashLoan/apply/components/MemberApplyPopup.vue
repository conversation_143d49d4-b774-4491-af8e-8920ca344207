<template>
  <div class="">
    <member-apply-dialog
      ref="memberApplyDialogRef"
      :data="data"
      @showMemberAgreement="showMemberAgreement"
    />
    <retain-dialog ref="retainDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { useAsyncState, useConfirmDialog, useToggle } from '@vueuse/core'

import { getConfing } from '@/api/base'

import MemberApplyDialog from './MemberApplyDialog.vue'
// import RetainDialog from './RetainDialog/index.vue'
import RetainDialog from '@/views/member/components/RetainDialog.vue'

import { computed } from 'vue'

const store = useStore()

const props = defineProps<{
  data: Record<string, any>
}>()

const emit = defineEmits<{
  showMemberAgreement: []
}>()

function showMemberAgreement() {
  emit('showMemberAgreement')
}

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  reveal,
})

const memberApplyDialogRef = ref()
const retainDialogRef = ref()

const { state: MEMBER_RETENTION_POPUP_PARTNER } = useAsyncState(
  async () => {
    const list =
      (await getConfing({ key: 'MEMBER_RETENTION_POPUP_PARTNER' })).data?.[0]?.configValue?.split(
        ','
      ) ?? []

    return {
      include: list.filter((v) => /^[^!]/.test(v)),
      exclude: list.filter((v) => /^!/.test(v)).map((v) => v.replace(/^!/, '')),
    }
  },
  {
    include: [],
    exclude: [],
  },
  {}
)

const userPartnerId = computed(() => store.getters.userInfo?.partnerId)

const retainDialogIsEnable = computed(() => {
  const { include, exclude } = MEMBER_RETENTION_POPUP_PARTNER.value
  if (exclude.length) {
    return !exclude.include('All') && !exclude.include(String(userPartnerId.value))
  }
  return include.includes('ALL') || include.includes(String(userPartnerId.value))
})

onReveal(async () => {
  const memberApplyDialogRes = await memberApplyDialogRef.value.reveal()
  if (!memberApplyDialogRes.isCanceled) {
    confirm(memberApplyDialogRes.data)
    return
  }

  if (retainDialogIsEnable.value) {
    const retainDialogRes = await retainDialogRef.value.reveal()
    if (!retainDialogRes.isCanceled) {
      confirm(retainDialogRes.data)
      return
    }
    cancel(retainDialogRes.data)
    return
  }
  cancel(memberApplyDialogRes.data)
})
</script>

<style scoped lang="scss"></style>
