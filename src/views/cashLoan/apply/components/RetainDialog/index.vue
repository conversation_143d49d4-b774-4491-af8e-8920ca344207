<template>
  <div>
    <dialog-1 ref="dialog1Ref" />
    <dialog-2 ref="dialog2Ref" />
    <dialog-3 ref="dialog3Ref" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Dialog1 from './RetainDialog2.vue'
import Dialog2 from './RetainDialog3.vue'
import Dialog3 from './RetainDialog4.vue'

import { useConfirmDialog } from '@vueuse/core'

const props = defineProps<{
  data: Record<string, any>
}>()

const vipPrice = computed(() => props.data?.vipPrice)
provide('vipPrice', vipPrice)

// function getVipPrice() {
//   return vipPrice.value
// }

const emit = defineEmits<{
  showMemberAgreement: []
}>()
function showMemberAgreement() {
  emit('showMemberAgreement')
}
provide('showMemberAgreement', showMemberAgreement)

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  reveal,
})

const dialog1Ref = ref()
const dialog2Ref = ref()
const dialog3Ref = ref()

onReveal(async () => {
  const d1res = await dialog1Ref.value.reveal()
  if (!d1res.isCanceled) {
    confirm({
      // data: d1res.data,
      // ...d1res.data,
      from: 'dialog1',
    })
    return
  }

  const d2res = await dialog2Ref.value.reveal()
  if (!d2res.isCanceled) {
    confirm({
      // data: d2res.data,
      // ...d2res.data,
      gift: d2res.data,
      from: 'dialog2',
    })
    return
  }

  const d3res = await dialog3Ref.value.reveal()
  if (!d3res.isCanceled) {
    confirm({
      // data: d3res.data,
      from: 'dialog3',
    })
    return
  }

  cancel(d3res.data)
  return
})

onConfirm(() => {})
onCancel(() => {})
</script>

<style scoped></style>
