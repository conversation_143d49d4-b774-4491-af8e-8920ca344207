<template>
  <van-popup
    :show="isRevealed"
    :close-on-click-overlay="false"
    teleport="body"
    class="dialog"
    @close="cancel()"
  >
    <div class="content" @click="confirm()"></div>
    <van-button class="confirm-btn" round block @click="confirm()">确定开通</van-button>
    <van-button class="cancel-btn" @click="cancel('cancel')">继续取消轻享花如意卡开通</van-button>
  </van-popup>
</template>

<script setup lang="ts">
import { useConfirmDialog, useToggle } from '@vueuse/core'
const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  reveal,
})
</script>

<style scoped lang="scss">
.dialog {
  background: transparent;
  background-image: url('@/assets/images/cash-loan/retain-dialog/dialog-2-bg.png');
  background-size: 100% 100%;
  width: 304px;
  height: 386px;
  padding: 188px 22px 10px;
}
.content {
  height: 110px;
}
.confirm-btn {
  border: none;
  color: #f4e1d0;
  font-size: 16px;
  background: linear-gradient(178deg, #432811 0%, #26221f 100%);
  height: 40px;
  margin-top: 12px;
}
.cancel-btn {
  margin: 5px auto 0;
  display: block;
  border: none;
  background: none;
  padding: 0;
  height: 19px;
  font-size: 13px;
  color: #b7b1a8;
}
</style>
