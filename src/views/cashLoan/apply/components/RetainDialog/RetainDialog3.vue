<template>
  <van-popup
    :show="isRevealed"
    :close-on-click-overlay="false"
    teleport="body"
    class="dialog"
    @close="cancel()"
  >
    <!-- <div class="">5465456</div> -->
    <img src="@/assets/images/cash-loan/retain-dialog/dialog-3-title.png" class="header" />
    <div class="dialog-body">
      <img src="@/assets/images/cash-loan/retain-dialog/dialog-3-badge.png" class="badge" />
      <button class="close-btn" @click="cancel('close')">
        <van-icon name="close" />
      </button>
      <div class="banner">
        <div class="title">可获得入会礼</div>
        <div class="subtitle">（以下商品可六选一）</div>
      </div>
      <div class="gift-wrapper">
        <ul class="gift-list">
          <li v-for="item in giftList">
            <!-- <div class="gift-badge">会员免费</div> -->
            <img class="img" :src="item.imgUrl" />
            <div class="name">{{ item.name }}</div>
            <div class="price">原价¥{{ formatMoney(item.salePrice) }}</div>
            <van-button class="btn" block @click="confirm(item)">选择TA</van-button>
          </li>
        </ul>
      </div>
      <div class="confirm-btn-wrapper">
        <van-button class="confirm-btn style-1" round @click="confirm()">开通稍后领取</van-button>
        <van-button class="confirm-btn style-2" round @click="confirm(sample(giftList))"
          >开通随机领取</van-button
        >
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { until, useAsyncState, useConfirmDialog, useToggle, whenever } from '@vueuse/core'
import { memberBenefitList } from '@/api/member.js'
import { computed } from 'vue'
const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
import sample from 'lodash/sample'

// const giftList = ref(Array(10))

const vipPrice = inject('vipPrice')

const { state, execute: getGiftList } = useAsyncState((id) => memberBenefitList({ id }), null, {
  immediate: false,
})

const giftList = computed(
  () =>
    state.value?.data?.find(({ packageType }) => packageType === 'LOAN_GIVE')?.vipBenefitGoodsInfo
)

// whenever(
//   () => vipPrice.value?.id,
//   async (id) => {
//     await getGiftList(0, id)
//   },
// )

defineExpose({
  reveal,
})

onReveal(async () => {
  const id = await until(() => vipPrice.value?.id).toBeTruthy()
  await getGiftList(0, id)
})

// function randomConfirm() {
//   const item = sample(giftList.value)
//   confirm({
//     type: 'checked',
//     data: item,
//   })
// }

// onMounted(async () => {
//   const id = await until(() => vipPrice.value?.id).toBeTruthy()
//   // console.log(id)
//   await getGiftList(0, id)
// })
</script>

<style scoped lang="scss">
.dialog {
  background: transparent;
}

.header {
  width: 262px;
  height: 68px;
  display: block;
  margin: 0 auto;
}

.dialog-body {
  margin-top: 20px;
  width: 285px;
  // height: 333px;
  overflow: hidden;
  border-radius: 24px;
  background: linear-gradient(169deg, #f2ddc9 0%, #ffffff 100%);
  // box-shadow: 0px 0 22px 0px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 0;
  padding: 31px 15px 12px;
  box-sizing: border-box;
  .badge {
    width: 125px;
    height: 38px;
    position: absolute;
    top: 0;
    left: 0;
  }
  .close-btn {
    position: absolute;
    top: 9px;
    right: 15px;
    width: 20px;
    height: 20px;
    padding: 0;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d0b8a2;
    font-size: 20px;
    .van-icon {
      display: block;
      font-size: 20px;
    }
  }
  .banner {
    // width: 100%;
    height: 91px;
    background-image: url('@/assets/images/cash-loan/retain-dialog/dialog-3-banner.png');
    background-size: 100% 100%;
    overflow: hidden;
    .title {
      font-size: 15px;
      color: #d1783c;
      line-height: 21px;
      font-weight: 600;
      margin-top: 30px;
      margin-left: 14px;
    }
    .subtitle {
      font-size: 13px;
      color: rgba(209, 120, 60, 0.3);
      line-height: 19px;
      font-weight: 600;
      margin-left: 6px;
    }
  }
}
.gift-wrapper {
  overflow-x: scroll;
  margin: 9px -15px 0;
  padding: 0 15px;
}
.gift-list {
  display: flex;
  gap: 5px;
  width: fit-content;
  height: 140px;
  li {
    width: 79px;
    height: 140px;
    border: 1px solid #ecc693;
    background: #f8fafb;
    border-radius: 4px;
    flex: none;
    position: relative;
    overflow: hidden;
    padding: 4px;
    box-sizing: border-box;
    .gift-badge {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 10px;
      color: #ffffff;
      line-height: 15px;
      font-weight: 400;
      background: linear-gradient(180deg, #432811 0%, #26221f 100%);
      border-radius: 4px 22px 22px 0px;
      padding: 2px 6px 3px 4px;
    }
    .img {
      width: 70px;
      height: 70px;
      border-radius: 4px;
      display: block;
      background: gray;
      // margin: 4px auto 0;
    }
    .name {
      font-weight: 400;
      font-size: 11px;
      color: #333333;
      line-height: 16px;
      // text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .price {
      font-weight: 400;
      font-size: 11px;
      color: #999999;
      line-height: 20px;
      // text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .btn {
      width: 68px;
      height: 20px;
      background: linear-gradient(180deg, #ff9b38 0%, #fe4218 100%);
      border-radius: 26px;
      font-weight: 400;
      font-size: 13px;
      color: #ffffff;
      line-height: 19px;
      padding: 0;
      border: none;
    }
  }
}
.confirm-btn-wrapper {
  margin-top: 11px;
  display: grid;
  gap: 7px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  .confirm-btn {
    height: 40px;
    &.style-1 {
      color: #432811;
      border: 1px solid currentColor;
    }
    &.style-2 {
      color: #f4e1d0;
      background: linear-gradient(180deg, #432811 0%, #26221f 100%);
      border: none;
    }
  }
}
</style>
