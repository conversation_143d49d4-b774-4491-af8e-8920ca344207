<template>
  <van-popup
    :show="isRevealed"
    :close-on-click-overlay="false"
    teleport="body"
    class="dialog"
    position="bottom"
    @close="cancel()"
  >
    <button class="close-btn"><van-icon name="cross" @click="cancel('close')" /></button>
    <div class="dialog-body">
      <van-button class="confirm-btn" block round @click="confirm({})"
        >继续享用轻享花如意卡</van-button
      >
      <van-button class="cancel-btn" round block @click="cancel('cancel')"
        >取消轻享花如意卡开通</van-button
      >
    </div>
    <div :class="{ 'iphonex-bottom': isIphoneX }" class="safe-area-inset-bottom"></div>
  </van-popup>
</template>

<script setup lang="ts">
import { useConfirmDialog, useToggle } from '@vueuse/core'
const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
const isIphoneX = window.isIphoneX

defineExpose({
  reveal,
})
</script>

<style scoped lang="scss">
.dialog {
  background: transparent;

  // padding-top: 20px;
}
.dialog-body {
  padding: 445px 27px 0;
  // padding-bottom: env(safe-area-inset-bottom, 0);
  background-image: url('@/assets/images/cash-loan/retain-dialog/dialog-4-bg.png');
  background-repeat: no-repeat;
  // background-color: #fff;
  height: 535px;
  // height: calc(535px + env(safe-area-inset-bottom, 0));
  background-size: 100% auto;
  box-sizing: border-box;
  // border: 1px solid black;
}
.safe-area-inset-bottom {
  background: #fff;
  margin-top: -1px;
  &.iphonex-bottom {
    // padding-bottom: 20px;
    padding-bottom: var(--safe-area-inset-bottom);
  }
}
.close-btn {
  padding: 0;
  border: none;
  background: transparent;
  color: #d0b8a2;
  font-size: 16px;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 178px;
  right: 26px;
  .van-icon {
    font-size: 16px;
    font-weight: bold;
  }
}
.confirm-btn {
  background: linear-gradient(180deg, #432811 0%, #26221f 100%);
  border: none;
  font-weight: 600;
  font-size: 17px;
  color: #ffffff;
  line-height: 24px;
  height: 43px;
}
.cancel-btn {
  background: transparent;
  padding: 0;
  margin: 8px auto 0;
  border: none;
  font-weight: 400;
  font-size: 13px;
  color: #b7b1a8;
  line-height: 19px;
  height: 19px;
}
</style>
