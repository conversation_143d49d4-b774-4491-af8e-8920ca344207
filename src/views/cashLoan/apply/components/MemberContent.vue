<template>
  <div class="member-wrapper" v-if="memberInfo.vipPrice">
    <div class="member-info">
      <div class="title">
        <img class="logo" src="@/assets/images/cash-loan/jixiangruyi.png">
        <div class="title-text">会员专享权益</div>
        <div class="title-right">
          <div class="privilege" v-if="!checked">
            <div class="tag">
              我要特权
            </div>
          </div>
          <van-switch class="switch" v-model="checked"/>
        </div>
      </div>
      <div class="card">
        <div class="lefttop">开通预计可达最高95%的推荐率</div>
        <div class="tips1">开通{{ memberInfo.vipPrice.vipName }}{{ memberInfo.vipPrice.discountPrice }}元，享<span class="highlight">优先推荐</span>特权</div>
        <div class="tips2">开卡费延后收取，先享后付</div>
        <div class="label">
          <div class="label-l">更多专属特权</div>
          <!-- <div class="label-r">{{ memberInfo.vipPrice.title }}</div> -->
        </div>
        <div class="benefit-list">
          <div class="benefit-list-item">
            <img src="@/assets/images/cash-loan/benefit-1.png" />
            <div class="text">专属通道</div>
          </div>
          <div class="benefit-list-item">
            <img src="@/assets/images/cash-loan/benefit-2.png" />
            <div class="text">尊享礼品</div>
          </div>
          <div class="benefit-list-item">
            <img src="@/assets/images/cash-loan/benefit-3.png" />
            <div class="text">无门槛券</div>
          </div>
          <div class="benefit-list-item">
            <img src="@/assets/images/cash-loan/benefit-4.png" />
            <div class="text">生活会员</div>
          </div>
          <div class="benefit-list-item">
            <img src="@/assets/images/cash-loan/benefit-5.png" />
            <div class="text">尝鲜专区</div>
          </div>
        </div>
      </div>
    </div>
    <div class="agreement">
      <van-checkbox v-model="checked" icon-size="16" :checked-color="$global.THMEM"></van-checkbox>
      <div class="text" @click="emit('showMemberAgreement')">
        <span>本人已充分阅读并同意</span>
        <span class="highlight">《权益服务协议》</span>
        <span>中的所有内容，并明确知悉：本平台的分期服务由持有金融牌照的专业机构提供，平台只提供信息展示及推荐，不对最终结果做出承诺。会员与您所申请的该服务并不挂钩，请您谨慎选择。</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter()
const memberSwitch = ref(true)
const checked = ref(false)
const props = defineProps({
  memberInfo: {
    type: Object,
    default: () => {}
  },
  modelValue: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['update:modelValue','showMemberAgreement'])
const handleChange = (val) => {
  emit('update:modelValue', val)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .member-info{
    background: #fff;
    border-radius: 8px;
    padding: 10px;
    margin: 12px 10px;
    padding-bottom: 14px;
    position: relative;
    .title {
      display: flex;
      align-items: center;

      .logo{
        width: 20px;
        height: 21px;
        display: block;
        margin-right: 3px;
      }
      .title-text {
        font-family: Roboto;
        font-size: 16px;
        color: #000;
      }
      .title-right {
        margin-left: auto;
        display: flex;
        align-items: center;

        .switch {
          --van-switch-node-size: 19px;
          --van-switch-width: 47px;
          --van-switch-height: 23px;
        }
        .privilege{
          display: flex;
          align-items: center;
          margin-right: 8px;
          .tag{
            background: #363636CC;
            border-radius: 4px;
            font-size: 13px;
            padding: 4px 8px;
            color: #ffffff;
            margin-left: 8px;
            position: relative;
          }
          .tag::before{
            content: "";
            position: absolute;
            right: -14px;
            top: 7px;
            width: 6px;
            height: 0;
            border: 5px solid transparent;
            border-left-color:#363636CC;
          }
          .text{
            font-size: 12px;
            zoom: 0.85;
            margin-left: 6px;
          }
        }
      }
    }
    .card{
      margin-top: 10px;
      padding: 10px;
      background: #FEF9F5;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
      .lefttop {
        position: absolute;
        left: 0;
        top: 0;
        width: 204px;
        height: 24px;
        line-height: 24px;
        background: linear-gradient(258.11deg, #F6CFB8 16.52%, #DD9A74 68.59%);
        border-radius: 0 0 10px 0;
        font-size: 13px;
        color: #fff;
        text-align: center;
      }
      .text-valid{
        font-size: 13px;
        color: #666666;
      }
      .tips1{
        margin-top: 24px;
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
        .highlight {
          color: #FF3927;
        }
      }
      .tips2{
        color: #999;
        font-size: 15px;
        transform-origin: left center;
        font-weight: 500;
        line-height: 13px;
        margin-top: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
      }
    }
    .title-tips{
      position: absolute;
      right: -1px;
      top: -2px;
      height: 30px;
      font-weight: bold;
      font-size: 12px;
      transform: scale(0.9);
      transform-origin: right center;
      color: #5B2C11;
      line-height: 30px;
      background: linear-gradient( 90deg, #FBE3D0 0%, #D2A082 100%);
      padding: 0 5px 0 13px;
      border-radius: 6px 6px 0 0;
    }
    // 向内斜角
    .title-tips:before{
      content: '';
      position: absolute;
      top: 0;
      left: -1px;
      width: 0;
      height: 0;
      border-top: 30px solid transparent;
      border-left: 10px solid #ffffff;
    }
    .label{
      padding: 0 20px;
      margin: 10px auto;
      text-align: center;
      width: 173px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      background: linear-gradient(89.99deg, rgba(244, 63, 47, 0) 0.97%, rgba(244, 63, 47, 0.4) 51.11%, rgba(255, 95, 108, 0) 99.99%);
      &-l{
        font-size: 14px;
        color: #fff;
      }
      &-r{
        font-size: 12px;
        color: #FB8942;
        line-height: 17px;
      }
    }
    // .benefit.list4{
    //   .benefit-list-item{
    //     flex-basis: 25%;
    //   }
    // }
    // .benefit.list3{
    //   .benefit-list-item{
    //     flex-basis: 33%;
    //   }
    // }
    .benefit-list {
      margin-top: 17px;
      display: flex;
      flex-flow: row wrap;
      &-item {
        display: flex;
        flex-direction: column;
        flex-basis: 20%;
        align-items: center;
        img{
          width: 40px;
          height: 40px;
          display: block;
        }
        .text{
          font-size: 12px;
          color: #666666;
          line-height: 17px;
          margin-top: 6px;
        }
      }
    }
  }
  .agreement{
    margin: 10px 16px;
    .van-checkbox{
      flex-shrink: 0;
      align-items:flex-start;
    }
    display: flex;
    .text{
      font-size: 12px;
      transform-origin: left center;
      color: rgba(0,0,0,30%);
      margin-left: 5px;
      line-height: 15px;
      text-align: justify;
    }
    .highlight {
      color: #F43C2C;
    }
  }
</style>