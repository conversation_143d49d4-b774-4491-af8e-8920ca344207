<template>
  <van-popup
    :show="isRevealed"
    :close-on-click-overlay="false"
    teleport="body"
    class="dialog"
    @close="cancel()"
  >
    <div class="dialog-body">
      <div class="confirm-btn" @click="handleConfirm()">
        <strong>0元激活推荐特权</strong>
        <span>开卡费延后收取</span>
      </div>
      <div class="cancel-btn" @click="cancel('cancel')">我再想想</div>
      <!-- <div class="header">
        <div class="title"> 
          <img src="@/assets/images/cash-loan/retain-dialog/dialog-1-title.png" />
        </div>
        <div class="subtitle">
          <span>开通{{ data.vipPrice.vipName }}{{ data.vipPrice.discountPrice }}元，</span
          ><strong>享优先推荐特权</strong>
        </div>
      </div>
      <div class="main">
        <img src="@/assets/images/cash-loan/retain-dialog/dialog-1-main.png" />
      </div>
      <div class="footer">
        <div class="confirm-btn-wrapper">
          <van-button class="confirm-btn" round block @click="handleConfirm()"
            >先享后付，0元激活推荐特权</van-button
          >
          <div class="help">开卡费延后收取<span v-if="agreementIsOpen">，先享后付</span></div>
        </div>
        <div
          class="agreement"
          :class="{
            'is-open': agreementIsOpen,
          }"
        >
          <van-checkbox v-model="agreementIsChecked" checked-color="#4671eb" icon-size="1em" />
          <div class="content">
            本人已充分阅读并同意<a @click.prevent="showMemberAgreement()">《权益服务协议》</a
            >中的所有内容，充分知晓:“先享后付”服务将于开通后7日内进行银行扣款。本平台的分期服务由持有金融牌照的专业机构提供，平台仅为该服务提供展示及推荐，不对最终审批放款结果负责。会员与您所申请的该服务无直接关联，请您谨慎选择。
          </div>
        </div>
        <div class="toggle-agreement-is-open-btn-wrapper">
          <button @click="toggleAgreementIsOpen()">
            <template v-if="agreementIsOpen">收起<van-icon name="arrow-up" /></template>
            <template v-else>查看全部<van-icon name="arrow-down" /></template>
          </button>
        </div>
      </div> -->
    </div>

    <!-- <button class="close-btn" @click="cancel('cancel')">
      <van-icon name="close" />
    </button> -->
  </van-popup>
</template>

<script setup lang="ts">
import { useConfirmDialog, useToggle } from '@vueuse/core'
import { showToast } from 'vant'

const props = defineProps<{
  data: Record<string, any>
}>()

const emit = defineEmits<{
  showMemberAgreement: []
}>()

function showMemberAgreement() {
  emit('showMemberAgreement')
}

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  reveal,
})

const [agreementIsChecked, toggleAgreementIsChecked] = useToggle(true)
const [agreementIsOpen, toggleAgreementIsOpen] = useToggle(false)

function handleConfirm() {
  if (!agreementIsChecked.value) {
    showToast('请阅读并同意协议')
    return
  }
  confirm()
}

onReveal(() => {
  toggleAgreementIsChecked(true)
})
</script>

<style scoped lang="scss">
.dialog {
  overflow: visible;
  background: transparent;
}
.dialog-body {
  z-index: 0;
  position: relative;
  width: 286px;
  height: 345px;
  background-image: url('@/assets/images/cash-loan/retain-dialog/dialog-1-bg.png');
  background-size: contain;
  font-size: 14px;
  overflow: hidden;
  .confirm-btn {
    width: 263px;
    height: 45px;
    border-radius: 25px;
    background-color: #5881fa;
    margin: 269px auto 0;
    padding-top: 2px;
    box-sizing: border-box;
    strong {
      display: block;
      text-align: center;
      font-size: 15px;
      color: #fff;
      line-height: 21px;
    }
    span {
      display: block;
      text-align: center;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.5);
      line-height: 18px;
    }
  }
  .cancel-btn {
    position: absolute;
    width: 100%;
    bottom: 11px;
    text-align: center;
    color: #d9d9d9;
  }
  // .header {
  //   padding: 20px 20px 0;
  //   .title {
  //     img {
  //       width: 242px;
  //       height: 24px;
  //     }
  //   }
  //   .subtitle {
  //     margin-top: 7px;
  //     // margin: 7px 20px 0;
  //     font-size: 14px;
  //     color: rgba(0, 0, 0, 0.3);
  //     font-weight: 600;
  //     strong {
  //       color: rgba(252, 80, 22, 1);
  //     }
  //   }
  // }
  // .main {
  //   z-index: 0;
  //   position: relative;
  //   padding: 7px 20px 0;
  //   // margin-top: 7px;
  //   img {
  //     width: 100%;
  //     //   width: 275px;
  //     //   height: 188px;
  //   }
  // }
  // .footer {
  //   z-index: 1;
  //   position: relative;
  //   margin-top: -188px + 72px;
  //   padding: 20px;
  //   // background: linear-gradient(123deg, #fff7e9 0%, #ffd6aa 100%);
  //   background-image: url('@/assets/images/cash-loan/retain-dialog/dialog-1-footer-bg.png');
  //   background-size: 100% auto;
  //   background-position: 0 0;
  //   // border-radius: 0 0 40px 40px;

  //   .confirm-btn-wrapper {
  //     position: relative;
  //     margin-top: 35px;
  //     .help {
  //       position: absolute;
  //       // top: 0;
  //       right: 0;
  //       bottom: 100%;
  //       border-radius: 999vw;
  //       padding: 1px 20px 10px;
  //       color: #fb6110;
  //       border: 1px solid currentColor;
  //       font-size: 13px;
  //       border-image: url('@/assets/images/cash-loan/retain-dialog/dialog-confirm-btn-help-bg.png')
  //         20 82 34 22 fill;
  //       border-image-width: 10px 41px 17px 11px;
  //       // border-image-repeat: stretch;
  //       // border-width: 10px 10px;
  //       // border-style: solid;

  //       // border-image-width: 128 29;
  //       // b
  //     }
  //     .confirm-btn {
  //       background: linear-gradient(180deg, #fd8436 0%, #fb4d19 100%);
  //       font-size: 17px;
  //       color: #fff;
  //       border: none;
  //     }
  //   }
  //   .agreement {
  //     font-size: 12px;
  //     color: rgba(0, 0, 0, 0.3);
  //     line-height: 17px;
  //     margin-top: 6px;
  //     display: flex;
  //     align-items: start;
  //     .content {
  //       height: 17px;
  //       overflow: hidden;
  //       text-overflow: ellipsis;
  //       white-space: nowrap;
  //       a {
  //         border: none;
  //         background: none;
  //         padding: 0;
  //         color: rgba(0, 0, 0, 0.6);
  //       }
  //     }
  //     &.is-open {
  //       .content {
  //         white-space: break-spaces;
  //         height: auto;
  //         text-align: justify;
  //       }
  //     }
  //     .van-checkbox {
  //       // display: inline-flex;
  //       flex: none;
  //       font-size: 16px;
  //       margin-right: 3px;
  //     }
  //   }
  //   .toggle-agreement-is-open-btn-wrapper {
  //     text-align: center;
  //     margin-top: 3px;
  //     button {
  //       border: none;
  //       background: none;
  //       padding: 0;
  //       color: rgba(0, 0, 0, 0.3);
  //       font-size: 12px;
  //     }
  //   }
  // }
}
// .close-btn {
//   position: absolute;
//   left: 50%;
//   top: calc(100% + 15px);
//   transform: translateX(-50%);
//   border: none;
//   background: none;
//   color: white;
//   padding: 0;
//   font-size: 26px;
// }
</style>
