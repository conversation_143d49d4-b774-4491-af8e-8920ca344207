<template>
  <div class="borrow-money-amount">
    <div class="borrow-money-amount-title">借多少</div>
    <div class="amount-list">
        <div
            v-for="(item, index) in quotaData.quotaList"
            :class="`amount-list-item ${modelValue === item ? 'active' : ''}`"
            @click="checkAmount(index)"
            :key="index"
        >
            ￥{{ item }}
        </div>
        <div 
            :class="`amount-list-item ${modelValue === 50000 ? 'active' : ''}`"
            @click="disabledAmount(50000)"
        >
            ￥{{ 50000 }}
        </div>
    </div>
    <limit-popup ref="limitPopupRef"></limit-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import LimitPopup from './LimitPopup';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    modelValue:{
        type: Number,
        default: 0
    },
    quotaData: {
        type: Object,
        default: () => {}
    }
})
const limitPopupRef = ref(null)
const emit = defineEmits(['updateAmount'])
const checkAmount = (val) => {
    emit('updateAmount', val)
}
const disabledAmount = () => {
    limitPopupRef.value.show = true
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
.borrow-money-amount {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 27px 20px 15px;
    margin: 12px 10px;
    position: relative;;
    &-title {
        font-weight: 500;
        font-size: 14px;
        color: #000000;
        line-height: 20px;
    }
    .amount-list{
        display: flex;
        justify-content: space-between;
        flex-flow:row wrap;
        margin-top: 16px;
        &-item{
            height: 48px;
            width: 144px;
            background: #F5F6F7;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            overflow:hidden;
            margin-bottom: 15px;
        }
        &-item.active{
            background: #FFF1E8;
            color: #FF671A;
            position: relative;
        }
        /* 三角形 */
        &-item.active::after {
        content: "";
        position: absolute;
        bottom: 0px;
        right: 0px;
        border-bottom: 18px solid #FF671A;
        border-left: 18px solid transparent;
        }
    
        /* 三角形勾 */
        &-item.active::before {
        content: '';
        position: absolute;
        width: 7px;
        height: 4px;
        background: transparent;
        bottom: 5px;
        right: 2px;
        border: 1px solid white;
        border-top: none;
        border-right: none;
        transform: rotate(-55deg);
        z-index: 9;
        }
        .mr-10{
            margin-right: 10px;
        }
    }
    }
</style>