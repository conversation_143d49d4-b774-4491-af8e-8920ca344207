<template>
  <van-popup
    :show="isRevealed"
    :close-on-click-overlay="false"
    teleport="body"
    class="dialog"
    @close="cancel()"
  >
    <van-button class="confirm-btn" round block @click="confirm()">我知道啦</van-button>
    <div class="content">
      更多请查看<a @click.prevent="handleOrder()">权益服务订单</a>，海量权益等您使用
    </div>
    <!-- <button class="close-btn" @click="cancel()">
      <van-icon name="close" />
    </button> -->
  </van-popup>
</template>

<script setup lang="ts">
import { useConfirmDialog } from '@vueuse/core'
const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()

defineExpose({
  reveal,
})
const router = useRouter()

function handleOrder() {
  cancel()
  router.push('/member/equity-order')
}
</script>

<style scoped lang="scss">
.dialog {
  width: 266px;
  height: 298px;
  background-image: url('@/assets/images/cash-loan/retain-dialog/open-success-bg.png');
  background-size: 100% 100%;
  background-color: transparent;
  overflow: visible;
}
.confirm-btn {
  border: none;
  //   width: 248px;
  //   width: max-content;
  margin: 219px auto 0;
  width: 230px;
  height: 38px;
  background: #5881FA;
  // font-weight: 600;
  font-size: 17px;
  color: #ffffff;
  line-height: 21px;
}
.content {
  margin: 7px auto 0;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  line-height: 17px;
  text-align: center;
  a {
    color: #5881FA;
  }
}

.close-btn {
  position: absolute;
  left: 50%;
  top: calc(100% + 15px);
  transform: translateX(-50%);
  border: none;
  background: none;
  color: white;
  padding: 0;
  font-size: 40px;
}
</style>
