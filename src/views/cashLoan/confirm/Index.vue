<template>
  <scroll-nav :pageName="quotaData.productName" @onLeftClick="onBackClick">
    <template #nav-right>
      <div class="kefu-button" @click="toMessageNofiy">
        <img src="@/assets/images/home/<USER>" />
      </div>
    </template>
    <div class="detail-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="trial-info">
        <confirm-amount
          :amount="amount"
          :maxAmount="maxAmount"
          :quotaList="quotaData.quotaList"
          @changed="onAmountChanged"
        />
        <borrow-periods
          :checked-period="checkedPeriod"
          :trial-data="trialData"
          @changePeriod="changePeriod"
        />
        <div class="trial-data hcsp55 solid-bottom" v-if="checkedPeriod.productResponse">
          <div class="trial-l">怎么还</div>
          <div class="trial-r" @click="handleRepaymentPlan">
            每期应还{{ formatMoney(checkedPeriod.productResponse.monthlyPayment) }}
            <img class="arrow-icon" src="@/assets/icons/icon-arrow.png" />
          </div>
        </div>
        <div class="bank-account hcsp55">
          <div class="trial-l">收款账户</div>
          <div class="trial-r bank-wrap">
            <div v-if="checkedBank.bankName" class="bank" @click="onBankCard">
              <div :class="`bank-logo ${bankCode(checkedBank.bankName)}`"></div>
              <span
                >{{ checkedBank.bankName }}({{ stringBankAccount(checkedBank.cardNoShort) }})
              </span>
            </div>
            <span v-else @click="onBankCard" class="text-gray">请添加银行卡</span>
            <img class="arrow-icon" src="@/assets/icons/icon-arrow.png" />
          </div>
        </div>
      </div>
      <trial-detail
        :form="form"
        :amount="amount"
        :checked-period="checkedPeriod"
        :checked-bank="checkedBank"
        @loanUsageChanged="onLoanUsageChanged"
      ></trial-detail>
    </div>
    <div :class="`agreement-wrapper solid-top`">
      <!-- <div class="tips">请仔细阅读借款协议</div> -->
      <div class="agreement">
        <van-checkbox
          v-model="checked"
          icon-size="16"
          :checked-color="$global.THMEM"
        ></van-checkbox>
        <span class="grey">我已阅读并同意签署</span
        ><span style="font-size: 12px; color: #4671eb" @click="showAgreementList = true"
          >《贷款分期相关协议》</span
        >
      </div>
      <div class="agreement-btn" @click="onSubmit">确认借款</div>
    </div>
    <agreement-component ref="AgreementRef" v-model="showAgreementList" />
    <bank-card
      ref="bankCardRef"
      :list="bankCards"
      v-model="checkedBank"
      :channel-id="'' + quotaData.bankcardChnlId"
      :productId="'' + quotaData.productId"
      :checked-period="checkedPeriod"
      @handleAddBank="handleAddBank"
    ></bank-card>
    <repayment-plan
      ref="repaymentPlanRef"
      :checked-period="checkedPeriod"
      :trial-data="trialData"
      @changePeriod="changePeriod"
      v-model="amount"
    ></repayment-plan>
    <sms-valid ref="smsValidRef" @validSucc="validSucc"></sms-valid>
    <!-- 会员卡 -->
    <!-- <not-bank-tips
      ref="notBankTipsRef"
      @add-bank="memberAddBankRef.show=true"
      title="您未绑定开通会员权益的扣款卡"
    ></not-bank-tips> -->
    <!-- <member-add-bank ref="memberAddBankRef" :bind-params="payInfo" @bindSuccess="bindSuccess" /> -->
    <!-- 用信卡 -->
    <member-add-bank
      ref="memberAddBankRef"
      :bind-params="{ bankChannelId: quotaData.bankcardChnlId, cmId: quotaData.cmId }"
      bind-type="cash"
      @bindSuccess="bindSuccess"
    />
    <!-- 开卡礼相关 -->
    <address-supplement ref="addrSuppleRef" @addRsult="addRsult" />
    <address-list
      :list="addressData"
      @updateAddr="updateAddr"
      @updateList="getAddressList"
      @addrAdd="addrSuppleRef.show = true"
      @onConfirm="onSubmit"
      ref="addressListRef"
    />
    <!-- <member-open-gift
      :check-goods="checkGoods"
      @onCancel="cancelFlag = true"
      @onAddress="addressListRef.show = true"
      @onCheckGoods="goodsCheckRef.show = true"
      ref="memberOpenGiftRef"
    /> -->
    <!-- <goods-check
      ref="goodsCheckRef"
      :check-goods="checkGoods"
      :list="benefitData.vipBenefitGoodsInfo"
      @updateCheckGoods="updateCheckGoods"
      @onConfirm="onConfirmGoods"
      @onCancel="cancelFlag = true"
    /> -->
    <loan-give-dialog :benefitList="[benefitData]" ref="loanGiveDialog"></loan-give-dialog>

    <customer-service ref="customerServiceRef" />
  </scroll-nav>
</template>

<script setup name="CashLoanConfirm">
import ScrollNav from '@/components/ScrollNavV2'
import CustomerService from '@/components/CustomerService'
import { trial, saveForm } from '@/api/borrow-money'
import { bankCardList, isNeedBindCard } from '@/api/member-bank'
import { getBankListGroup } from '@/api/bankcard'
import { memberBenefitInfo } from '@/api/member'
import { listAddress } from '@/api/address'
import { creditQuota } from '@/api/customer'
import { bankCode } from '@/constant/bank'
import { saveGoodsOrder } from '@/api/goods-order'
import {
  RepaymentPlan,
  SmsValid,
  BankCard,
  AddressSupplement,
  AddressList,
  AgreementComponent,
  ConfirmAmount,
  BorrowPeriods,
  TrialDetail,
  GoodsCheck,
  MemberOpenGift,
} from './components/index'
import NotBankTips from '@/views/member/components/memberBank/NotBankTips'
import MemberAddBank from '@/views/member/components/memberBank/AddBank'
import { useAcceleratorCard } from '@/hooks/useAcceleratorCard'
import { showToast, closeToast, showLoadingToast } from 'vant'
import goodsCheckStore from '@/store/goodsCheck.js'
import { computed, useTemplateRef } from 'vue'
import LoanGiveDialog from '@/views/member/components/LoanGiveDialog.vue'

const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const isIphoneX = window.isIphoneX
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const bankCards = ref([])
const checked = ref(true)
const bankCardRef = ref(null)
const repaymentPlanRef = ref(null)
const showAgreementList = ref(false)
const smsValidRef = ref(null)
const trialData = ref([])
const amount = ref(undefined)
const notBankTipsRef = ref(null)
const memberAddBankRef = ref(null)
const addrSuppleRef = ref(null)
const addressListRef = ref(null)
const goodsCheckRef = ref(null)
const memberOpenGiftRef = ref(null)
const addressData = ref([])
const goodsList = ref([])
const cancelFlag = ref(false)
const maxAmount = ref(0)
const data = reactive({
  form: {
    id: undefined,
    verifyCode: undefined,
    productId: undefined,
    loanType: 'CASH',
    orderPreInfo: {},
  },
  checkedBank: {},
  checkedPeriod: {},
  quotaData: {},
  isNeedCard: {},
  payInfo: {},
  benefitData: {},
  checkGoods: {},
  memberInfo: {},
  checkAddr: {},
})
// 客服组件
const customerServiceRef = ref()
const {
  form,
  checkedBank,
  checkedPeriod,
  quotaData,
  isNeedCard,
  payInfo,
  benefitData,
  checkGoods,
  memberInfo,
  checkAddr,
} = toRefs(data)
provide('checkAddr', checkAddr)

const loanGiveDialogRef = useTemplateRef('loanGiveDialog')

// 数量改变后重新计算一些东西
const afterAmountChanged = async () => {
  // 计算
  await trial({ amount: amount.value || 0, productId: quotaData.value.productId }, false).then(
    (res) => {
      trialData.value = res.data
      checkedPeriod.value = res.data[0]
      // 防止银行卡新增后期数选择重选
      const storeCheckedPeriod = localStorage.getItem('checkedPeriod')
        ? JSON.parse(localStorage.getItem('checkedPeriod'))
        : null
      if (res.data.length > 0 && storeCheckedPeriod) {
        res.data.map((item) => {
          if (
            item.productResponse.productId === storeCheckedPeriod.productResponse.productId &&
            item.productResponse.subProductId === storeCheckedPeriod.productResponse.subProductId
          ) {
            checkedPeriod.value = storeCheckedPeriod
          }
        })
      }
      form.value.orderPreInfo = checkedPeriod.value.productResponse
      form.value.orderPreInfo.loanUsage = '个人日常消费'
      form.value.orderPreInfo.amount = amount.value || 0
    }
  )
  // 用信卡
  await getBankCardList()
  if (bankCards.value.length === 0) {
    // 未绑定卡默认获取上次绑定的卡
    const oldBnakRes = await getBankListGroup()
    checkedBank.value = oldBnakRes.data[0] || {}
  }
  // 先享后付是否存在待绑卡
  // const bindRes = await isNeedBindCard({ sceneCode: 'APPLY' })
  // isNeedCard.value = bindRes.data
  // 会员信息, 是否需要选商品及地址
  await useAcceleratorCard(
    { stage: 'APPLY', productId: quotaData.value.productId, amount: amount.value || 0 },
    (data) => {
      memberInfo.value = data
    }
  )
  // memberInfo.value.receiveFlag = 'N'
  if (memberInfo.value?.receiveFlag === 'N') {
    await getAddressList()
    const benefitRes = await memberBenefitInfo({
      id: memberInfo.value.vipPriceId,
      packageType: 'LOAN_GIVE',
    })
    benefitData.value = benefitRes.data
    checkGoods.value = benefitData.value.vipBenefitGoodsInfo[0]
  }
}

onMounted(async () => {
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  // 获取金融产品信息
  const res = await creditQuota(
    { requestType: 'query', sceneCode: proxy.$global.CREDIT_SCENE_CASH },
    false
  )
  // 测试代码
  // if (import.meta.env.VITE_APP_ENV === 'development') {
  //   res.data.ultimaCash = 2500
  // }
  quotaData.value = res.data
  form.value.productId = quotaData.value.productId
  // 最大额度
  maxAmount.value = quotaData.value.ultimaCash ? quotaData.value.ultimaCash : 0
  if (route.query.amount) {
    const n = Number.parseInt(route.query.amount)
    if (!Number.isNaN(n)) {
      amount.value = n
    }
  } else {
    // 未设置数量时，使用第一个预设数量
    if (quotaData.value.quotaList.length > 0) {
      amount.value = quotaData.value.quotaList[0]
    }
  }
  await afterAmountChanged()
  closeToast() // 清除加载中
})
const updateCheckGoods = (item) => {
  checkGoods.value = item
}
// 商品下单
const onConfirmGoods = (item) => {
  if (item) {
    checkGoods.value = item
  }
  if (checkGoods.value.price > 0) {
    // 非 0 元商品
    showToast('当前商品配置错误')
    return
  }
  const msOrder = {
    shoppingFlag: 'V',
    point: 0,
    payAmount: 0,
    totalAmount: checkGoods.value.salePrice,
    packageId: benefitData.value.id,
    addressId: checkAddr.value.id,
    categoryGroup: 'GENERAL',
    msOrderSpus: {
      quantity: 1,
      skuId: checkGoods.value.skuId,
      spuId: checkGoods.value.spuId,
    },
  }
  saveGoodsOrder({ msOrder }).then((res) => {
    proxy.onToastSucc(() => {
      cancelFlag.value = true // 领取成功、取消再次领取
      goodsCheckStore.value = null // 清除预选礼物
      onSubmit()
    }, '领取成功')
  })
}
const onBackClick = () => {
  router.go(-1)
}
const onLoanUsageChanged = (val) => {
  console.log('用途改变', val)
  form.value.orderPreInfo.loanUsage = val
}
const stringBankAccount = (accountNo) => {
  if (accountNo) return accountNo.slice(accountNo.length - 4, accountNo.length)
}
const handleRepaymentPlan = () => {
  repaymentPlanRef.value.show = true
}
const changePeriod = (item) => {
  checkedPeriod.value = item
  form.value.orderPreInfo = checkedPeriod.value.productResponse
  form.value.orderPreInfo.loanUsage = '个人日常消费'
  form.value.orderPreInfo.amount = amount.value
}
// 获取地址
const getAddressList = async () => {
  const addrRes = await listAddress({ pageNum: 1, pageSize: 100 }, false)
  addressData.value = addrRes.data
  if (addressData.value?.length > 0) {
    checkAddr.value = addressData.value[0]
  }
}
// 打开银行窗口
const onBankCard = () => {
  bankCardRef.value.show = true
}
// 打开新增银行卡
const handleAddBank = () => {
  memberAddBankRef.value.defaultBankCard()
  memberAddBankRef.value.show = true
}
// 先享后付绑卡成功
const bindSuccess = async () => {
  // 更新先享后付绑卡，避免重新提交没有 (弃用，先享后付绑卡在上一个页面已处理)
  // const bindRes = await isNeedBindCard({ sceneCode: 'APPLY' })
  // isNeedCard.value = bindRes.data
  await getBankCardList()
  onSubmit()
}
// 获取短信验证码
const submitSms = () => {
  form.value.id = checkedBank.value.id
  smsValidRef.value.show = true
  smsValidRef.value.getCode()
}
// 更新地址
const updateAddr = (item) => {
  checkAddr.value = item
}
// 新增地址回调
const addRsult = async (id) => {
  getAddressList()
  if (goodsCheckStore.value) {
    // 开通前已经预选了礼物
    onConfirmGoods(goodsCheckStore.value)
    return
  }
  // goodsCheckRef.value.show = true
  const { isCanceled, data: loanGiveData } = await loanGiveDialogRef.value.reveal()
  if (isCanceled) {
    cancelFlag.value = true
    return
  }
  onConfirmGoods(loanGiveData.goodsInfo)
}
const onSubmit = async () => {
  if (!checked.value) {
    showToast('请阅读协议并同意')
    return
  }
  if (bankCards.value.length === 0) {
    // 未绑定现金卡
    memberAddBankRef.value.defaultBankCard(checkedBank.value)
    memberAddBankRef.value.show = true
    return
  }
  // if (isNeedCard.value.supplement === 'Y') { // 会员绑卡 （已在上个页面绑卡）
  //   payInfo.value.orderId = isNeedCard.value.orderId
  //   payInfo.value.bankChannelId = isNeedCard.value.chnlId
  //   notBankTipsRef.value.show = true
  //   return
  // }
  /***
   * cancelFlag=true 放弃领取
   * receiveFlag = N 待领取
   *  */
  if (!cancelFlag.value && memberInfo.value?.receiveFlag === 'N') {
    if (addressData.value.length > 0) {
      if (goodsCheckStore.value) {
        // 开通前已经预选了礼物
        onConfirmGoods(goodsCheckStore.value)
        return
      }
      // goodsCheckRef.value.show = true
      const { isCanceled, data: loanGiveData } = await loanGiveDialogRef.value.reveal()
      if (isCanceled) {
        cancelFlag.value = true
        return
      }
      onConfirmGoods(loanGiveData.goodsInfo)
      return
    } else {
      addrSuppleRef.value.show = true
      return
    }
  }
  // 发起短信验证
  submitSms()
}
// 发起借款
const validSucc = (code) => {
  form.value.verifyCode = code
  saveForm(form.value)
    .then((res) => {
      smsValidRef.value.show = false
      localStorage.removeItem('checkedPeriod') // 删除记录所选期数
      router.replace({
        path: 'cash-loan-finish',
        query: { orderId: res.data.orderId, productId: quotaData.value.productId },
      })
    })
    .catch(() => {
      smsValidRef.value.show = false
    })
}
const getBankCardList = async () => {
  // 查询银行卡列表
  const bankRes = await bankCardList({
    pageNum: 1,
    pageSize: 100,
    channelId: quotaData.value.bankcardChnlId,
    cmId: quotaData.value.cmId,
  })
  bankCards.value = bankRes.data
  // 银行卡测试外观的假数据
  // bankCards.value = [{
  //   id: '1',
  //   bankName: '工商银行',
  //   cardNoShort: '****************',
  // }, {
  //   id: '2',
  //   bankName: '招商银行',
  //   cardNoShort: '****************',
  // }, {
  //   id: '3',
  //   bankName: '农业银行',
  //   cardNoShort: '****************',
  // }]
  checkedBank.value = bankRes.data[0] || {}
}
// 跳转客服页面
const toMessageNofiy = () => {
  customerServiceRef.value.show = true
}
// 数量改变
const onAmountChanged = async (val) => {
  amount.value = val
  console.log('数量改变: ', val)
  // 更新
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  await afterAmountChanged()
  closeToast() // 清除加载中
}
</script>

<style lang="scss" scoped>
.detail-page-content {
  margin-top: -1px;
  min-height: calc(100% - 92px);
  padding-bottom: 20px;
  box-sizing: border-box;
  .trial-info {
    background: #ffffff;
    border-radius: 15px;
    margin: 12px 10px;
    margin-top: 0;
  }

  :deep(.arrow-icon) {
    width: 12px;
    height: 12px;
  }
  :deep(.hcsp55) {
    height: 55px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 15px;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    .bank-wrap {
      display: flex;
      align-items: center;
      .bank {
        display: flex;
        align-items: center;
        .bank-logo {
          width: 18px;
          height: 18px;
          margin-right: 2px;
        }
      }
    }
  }
  :deep(.hcsp55::after) {
    content: ' ';
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
  }
}
.agreement-wrapper.opacity {
  opacity: 0.6;
}
.agreement-wrapper {
  position: fixed;
  position: sticky;
  bottom: 0;
  left: 0;
  width: calc(100% - 24px);
  padding: 12px;
  padding-bottom: calc(12px + var(--safe-area-inset-bottom));
  background: #ffffff;
  .tips {
    font-size: 12px;
    transform: scale(0.9);
    transform-origin: left top;
    color: #4671eb;
    line-height: 16px;
    margin-left: 2px;
  }
  .agreement {
    margin-top: 2px;
    margin-left: 2px;
    display: flex;
    align-items: center;
    .img-icon {
      width: 15px;
      height: 15px;
      flex-shrink: 0;
    }
    .grey {
      margin-left: 2px;
      font-size: 12px;
      font-weight: 400;
      color: #a8a8a8;
    }
    .highlight {
      font-size: 12px;
    }
  }
  .agreement-btn {
    margin-top: 10px;
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    font-size: 15px;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
  }
  .agreement-btn.disabled {
    background: #d1d1d1 !important;
    color: #ffffff;
  }
}
.agreement-wrapper.iphonex {
  // padding-bottom: 64px;
  // padding-bottom: calc(12px + var(--safe-area-inset-bottom));
}

.kefu-button {
  padding: 8px;

  img {
    width: 21px;
    height: 20px;
  }
}
</style>
