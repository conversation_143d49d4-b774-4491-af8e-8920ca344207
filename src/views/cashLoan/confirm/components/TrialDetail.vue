<template>
  <div class="trial-detail">
    <div class="hcsp55">
      <div class="trial-l">借款用途</div>
      <div class="trial-r" @click="loanUsageRef.show=true">
        {{ form.orderPreInfo.loanUsage }}
        <img class="arrow-icon" src="@/assets/icons/icon-arrow.png">
      </div>
    </div>
    <div class="hcsp55">
      <div class="trial-l">借款详细</div>
      <div class="trial-r" @click="showDetail = true">
        查看确认
        <img class="arrow-icon" src="@/assets/icons/icon-arrow.png">
      </div>
    </div>
    <van-popup v-model:show="showDetail" round position="bottom" closeable>
      <div class="trial-detail-title">借钱明细</div>
      <div class="trial-detail-content">
        <div class="group">
          <div class="group-label">借款金额</div>
          <div class="group-text">¥{{formatMoney(amount)}}</div>
        </div>
        <div class="group" v-if="checkedBank">
          <div class="group-label">收款账户</div>
          <div class="group-text">
            {{ checkedBank.bankName }}({{ stringBankAccount(checkedBank.cardNoShort) }})
          </div>
        </div>
        <div class="group">
          <div class="group-label">初始年利率(单利)</div>
          <div class="group-text">{{checkedPeriod.trialResponse.yearRate*100}}%</div>
        </div>
        <div class="group">
          <div class="group-label">还款方式</div>
          <div class="group-text">{{ form.orderPreInfo.repayTypeName }}</div>
        </div>
        <div class="group">
          <div class="group-label">还款日</div>
          <div class="group-text">
            <div class="p1">
              <span v-if="checkedPeriod.productResponse.termType === 'MONTH'">每月{{ parseTime(checkedPeriod.trialResponse.repayPlan[0].dueTime, '{d}日') }}，</span>
              首次还款{{ parseTime(checkedPeriod.trialResponse.repayPlan[0].dueTime, '{m}月{d}日') }}</div>
            <div class="p2 scale">预计首期还款日，具体以放款后还款计划为准</div>
          </div>
        </div>
        <div class="group">
          <div class="group-label">总利息</div>
          <div class="group-text">
            借满{{checkedPeriod.productResponse.termTypeValue}}
            {{checkedPeriod.productResponse.termType === 'DAY' ?'天':'个月' }}，共¥{{ formatMoney(checkedPeriod.trialResponse.repayAmount - form.orderPreInfo.amount) }}</div>
        </div>
        <div class="group">
          <div class="group-label">借款人</div>
          <div class="group-text">{{ showFirstName(user.custIdCard.name) }}</div>
        </div>
        <div class="group">
          <div class="group-label">证件类型</div>
          <div class="group-text">身份证</div>
        </div>
        <div class="group">
          <div class="group-label">证件号码</div>
          <div class="group-text">{{plusXing(user.custIdCard.idcard, 1, 1) }}</div>
        </div>
      </div>
    </van-popup>
    <loan-usage ref="loanUsageRef" v-if="form.orderPreInfo" :form="form" @updateLoanUsage="updateLoanUsage" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { showFirstName, plusXing } from '@/utils/common'
import LoanUsage from './LoanUsage'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  form: {
    type: Object,
    default: () => {}
  },
  amount: {
    type: Number,
    default: 0
  },
  checkedPeriod: {
    type: Object,
    default: () => {}
  },
  checkedBank: {
    type: Object,
    default: () => {}
  },
})
const showDetail = ref(false)
const loanUsageRef = ref(null)
const emit = defineEmits(['onShowDetail', 'loanUsageChanged'])
// 更新借款用途
const updateLoanUsage = (val) => {
  emit('loanUsageChanged', val)
}
const stringBankAccount = (accountNo) => {
  if(accountNo)
  return accountNo.slice(accountNo.length-4,accountNo.length)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .trial-detail{
    background: #FFFFFF;
    border-radius: 8px;
    margin: 0 12px;
    margin-top: 0;
    margin-bottom: 63px;
    .trial-detail-title {
      font-size: 16px;
      font-weight: 600;
      color: #000;
      text-align: center;
      padding: 15px 0;
    }
    .trial-detail-content{
      padding: 0 26px 67px;
      .group{
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        &-label{
          color: #A8A8A8;
        }
        &-text{
          color: #363636;
          text-align: right;
          .p2.scale {
            margin-top: 2px;
            font-size: 12px;
            transform: scale(0.9);
            transform-origin: right top;
            color: #A8A8A8;
          }
        }
      }
    }
    .hcsp55{
      height: 55px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 15px;
      color: #363636;
      .bank-wrap{
        display: flex;
        align-items: center;
        .bank{
          display: flex;
          align-items: center;
          .bank-logo{
            width: 18px;
            height: 18px;
            margin-right: 2px;
          }
        }
      }
    }
  }

</style>