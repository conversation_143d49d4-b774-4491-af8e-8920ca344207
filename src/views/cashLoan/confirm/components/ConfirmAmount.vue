<template>
  <div class="borrow-money-amount">
    <div class="borrow-money-amount-title">可取现总额度{{ maxAmount }}元</div>
    <div class="money-wrapper">
      <span class="unit">￥</span>
      <input
        class="amount"
        v-model="inputVal"
        @change="onAmountInputChange"
        type="number"
        placeholder="最多可取40000元"
      />
    </div>
    <div class="amount-list">
      <div
        v-for="(item, index) of quotaList"
        class="amount-list-item"
        :class="{ active: inputVal === item }"
        @click="checkAmount(item)"
        :key="index"
      >
        取{{ item }}
      </div>
      <div
        class="amount-list-item"
        :class="{ active: inputVal === maxAmount }"
        @click="checkAmount(maxAmount)"
      >
        全部
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const inputVal = ref(undefined)
const emit = defineEmits(['changed'])
const props = defineProps({
  amount: {
    type: Number,
    default: undefined,
  },
  // 总额度
  maxAmount: {
    type: Number,
    default: 0,
  },
  // 年利率
  yearRate: {
    type: Number,
    default: 0,
  },
  quotaList: {
    type: Array,
    default() {
      return []
    },
  },
})
watch(
  () => props.amount,
  () => {
    console.log('数量->：', props.amount)
    inputVal.value = props.amount
  }
)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const onAmountInputChange = (event) => {
  const n = parseFloat(event.target.value)
  if (Number.isNaN(n)) {
    inputVal.value = undefined
  } else {
    inputVal.value = Math.max(0, Math.min(props.maxAmount, n))
  }
  emit('changed', inputVal.value)
}
const checkAmount = (val) => {
  if (val <= props.maxAmount) {
    inputVal.value = val
    emit('changed', val)
  } else {
    showToast('您当前可用现金额度不足' + val)
  }
}
</script>
<style scoped lang="scss">
.borrow-money-amount {
  position: relative;
  border-radius: 8px;
  padding: 21px 20px 14px 20px;

  &-title {
    font-size: 14px;
    font-weight: 400;
    color: 333;
  }

  .tips {
    margin-top: 10px;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
  }

  .money-wrapper {
    display: flex;
    align-items: center;
    padding: 15px 0 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.4);

    .unit {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-top: 4px;
    }

    .amount {
      width: calc(100% - 18px);
      font-size: 25px;
      font-weight: 600;
      color: #333;

      &::placeholder {
        color: #cfcfcf;
        font-weight: 400;
      }
    }

    img {
      width: 20px;
      height: 20px;
      display: block;
    }
  }

  .amount-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;

    .amount-list-item {
      white-space: nowrap;
      min-width: 45px;
      padding: 5px 7px;
      font-size: 14px;
      font-weight: 400;
      border: 1px solid #ddd;
      border-radius: 4px;
      text-align: center;
      &.active {
        background-color: rgba(var(--primary-color), 0.05);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }
  }
}
</style>
