<template>
    <div class="trial-periods">
        <div class="hcsp55" v-if="checkedPeriod.productResponse">
            <div class="trial-l">借多久</div>
            <div class="trial-r">
            {{checkedPeriod.productResponse.termType === 'DAY' ? (checkedPeriod.productResponse.termTypeValue+'天（1期）'): (checkedPeriod.productResponse.termTypeValue+'个月')}}
            <!-- <img v-if="periodsArrowDown" class="arrow-icon" src="@/assets/icons/icon-arrow-top.png">
            <img v-else class="arrow-icon" src="@/assets/icons/icon-arrow-down.png"> -->
            </div>
        </div>
        <div class="periods-num solid-top solid-bottom">
            <div
            v-for="(item, index) in trialData"
            :key="index"
            :class="`num-item ${ item.productResponse.termTypeValue === checkedPeriod.productResponse.termTypeValue? 'active' : ''}`"
            @click="emit('changePeriod',item)"
            >
            {{item.productResponse.termType === 'DAY' ? (item.productResponse.termTypeValue+'天'): (item.productResponse.termTypeValue+'个月')}}
                <img v-if="item.productResponse.termTypeValue === checkedPeriod.productResponse.termTypeValue" class="block-checked" src="@/assets/images/cash-loan/block-checked.png">
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
// 期数展开
const periodsArrowDown = ref(true)
const props = defineProps({
    checkedPeriod: {
        type: Object,
        default: () => {}
    },
    trialData: {
        type: Array,
        default: () => []
    }
})
const emit = defineEmits(['changePeriod'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .periods-num{
        padding: 10px 0;
        margin: 0 10px;
        display: flex;
        justify-content: flex-start;
        .num-item{
            width: 64px;
            height: 29px;
            text-align: center;
            line-height: 29px;
            color: #676767;
            font-size: 15px;
            font-weight: 400;
            background-color: #f6f6f6;
            border: 1px solid #f6f6f6;
            margin-right: 10px;
            border-radius: 4px;
            position: relative;
            img{
                width: 27px;
                height: 27px;
                position: absolute;
                right: 0;
                bottom: 0;
            }
        }
        .num-item:last-child{
            margin-right: 0;
        }
        .num-item.active{
            color: #F43C2D;
            background-color: #FEEBEA;
            border: 1px solid #F43C2D;
            img{
                width: 19px;
                height: 15px;
                position: absolute;
                right: -1px;
                bottom: -1px;
            }
        }
    }
</style>