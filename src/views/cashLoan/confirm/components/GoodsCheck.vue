<template>
  <van-popup
    v-model:show="show"
    :style="{ 'border-radius': $px2rem('8px'), 'width': $px2rem('280px') }"
    :close-on-click-overlay="false"
  >
    <div class="content">
      <img class="colse" src="@/assets/images/popup-close-gray.png" @click="handleCancel">
      <div class="title">请选择心仪的礼品</div>
      <div class="tips">
        <div class="text">您可<span class="theme-text">免费领取</span>一份会员专属礼品</div>
        <div class="badge">包邮</div>
      </div>
      <swiper
          class="swiperBox"
          :slides-per-view="1.7"
        >
        <swiper-slide v-for="(item, index) in list" :key="index">
          <div
            :class="`benefit-item ${ checkGoods?.id === item.id ? 'active' : '' }`"
            @click="handleGoods(item)"
          >
            <img v-if="checkGoods?.id === item.id" src="@/assets/images/cash-loan/goods-checked.png" class="check-img">
            <img v-else src="@/assets/images/cash-loan/goods-incheck.png" class="check-img">
            <img class="goods-img" :src="item.imgUrl">
            <div class="title overflow-1">{{ item.name }}</div>
            <div class="price">
              <div class="text">价值 ¥{{ item.salePrice }}</div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <div class="btn-wrapper">
        <div class="btn cancel" @click="handleRandom">随机发货</div>
        <div class="btn confirm" @click="handleConfirm">我选好了</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { getRandomNumber } from '@/utils/common'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const porps = defineProps({
  list: {
   type: Array,
   default: () => []
  },
  checkGoods: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['updateCheckGoods', 'onConfirm', 'onCancel'])
const handleCancel = () => {
  show.value = false
  emit('onCancel')
}
const handleRandom = () => {
  const index = getRandomNumber(0, porps.list.length)
  show.value = false
  // 发起下单
  emit('onConfirm', porps.list[index])
}
const handleGoods = (item) => {
  emit('updateCheckGoods', item)
}
const handleConfirm = () => {
  show.value = false
  emit('onConfirm')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .content{
    position: relative;
    padding-top: 20px;
    .colse{
      position: absolute;
      right: 15px;
      top: 15px;
      width: 16px;
      height: 16px;
      display: block;
    }
    .title{
      font-weight: bold;
      font-size: 18px;
      text-align: center;
    }
    .tips{
      margin-top: 8px;
      margin-left: 25px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #000000;
    }
    .swiperBox{
      padding-left: 15px;
      margin-top: 25px;
      .benefit-item.active {
        border: 2px solid #FFF9D1;
        background: linear-gradient( 90deg, #FEF8E6 0%, #FFDBAE 100%);
      }
      .benefit-item{
        width: 145px;
        border-radius: 8px;
        border: 2px solid #EDEDED;
        background: linear-gradient( 90deg, #F3F3F3 0%, #D3D3D3 100%), rgba(255,255,255,0.2);
        overflow: hidden;
        padding-bottom: 5px;
        position: relative;
        .check-img{
          position: absolute;
          width: 20px;
          height: 20px;
          display: block;
          left: 9px;
          top: 8px;
        }
        .goods-img{
          width: 135px;
          display: block;
          margin: 4px auto 0;
          border-radius: 6px;
        }
        .title{
          font-size: 12px;
          color: #8E8E8E;
          line-height: 18px;
          margin-top: 5px;
          font-weight: 400;
        }
        .price{
          position: absolute;
          top: 115px;
          left: 50%;
          transform: translate(-50%);
          border-radius: 20px;
          background: rgba(0,0,0,0.5);
          line-height: 18px;
          text-align: center;
          height: 16px;
          display: flex;
          align-items: center;
          padding: 0 7px;
          .text{
            font-size: 12px;
            zoom: 0.83;
            color: #FFFFFF;
          }
        }
      }
    }

    .btn-wrapper{
      display: flex;
      justify-content: space-between;
      margin: 15px;
      .btn{
        width: 120px;
        height: 40px;
        border-radius: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
      }
      .btn.cancel{
        color: #333333;
        border: 1px solid #F0F0F0;
        background: #FFFFFF;
      }
      .btn.confirm{
        background: #FF571A;
        border: 1px solid #FF571A;
        color: #FFFFFF;
      }
    }
  }
</style>
