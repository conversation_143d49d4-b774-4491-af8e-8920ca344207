<template>
  <van-popup
    v-model:show="show"
    class="overflow-inherit"
    :style="{ 'border-radius': $px2rem('8px'), 'width': $px2rem('280px') }"
    :close-on-click-overlay="false"
  >
  <div class="content">
    <img src="@/assets/images/cash-loan/open-gift-header.png" class="header-img">
    <div class="address-wrapper solid-bottom" @click="handleAddress">
      <img class="addr-img" src="@/assets/images/cash-loan/address-icon.png">
      <div class="address-info">
        <div class="address">{{ checkAddr.province }}{{ checkAddr.city }}{{ checkAddr.district }}{{ checkAddr.address }}</div>
        <div class="name-phone">{{ checkAddr.contactName }} {{ checkAddr.contactTelShort }}</div>
      </div>
      <van-icon name="arrow" color="#cccccc"></van-icon>
    </div>
    <div class="goods-info">
      <img class="goods-img" :src="checkGoods.imgUrl">
      <div class="title overflow-1">{{ checkGoods.name }}</div>
      <div class="price">原价：¥{{ checkGoods.salePrice }}</div>
    </div>
    <div class="tips">提交后将订单先后顺序发货</div>
    <div class="btn-wrapper">
      <div class="btn cancel" @click="handleCancel">再想想</div>
      <div class="btn confirm" @click="handleReceive">免费领取</div>
    </div>
  </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const checkAddr = inject('checkAddr')
const props = defineProps({
  checkGoods: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['onCancel', 'onAddress','onCheckGoods'])
// 取消选择
const handleCancel = () => {
  show.value = false
  emit('onCancel')
}
// 领取选择
const handleReceive = () => {
  show.value = false
  emit('onCheckGoods')
}
// 更换地址
const handleAddress = () => {
  show.value = false
  emit('onAddress')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .content{
    position: relative;
    padding-top: 33px;
    background: linear-gradient( 180deg, #FFEFC2 0%, #FFFFFF 13%, #FDFDFB 100%);
    border-radius: 8px;
    .header-img{
      width: 225px;
      height: 58px;
      position: absolute;
      left: calc(50% - 112px);
      top: -25px;
    }
    .goods-info{
      width: 98px;
      height: 144px;
      border-radius: 12px 12px 12px 12px;
      border: 1px solid #FF571A;
      margin: 20px auto 0;
      overflow: hidden;
      .goods-img{
        width: 98px;
        height: 98px;
        display: block;
      }
      .title{
        font-size: 13px;
        color: #000000;
        line-height: 18px;
        margin-top: 5px;
        text-align: center;
      }
      .price{
        font-size: 12px;
        zoom: 0.83;
        color: rgba(153,153,153,0.6);
        line-height: 18px;
        text-align: center;
      }
    }
    .tips{
      font-size: 12px;
      color: #000000;
      line-height: 14px;
      text-align: center;
      margin-top: 20px;
    }
    .address-wrapper{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      font-size: 13px;
      color: #000000;
      line-height: 18px;
      .addr-img{
        width: 16px;
        height: 16px;
        display: block;
      }
      .address-info{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 10px;
      }
    }
    .btn-wrapper{
      display: flex;
      justify-content: space-between;
      margin: 20px 15px;
      .btn{
        width: 120px;
        height: 40px;
        border-radius: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
      }
      .btn.cancel{
        color: #333333;
        border: 1px solid #f5f5f5;
        background: #FFFFFF;
      }
      .btn.confirm{
        background: #FF571A;
        border: 1px solid #FF571A;
        color: #FFFFFF;
      }
    }
  }
</style>