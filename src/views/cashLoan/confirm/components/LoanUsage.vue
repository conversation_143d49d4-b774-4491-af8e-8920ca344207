<template>
  <van-popup v-model:show="show" round position="bottom" closeable>
    <div class="loan-usage-content">
      <div class="title">请选择借款用途</div>
      <div class="decs">请选择实际资金用途，请勿用于购房、投资及各种非消费场景</div>
      <div class="loan-usage-list">
        <div
          v-for="(item, index) in $global.LOANUSAGE_OPTIONS2"
          :key="index"
          :class="`item ${item.text === form.orderPreInfo.loanUsage? 'active': ''}`"
          @click="updateLoanUsage(item.text)"
        >
          {{item.text}}
          <img v-if="item.text === form.orderPreInfo.loanUsage" class="block-checked" src="@/assets/images/cash-loan/block-checked.png">
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {}
    }
  })
  const show = ref(false)
  const emit = defineEmits(['updateLoanUsage'])
  const updateLoanUsage = (val) => {
    emit('updateLoanUsage', val)
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .loan-usage-content{
    padding-bottom: 60px;
    .title{
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #333;
      line-height: 23px;
      text-align: center;
      padding: 15px 0 6px;
    }
    .decs{
      text-align: center;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #A8A8A8;
      line-height: 17px;
    }
    .loan-usage-list{
      margin: 10px 17px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      .item{
        height: 44px;
        line-height: 44px;
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: #000;
        border-radius: 5px;
        background: #F6F6F6;
        border: 1px solid #F6F6F6;
        position: relative;
        img{
          width: 21px;
          height: 17px;
          position: absolute;
          right: -1px;
          bottom: -1px;
        }
      }
      .item.active{
        background:#F43C2D1A;
        border: 1px solid #F43C2D;
      }
    }
  }
</style>