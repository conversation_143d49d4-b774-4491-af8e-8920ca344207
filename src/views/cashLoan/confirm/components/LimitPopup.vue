<template>
  <van-popup round v-model:show="show" :close-on-click-overlay="false">
    <div class="limit-popup">
      <div class="title">提升额度说明</div>
      <div class="content">请持续使用多次备用金产品，保持良好信用正常还款即可提额50000元-12期产品</div>
      <div class="btn theme-linear-gradient" @click="onHide">我知道了</div>
    </div>
  </van-popup>
</template>

<script setup>
  const show = ref(false)
  const emit = defineEmits(['hideLimit'])
  const onHide = () => {
    show.value = false
    emit('hideLimit')
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .limit-popup{
    width: 300px;
    padding: 27px 21px;
    box-sizing: border-box;
    .title{
      font-size: 17px;
      font-weight: bold;
      color: #363636;
      text-align: center;
      margin-top: 3px;
    }
    .content{
      margin-top: 21px;
      font-size: 13px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 18px;
    }
    .btn{
      margin-top: 36px;
      height: 41px;
      border-radius: 21px;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 41px;
    }
  }
</style>