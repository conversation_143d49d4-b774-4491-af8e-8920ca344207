<template>
  <van-popup v-model:show="show" round position="bottom" closeable safe-area-inset-bottom>
    <div class="bank-card-content">
      <div class="title">选择收款账号</div>
      <div v-if="list.length > 0" class="card-list">
        <div
          v-for="(item, index) in list" :key="item.id"
          class="item solid-bottom"
          @click="handleAccount(item)"
        >
          <div class="info">
            <div :class="`bank-logo ${ bankCode(item.bankName) }`"></div>
            <div class="content">
              {{ item.bankName }}（{{ stringBankAccount(item.cardNoShort) }}）
            </div>
          </div>
          <img v-if="modelValue.id === item.id" class="checked" src="@/assets/icons/borrow-money/icon-checked.png">
        </div>
      </div>
      <div class="add" @click="toBindCardBill">
        <img src="@/assets/images/member/add.png">添加储蓄卡
        <van-icon class="add-icon" name="arrow" color="#cbcbcb" size="12"/>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
  import { bankCode } from '@/constant/bank'
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Object,
      default: () => {}
    },
    channelId: {
      type: String,
      default: ''
    },
    productId: {
      type: String,
      default: ''
    },
    checkedPeriod: {
      type: Object,
      default: () => {}
    }
  })
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const show = ref(false)
  const emit = defineEmits(['update:modelValue', 'handleAddBank'])
  // 绑定银行卡
  const toBindCardBill = () => {
    show.value = false
    emit('handleAddBank')
    //记录跳转新增前选择的期数
    // localStorage.setItem('checkedPeriod', JSON.stringify(props.checkedPeriod))
    // router.push({path: '/my/bankcard/add', 
    // query: { channelId: props.channelId, productId: props.productId }})

  }
  const stringBankAccount = (accountNo) => {
    if(accountNo)
    return accountNo.slice(accountNo.length-4,accountNo.length)
  }
  // 更新银行
  const handleAccount = (item) => {
    show.value = false
    emit('update:modelValue', item)
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .bank-card-content{
    padding-bottom: 50px;
    background: #F5F6F7;
    .title{
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #333;
      line-height: 23px;
      text-align: center;
      padding: 13px 0;
    }
    .card-list{
      margin: 0 12px;
      margin-top: 10px;
      padding: 0 13px;
      border-radius: 8px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        .info {
          display: flex;
          align-items: center;
          .bank-logo{
            width: 50px;
            height: 50px;
            margin-right: 9px;
          }
          .content {
            font-size: 15px;
            font-weight: 500;
            color: #363636;
            line-height: 21px;
            .tips{
              font-size: 13px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #A8A8A8;
              line-height: 18px;
              margin-top: 3px;
            }
          }
        }
        .checked{
          width: 17.5px;
          height: 12px;
        }
      }
    }
    .add{
      margin: 0 12px;
      padding: 0 13px;
      margin-top: 10px;
      padding-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: left;
      font-size: 15px;
      font-weight: 400;
      color: #333;
      img{
        padding: 6px;
        width: 38px;
        height: 38px;
        margin-right: 9px;
      }
      .add-icon {
        margin-left: auto;
        margin-right: 12px;
      }
    }
  }
</style>