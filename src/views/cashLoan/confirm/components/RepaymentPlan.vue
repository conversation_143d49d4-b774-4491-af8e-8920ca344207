<template>
  <van-popup position="bottom" v-model:show="show" closeable round safe-area-inset-bottom :style="{'max-height': '80%'}">
    <div class="trial-wrapper">
      <div class="header-title">怎么还</div>
      <div class="header-desc">每月随时还，提前还无手续费</div>
      <PeriodPicker :checked-period="checkedPeriod" :trial-data="trialData" @changePeriod="onChangePeriod"/>
      <!-- <van-notice-bar
        left-icon="volume-o"
        :color="$global.THMEM"
        background="#FFF1E8"
        text="预计首期还款日，具体以放款后还款计划为准"
      /> -->
      <div class="content">
        <div class="explain">应还总额（元）</div>
        <div class="repay-amount">{{ formatMoney(checkedPeriod.trialResponse.repayAmount) }}</div>
        <div class="total-interest">总利息{{ formatMoney(checkedPeriod.trialResponse.repayAmount-modelValue) }}元</div>
        <div class="bill-list">
          <div class="bill-item" v-for="(item, index) in checkedPeriod.trialResponse.repayPlan" :key="index">
            <div class="left">
              <div class="period-text">{{item.periodNo === 1 ? '首': item.periodNo}}期</div>
              <div class="date">{{ parseTime(item.dueTime, '{y}.{m}.{d}') }}</div>
            </div>
            <div class="division">
              <div class="circle"></div>
              <div v-if="checkedPeriod.trialResponse.repayPlan.length > (index+1)" class="line"></div>
            </div>
            <div class="right">
              <div class="title">{{ formatMoney(item.repayAmount) }}</div>
              <div class="repay-info">本金：{{formatMoney(item.repayPrincipal)}} + 息费：{{formatMoney(item.repayInterest)}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from 'vue';
import PeriodPicker from './PeriodPicker.vue';

const emit = defineEmits(['changePeriod'])

const props = defineProps({
  checkedPeriod: {
    type: Object,
    default: () => {}
  },
  trialData: {
      type: Array,
      default: () => []
  },
  modelValue: {
    type: Number,
    default: 0
  }
})
const show = ref(false)

const onChangePeriod = (item) => {
  emit('changePeriod', item)
}

defineExpose({ show })
</script>

<style lang="scss" scoped>
  .trial-wrapper{
    padding-bottom: 40px;
    .header-title{
      font-size: 18px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #363636;
      line-height: 25px;
      padding-bottom: 6px;
      text-align: center;
      margin-top: 15px;
    }
    .header-desc {
      font-size: 13px;
      font-weight: 400;
      text-align: center;
      color: #999;
    }
    .content{
      padding: 0 35px;
      .explain {
        font-size: 12px;
        font-weight: 500;
        color: #A8A8A8;
        line-height: 17px;
        margin-top: 14px;
      }
      .repay-amount {
        font-family: DINAlternate, DINAlternate;
        font-size: 28px;
        font-weight: 500;
        color: #363636;
        line-height: 40px;
        margin-top: 5px;
      }
      .total-interest {
        font-size: 12px;
        font-weight: 500;
        color: #A8A8A8;
        line-height: 17px;
        margin-top: 5px;
      }
      .bill-list{
        margin-top: 20px;
        .bill-item{
          display: flex;
          .left{
            width: 62px;
            .period-text{
              text-align: center;
              font-size: 16px;
              font-weight: 600;
              color: #333;
              text-align: left;
              margin-top: -4px;
            }
            .date{
              margin-top: 8px;
              font-size: 12px;
              transform: scale(0.9);
              transform-origin: left top;
              color: #A8A8A8;
            }
          }
          .division{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 20px 0 0;
            .circle{
              width: 7px;
              height: 7px;
              border-radius: 5000px;
              border: 2.5px solid #f43c2d;
            }
            .line{
              width: 2px;
              height: 41px;
              border-radius: 1px;
              background-color: rgba(0,0,0,0.1);
            }
          }
          .right{
            margin-top: -5px;
            .title{
              font-size: 17px;
              font-weight: 600;
              color: #333;
            }
            .repay-info{
              margin-top: 5px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #868686;
            }
          }
        }
      }
    }
  }
</style>