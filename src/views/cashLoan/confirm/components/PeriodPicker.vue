<script setup>
const emit = defineEmits(['changePeriod'])

const props = defineProps({
  checkedPeriod: {
      type: Object,
      default: () => {}
  },
  trialData: {
    type: Array,
    default() {
      return []
    }
  }
})

const onClickItem = (item) => {
  emit('changePeriod', item)
}
</script>

<template>
  <div class="period-picker">
    <div v-for="(item, index) in trialData" :key="index"
      :class="['period-item', (item.productResponse.termTypeValue === checkedPeriod.productResponse.termTypeValue) && 'period-item-actived']"
      @click="() => onClickItem(item)"
    >
      {{item.productResponse.termType === 'DAY' ? (item.productResponse.termTypeValue+'天'): (item.productResponse.termTypeValue+'个月')}}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.period-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 12px 35px;

  .period-item {
    width: 64px;
    height: 29px;
    line-height: 29px;
    background-color: #f6f6f6;
    border: 1px solid #f6f6f6;
    border-radius: 4px;
    color: #676767;
    font-size: 15px;
    font-weight: 400;
    text-align: center;
  }

  .period-item-actived {
    color: #F43C2D;
    background-color: #FEEBEA;
    border: 1px solid #F43C2D;
    background: url('@/assets/images/cash-loan/block-checked.png') no-repeat bottom right / 19px 15px border-box;
  }
}
</style>