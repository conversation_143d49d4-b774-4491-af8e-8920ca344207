<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    :style="{ 'height': $px2rem('500px') }"
    :close-on-click-overlay="false"
    safe-area-inset-bottom
  >
    <div class="popup-content">
      <div class="header">
        <img class="close-img" @click="show=false" src="@/assets/images/popup-close.png">
        <div class="header-title">
          <div class="p1">还差最后一步！您还有信息未完善</div>
          <div class="p2"><span class="theme-text">请准确填写地址信息</span>可用于免费领取会员礼品</div>
        </div>
      </div>
      <div class="list-wrapper">
        <div class="list" v-if="list.length > 0">
          <div class="list-item" v-for="item in list" @click="handleChecked(item)">
            <div class="item-info">
              <div class="info-name">
                {{ showFirstName(item.contactName) }}<span class="phone">{{ item.contactTelShort }}</span></div>
              <div class="info-addr">{{ item.province }}{{ item.city }}{{ item.district }}{{ item.address }}</div>
            </div>
            <div class="item-action solid-top">
              <div class="action-left">
                <img v-if="checkAddr.id === item.id" src="@/assets/images/cash-loan/checked.png">
                <img v-else src="@/assets/images/cash-loan/incheck.png">
                <div class="text" v-if="item.isDefault">默认地址</div>
              </div>
              <div class="action-right">
                <!-- <div class="action-btn">编辑</div> -->
                <div class="action-btn" @click.stop="handleRemove(item)">删除</div>
              </div>
            </div>
          </div>
        </div>
        <van-empty v-else>暂无地址</van-empty>
      </div>
      <div class="btn-wrapper">
        <div class="add btn" @click="handleAdd">新增地址</div>
        <div class="confirm btn theme-bg" @click="handleConfirm">确认选择</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { showFirstName } from '@/utils/common'
import { deleteAddress } from '@/api/address'
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})
const checkAddr = inject('checkAddr')
const emit = defineEmits(['addrAdd', 'updateList', 'updateAddr', 'onConfirm'])
const handleRemove = (item) => {
  deleteAddress({ id: item.id }).then(res => {
    proxy.onToastSucc(() => {
      emit('updateList')
    }, '删除成功！')
  })
}
const handleChecked = (item) => {
  emit('updateAddr',item)
}
const handleAdd = () => {
  show.value = false
  emit('addrAdd')
}
const handleConfirm = () => {
  show.value = false
  emit('onConfirm')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .popup-content{
    display: flex;
    flex-direction: column;
    height: 100%;
    .header{
      position: relative;
      padding: 20px;
      .close-img{
        position: absolute;
        right: 15px;
        top: 15px;
        width: 16px;
        height: 16px;
        display: block;
      }
      .p1{
        font-size: 16px;
        color: #000000;
        line-height: 18px;
        font-weight: bold;
      }
      .p2{
        font-size: 12px;
        line-height: 18px;
        margin-top: 4px;
        span{
          margin-right: 10px;
        }
      }
    }
    .list-wrapper{
      flex: 1;
      overflow-y: auto;
      background: #F5F5F5;
      padding-bottom: 10px;
      .list-item {
        background: #FFFFFF;
        border-radius: 8px;
        margin: 10px 15px 0;
        .item-info{
          padding: 10px 15px;
          .info-name, .info-addr{
            font-size: 14px;
            color: #000000;
            line-height: 18px;
            font-weight: bold;
            span{
              margin-left: 20px;
            }
          }
          .info-addr{
            font-weight: 400;
            color: #999999;
            margin-top: 4px;
          }
        }
        .item-action{
          display: flex;
          justify-content: space-between;
          height: 40px;
          align-items: center;
          padding: 0 11px;
          .action-left{
            display: flex;
            align-items: center;
            img{
              width: 20px;
              height: 20px;
              display: block;
              margin-right: 14px;
            }
            .text{
              font-size: 13px;
              font-weight: bold;
            }
          }
          .action-right{
            display: flex;
            .action-btn{
              font-size: 13px;
              font-weight: bold;
            }
            .action-btn +.action-btn{
              margin-left: 20px;
            }
          }
        }
      }
    }
    .btn-wrapper.iphonex-bottom{
      // padding-bottom: 40px;
      padding-bottom: calc(10px + var(--safe-area-inset-bottom));
    }
    .btn-wrapper{
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      .btn{
        width: 157px;
        height: 48px;
        font-size: 16px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
      }
      .add.btn{
        font-size: 16px;
        color: #AFAFAF;
        background: #E8E8E8;

      }
    }
  }
</style>