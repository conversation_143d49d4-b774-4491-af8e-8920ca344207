<template>
  <div>
    <van-popup
      v-model:show="show"
      safe-area-inset-bottom
      position="bottom"
      round
      :close-on-click-overlay="false"
    >
      <div class="popup-content">
        <img class="close-img" @click="show = false" src="@/assets/images/popup-close.png" />
        <div class="popup-title">
          <div class="p1">还差最后一步！您还有信息未完善</div>
          <div class="p2">
            <span class="theme-text">请准确填写地址信息</span>可用于免费领取会员礼品
          </div>
        </div>
        <van-form @submit="onSubmit">
          <van-field
            v-model="addressInfo.contactName"
            name="contactName"
            label="收货人"
            placeholder="请填写收货人"
            :rules="[{ required: true, message: '请填写收货人' }]"
          />
          <van-field
            v-model="addressInfo.contactTel"
            name="contactTel"
            label="手机号码"
            type="tel"
            maxlength="11"
            placeholder="请填写手机号码"
            :rules="[{ required: true, validator: validatorPhone, message: '请填写手机号码' }]"
          />
          <van-field
            v-model="addressInfo.areaData"
            is-link
            readonly
            label="所在地址"
            placeholder="点击选择省市区"
            :rules="[{ validator: validArea, message: '请选择省市区' }]"
            @click="showArea = true"
          />
          <van-field
            label="详情地址"
            v-model="addressInfo.address"
            placeholder="请补充详细地址 如：XX路/街道XX号XX小区XX栋XX"
            type="textarea"
            :rules="[{ required: true, message: '请填写详细地址' }]"
          />
          <div class="empty"></div>
          <div class="swith-warpper">
            <div class="label">
              <div class="title">设为默认地址</div>
              <div class="tips">提醒：每次下单会默认推荐使用该地址</div>
            </div>
            <van-switch v-model="checked" active-color="#FF671A" />
          </div>
          <div class="footer">
            <van-button class="addr-btn theme-bg" round block native-type="submit">保存</van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
    <van-popup v-model:show="showArea" position="bottom">
      <van-area
        v-model="addressInfo.districtCode"
        :area-list="areaList"
        @confirm="onAreaConfirm"
        @cancel="showArea = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { areaList } from '@vant/area-data'
import { validPhone } from '@/utils/common'
import { addAddress, updateAddress } from '@/api/address'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const show = ref(false)
const checked = ref(false)
const data = reactive({
  addressInfo: {},
})
const { addressInfo } = toRefs(data)
const validArea = (val) => (val ? true : false)
const validatorPhone = (val) => {
  if (!validPhone(val)) {
    return '请填写正确的手机号码'
  }
}
const emit = defineEmits(['addRsult'])
const showArea = ref(false)
const onAreaConfirm = (areaValues) => {
  addressInfo.value.areaData = areaValues.selectedOptions
    .filter((item) => !!item)
    .map((item) => item.text)
    .join('/')
  addressInfo.value.province = areaValues.selectedOptions[0].text
  addressInfo.value.city = areaValues.selectedOptions[1].text
  addressInfo.value.district = areaValues.selectedOptions[2].text
  addressInfo.value.districtCode = areaValues.selectedOptions[2].value
  showArea.value = false
}
// 提交
const onSubmit = () => {
  addressInfo.value.isDefault = checked.value ? 'Y' : 'N'
  if (addressInfo.value.id) {
    updateAddress({ address: addressInfo.value }).then((res) => {
      proxy.onToastSucc(() => {
        show.value = false
      }, '编辑成功！')
    })
  } else {
    addAddress({ address: addressInfo.value }).then((res) => {
      proxy.onToastSucc(() => {
        show.value = false
        emit('addRsult')
      }, '新增成功！')
    })
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
.popup-content {
  position: relative;
  padding: 20px 0;
  .close-img {
    position: absolute;
    right: 15px;
    top: 15px;
    width: 16px;
    height: 16px;
    display: block;
  }
  .popup-title {
    margin: 0 20px;
    .p1 {
      font-size: 16px;
      color: #000000;
      line-height: 18px;
      font-weight: bold;
    }
    .p2 {
      font-size: 12px;
      line-height: 18px;
      margin-top: 4px;
      span {
        margin-right: 10px;
      }
    }
  }
  :deep(.van-cell) {
    padding: 15px 20px;
  }
  :deep(.van-cell:after) {
    left: 0;
    right: 0;
  }
  .empty {
    height: 10px;
    background: #f5f5f5;
  }
  .swith-warpper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    border-radius: 8px;
    padding: 14px 16px;
    .title {
      font-size: 14px;
      color: #222222;
      line-height: 18px;
    }
    .tips {
      font-size: 12px;
      color: #a6a6a6;
      line-height: 18px;
    }
  }
  .footer {
    margin: 0 20px;
    :deep(.van-button__text) {
      font-size: 16px;
      color: #ffffff;
      font-weight: bold;
    }
  }
}
</style>