<template>
  <div class="cash-loan-order-page">
    <navigation-bar pageName="借款订单" @onLeftClick="onBackClick"></navigation-bar>
    <div class="cash-loan-order-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const  isIphoneX = window.isIphoneX
  const store = useStore()
  const route = useRoute()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .cash-loan-order-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }
  }
</style>