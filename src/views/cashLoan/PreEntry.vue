<template>
  <div class="page" v-if="requestFinish">
    <navigation-bar
      pageName="极享金推荐"
      @onLeftClick="onBackClick"
      :navBarStyle="{}"
    ></navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <page-header />
      <loan-money v-model="form.amount" />
      <div class="loan-info">
        <loan-period
          :list="periods"
          :period="form.orderPreInfo.period"
          @updatePeriod="updatePeriod"
        />
        <div class="loan-usage solid-top">
          <div class="usage-label">借款用途</div>
          <div class="usage" @click="loanUsageRef.show = true">
            {{ form.orderPreInfo.loanUsage }}
            <van-icon name="arrow"></van-icon>
          </div>
        </div>
      </div>
      <member-content
        ref="memberContentRef"
        v-model="memberOpenFlag"
        v-if="form.orderPreInfo.period"
        :memberInfo="memberInfo"
        @showMemberAgreement="memberAgreementRef.show = true"
      ></member-content>
    </div>
    <loan-footer @onConfirm="onConfirm" />
    <loan-usage ref="loanUsageRef" :form="form" @updateLoanUsage="updateLoanUsage" />
    <member-apply-popup
      :data="memberInfo"
      ref="memberApplyRef"
      @showMemberAgreement="memberAgreementRef.show = true"
      @handleOpen="memberOpenFn"
      @onCancel="cancelOpen"
    ></member-apply-popup>
    <open-success
      ref="openSuccessRef"
      title="激活成功"
      v-if="memberInfo.vipPrice"
      :tips="`成功开通如意卡·${memberInfo.vipPrice.vipName}，享优先推荐特权`"
      :show-btn="true"
      :pre-flag="true"
      @onConfirm="nextPage"
    ></open-success>
    <agreement-popup
      url="/agreement/member-service.htm"
      title="权益服务协议"
      ref="memberAgreementRef"
    ></agreement-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { useAcceleratorCard } from '@/hooks/useAcceleratorCard'
import {
  PageHeader,
  LoanPeriod,
  LoanMoney,
  LoanUsage,
  LoanFooter,
  MemberContent,
  MemberApplyPopup,
} from './components/pre/index'
import { creditQuota } from '@/api/customer'
import { submitMemberOrder, payProduct, submitPay, isNeedBindCard } from '@/api/member-bank'
import { acceleratorCardConfig } from '@/api/member'
import AgreementPopup from '@/components/AgreementPopup'
import OpenSuccess from '@/views/cashLoan/apply/components/OpenSuccessDialog.vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import goodsCheckStore from '@/store/goodsCheck.js'

const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const loanUsageRef = ref(null)
const requestFinish = ref(false)
const memberOpenFlag = ref(false)
const memberApplyRef = ref(null)
const memberContentRef = ref(null)
const memberAgreementRef = ref(null)
const openSuccessRef = ref(null)
const amount = ref(1000)
const periods = [
  { name: '3个月', value: 3 },
  { name: '6个月', value: 6 },
  { name: '12个月', value: 12 },
]
const data = reactive({
  form: {
    orderPreInfo: {
      loanUsage: '个人日常消费',
      period: 0,
    },
    amount: null,
  },
  quotaData: {},
  memberInfo: {},
  bandBankInfo: {},
  payInfo: {},
})
const { form, quotaData, memberInfo, bandBankInfo, payInfo } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const updateLoanUsage = (value) => {
  loanUsageRef.value.show = false
  form.value.orderPreInfo.loanUsage = value
}
const updatePeriod = (value) => {
  form.value.orderPreInfo.period = value
}
// 提交支付
const launchPay = async () => {
  const payRes = await submitPay({
    cashierId: payInfo.value.id,
    orderId: payInfo.value.orderId,
    productId: payInfo.value.trials[0].productResponse.productId,
    subProductId: payInfo.value.trials[0].productResponse.subProductId,
  }).catch((error) => {
    console.log(error)
  })
  if (payRes) {
    // openSuccessRef.value.show = true
    // openSuccessRef.value.startTimes()
    openSuccessRef.value.reveal().then(({ isCanceled }) => {
      nextPage()
    })
  }
}
// 取消开通
const cancelOpen = () => {
  if (memberInfo.value.jskFlag === 'Y') {
    nextPage()
  }
}
// 先发起会员开通
const memberOpenFn = async () => {
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  try {
    const configDataRes = await acceleratorCardConfig({
      stage: 'APPLY',
      productId: quotaData.value.productId,
      amount: amount.value,
    })
    if (configDataRes.data.jskFlag !== 'N') {
      // 再次校验是否已开通
      const orderRes = await submitMemberOrder({
        sceneCode: proxy.$global.CREDIT_SCENE_CASH,
        stage: 'APPLY',
        scenePage: 'OTHER',
        virtualMsg: { vipPriceId: memberInfo.value.vipPrice.id },
      })
      const orderId = orderRes.data.orderId
      // 获取支付产品信息
      const payRes = await payProduct({ orderId })
      payInfo.value = payRes.data.cashiers[0]
      payInfo.value.orderId = orderId
      // 提交支付
      await launchPay()
    } else {
      nextPage()
    }
    closeToast() // 清除加载中
  } catch (e) {
    closeToast() // 清除加载中
  }
}
const onConfirm = async () => {
  if (!form.value.amount) {
    showToast('请填写借款金额')
    return
  }
  const r = /^[1-9]\d*000$/
  if (!r.test(form.value.amount)) {
    showToast('请输入1000的整倍数')
    return
  }
  if (!form.value.orderPreInfo.period) {
    showToast('请选择期数')
    return
  }
  if (memberInfo.value.jskFlag === 'Y' || memberInfo.value.jskFlag === 'FORCE') {
    if (memberOpenFlag.value) {
      memberOpenFn()
    } else {
      goodsCheckStore.value = null
      const { isCanceled, data } = await memberApplyRef.value.reveal()
      if (!isCanceled) {
        if (data?.gift) {
          goodsCheckStore.value = data.gift
        }
        await memberOpenFn()
        return
      }
      if (data === 'cancel') {
        cancelOpen()
      }
    }
  }
}
const nextPage = async () => {
  const bindRes = await isNeedBindCard({ sceneCode: 'APPLY' })
  if (bindRes.data.supplement === 'Y') {
    router.replace(
      '/cash-loan-prebank?chnlId=' +
        bindRes.data.chnlId +
        '&orderId=' +
        bindRes.data.orderId +
        '&cmId=' +
        bindRes.data.cmId
    )
  } else {
    router.replace('/cash-loan-matching')
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(async () => {
  await creditQuota({ requestType: 'query', sceneCode: proxy.$global.CREDIT_SCENE_CASH }).then(
    (res) => {
      quotaData.value = res.data
    }
  )
  await useAcceleratorCard(
    { stage: 'APPLY', productId: quotaData.value.productId, amount: amount.value },
    (data) => {
      memberInfo.value = data
    }
  )
  const bindRes = await isNeedBindCard({ sceneCode: 'APPLY' })
  bandBankInfo.value = bindRes.data
  // 需要开通会员
  if (memberInfo.value.jskFlag === 'Y' || memberInfo.value.jskFlag === 'FORCE') {
    requestFinish.value = true
  } else {
    // 已开通会员
    if (bandBankInfo.value.supplement === 'Y') {
      router.replace(
        '/cash-loan-prebank?chnlId=' +
          bandBankInfo.value.chnlId +
          '&orderId=' +
          bandBankInfo.value.orderId +
          '&cmId=' +
          bindRes.data.cmId
      )
    } else {
      router.replace('/cash-loan')
    }
  }
})
</script>
<style scoped lang="scss">
.page {
  height: 100%;
  width: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(
    180deg,
    #4671eb 0%,
    #f5b8b3 39.77%,
    #f6c7c3 45.86%,
    #f6d6d3 52.39%,
    #f6e6e5 60.99%,
    #f6f6f6 72.14%
  );
  .nav-bar {
    color: #fff;
    background-color: transparent;
    :deep(.page-title) {
      color: #fff;
    }
  }
  &-context {
    padding-bottom: 100px;
    overflow-y: auto;

    .loan-info {
      margin: 10px 15px 0;
      background: #ffffff;
      border-radius: 8px;
      .loan-usage {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 50px;
        padding: 0 15px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
}
</style>
