<template>
  <div class="finish-page">
    <navigation-bar pageName="提交结果" :navBarStyle="{ fontSize: '16px', fontWeight: 600, color: '#333' }" @onLeftClick="onBackClick"></navigation-bar>
    <div class="finish-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="tip">温馨提示：近期诈骗案件高发，谨防上当受骗</div>
      <div class="result-wrapper">
        <img class="result-img" src="@/assets/images/cash-loan/finish.png">
        <div class="title">借款提交成功</div>
        <div class="p1">正在努力为您安排放款，放款成功后将会短信通知</div>
        <ProcessBarFinish/>
        <member-open :data="memberInfo" v-if="memberInfo.jskFlag === 'Y' || memberInfo.jskFlag === 'FORCE'"></member-open>
        <!-- <template>
          <div class="tips">
            请您保持手机通畅，部分合作构在审核中会有电话或短信联系您协助审核(如需要)，需要你继续操作APP进件放款补充操作，请您留着。
          </div>
          <div class="btn theme-linear-gradient" @click="onBackClick">
            查看结果
          </div>
        </template> -->
      </div>
      <div v-if="swiperDatas.length>0" class="bannar-wrapper">
        <my-swiper :swiperDatas="swiperDatas"></my-swiper>
      </div>
    </div>
    <div class="footer">请按时还款，保持良好信用，逾期将带来严重不良影响</div>
  </div>
</template>

<script setup>
  import MemberOpen from './components/MemberOpen'
  import { useAcceleratorCard } from '@/hooks/useAcceleratorCard'
  import { getAdList } from '@/api/base'
  import MySwiper from '@/components/MySwiper'
  import ProcessBarFinish from './components/ProcessBarFinish.vue'
  const isIphoneX = window.isIphoneX
  const route = useRoute()
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const productId = route.query.productId
  const swiperDatas = ref([])
  const data = reactive({
    memberInfo: {}
  })
  const { memberInfo } = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(async () => {
    useAcceleratorCard({stage: 'AFTER', productId, amount: 0 }, (data) => {
      memberInfo.value = data
    })
    await getAdList({ regionType: [proxy.$global.AD_POSITION_LOAN_FINISH] }).then(res => {
      if(res.data && JSON.stringify(res.data) !== '{}') {
        swiperDatas.value = res.data[proxy.$global.AD_POSITION_LOAN_FINISH]
      }
    })
  })
</script>

<style lang="scss" scoped>
  .finish-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #ffffff;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .tip {
        font-size: 13px;
        font-weight: 400;
        color: #333;
        padding: 7px 21px;
        background: #FFF5E8;
      }
      
      .result-wrapper{
        padding: 0 30px;
        text-align: center;
        background: #ffffff;
        .result-img{
          width: 245px;
          height: 245px;
          margin-top: 13px;
        }
        .title{
          font-size: 18px;
          font-weight: 600;
          color: #363636;
          margin-top: -70px;
        }
        .p1{
          font-size: 13px;
          color: #999999;
          line-height: 20px;
          margin-top: 9px;
        }
        .tips{
          font-size: 12px;
          font-weight: 400;
          color: #A8A8A8;
          line-height: 18px;
          margin-top: 5px;
        }
        .btn{
          height: 49px;
          border-radius: 25px;
          text-align: center;
          line-height: 49px;
          color: #ffffff;
          font-size: 16px;
          margin-top: 13px;
        }
      }
      .bannar-wrapper{
        margin: 50px 15px;
      }
    }
    .footer {
      position: absolute;
      bottom: 0;
      width: 100%;
      text-align: center;
      font-size: 13px;
      color: #BCBCBC;
      padding-bottom: calc(20px + var(--safe-area-inset-bottom));
    }
  }
</style>