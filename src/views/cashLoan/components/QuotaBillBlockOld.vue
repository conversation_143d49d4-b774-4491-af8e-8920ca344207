<template>
    <div class="quota-bill-wrapper">
        <div class="quota-boll-padd">
            <div class="borrow-quota" @click="handleQuota">
            <div class="p1">额度管理</div>
            <div class="p2">总额度{{ formatMoney(creditAmount, 0) }}</div>
            </div>
            <div class="divider"></div>
            <div class="borrow-quota" @click="handleBill">
            <div class="p1">查账还款</div>
            <div class="p2">保持良好信用</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    creditAmount: {
        type: Number,
        default: 200000
    }
})
const handleQuota = () => {
    router.push('/my/quota')
}
const handleBill = () => {
    router.push('/my/bill')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .quota-bill-wrapper{
        background: linear-gradient( 144deg, #FFFEFD 0%, #FFF9F6 100%);
        border-radius: 8px;
        margin: 10px 16px 0;
        padding: 7px;
        .quota-boll-padd{
            border-radius: 5px;
            border: 1px solid #FFD5BE;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            .divider{
                width: 1px;
                height: 28px;
                background: #E6E6E6;
            }
            .borrow-quota{
                text-align: center;
                flex: 1;
                .p1 {
                font-size: 15px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #363636;
                line-height: 21px;
                }
                .p2 {
                margin-top: 3px;
                font-size: 12px;
                font-family: PingFang-SC-Medium, PingFang-SC;
                font-weight: 500;
                color: #A8A8A8;
                line-height: 17px;
                }
            }
        }
    }
</style>