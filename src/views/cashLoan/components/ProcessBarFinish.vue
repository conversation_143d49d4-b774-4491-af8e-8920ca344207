<template>
  <div class="process">
    <div class="left">
      <img class="process-bar" src="@/assets/images/cash-loan/process-bar.png" />
      <div class="cursor">进度30%</div>
    </div>
    <img class="right-icon" src="@/assets/images/cash-loan/right.png" />
    <div class="current">当前进度</div>
    <div class="end">放款成功</div>
  </div>
</template>

<style lang="scss" scoped>
.process {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 29px auto;
  position: relative;

  .left {
    border-radius: 7px;
    width: 286px;
    height: 13px;
    position: relative;
    margin-right: 2px;
    margin-left: 8px;

    .process-bar {
      position: absolute;
      left: 0;
      top: 0px;
      width: 286px;
      height: 13px;
    }

    .cursor {
      position: absolute;
      left: 42px;
      top: -2px;
      background-color: #F36B60;
      border-radius: 10px;
      width: 62px;
      height: 18px;
      line-height: 18px;
      text-align: center;

      font-size: 12px;
      font-weight: 600;
      color: #fff;
    }
  }

  .right-icon {
    width: 18px;
    height: 18px;
  }

  .current {
    position: absolute;
    top: 25px;
    left: 53px;
    font-size: 13px;
    color: #999;
  }

  .end {
    position: absolute;
    top: 25px;
    right: 1px;
    font-size: 13px;
    color: #999;
  }
}
</style>