<template>
    <div class="quota-boll-padd">
        <div class="borrow-quota" @click="handleQuota">
            <img src="@/assets/images/cashier/circle.png"/>
            <div class="p1">极享金总额度</div>
            <div class="p2">{{ hasAmount ? `￥${formatMoney(creditAmount, 0)}` : '*****' }}</div>
            <div class="p3">{{ hasAmount ? '总额度' : '补充资料即可申请' }}</div>
        </div>
        <div class="borrow-quota" @click="handleBill">
            <img src="@/assets/images/cashier/circle.png"/>
            <div class="p1">查账还款</div>
            <div class="p3">暂无欠款</div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    hasAmount: {
        type: Boolean,
        default: true,
    },
    creditAmount: {
        type: Number,
        default: 200000
    }
})
const handleQuota = () => {
    // router.push('/my/quota')
}
const handleBill = () => {
    router.push('/my/bill')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
.quota-boll-padd{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin: 0 10px 10px 10px;
    .divider{
        width: 1px;
        height: 28px;
        background: #E6E6E6;
    }
    .borrow-quota{
        background-color: #fff;
        border-radius: 10px;
        position: relative;
        padding: 10px 10px 20px 10px;
        .p1 {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin-bottom: 14px;
        }
        .p2 {
        font-size: 16px;
        font-weight: 600;
        color: #4F0601;
        }
        .p3 {
        margin-top: 5px;
        font-size: 13px;
        font-weight: 400;
        color: #999;
        }

        img {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 62px;
        }
    }
}
</style>