<template>
  <srcoll-nav-bar :page-style="pageStyle" custom-back @back="$router.replace('/')">
    <van-pull-refresh class="text-white" v-model="loading" @refresh="emit('onRefresh')">
      <div class="non-credit-wrapper">
        <!-- <img class="credit-img" src="@/assets/images/cash-loan/non-credit-bg2.png"> -->
        <div class="yunji">
          <!-- <img v-if="modelValue.productIcon" :src="modelValue.productIcon" class="logo-img">
          <img v-else src="@/assets/images/cash-loan/logo.png" class="logo-img"> -->
          <div class="info">
            <div class="p1">欢迎来到轻享花</div>
            <div class="p2">
              属于您的专属极享金
              <van-icon style="font-size: 10px" name="play" />
            </div>
            <img class="icon" src="@/assets/images/cashier/coins.png" />
          </div>
        </div>
        <div class="content">
          <!-- <div class="top">年利率(单利)12.4%，1千元1天仅需2.65元</div> -->
          <div class="p1">
            当前可借额度(元)
            <div class="p1-desc">
              <img style="height: 13px; margin-top: 3px" src="@/assets/icons/cashier/safe.png" />
              <span
                style="
                  color: #6113004d;
                  font-size: 12px;
                  margin-left: 4px;
                  margin-top: 2px;
                  font-weight: normal;
                "
                >安全保障中</span
              >
            </div>
          </div>
          <div class="features">
            <div>限时免息</div>
            <div>极速审批</div>
            <div>灵活借钱</div>
          </div>
          <div class="p2">******</div>
          <!-- <div class="button-tips">
            <img src="@/assets/images/cashier/借钱.png" />
            <span>优质用户专享权益</span>
          </div> -->
          <!-- <div class="agreement">
            <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
            <span class="agree">我已阅读并知晓
              <span class="highlight theme-text" @click="agreementRef.show=true">《个人信息查询及使用授权》</span>
            </span>
          </div> -->
          <div class="apply-btn" @click="onCashLoan">
            {{
              modelValue.creditStatus === 'APPLY' || modelValue.creditStatus === 'VERIFY'
                ? '已提交，正在审核中'
                : '马上借钱'
            }}
          </div>
          <AgreementPersonalInfoAuth v-model="checked" />
          <ProcessBar :creditStatus="modelValue.creditStatus" />
        </div>
        <div class="swiper">
          <my-swiper :swiperDatas="swiperDatas"></my-swiper>
        </div>
        <div class="padding-top-sm">
          <quota-bill-block :hasAmount="false" />
        </div>
        <LiXiangQuanYi />
        <loan-footer />
      </div>
    </van-pull-refresh>
    <!-- <van-floating-bubble v-model:offset="offset" @click="$customerService">
      <img class="cs-img" src="@/assets/images/cash-loan/customer-service.png">
    </van-floating-bubble> -->
    <agreement-popup
      url="/agreement/personal-info-auth.htm"
      title="个人信息查询及使用授权"
      ref="agreementRef"
    ></agreement-popup>
  </srcoll-nav-bar>
</template>

<script setup>
import SrcollNavBar from '@/components/ScrollNavBar'
import QuotaBillBlock from './QuotaBillBlock'
import LoanFooter from './LoanFooter'
import pageBg from '@/assets/images/cash-loan/non-credit-bg.png'
import { showToast } from 'vant'
import AgreementPopup from '@/components/AgreementPopup'
import MySwiper from '@/components/MySwiper'
import LiXiangQuanYi from './LiXiangQuanYi.vue'
import AgreementPersonalInfoAuth from '@/components/AgreementPersonalInfoAuth'
import ProcessBar from './ProcessBar.vue'
import { getAdList } from '@/api/base'
const { proxy } = getCurrentInstance()
const loading = ref(false)
const offset = ref({ x: proxy.$px2rem('300px'), y: 125 })
const checked = ref(false)
const agreementRef = ref(false)
const store = useStore()
const swiperDatas = ref([])
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
})
const pageStyle = {
  background:
    'linear-gradient(180deg, #4671eb 0%, #F5B8B3 39.77%, #F6C7C3 45.86%, #F6D6D3 52.39%, #F6E6E5 60.99%, #F6F6F6 72.14%) no-repeat',
  'background-size': `100% ${proxy.$px2rem('476px')}`,
  'background-position': '0 0',
  color: '#fff',
}
const router = useRouter()
const emit = defineEmits(['onCashLoan', 'onRefresh'])
const onCashLoan = () => {
  const creditStatus = props.modelValue.creditStatus
  if (creditStatus === 'APPLY' || creditStatus === 'VERIFY') {
    showToast('资方审核中，请耐心等待')
    return
  }
  if (checked.value) {
    emit('onCashLoan')
  } else {
    showToast('请阅读并同意协议')
  }
}
const initPage = async () => {
  // 广告
  await getAdList({ regionType: [proxy.$global.AD_POSITION_BORROW] }).then((res) => {
    if (res.data && JSON.stringify(res.data) !== '{}') {
      swiperDatas.value = res.data[proxy.$global.AD_POSITION_BORROW]
    }
  })
}

onMounted(async () => {
  await initPage()
})
defineExpose({ loading })
</script>

<style lang="scss" scoped>
.non-credit-wrapper {
  position: relative;
  .credit-img {
    width: 184px;
    height: 225px;
    position: absolute;
    top: 9px;
    right: -7px;
  }
  .yunji {
    padding-top: 2px;
    margin-left: 20px;
    display: flex;
    align-items: center;
    position: relative;
    .logo-img {
      width: 40px;
      height: 40px;
      display: block;
      margin-right: 11px;
    }
    .p1 {
      font-weight: 600;
      font-size: 16px;
      color: #fff;
      font-size: 20px;
      font-weight: 600;
    }
    .p2 {
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin-top: 5px;
    }
    .icon {
      position: absolute;
      width: 68px;
      height: 68px;
      right: 20px;
      bottom: -20px;
    }
  }
  .shzl-img {
    display: block;
    margin: 0 auto;
    margin-top: 38px;
    width: 240px;
    height: 32px;
  }
  .content {
    width: 355px;
    margin: 0 auto;
    margin-top: 15px;
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    .top {
      background: #f43b2c1a;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #b525024d;
      font-size: 14px;
      font-weight: 400;
    }
    .p1 {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 22px auto 10px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 18px;
      position: relative;

      .p1-desc {
        position: absolute;
        right: 36px;
        display: flex;
        align-items: center;
      }
    }
    .features {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      div {
        background: #f43b2c1a;
        border-radius: 4px;
        color: #b52502;
        font-size: 12px;
        text-align: center;
        font-weight: 400;
        padding: 4px 8px 3px;
      }
    }
    .p2 {
      margin-top: 10px;
      font-size: 45px;
      font-weight: 600;
      color: #8a2107;
      letter-spacing: 5px;
      text-align: center;
    }
    .button-tips {
      width: 210px;
      height: 31px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(
        180deg,
        rgba(244, 56, 40, 0.1) 0%,
        rgba(244, 56, 40, 0.01) 34.53%
      );
      border-radius: 10px;

      img {
        height: 36px;
        margin-top: -14px;
      }

      span {
        margin-left: 13px;
        margin-top: -7px;
        color: #f87e47;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
  .swiper {
    // display: block;
    width: 355px;
    height: auto;
    margin: 10px auto;
    margin-bottom: 0;
  }
  .apply-btn {
    width: 257px;
    height: 36px;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    margin: 0px auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
    border-radius: 18px;
    position: sticky;
  }
  .verify-tips {
    font-size: 12px;
    color: #4671eb;
    line-height: 17px;
    text-align: center;
    margin-top: 22px;
  }
}

.van-floating-bubble {
  .cs-img {
    width: 100%;
    height: 100%;
  }
}
.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  .agree {
    font-weight: 400;
    font-size: 12px;
    transform: scale(0.9);
    color: #999999;
    transform-origin: left center;
    margin-left: 8px;
  }
}
</style>
