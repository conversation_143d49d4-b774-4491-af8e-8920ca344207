<template>
  <van-popup round v-model:show="show">
    <div class="invalid-popup">
      <div class="icon-wrapper">
        <img class="icon" src="@/assets/images/cash-loan/fail.png" />
      </div>
      <div class="title">别灰心！试试其它产品</div>
      <div class="content">
        <div>
          尊敬的用户，抱歉的通知您，您的额度
          {{ modelValue.creditStatus === 'INVALID' ? '已失效/暂不满足办理条件' : '未能审核通过' }}
          。请于以下时间过后再重新申请：
        </div>
        <div class="margin-top-xs text-center">
          <span class="time">{{ parseTime(modelValue.allowTime) }}</span>
        </div>
      </div>
      <div class="btn" @click="onMoreProduct">匹配更多产品</div>
    </div>
  </van-popup>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
})
const show = ref(false)
const emit = defineEmits(['moreProduct'])
const onMoreProduct = () => {
  show.value = false
  emit('moreProduct')
}
defineExpose({ show })
</script>

<style lang="scss" scoped>
.invalid-popup {
  width: 301px;
  padding: 19px 21px 26px 21px;
  box-sizing: border-box;
  background: linear-gradient(12.2deg, #ffffff 74.4%, #feefee 84.45%, #4671eb 144.13%);
  .icon-wrapper {
    text-align: center;
    margin-bottom: 15px;
    .icon {
      width: auto;
      height: 45px;
    }
  }
  .title {
    font-size: 17px;
    font-weight: bold;
    text-align: center;
    margin-top: 3px;
    background: linear-gradient(137.3deg, #b52502 24.34%, #611300 81.24%);
    -webkit-background-clip: text;
    background-clip: text;
  }
  .content {
    margin-top: 21px;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #666666;
    line-height: 22px;
    .time {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .btn {
    margin-top: 36px;
    height: 42px;
    line-height: 42px;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
    border-radius: 21px;
    text-align: center;
    font-size: 15px;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>