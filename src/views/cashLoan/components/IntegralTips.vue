<template>
  <van-popup round v-model:show="show">
    <div class="invalid-popup">
      <div class="title">您当前可用积分不足</div>
      <div class="content">
        您可通过开通会员服务获赠积分
      </div>
      <div class="btn" @click="toMember">去获得积分</div>
    </div>
  </van-popup>
</template>

<script setup>
  const router = useRouter()
  const show = ref(false)
  const toMember = () => {
    show.value = false
    router.push('/member?partnerId=9')
  }
  defineExpose({ show })
</script>

<style lang="scss" scoped>
  .invalid-popup{
    width: 195px;
    padding: 20px;
    .title{
      font-size: 17px;
      font-weight: bold;
      color: #363636;
      text-align: center;
      margin-top: 3px;
    }
    .content{
      margin-top: 11px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 18px;
    }
    .btn{
      margin-top: 20px;
      height: 36px;
      background: #FF5533;
      border-radius: 18px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 36px;
    }
  }
</style>