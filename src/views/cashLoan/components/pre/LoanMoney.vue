<template>
  <div class="loan-money">
    <div class="money solid-bottom">
      <div class="unit">¥</div>
      <input v-model="amount" @input="onInput" type="number" pattern="[0-9]*" placeholder="最高可借200000" />
    </div>
    <div class="tips">单笔可借1000-200000元，请输入1000的整数倍</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const amount = ref(null)
const porps = defineProps({
  modelValue: {
    type: Number,
    default: null
  }
})
const emit = defineEmits(['update:modelValue'])
const onInput = () => {
  emit('update:modelValue', Number(amount.value))
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .loan-money{
    background: #FFFFFF;
    border-radius: 8px;
    margin: 10px 15px;
    .money{
      padding: 23px 0 23px 15px;
      display: flex;
      align-items: center;
      .unit{
        font-size: 24px;
        color: #000000;
        margin-right: 3px;
      }
      input{
        font-size: 24px;
      }
      input::placeholder {
        color: #B2B2B2;
      }
    }
    .tips{
      font-size: 13px;
      color: #666666;
      line-height: 15px;
      padding: 16px 15px;
    }
  }
</style>