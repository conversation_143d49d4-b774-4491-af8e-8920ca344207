<template>
  <div class="header">
    <img class="label-img" src="@/assets/images/cash-loan/pre/pre-top-label.png">
    <div class="text">智能匹配低息资金方，超3000万人已放款</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .header{
    height: 40px;
    background: #FFF1DD;
    display: flex;
    align-items: center;
    padding: 0 15px;
    .label-img{
      width: 18px;
      height: 13px;
      display: block;
      margin-right: 10px;
    }
    .text{
      font-size: 13px;
      color: #EE6825;
    }
  }
</style>