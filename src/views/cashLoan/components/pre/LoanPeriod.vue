<template>
  <div class="period">
    <div class="period-label">借多久</div>
    <div class="period-list">
      <div
        :class="`period-item ${item.value === period ? 'active': ''}`"
        v-for="item in list" @click="updatePeriod(item)">
        {{ item.name }}
        <img
          v-if="item.value === period"
          src="@/assets/images/cash-loan/pre/month-checked.png"
          class="checked-img"
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  period: {
    type: Number,
    default: 0
  }
})
const emit = defineEmits(['updatePeriod'])
const updatePeriod = (item) => {
  emit('updatePeriod', item.value)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .period{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    .period-label{
      font-size: 14px;
      color: #333333;
    }
    .period-list{
      display: flex;
      .period-item.active{
        background: #FFE8DF;
        color: #FF571A;
      }
      .period-item{
        height: 36px;
        padding: 0 13px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        color: #333333;
        background: #F3F3F3;
        border-radius: 2px;
        overflow: hidden;
        position: relative;
        .checked-img{
          position: absolute;
          right: 0px;
          bottom: 0px;
          width: 20px;
          height: 44px;
        }
      }
      .period-item +.period-item{
        margin-left: 8px;
      }
    }
  }
</style>