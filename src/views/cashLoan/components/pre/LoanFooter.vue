<template>
  <div class="loan-footer" :class="{ 'iphonex-bottom': isIphoneX }">
    <div class="loan-btn" @click="emit('onConfirm')">马上领钱</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const emit = defineEmits(['onConfirm'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .loan-footer{
    position: fixed;
    bottom: 0;
    width: calc(100% - 40px);
    padding: 14px 20px;
    .loan-btn{
      height: 40px;
      // background: #FF571A;
      background-image: var(--primary-linear-to-bottom);
      border-radius: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #FFFFFF;
    }
  }
  .loan-footer.iphonex-bottom{
    // padding-bottom: 48px;
    padding-bottom: calc(14px + var(--safe-area-inset-bottom));
  }
</style>