<template>
  <srcoll-nav-bar :page-style="pageStyle" custom-back @back="$router.replace('/')">
    <van-pull-refresh class="text-white" v-model="loading" @refresh="emit('onRefresh')">
      <div class="credit-wrapper">
        <div class="yunji">
          <!-- <img v-if="modelValue.productIcon" :src="modelValue.productIcon" class="logo-img">
          <img v-else src="@/assets/images/cash-loan/logo.png" class="logo-img"> -->
          <div class="info">
            <div class="p1">欢迎来到轻享花</div>
            <div class="p2">
              属于您的专属极享金
              <van-icon style="font-size: 10px" name="play" />
            </div>
            <img class="icon" src="@/assets/images/cashier/coins.png" />
          </div>
        </div>
        <div class="content">
          <!-- <div class="top">年利率(单利)12.4%，1千元1天仅需2.65元</div> -->
          <div class="p1">
            {{ orderInfo.status === 'CASH_SUCC' ? '下期可借额度' : '当前可借额度（元）' }}
            <div class="p1-desc">
              <img style="height: 13px; margin-top: 3px" src="@/assets/icons/cashier/safe.png" />
              <span
                style="
                  color: #6113004d;
                  font-size: 12px;
                  margin-left: 4px;
                  margin-top: 2px;
                  font-weight: normal;
                "
                >安全保障中</span
              >
            </div>
          </div>
          <div class="features">
            <div>限时免息</div>
            <div>极速审批</div>
            <div>灵活借钱</div>
          </div>
          <div class="p2">
            ¥{{
              formatMoney(
                orderInfo.status === 'CASH_SUCC' ? modelValue.nextPreAmount : modelValue.ultimaCash,
                0
              )
            }}
          </div>
          <div class="button-tips">
            <img src="@/assets/images/cashier/借钱.png" />
            <span>优质用户专享权益</span>
          </div>
          <div class="apply-btn" @click="onCashLoan">
            {{
              orderInfo.status === 'CASH_WAIT'
                ? '资方审核中'
                : orderInfo.status === 'CASH_SUCC'
                ? '查账还款'
                : orderInfo.status === 'CASH_SUPPLEMENT'
                ? '借款补件'
                : '马上借钱'
            }}
          </div>
          <div class="order-tips" v-if="orderInfo.status === 'APPLY_FAIL'">
            您最近一笔申请备用金审核未能通过
          </div>
          <div class="order-tips" v-if="orderInfo.status === 'CASH_FAIL'">
            您最近一笔申请备用金放款失败
          </div>
          <div
            class="order-succ"
            v-if="orderInfo.status === 'CASH_WAIT' || orderInfo.status === 'APPLY_VERIFY'"
          >
            您有一笔订单资方审核中，请耐心等待
          </div>
          <div class="order-succ" v-if="orderInfo.status === 'CASH_SUCC'">
            您有一笔订单已放款成功,请按时还款
          </div>
          <div
            class="invalid-day"
            v-if="
              orderInfo.status !== 'CASH_WAIT' &&
              orderInfo.status !== 'APPLY_VERIFY' &&
              orderInfo.status !== 'APPLY_FAIL' &&
              orderInfo.status !== 'CASH_SUCC' &&
              orderInfo.status !== 'CASH_SUPPLEMENT'
            "
          >
            <div class="text">限时申请，额度有效时间</div>
            <van-count-down
              :time="compareMillisecond(new Date(), modelValue.invalidDay)"
              @finish="onFinish"
              format="DD 天 HH 时 mm 分 ss 秒"
            >
            </van-count-down>
          </div>
        </div>
        <div class="swiper">
          <my-swiper :swiperDatas="swiperDatas"></my-swiper>
        </div>
        <quota-bill-block :credit-amount="modelValue.creditAmount"></quota-bill-block>
        <LiXiangQuanYi />
        <loan-footer></loan-footer>
      </div>
    </van-pull-refresh>
  </srcoll-nav-bar>
</template>

<script setup>
import SrcollNavBar from '@/components/ScrollNavBar'
import MySwiper from '@/components/MySwiper'
import { showToast } from 'vant'
import { compareMillisecond } from '@/utils/date'
import QuotaBillBlock from './QuotaBillBlock'
import LiXiangQuanYi from './LiXiangQuanYi.vue'
import LoanFooter from './LoanFooter'
import { getAdList } from '@/api/base'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const loading = ref(false)
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
  orderInfo: {
    type: Object,
    default: () => {},
  },
})
const router = useRouter()
const swiperDatas = ref([])
const pageStyle = {
  background:
    'linear-gradient(180deg, #4671eb 0%, #F5B8B3 39.77%, #F6C7C3 45.86%, #F6D6D3 52.39%, #F6E6E5 60.99%, #F6F6F6 72.14%) no-repeat',
  'background-size': `100% ${proxy.$px2rem('476px')}`,
  'background-position': '0 0',
  color: '#fff',
}
const emit = defineEmits(['onCashLoan', 'onRefresh'])
const onCashLoan = () => {
  if (props.orderInfo.status === 'CASH_WAIT' || props.orderInfo.status === 'APPLY_VERIFY') {
    showToast('资方审核中,请耐心等待')
  } else if (props.orderInfo.status === 'CASH_SUCC') {
    router.push('/my/bill')
  } else if (props.orderInfo.status === 'CASH_SUPPLEMENT') {
    emit('toCashSupplement')
  } else {
    emit('onCashLoan')
  }
}
const onFinish = () => {
  emit('onRefresh')
}
const initPage = async () => {
  // 广告
  await getAdList({ regionType: [proxy.$global.AD_POSITION_BORROW] }).then((res) => {
    if (res.data && JSON.stringify(res.data) !== '{}') {
      swiperDatas.value = res.data[proxy.$global.AD_POSITION_BORROW]
    }
  })
}

onMounted(async () => {
  await initPage()
})
defineExpose({ loading })
</script>

<style lang="scss" scoped>
:deep(.page-title) {
  color: #ffffff !important;
}
.credit-wrapper {
  margin-top: 20px;
  .credit-img {
    width: 184px;
    height: 225px;
    position: absolute;
    top: 9px;
    right: -7px;
  }
  .yunji {
    padding-top: 2px;
    margin-left: 20px;
    display: flex;
    align-items: center;
    position: relative;
    .logo-img {
      width: 40px;
      height: 40px;
      display: block;
      margin-right: 11px;
    }
    .p1 {
      font-weight: 600;
      font-size: 16px;
      color: #fff;
      font-size: 20px;
      font-weight: 600;
    }
    .p2 {
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin-top: 5px;
    }
    .icon {
      position: absolute;
      width: 68px;
      height: 68px;
      right: 20px;
      bottom: -20px;
    }
  }

  .content {
    width: 355px;
    margin: 0 auto;
    margin-top: 15px;
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    padding-bottom: 20px;
    .top {
      background: #f43b2c1a;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #b525024d;
      font-size: 14px;
      font-weight: 400;
    }
    .p1 {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 22px auto 10px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 18px;
      position: relative;

      .p1-desc {
        position: absolute;
        right: 36px;
        display: flex;
        align-items: center;
      }
    }
    .features {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      div {
        background: #f43b2c1a;
        border-radius: 4px;
        color: #b52502;
        font-size: 12px;
        text-align: center;
        font-weight: 400;
        padding: 4px 8px 3px;
      }
    }
    .p2 {
      margin-top: 10px;
      font-size: 36px;
      font-weight: 600;
      color: #8a2107;
      // letter-spacing: 5px;
      text-align: center;
      margin-bottom: 10px;
      font-family: DIN, DIN !important;
    }
    .button-tips {
      width: 210px;
      height: 31px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(
        180deg,
        rgba(244, 56, 40, 0.1) 0%,
        rgba(244, 56, 40, 0.01) 34.53%
      );
      border-radius: 10px;

      img {
        height: 36px;
        margin-top: -14px;
      }

      span {
        margin-left: 13px;
        margin-top: -7px;
        color: #f87e47;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
  .swiper {
    // display: block;
    width: 355px;
    height: auto;
    margin: 10px auto;
    // margin-bottom: 0;
  }
  .apply-btn {
    width: 257px;
    height: 36px;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    margin: -10px auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
    border-radius: 18px;
    position: sticky;
  }
  .verify-tips {
    font-size: 12px;
    color: #4671eb;
    line-height: 17px;
    text-align: center;
    margin-top: 22px;
  }
  .explain-img {
    width: 343px;
    height: 100px;
    display: block;
    margin: 10px auto;
  }
  .order-tips {
    padding: 20px 0;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #999;
  }
  .order-succ {
    padding: 20px 0;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #999;
  }
  .invalid-day {
    padding: 20px 0;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    color: #999;
    :deep(.van-count-down) {
      color: #999;
    }
  }
}
</style>
