<template>
  <div class="member-open-container">
    <!-- <img class="open-member" src="@/assets/images/cash-loan/open-member.png"> -->
    <div class="member-tips">您有优先放款权未使用，可提速到80%</div>
    
    <div class="open-btn theme-linear-gradient" @click="handleOpen">
        马上激活优先放款权
        <!-- <img class="lightning" src="@/assets/images/cash-loan/lightning.png"> -->
    </div>
    <div class="p2">放款审核更快，超时送20元免息劵</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { submitMemberOrder } from '@/api/member'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const orderId = route.query.orderId
const handleOpen = () => {
  submitMemberOrder({ sceneCode: proxy.$global.CREDIT_SCENE_CASH, 
    stage: 'AFTER',
    orderId,
    virtualMsg: { vipPriceId: 1 } }).then(res => {
    router.push('/cashier?orderId='+res.data.orderId+'&orderSource='+proxy.$global.CASHIER_VIP_BUY)
  })
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
.member-open-container {
  margin-top: 38px;
  position: relative;
  padding-top: 20px;
}
.open-member {
  width: 280px;
  height: 75px;
  margin: 36px auto 0;
}

.member-tips {
  position: absolute;
  top: 0;
  right: 0;
  background-image: url('@/assets/images/cash-loan/hint.png');
  background-size: 100% 100%;
  width: 235px;
  height: 27px;
  font-size: 13px;
  color: #4F0601;
  line-height: 27px;
  text-align: center;
  // z-index: 9999;
}
.open-btn {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  margin: 0 auto;
  line-height: 44px;
  text-align: center;
  font-weight: 500;
  font-size: 15px;
  color: #FFFFFF;
  // position: relative;
  .lightning {
    position: absolute;
    top: 2px;
    right: 27px;
    width: 40px;
    height: 40px;
  }
}
.p2 {
  font-size: 13px;
  color: #999999;
  line-height: 17px;
  margin: 9px auto 0;
}
</style>