<template>
    <div class="footer">
      推荐贷款产品的最高额度以各产品历史授信额度综合测算，<br/>
      仅供参考，具体额度以第三方产品批准为主。
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .footer{
        margin: 10px 20px;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: center;
    }
</style>