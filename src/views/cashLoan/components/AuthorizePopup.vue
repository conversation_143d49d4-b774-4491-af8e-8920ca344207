<template>
  <van-popup round v-model:show="show">
    <div class="authorize-popup">
      <div class="icon-wrapper">
        <img class="icon" src="@/assets/images/cash-loan/safe.png" />
      </div>
      <div class="title">
        您正在申请借款<br />
        请您充分阅读提示内容
      </div>
      <div class="content">
        <div class="p2">1、额度评估时需要查询您的设备信息，有助于提升通过率，请您授权操作；</div>
        <div class="p2">
          2、审核过程中，本平台客服及合作方不会单独向用户收费或要求线下打款，谨防诈骗。
        </div>
      </div>
      <div class="btn-wrapper">
        <div class="btn text-black cancel" @click="cancel()">放弃授权</div>
        <div class="btn text-white confirm" @click="authorizeGet()">授权获得额度</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog, useToggle } from '@vueuse/core'
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import appJs from '@/utils/appJs'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const [show, toggleShow] = useToggle(false)
const emit = defineEmits(['authorizeGet', 'authorizeCancel'])
const { reveal, confirm, cancel, onReveal, onCancel, onConfirm } = useConfirmDialog()
onReveal(() => {
  toggleShow(true)
})
onCancel(() => {
  toggleShow(false)
})
onConfirm((data) => {
  toggleShow(false)
  emit('authorizeGet', data)
})
const user = computed(() => store.getters.userInfo)
async function authorizeGet() {
  toggleShow(false)
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  try {
    const [phoneInfo, locationInfo] = await Promise.allSettled([
      user.value?.contactsFlag !== 'Y' ? appJs.appGetPhoneInfo() : null,
      appJs.appGetLocationInfo(),
    ])
    closeToast()
    confirm({
      phoneInfo: phoneInfo.status === 'fulfilled' ? phoneInfo.value : null,
      locationInfo: locationInfo.status === 'fulfilled' ? locationInfo.value : null,
    })
  } catch {
    closeToast()
    cancel()
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show, toggleShow, reveal, confirm, cancel })
</script>
<style scoped lang="scss">
.authorize-popup {
  width: 301px;
  padding: 19px 21px 22px 21px;
  background: linear-gradient(12.2deg, #ffffff 74.4%, #feefee 84.45%, #4671eb 144.13%);
  border-radius: 7px;
  box-sizing: border-box;
  .icon-wrapper {
    text-align: center;
    margin-bottom: 3px;
    .icon {
      width: auto;
      height: 85px;
    }
  }
  .title {
    margin: 0 24px;
    font-size: 17px;
    font-weight: 700;
    line-height: 24px;
    text-align: center;
    background: linear-gradient(137.3deg, #b52502 24.34%, #611300 81.24%);
    -webkit-background-clip: text;
    background-clip: text;
  }
  .content {
    margin-top: 10px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    line-height: 22px;
    .p1 {
      margin-top: 12px;
      margin-bottom: 10px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #363636;
    }
  }
  .btn-wrapper {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    .btn {
      width: 120px;
      height: 40px;
      border-radius: 21px;
      line-height: 40px;
      text-align: center;
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 600;
      background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
    }
    .btn.cancel {
      background: #eeeeee;
      font-weight: 400;
    }
  }
}
</style>
