<template>
  <div>
    <van-popup
      v-model:show="modelValue"
      safe-area-inset-bottom
      position="bottom"
      round
    >
      <div class="title">
        <div class="text-gray close" @click="colsePopup">取消</div>
        相关协议
      </div>
      <div class="list padding-bottom">
        <!-- <div class="solid-top item" @click="handleAgreement('yql-sign')">《电子签章授权书》</div> -->
        <div class="solid-top item" @click="handleAgreement('loan-contract')">《借款合同》</div>
      </div>
    </van-popup>
    <van-popup
      v-model:show="showAgreement"
      safe-area-inset-bottom
      :style="{ height: '100%', width: '100%'}"
      class="full-screen"
    >
      <navigation-bar :isShowBack="false" :pageName="agreementName">
        <template #nav-left>
          <van-icon name="cross" size="22" class="text-gray" @click="showAgreement=false"/>
        </template>
      </navigation-bar>
      <iframe class="external-links" :src="agreementUrl" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </van-popup>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const showAgreement = ref(false)
  const agreementName = ref('')
  const agreementUrl = ref('')
  const handleAgreement = (type) => {
    agreementName.value = type === 'yql-sign' ? '电子签章授权书' : type === 'loan-contract' ? '借款合同' : '人脸识别授权书'
    agreementUrl.value = '/agreement/'+type+'.htm'
    showAgreement.value = true
  }
  const colsePopup = () => {
    emit('update:modelValue', false)
  }
</script>

<style lang="scss" scoped>
  .title{
    text-align: center;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    position: relative;
    .close{
      position: absolute;
      left: 20px;
      font-size: 14px;
    }
  }
  .list {
    padding-bottom: 50px;
    .item{
      height: 50px;
      line-height: 50px;
      font-size: 18px;
      text-align: center;
    }
  }
</style>