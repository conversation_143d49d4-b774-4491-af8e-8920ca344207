<template>
  <srcoll-nav-bar :pageName="modelValue.productName" :page-style="pageStyle">
    <van-pull-refresh class="text-white" v-model="loading" @refresh="emit('onRefresh')">
      <div class="non-credit-wrapper">
        <img class="credit-img" src="@/assets/images/cash-loan/non-credit-bg2.png">
        <div class="yunji">
          <img v-if="modelValue.productIcon" :src="modelValue.productIcon" class="logo-img">
          <img v-else src="@/assets/images/cash-loan/logo.png" class="logo-img">
          <div class="info">
            <div class="p1">{{ modelValue.productName }}</div>
            <div class="p2">您的专属备用金</div>
          </div>
        </div>
        <div class="content">
          <div class="p1">您的可借额度（元）</div>
          <div class="p2">******</div>
          <div class="p3">额度高，利率低，放款快</div>
        </div>
        <div class="agreement">
          <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
          <span class="agree">我已阅读并知晓
            <span class="highlight theme-text" @click="agreementRef.show=true">《个人信息查询及使用授权》</span>
          </span>
        </div>
        <div class="apply-btn theme-linear-gradient" @click="onCashLoan">
          {{ (modelValue.creditStatus === 'APPLY' || modelValue.creditStatus === 'VERIFY') ? '额度审核中，邀请您加入省钱卡提额' : '立即申请额度' }}
        </div>
        <div class="verify-tips" v-if="modelValue.creditStatus === 'APPLY' || modelValue.creditStatus === 'VERIFY'">
          审核结果将在24小时内确认,请耐心等待
        </div>
        <div class="padding-top-sm">
          <quota-bill-block />
        </div>
        <loan-footer />
      </div>
    </van-pull-refresh>
    <van-floating-bubble v-model:offset="offset" @click="$customerService">
      <img class="cs-img" src="@/assets/images/cash-loan/customer-service.png">
    </van-floating-bubble>
    <agreement-popup url="/agreement/personal-info-auth.htm" title="个人信息查询及使用授权" ref="agreementRef"></agreement-popup>
  </srcoll-nav-bar>
</template>

<script setup>
  import SrcollNavBar from '@/components/ScrollNavBar'
  import QuotaBillBlock from './QuotaBillBlock'
  import LoanFooter from './LoanFooter';
  import pageBg from '@/assets/images/cash-loan/non-credit-bg.png'
  import { showToast } from 'vant';
  import AgreementPopup from '@/components/AgreementPopup'
  const { proxy } = getCurrentInstance()
  const loading = ref(false)
  const offset = ref({ x: proxy.$px2rem('300px'), y: 125 })
  const checked = ref(true)
  const agreementRef = ref(false)
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {}
    }
  })
  const pageStyle = {
    'background': `url(${pageBg}) no-repeat`,
    'background-size': `100% ${proxy.$px2rem('319px')}`,
    'background-position': '0 0',
  }
  const router = useRouter()
  const emit = defineEmits(['onCashLoan', 'onRefresh'])
  const onCashLoan = () => {
    if(checked.value) {
      emit('onCashLoan')
    } else {
      showToast('请阅读并同意协议')
    }
  }
  defineExpose({ loading })
</script>

<style lang="scss" scoped>
  .non-credit-wrapper{
    position: relative;
    .credit-img{
      width: 184px;
      height: 225px;
      position: absolute;
      top: 9px;
      right: -7px;
    }
    .yunji{
      padding-top: 26px;
      margin-left: 17px;
      display: flex;
      align-items: center;
      .logo-img{
        width: 40px;
        height: 40px;
        display: block;
        margin-right: 11px;
      }
      .p1{
        font-weight: 600;
        font-size: 16px;
        color: #222222;
        line-height: 22px;
      }
      .p2{
        font-size: 14px;
        color: #222222;
        line-height: 20px;
        margin-top: 2px;
      }
    }
    .shzl-img{
      display: block;
      margin: 0 auto;
      margin-top: 38px;
      width: 240px;
      height: 32px;
    }
    .content {
      width: 363px;
      height: 266px;
      margin: 0 auto;
      margin-top: 22px;
      background: url('@/assets/images/cash-loan/non-credit-content-bg.png') no-repeat;
      background-size: 100% 100%;
      overflow:hidden;
      position: relative;
      .p1{
        margin: 40px 0 0 32px;
        font-size: 13px;
        color: #FFFFFF;
        line-height: 18px;
      }
      .p2{
        margin: 22px 0 0 30px;
        font-weight: bold;
        font-size: 36px;
        color: #FFFFFF;
        letter-spacing: 10px;
      }
      .p3{
        margin: 35px 0 0 30px;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 17px;
      }
    }
    .apply-btn {
      width: 351px;
      height: 50px;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      margin: 10px auto 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .verify-tips{
      font-size: 12px;
      color: #FF671A;
      line-height: 17px;
      text-align: center;
      margin-top: 22px;
    }
  }
  
  .van-floating-bubble {
    .cs-img{
      width: 100%;
      height: 100%;
    }
  }
  .agreement{
    display: flex;
    align-items: center;
    margin-top: 22px;
    margin-left: 20px;
    .agree{
      font-weight: 400;
      font-size: 12px;
      transform: scale(0.9);
      color: #999999;
      transform-origin: left center;
      margin-left: 8px;
    }
  }
</style>