<template>
  <van-popup v-model:show="show" round closeable>
    <div class="phone-popup">
      <div class="title">预留手机号</div>
      <div class="tips">该手机号码是您办理该银行卡时在银行填写的号码，如有疑问请联系该银行客服</div>
      <div class="btn theme-linear-gradient" @click="show = false">确认</div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const show = ref(false)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .phone-popup{
    width: 300px;
    .title{
      font-size: 16px;
      font-weight: 500;
      color: #363636;
      line-height: 22px;
      text-align: center;
      margin-top: 22px;
    }
    .tips{
      font-size: 13px;
      color: #A8A8A8;
      line-height: 18px;
      margin: 12px 20px 0;
    }
    .btn{
      width: 252px;
      height: 40px;
      line-height: 40px;
      border-radius: 21px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      margin: 0 auto;
      margin-top: 38px;
      margin-bottom: 38px;
      text-align: center;
    }
  }
</style>