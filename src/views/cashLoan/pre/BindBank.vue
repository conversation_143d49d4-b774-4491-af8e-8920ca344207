<template>
  <div class="page" ref="pageRef" :style="{ height: pageHeight }">
    <navigation-bar pageName="添加银行卡" @onLeftClick="onBackClick"></navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="header">
        <van-circle
          v-model:current-rate="currentRate"
          :rate="90"
          :speed="100"
          :stroke-width="80"
          color="#FF571A"
          layer-color="#DEDEDE"
          :text="text"
        />
        <div class="text">绑卡领取额度 <span class="theme-text">建议使用常用银行卡</span></div>
      </div>
      <van-form class="form-wrapper" ref="formSubmit" @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-if="user.flow !== $global.USER_FLOW_IDCARD"
            v-model="form.custIdCard.shieldName"
            label="持卡人"
            readonly
          />
          <van-field
            v-else
            v-model="form.custIdCard.name"
            label="持卡人"
            placeholder="请填写本人姓名"
            :rules="[{ required: true, message: '请填写本人姓名' }]"
          />
          <van-field
            v-if="user.flow === $global.USER_FLOW_IDCARD"
            v-model="form.custIdCard.idcard"
            label="持卡人身份证"
            placeholder="请填写持卡人身份证"
            :rules="[{ required: true, message: '请填写持卡人身份证' }]"
          />
          <van-field
            v-model="form.bankCard.phone"
            label="手机号码"
            placeholder="请填写银行预留手机号码"
            type="tel"
            maxlength="11"
            :rules="[{ required: true, message: '请填写银行预留手机号码' }]"
          >
            <template #right-icon>
              <van-icon name="warning-o" @click="phonePopupRef.show=true" />
            </template>
          </van-field>
          <van-field
            v-model="form.bankCard.cardNo"
            label="银行卡卡号"
            type="number"
            pattern="[0-9]*"
            placeholder="请填写银行卡卡号"
            :formatter="formatter"
            maxlength="25"
            @blur="onBankBlur"
            :rules="[{ required: true, message: '请填写银行卡卡号' }]"
          />
          <van-field
            v-model="form.bankName"
            label="银行名称"
            placeholder="请填写银行卡卡号"
            readonly
          >
            <template #right-icon>
              <van-icon name="warning-o" @click="supportingBanksRef.show=true" />
            </template>
          </van-field>
        </van-cell-group>
      </van-form>
    </div>
    <div :class="`bottom-confirm ${isIphoneX ? 'iphonex' : ''}`">
      <div class="agreement">
        <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
        <span class="agree">已同意阅读<span class="highlight theme-text" @click="accountAgreementRef.show=true">《账户委托扣款授权书》</span></span>
      </div>
      <div class="submit-btn theme-linear-gradient" @click="handleSubmit">确定</div>
    </div>
    <agreement-popup url="/agreement/member-service.htm" title="权益服务协议" ref="accountAgreementRef"></agreement-popup>
    <supporting-banks ref="supportingBanksRef"></supporting-banks>
    <PhonePopup ref="phonePopupRef"></PhonePopup>
    <van-popup v-model:show="smsShow" round :close-on-click-overlay="false">
      <div class="sms-popup">
        <div class="phone-text">确认手机号</div>
        <div class="tips">已发送验证码短信到</div>
        <div class="phone-hide">{{ phoneFormat(form.bankCard.phone) }}</div>
        <van-field
          v-model="code"
          placeholder="请输入验证码"
          type="number"
          @input="resetInputVal"
        >
          <template #button>
            <van-button v-if="times===0" class="sms-btn theme-text" size="small" round type="text" @click="getCode">发送验证码</van-button>
            <van-button v-else class="sms-btn text-gray" size="small" round type="text">{{times}}s</van-button>
          </template>
        </van-field>
        <div class="btn-wrapper">
          <div class="cancel-btn btn" @click="smsClose">取消</div>
          <div class="confirm-btn btn theme-linear-gradient" @click="onBindCard">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { saveBankCard, saveBindCard, getBankName, getBankSmsCode, getBankListGroup } from '@/api/bankcard'
import { showFirstName, phoneFormat, plusXing } from '@/utils/common'
import SupportingBanks from '@/components/SupportingBanks'
import AgreementPopup from '@/components/AgreementPopup'
import PhonePopup from './PhonePopup'
import { onUnmounted } from 'vue'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const currentRate = ref(0);
const checked = ref(true)
const accountAgreementRef = ref(null)
const supportingBanksRef = ref(null)
const phonePopupRef = ref(null)
const formSubmit = ref(null)
const smsShow = ref(false)
const times = ref(0)
const newCardId = ref(0)
const code = ref('')
const text = computed(() => currentRate.value.toFixed(0) + '%');
const user = computed(() => store.getters.userInfo)
const pageRef = ref(null)
const pageHeight = ref('100%')
const data = reactive({
  form: {
    bankCard: {
      channelId: '',
      phone: '',
      cardNo: ''
    },
    custIdCard: {
      name: '',
      idcard: ''
    },
    bankName: ''
  }
})
const { form } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
// 格式化银行卡
const formatter = (value) => {
  return value.replace(/\s/,'').replace(/([0-9]{4})(?=[0-9])/g,"$1 ");
}
const onBankBlur = () => {
  const _cardNo = form.value.bankCard.cardNo
  if (_cardNo && _cardNo.length > 15) {
    getBankName({ bankCard: { cardNo: _cardNo.replace(/\s/g,"") } }).then(res => {
      form.value.bankName = res.data.bankName
    })
  } else {
    form.value.bankName = ''
  }
}
const handleSubmit = () => {
  formSubmit.value.submit()
}
// 提交绑定
const onSubmit = () => {
  form.value.bankCard.cardNo = form.value.bankCard.cardNo.replace(/\s/g,"")
  form.value.returnUrl = route.query.returnUrl
  saveBankCard(form.value).then(res => {
    if(res.data.status === 'VALID') {
      proxy.onToastSucc(() => {
        router.replace('/cash-loan-matching')
      }, '绑卡成功')
    } else {
      if (res.data.signMode === 'API') { // 内部验证码
        countDown()
        smsShow.value = true
        newCardId.value =res.data.id
      } else if (res.data.signMode === 'PAGE') { // 需要去通道绑卡
        proxy.appJS.appOtherWebView(res.data.submitUrl )
      }
    }
  })
}
const resetInputVal = () => {
  const newCode = '' + code.value
  if (newCode > 6) {
    code.value = newCode.substring(0,6)
  }
}
// 重新获取验证码
const getCode = () => {
  if (times.value === 0) {
    countDown()
    getBankSmsCode({ id: newCardId.value })
  }
}
let timer = null
const countDown = () => {
  times.value = 60
  timer = setInterval(function() {
    times.value--
    if(times.value === 0) {
      clearInterval(timer)
    }
  },1000)
}
const smsClose = () => {
  smsShow.value = false
  clearInterval(timer)
}
// 验证码确认
const onBindCard = () => {
  if(code.value) {
    saveBindCard({ verifyCode: code.value, id: newCardId.value }).then(res => {
      proxy.onToastSucc(() => {
        router.replace('/cash-loan-matching')
      }, '绑定成功')
    })
  } else {
    showToast('请填写验证码！')
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onUnmounted(() => {
  document.body.removeEventListener('focusin', watchKeyBoard)
  document.body.removeEventListener('focusout', watchKeyBoard)
})
const watchKeyBoard = (isShow) => {
  setTimeout(() => {
    if (isShow) {// 键盘弹起
      pageHeight.value = (window.visualViewport.height || window.innerHeight) + 'px'
      window.scrollTo(0, 0);
    } else { // 键盘收起
      pageHeight.value = '100%'
    }
  },100)
}
onMounted(() => {
  form.value.bankCard.channelId = route.query.chnlId
  form.value.orderId = route.query.orderId
  form.value.cmId = route.query.cmId
  if(user.value?.flow !== proxy.$global.USER_FLOW_IDCARD) {
    form.value.custIdCard.name = user.value.custIdCard.name
    form.value.custIdCard.shieldName = showFirstName(user.value.custIdCard.name)
    form.value.custIdCard.idcard = user.value.custIdCard.idcard
    form.value.custIdCard.shieldId = plusXing(user.value.custIdCard.idcard, 14, 0)
  }
  getBankListGroup().then(res => {
    if (res.data?.length > 0) {
      // 默认银行卡
      form.value.bankCard.cardNo = res.data[0].cardNo
      form.value.bankName = res.data[0].bankName
      form.value.bankCard.phone = res.data[0].wholePhone
    }
  })
  var u = navigator.userAgent;
  if(!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) { // ios
    document.body.addEventListener('focusin', () => {
      watchKeyBoard(true)
    })
    document.body.addEventListener('focusout', () => {
      watchKeyBoard(false)
    })
  }
})
</script>
<style scoped lang='scss'>
  .page {
    height: 100%;
    width: 100%;
    position: absolute;
    overflow-y: auto;
    &-context{
      .header{
        height: 50px;
        margin: 17px 15px 0;
        background: #ffffff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 0 14px;
        .van-circle{
          width: 36px;
          height: 36px;
        }
        .text{
          margin-left: 10px;
          font-weight: bold;
          font-size: 14px;
          color: #000000;
          span{
            font-weight: 400;
            margin-left: 5px;
          }
        }
        :deep(.van-circle__text){
          font-size: 12px;
          zoom: 0.83;
          color: #FF571A;
        }
      }
      .form-wrapper{
        margin-top: 17px;
      }
    }
    .bottom-confirm{
      width:calc(100% - 32px);
      position: fixed;
      bottom: 0;
      background: #FFFFFF;
      padding: 0 16px;
      .agreement{
        display: flex;
        font-size: 12px;
        align-items: center;
        line-height: 22px;
        margin-top: 7px;
        .agree{
          margin-left: 3px;
        }
        .highlight{
          display: inline-block;
        }
      }
      .submit-btn{
        text-align: center;
        height: 40px;
        border-radius: 20px;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 40px;
        margin-top: 5px;
        margin-bottom: 10px;
      }
    }
    .bottom-confirm.iphonex{
      // padding-bottom: 34px;
      padding-bottom: var(--safe-area-inset-bottom);
    }
  }
  .van-cell{
    padding: 12px 15px !important;
  }
</style>