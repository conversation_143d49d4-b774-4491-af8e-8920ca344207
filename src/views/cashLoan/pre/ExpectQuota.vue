<template>
  <div class="page">
    <navigation-bar pageName="初审额度" @onLeftClick="onBackClick"></navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="expect-quota">
        <div class="tips">初审预估额度（元）</div>
        <div class="quota-number">
          1000~{{ maxQuota }}
        </div>
      </div>
      <div class="credit-tips">您还未完成授信无法准确评估额度</div>
      <div class="credit-btn" @click="handleCredit">完善授信提交，提交额度审核</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const user = computed(() => store.getters.userInfo)
const router = useRouter();
const maxQuota = ref(0)
function getRandomInteger(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
const handleCredit = () => {
  const path = user.value.flow === proxy.$global.USER_FLOW_INFO ? '/cust-info-result' : '/credit-facilities'
  localStorage.setItem(proxy.$global.LOCAL_CREDIT_SCENE, proxy.$global.CREDIT_SCENE_CONSUME);  // 实名授信通道
  router.replace(path)
}
const onBackClick = () => {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  maxQuota.value = getRandomInteger(4, 10) * 1000
})
</script>
<style scoped lang='scss'>
  .page {
    height: 100%;
    width: 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    &-context{
      .expect-quota{
        margin: 40px 20px 0;
        background: #ffffff;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .tips{
          font-size: 12px;
          color: #666666;
          line-height: 14px;
        }
        .quota-number{
          font-size: 42px;
          color: #000000;
          line-height: 49px;
          margin-top: 10px;
          font-weight: bold;
        }
      }
      .credit-tips{
        text-align: center;
        margin-top: 10px;
        font-size: 14px;
        color: #B2B2B2;
        line-height: 16px;
      }
      .credit-btn{
        height: 48px;
        margin: 30px 35px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #FFFFFF;
        font-weight: bold;
        border-radius: 40px;
        background: #FF571A;
      }
    }
  }
</style>