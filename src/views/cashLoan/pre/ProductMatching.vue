<template>
  <div class="page">
    <navigation-bar pageName="产品匹配中" @onLeftClick="onBackClick"></navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="title">产品匹配中......</div>
      <div class="loading">
        <van-circle
          v-model:current-rate="currentRate"
          :rate="100"
          :stroke-width="50"
          :speed="20"
          color="#FF571A"
          layer-color="#DEDEDE"
          text="激活中..."
          :size="$px2rem('120px')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const user = computed(() => store.getters.userInfo)
const currentRate = ref(0);
const onBackClick = () => {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  setTimeout(() => {
    if (user.value.flow === proxy.$global.USER_FLOW_FINISH) {
      router.replace('/cash-loan')
    } else {
      router.replace('/cash-loan-quota')
    }
  }, 3000)
})
</script>
<style scoped lang='scss'>
  .page {
    height: 100%;
    width: 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    &-context{
      flex: 1;
      .title{
        margin-top: 200px;
        font-size: 14px;
        color: #000000;
        line-height: 16px;
        text-align: center;
      }
      .loading{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }
</style>