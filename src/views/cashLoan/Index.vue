<template>
  <div class="cash-page">
    <credit-content
      v-if="quotaData.creditStatus === 'PASS'"
      v-model="quotaData"
      :order-info="orderInfo"
      @onCashLoan="onCashLoan"
      @onRefresh="onRefresh"
      ref="creditRef"
    >
    </credit-content>
    <non-credit-content
      v-if="quotaData.creditStatus && quotaData.creditStatus !== 'PASS'"
      v-model="quotaData"
      @onCashLoan="onCashLoan"
      @refresh="getCreditQuota"
      @onRefresh="onRefresh"
      ref="nonCreditRef"
    >
    </non-credit-content>
    <invalid-popup ref="invalidPopupRef" v-model="quotaData" />
    <authorize-popup ref="authorizePopupRef" />
  </div>
</template>

<script setup name="cashLoan">
import NonCreditContent from './components/NonCreditContent'
import CreditContent from './components/CreditContent'
import InvalidPopup from './components/InvalidPopup'
import AuthorizePopup from './components/AuthorizePopup'
import { creditQuota } from '@/api/customer'
import { listOrder } from '@/api/cashloan'
import { showToast, showLoadingToast, showSuccessToast } from 'vant'
import { useGetPhoneInfo } from '@/hooks/useGetPhoneInfo'
import { useAcceleratorCard } from '@/hooks/useAcceleratorCard'
const { proxy } = getCurrentInstance()
const store = useStore()
const isIphoneX = window.isIphoneX
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const router = useRouter()
const invalidPopupRef = ref(null)
const authorizePopupRef = ref(null)
const nonCreditRef = ref(null)
const creditRef = ref(null)
const data = reactive({
  quotaData: {},
  orderInfo: {},
})
const { quotaData, orderInfo } = toRefs(data)
const getCreditQuota = async (requestType, useDevice, loadingFlag) => {
  await creditQuota({
    requestType: requestType,
    sceneCode: proxy.$global.CREDIT_SCENE_CASH,
    useDevice,
  }).then((res) => {
    if (loadingFlag) {
      // 下拉刷新
      if (nonCreditRef.value) nonCreditRef.value.loading = false
      if (creditRef.value) creditRef.value.loading = false
    }
    if (useDevice) {
      showSuccessToast('提交成功')
    }
    // 测试代码
    // if (import.meta.env.VITE_APP_ENV === 'development') {
    //   res.data.creditStatus = 'PASS'
    //   res.data.invalidDay = '2025-04-18 19:00:00'
    //   res.data.creditAmount = 200000
    // }

    quotaData.value = res.data
    if (quotaData.value.productId) {
      listOrder({ pageSize: 1, pageNum: 1, productId: quotaData.value.productId }).then((res) => {
        if (res.data && res.data.status) {
          orderInfo.value = res.data
          memberFunc('AFTER', 0)
        } else {
          orderInfo.value = {}
        }
        // 测试代码
        // if (import.meta.env.VITE_APP_ENV === 'development') {
        //   orderInfo.value.status = 'APPLY_VERIFY'
        //   console.log('订单数据：', orderInfo.value)
        // }
      })
    }
  })
}
const memberFunc = (stage, amount, backcall) => {
  useAcceleratorCard({ stage, productId: quotaData.value.productId, amount }, (data) => {
    //backcall(data)
  })
}
// 借款
const onCashLoan = async () => {
  if (quotaData.value.creditStatus === 'UNREALNAME') {
    // 去授信
    // 新的逻辑：跳转到实名认证页面的第三步（填写客户信息）
    // （原来逻辑：跳转到实名认证页面或填写客户信息页面）
    const name = proxy.$global.USER_FLOW_IDCARD_NAME
    const faceRecognition = user.value.flow === proxy.$global.USER_FLOW_INFO ? 'success' : undefined
    router.push({ name, query: { faceRecognition } })
    localStorage.setItem(proxy.$global.LOCAL_CREDIT_SCENE, proxy.$global.CREDIT_SCENE_CASH) // 实名授信通道
    return
  }
  if (
    quotaData.value &&
    (quotaData.value.creditStatus === 'NON' ||
      quotaData.value.creditStatus === 'INVALID' ||
      quotaData.value.creditStatus === 'NOPASS')
  ) {
    // 未授信、审核不通过、已失效
    if (
      quotaData.value.creditStatus === 'NOPASS' &&
      new Date().getTime() < quotaData.value.allowTime
    ) {
      invalidPopupRef.value.show = true
    } else {
      // authorizePopupRef.value.show = true
      const { data, isCanceled } = await authorizePopupRef.value.reveal()
      if (!isCanceled && data?.phoneInfo) {
        saveContacts({ contacts: data.phoneInfo }).then(() => {
          store.dispatch('GetInfo')
        })
      }
      await getCreditQuota('credit', data?.locationInfo)
    }
    // 经纬度
    return
  }
  if (quotaData.value && quotaData.value.creditStatus === 'PASS') {
    if (user.value.contactsFlag !== 'Y') {
      authorizePopupRef.value.show = true
      const { data, isCanceled } = await authorizePopupRef.value.reveal()
      if (!isCanceled && data?.phoneInfo) {
        saveContacts({ contacts: data.phoneInfo }).then(() => {
          store.dispatch('GetInfo')
        })
      }
    }
    router.push('/cash-loan-apply')

    return
  }
  if (quotaData.value.creditStatus === 'APPLY' || quotaData.value.creditStatus === 'VERIFY') {
    router.push('/save-money')
    return
  }
  return
}
// 发起授权
const authorizeGet = () => {
  authorizePopupRef.value.show = false
  // 锁住页面。防住经纬度未获取成功重复点击
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true,
  })
  // 获取经纬度、通讯录
  if (user.value.contactsFlag !== 'Y') {
    // 通讯录
    proxy.appJS.appGetPhoneInfo()
  }
  // 经纬度、
  proxy.appJS.appGetLocationInfo()
}
// 取消获取通讯录
const authorizeCancel = () => {
  authorizePopupRef.value.show = false
  if (
    quotaData.value.creditStatus === 'NON' ||
    quotaData.value.creditStatus === 'INVALID' ||
    quotaData.value.creditStatus === 'NOPASS'
  ) {
    getCreditQuota('credit', {})
  } else {
    router.push('/cash-loan-apply')
  }
}
// 通讯录回调
const getPhoneInfo = (params) => {
  useGetPhoneInfo(params, () => {
    console.log('通讯录提交成功！')
  })
}
// 经纬度回调
const getLocationInfo = async (params) => {
  const data = JSON.parse(params.replace(/\r|\n/g, ''))
  await getCreditQuota('credit', data)
  if (quotaData.value && quotaData.value.creditStatus === 'PASS') {
    router.push('/cash-loan-apply')
  }
}
const onRefresh = async () => {
  // 授信查询
  await getCreditQuota('query', null, true)
}
onMounted(() => {
  if (user.value) {
    // 授信查询
    getCreditQuota('query', null)
    // 获取通讯录
    // window.getPhoneInfo = getPhoneInfo
    // window.getLocationInfo = getLocationInfo
  } else {
    proxy.appJS.appLogin()
  }
})
</script>

<style lang="scss" scoped>
.cash-page {
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  position: absolute;
}
</style>
