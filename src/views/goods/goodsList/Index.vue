<template>
  <div class="goods-list-page">
    <div class="goods-list-top">
      <div class="goods-list-nav-bar">
        <img class="back" @click="onBackClick" src="@/assets/icons/white-back.png" />
        <van-search
          class="van-search"
          v-model="queryParams.keywords"
          shape="round"
          placeholder="请输入搜索关键词"
          @search="onRefresh"
          clearable
        >
          <template #left-icon>
            <img class="icon" src="@/assets/images/home/<USER>" />
          </template>
        </van-search>
      </div>
      <!-- <navigation-bar :isDefault="false">
        <template #nav-left>
          <img class="back-img" src="@/assets/icons/back.png" @click="onBackClick" />
        </template>
        <template #nav-center>
          <form action="/">
            <van-search
              v-model="queryParams.keywords"
              shape="round"
              placeholder="请输入搜索关键词"
              @search="onRefresh"
              clearable
            >
              <template #left-icon>
                <img src="@/assets/icons/search-gray.png" />
              </template>
            </van-search>
          </form>
        </template>
        <template #nav-right>
          <img
            @click="changeStyle"
            v-if="goodsListStyle === 'ROW'"
            class="component-icon"
            src="@/assets/icons/goods-row.png"
          />
          <img
            @click="changeStyle"
            v-if="goodsListStyle === 'CARD'"
            class="component-icon"
            src="@/assets/icons/goods-card.png"
          />
        </template>
      </navigation-bar> -->
      <div class="filter-wrapper" ref="filterRef">
        <div class="filter-item" @click="changeRecommend">
          <span>综合推荐</span>
        </div>
        <div class="filter-item" @click="handleSort('SALE')">
          <span :class="`${queryParams.sortType === 'SALE' ? 'active theme-text' : ''}`">销量</span>
          <img
            v-if="queryParams.sortType === 'SALE' && queryParams.sortRule === 'ASC'"
            src="@/assets/icons/sort-asc.png"
          />
          <img
            v-else-if="queryParams.sortType === 'SALE' && queryParams.sortRule === 'DESC'"
            src="@/assets/icons/sort-desc.png"
          />
          <img v-else src="@/assets/icons/sort.png" />
        </div>
        <div class="filter-item" @click="handleSort('PRICE')">
          <span :class="`${queryParams.sortType === 'PRICE' ? 'active theme-text' : ''}`"
            >价格</span
          >
          <img
            v-if="queryParams.sortType === 'PRICE' && queryParams.sortRule === 'ASC'"
            src="@/assets/icons/sort-asc.png"
          />
          <img
            v-else-if="queryParams.sortType === 'PRICE' && queryParams.sortRule === 'DESC'"
            src="@/assets/icons/sort-desc.png"
          />
          <img v-else src="@/assets/icons/sort.png" />
        </div>
        <div class="filter-item" @click="handleFilter">
          <span>筛选</span>
          <img src="@/assets/icons/filter.png" />
        </div>
        <div class="change-list-style">
          <img
            @click="changeStyle"
            v-if="goodsListStyle === 'ROW'"
            class="component-icon"
            src="@/assets/icons/goods-row.png"
          />
          <img
            @click="changeStyle"
            v-if="goodsListStyle === 'CARD'"
            class="component-icon"
            src="@/assets/icons/goods-card.png"
          />
        </div>
      </div>
    </div>
    <div class="goods-list-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="——没有更多了——"
        @load="getList"
      >
        <goods-row v-if="goodsListStyle === 'ROW'" :goods-list="list" />
        <goods-card v-if="goodsListStyle === 'CARD'" :goods-list="list" />
      </van-list>
    </div>
    <filter-content
      v-model="queryParams"
      ref="filterContentRef"
      @updateQueryParams="updateQueryParams"
    />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import GoodsRow from '@/components/GoodsRow'
import GoodsCard from '@/components/GoodsCard'
import FilterContent from '../components/FilterContent'
import { goodsList, regionGoodsPage } from '@/api/goods'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const route = useRoute()
const router = useRouter()
const filterRef = ref(null)
const filterContentRef = ref(null)
const list = ref([])
const goodsListStyle = ref('')
const loading = ref(false)
const finished = ref(false)
const data = reactive({
  queryParams: {
    recommend: 'z',
    sortType: undefined,
    sortRule: undefined,
    categoryId: undefined,
    categoryName: undefined,
    keywords: undefined,
    pageNum: 1,
    pageSize: 20,
    spuIds: '',
  },
})
const { queryParams } = toRefs(data)
const onSearch = () => {
  onRefresh()
  getList()
}
const onBackClick = () => {
  router.go(-1)
}
const changeRecommend = () => {
  queryParams.value.sortRule = null
  queryParams.value.sortType = null
  onRefresh()
}
const handleSort = (value) => {
  if (value !== queryParams.value.sortType) {
    queryParams.value.sortRule = 'ASC'
  } else {
    queryParams.value.sortRule = queryParams.value.sortRule === 'ASC' ? 'DESC' : 'ASC'
  }
  queryParams.value.sortType = value
  onRefresh()
}
const changeStyle = () => {
  goodsListStyle.value = goodsListStyle.value === 'ROW' ? 'CARD' : 'ROW'
  localStorage.setItem(proxy.$global.GOODS_LIST_STYLE, goodsListStyle.value)
}
const getList = () => {
  loading.value = true
  if (queryParams.value.recommend === 'z') {
    // 普通
    goodsList(queryParams.value)
      .then((res) => {
        loading.value = false
        list.value = [...list.value, ...res.data]
        if (list.value.length >= res.total) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  } else if (queryParams.value.recommend === 'n') {
    // 新品
    regionGoodsPage(queryParams.value)
      .then((res) => {
        loading.value = false
        list.value = [...list.value, ...res.data]
        if (list.value.length >= res.total) {
          finished.value = true
        } else {
          queryParams.value.pageNum++
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  }
}
const handleFilter = () => {
  filterContentRef.value.show = true
}
// filter查询
const updateQueryParams = (query) => {
  queryParams.value = { ...queryParams.value, ...query }
  onRefresh()
}
// 重置列表数据
const onRefresh = () => {
  list.value = []
  finished.value = false
  loading.value = true
  queryParams.value.pageNum = 1
  getList()
}
onMounted(() => {
  queryParams.value.categoryId = route.query.categoryId
  queryParams.value.categoryName = route.query.categoryName
  queryParams.value.brandId = route.query.brandId
  queryParams.value.brandName = route.query.brandName
  queryParams.value.spuIds = route.query.spuIds ? route.query.spuIds.split(',') : ''
  queryParams.value.keywords = route.query.keywords
  // 排版
  goodsListStyle.value = localStorage.getItem(proxy.$global.GOODS_LIST_STYLE) || 'ROW'
})
</script>

<style lang="scss" scoped>
.goods-list-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(
    180deg,
    #5581f6 0%,
    #aabefb 5%,
    #d6dffa 10%,
    #e8edfa 20%,
    #f4f6fa 30%
  );
  .goods-list-top {
    .goods-list-nav-bar {
      box-sizing: border-box;
      width: 100%;
      padding: 40px 14px 10px 14px;

      display: flex;
      align-items: center;
      .back {
        width: 9px;
        display: block;
        margin-left: 10px;
        margin-right: 12px;
      }
      .icon {
        display: block;
        width: 19px;
        height: 19px;
        margin-left: 3px;
      }
    }
  }
  .goods-list-page-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f4f6fa;
    padding-top: 10px;
  }
  .van-search {
    flex: 1;
    background: none;
    width: 100%;
    padding: 0 !important;
    :deep(.van-search__content) {
      padding-left: 0;
    }
    :deep(.van-search .van-field__control::placeholder) {
      color: rgba(255, 255, 255, 0.9);
    }
    :deep(.van-icon-clear) {
      color: #fff;
    }
    --van-search-background: rgba(255, 255, 255, 30%);
    --van-search-content-background: rgba(255, 255, 255, 30%);
    --van-field-input-disabled-text-color: #fff;
  }

  .filter-wrapper {
    display: flex;
    height: 38px;
    align-items: center;
    background: #f4f6fa;
    border-radius: 24px 24px 0 0;
    padding-right: 14px;
    .filter-item {
      display: flex;
      justify-content: center;
      flex: 1;
      font-size: 13px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: 400;
      color: #333;
      img {
        width: 8px;
        object-fit: contain;
        margin-left: 2px;
      }
      // .filter-img {
      //   width: 10px;
      // }
    }
    .filter-item:first-child {
      color: #4671eb;
    }
    .change-list-style {
      display: flex;
      align-self: center;
      margin-top: 2px;
      .component-icon {
        width: 14px;
        height: 14px;
      }
    }
  }
}
</style>