<template>
  <div class="goods-detail-page" @scroll="onScrollChange" ref="goodsRef">
    <goods-nav
      :scroll-top-value="scrollTopValue"
      :nav-bar-style="navBarStyle"
      @onLeftClick="onBackClick"
    />
    <div class="goods-detail-page-content" v-if="spuInfo">
      <div class="goods-imgs">
        <van-swipe lazy-render :autoplay="3000">
          <van-swipe-item v-for="item in spuInfo.albumPicsArray" :key="item">
            <img style="width: 100%" :src="item" />
          </van-swipe-item>
          <template #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
          </template>
        </van-swipe>
        <img class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick" />
      </div>

      <div class="main-wrapper">
        <OpenMemberV2 :goods-sku="goodsSku" style="" />
        <GoodsBaseInfoV2
          :goods-sku="goodsSku"
          :spu-info="spuInfo"
          :integral-flag="integralFlag"
          :shoppingFlag="shoppingFlag"
          :sku-list="skuList"
          style=""
        />
        <div class="goods-select">
          <div class="goods-select-item" @click="handleSku">
            <div class="label">选择</div>
            <div class="content">
              <div class="checked-value flex">
                <div
                  class="checked-item overflow-1"
                  v-for="(item, index) in goodsSpecData"
                  :key="index"
                >
                  <template v-if="item.checked">
                    <div v-for="(item2, index2) in item.leaf" :key="index2">
                      <div v-if="item.checked == item2.id">{{ item2.value }}/</div>
                    </div>
                  </template>
                </div>
                <div class="goods-unit">{{ skuNum }}{{ spuInfo.unit }}</div>
              </div>
            </div>
            <van-icon color="#A8A8A8" name="arrow" />
          </div>
          <div class="goods-select-item" @click="handleAdrress">
            <div class="label">配送</div>
            <div class="content">
              {{
                addressInfo.id
                  ? addressInfo.province +
                    ' ' +
                    addressInfo.city +
                    ' ' +
                    addressInfo.district +
                    ' ' +
                    addressInfo.address
                  : '请选择收货地址'
              }}
            </div>
            <van-icon color="#A8A8A8" name="arrow" />
          </div>
        </div>
        <div class="goods-content">
          <div class="goods-content-title">宝贝详情</div>
          <div v-html="content" class="details" />
        </div>
        <RecommendForYou />
        <GoodsList ref="goodsListRef" :listType="listType" />
      </div>
    </div>
    <action-bar
      :cart-num="cartNum"
      :saleable="spuInfo.saleable"
      @handleCart="handleCart"
      @handleBuy="handleBuy"
    />
    <goods-sku-component
      ref="goodsSkuRef"
      :spu-info="spuInfo"
      :sku-list="skuList"
      v-model:skuNum="skuNum"
      :goods-type="integralFlag ? true : false"
      :goods-sku="goodsSku"
      :goods-spec-data="spuInfo.specType === 'MORE' ? goodsSpecData : []"
      :modal-sku-type="modalSkuType"
      :addressInfo="addressInfo"
      @updateSpecSku="updateSpecSku"
      @updateSkuNum="updateSkuNum"
      @onConfirm="onConfirm"
      @selectAddress="handleAdrress"
    />
    <address-popup
      v-if="user"
      ref="addressPopupRef"
      :address-info="addressInfo"
      @updateAddress="updateAddress"
      @deleteAddress="deleteAddress"
      :goods-id="goodsId"
    />
    <OrderConfirmPopup
      v-if="user"
      ref="orderConfirmPopupRef"
      @updateSpecSku="updateSpecSku"
      @updateSkuNum="updateSkuNum"
      @addressChanged="onAddressChanged"
    />
  </div>
</template>

<script setup>
import { goodsDetail } from '@/api/goods'
import { goodsCartNumber, addShoppingCart } from '@/api/shopping-cart'
import { getAddressDefault } from '@/api/address'
import { formatHtml } from '@/utils/common'
import { showToast, showSuccessToast } from 'vant'
import ActionBar from '../components/ActionBar'
import GoodsSkuComponent from '@/components/GoodsSku'
import AddressPopup from '../components/AddressPopup'
import GoodsNav from './components/GoodsNav'
import OpenMemberV2 from './components/OpenMemberV2'
import GoodsBaseInfoV2 from './components/GoodsBaseInfoV2'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
import RecommendForYou from '@/components/GoodsList/RecommendForYou.vue'
import OrderConfirmPopup from '@/components/OrderConfirmPopup/Index.vue'
const store = useStore()
const user = computed(() => store.getters.userInfo)
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const integralFlag = Number(route.query.integralFlag)
let { skuId, shoppingFlag, packageId } = route.query
const cartNum = ref(0)
const skuNum = ref(0)
const goodsId = ref(route.query.goodsId ? route.query.goodsId : route.params.goodsId)
const content = ref('')
const skuList = ref([])
const goodsSkuRef = ref(null)
const addressPopupRef = ref(null)
const goodsSpecData = ref([])
const modalSkuType = ref('select')
const goodsRef = ref(null)
const goodsListRef = ref()
const listType = { type: 'region', value: proxy.$global.GOODS_REGION_RECOMMEND }
const orderConfirmPopupRef = ref()
const data = reactive({
  goodsInfo: {},
  spuInfo: {},
  addressInfo: {},
  goodsSku: {},
  navBarStyle: {
    backgroundColor: '#ffffff',
    position: 'fixed',
  },
})
const { goodsInfo, spuInfo, addressInfo, goodsSku, navBarStyle } = toRefs(data)
const scrollTopValue = ref(-1)
const ANCHOR_SCROLL_TOP = 64
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
  let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP
  navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
}
watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)
// 监听商品id变化
watch(
  () => route.query,
  (newQuery, oldQuery) => {
    goodsId.value = newQuery.goodsId ? newQuery.goodsId : route.params.goodsId
    skuId = newQuery.skuId
    shoppingFlag = newQuery.shoppingFlag
    packageId = newQuery.packageId

    // 执行更新逻辑，例如重新获取数据
    initPage()
  }
)
const onBackClick = () => {
  router.go(-1)
}

// 加入购物车
const handleCart = () => {
  if (user.value) {
    // 加入购物车需要登录
    if (!integralFlag && shoppingFlag !== 'V') {
      handleSkuComm('cart')
    } else {
      showToast((shoppingFlag === 'V' ? '权益商品' : '积分商品') + '不支持加入购物车')
    }
  } else {
    proxy.appJS.appLogin()
  }
}

// 立即购买
const handleBuy = () => {
  if (user.value) {
    // 加入购物车需要登录
    handleSkuComm('buy')
  } else {
    // 登录
    proxy.appJS.appLogin()
  }
}
const handleSku = () => {
  handleSkuComm('select')
}
// 规格处理
const handleSkuComm = (type) => {
  modalSkuType.value = type
  if (goodsSku.value.skuId) {
    if (type === 'cart' && !goodsSpecData.value.find((t) => t.checked)) {
      goodsSkuRef.value.openForCart()
    } else if (type === 'select') {
      goodsSkuRef.value.show = true
    } else {
      if (goodsSku.value.stock < 1) {
        showToast(spuInfo.value.specType === 'MORE' ? '当前所选规格暂无库存' : '当前商品暂无库存')
        return
      }
      onConfirm()
    }
  } else {
    if (type === 'cart') {
      goodsSkuRef.value.openForCart()
    } else if (type === 'buy') {
      showOrderConfirmPopup()
    } else {
      goodsSkuRef.value.show = true
    }
  }
}
// onConfirm提交
const onConfirm = () => {
  const params = [
    {
      spuId: spuInfo.value.spuId,
      skuId: goodsSku.value.skuId,
      quantity: skuNum.value,
    },
  ]
  if (modalSkuType.value === 'cart') {
    // 加入购物车
    addShoppingCart({ carts: params }).then((res) => {
      showSuccessToast('添加购物车成功')
      getShoppingCartNumber()
    })
  } else if (modalSkuType.value === 'buy') {
    // 立即购买
    showOrderConfirmPopup()
  }
}

// 显示支付确认弹窗
const showOrderConfirmPopup = () => {
  const params = [
    {
      spuId: spuInfo.value.spuId,
      skuId: goodsSku.value.skuId,
      quantity: skuNum.value,
    },
  ]
  localStorage.setItem(
    proxy.$global.PARAM_ORDERCONFIRM,
    JSON.stringify({
      shoppingFlag: integralFlag ? 'I' : shoppingFlag === 'V' ? 'V' : 'N',
      packageId: packageId,
      msOrderSpus: params,
      addressInfo: addressInfo.value,
    })
  )
  localStorage.setItem(
    proxy.$global.PARAM_ORDERCONFIRM_EXTRA_INFO,
    JSON.stringify({
      spuInfo: spuInfo.value,
      goodsSku: goodsSku.value,
      goodsSpecData: goodsSpecData.value,
      skuList: skuList.value,
      skuNum: skuNum.value,
    })
  )
  orderConfirmPopupRef.value.open()
}

// 选择收获地址
const handleAdrress = () => {
  if (user.value) {
    addressPopupRef.value.open()
  } else {
    proxy.appJS.appLogin()
  }
}
// 更新地址
const updateAddress = (addr) => {
  sessionStorage.removeItem(proxy.$global.GOODS_DETAIL_ADDRESS)
  addressInfo.value = addr
}
// 删除地址
const deleteAddress = (id) => {
  if (addressInfo.value.id === id) {
    addressInfo.value = {}
  }
}
// 更新规格/sku
const updateSpecSku = (spec, sku) => {
  console.log('详情页收到规格改变：', spec, sku)
  goodsSpecData.value = spec
  goodsSku.value = sku
}
// 更新购物车数据
const updateSkuNum = (num) => {
  skuNum.value = num
}
const initPage = () => {
  let data = { spuId: goodsId.value }
  if (integralFlag) {
    // 积分模式
    data.point = 'INTEGRAL'
  }
  data.skuId = skuId
  data.shoppingFlag = shoppingFlag
  data.packageId = packageId
  goodsDetail(data).then((res) => {
    goodsInfo.value = res.data
    spuInfo.value = res.data.spu
    skuList.value = res.data.skuList
    goodsSpecData.value = res.data.spu.specList
    selectFirstSpec()
    skuNum.value = res.data.spu.minPurQty || 1
    goodsSku.value = res.data.skuList[0]
    // 规格描述
    content.value = formatHtml(res.data.spu.description)
    if (!spuInfo.value.albumPicsArray) {
      spuInfo.value.albumPicsArray = [spuInfo.value.thumbPics]
    }
    // 会员权益商品多规格，默认选中
    if ((shoppingFlag === 'V' || integralFlag) && res.data.spu.specType === 'MORE') {
      goodsSpecData.value[0].checked = goodsSpecData.value[0].leaf[0]?.id
    }
    // 滚动到顶部
    goodsRef.value.scrollTop = 0
  })
  if (user.value) {
    // 登录状态下加载购物车数量和收获地址
    getShoppingCartNumber()
    if (sessionStorage.getItem(proxy.$global.GOODS_DETAIL_ADDRESS)) {
      // 选择地址返回
      console.log('存在全局地址')
      addressInfo.value = JSON.parse(sessionStorage.getItem(proxy.$global.GOODS_DETAIL_ADDRESS))
    } else {
      console.log('不存在全局地址')
      getAddressDefault().then((res) => {
        if (res.data && JSON.stringify(res.data) !== '{}') {
          addressInfo.value = res.data
        }
      })
    }
  }
}
const selectFirstSpec = () => {
  const specs = JSON.parse(
    JSON.stringify(Array.isArray(goodsSpecData.value) ? goodsSpecData.value : [])
  )
  for (const spec of specs) {
    const leafs = Array.isArray(spec.leaf) ? spec.leaf : []
    if (leafs.length > 0) {
      if (!spec.checked) {
        spec.checked = leafs[0].id
      }
    }
  }
  goodsSpecData.value = specs
  console.log('goodsSpecData', specs)
}
// 处理选择地址的弹窗发送的地址改变事件
const onAddressChanged = (item) => {
  addressInfo.value = item
}
onMounted(() => {
  initPage()
})
// 获取购物车数量
const getShoppingCartNumber = () => {
  goodsCartNumber().then((res) => {
    cartNum.value = res.data.total
  })
}
</script>

<style lang="scss" scoped>
.goods-detail-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  overflow: hidden;
  overflow-y: auto;

  .open-member {
    margin-bottom: -26px;
    + .goods-base-info {
      margin-top: 0px;
    }
  }
  .goods-base-info {
    // margin-top: -15px;
  }

  &-content {
    .goods-imgs {
      position: relative;
      z-index: 0;

      .van-swipe-item {
        display: flex;
        height: 100%;
      }

      .back-img {
        width: 32px;
        height: 32px;
        position: absolute;
        left: 16px;
        top: 39px;
        display: block;
      }
    }

    .goods-select {
      margin: 0 10px 10px 10px;
      background: #ffffff;
      padding: 16px;
      border-radius: 8px;
      font-size: 14px;
      line-height: 20px;

      &-item {
        display: flex;
        align-items: center;

        .label {
          color: #666666;
          white-space: nowrap;
        }

        .content {
          flex: 1;
          margin-left: 16px;
          margin-right: 10px;
        }

        .goods-unit {
          white-space: nowrap;
        }
      }

      .goods-select-item + .goods-select-item {
        margin-top: 16px;
      }
    }
  }

  .goods-content {
    margin: 12px 10px;

    .goods-content-title {
      font-size: 14px;
      font-weight: 600;
      padding: 12px;
      background-color: #fff;
      border-radius: 5px 5px 0 0;
    }

    .divider-content {
      margin: 0 110px;
    }
  }

  :deep(.details p) {
    font-size: 15px;
  }
}

.custom-indicator {
  position: absolute;
  right: 10px;
  bottom: 18px;
  padding: 3px 11px;
  font-size: 12px;
  transform: scale(0.9);
  line-height: 16px;
  border-radius: 11px;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
}

.integral {
  background: #eaf5ff;
  padding: 4px 9px;
  display: flex;
  align-items: center;
  color: #4da6ff;

  .integral-tag {
    background: #4da6ff;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px;

    span {
      font-size: 12px;
    }

    span.text {
      color: #ffffff;
      margin-left: 5px;
      margin-right: 4px;
    }

    span.number {
      color: #4da6ff;
      background: #ffffff;
      height: 16px;
      line-height: 16px;
      border-radius: 0 3px 3px 0;
      padding: 0 4px;
    }
  }

  .tips {
    font-size: 12px;
    margin-left: 8px;
  }
}
.goods-detail-page-content {
  z-index: 0;
  position: relative;
}
.main-wrapper {
  margin-top: -15px;
  // background-color: #f6f6f6;
  z-index: 1;
  position: relative;
  // border-radius: 15px 15px 0 0;
  // background-image: linear-gradient(to bottom, rgba(246, 246, 246, 0.1), #f6f6f6);
  // background-image: linear-gradient(to bottom, rgba(246, 246, 246, 0.1), rgba(246, 246, 246, 0.1));
  // background-image: linear-gradient(to bottom, red, #f6f6f6);
  // background-size: 100% 15px;
  // background-repeat: no-repeat;
  // background-position: top;
  // border-radius: 15px 15px 0 0;
}
</style>
