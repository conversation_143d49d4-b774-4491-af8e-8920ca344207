<template>
  <div class="goods-base-info">
    <div class="goods-price">
      <div v-if="goodsSku.salesPrice" class="left">
        <!-- 会员权益商品 -->
        <template v-if="shoppingFlag === 'V'">
          <div class="sales">
            <span class="symbol">￥</span
            ><span class="integer">{{ ~~goodsSku.vipBenefitPrice }}</span
            ><span v-if="(goodsSku.vipBenefitPrice + '').split('.')[1]" class="decimal"
              >.{{ (goodsSku.vipBenefitPrice + '').split('.')[1] }}</span
            >
          </div>
          <!-- <div class="market" v-if="goodsSku.salesPrice !== goodsSku.vipBenefitPrice">¥{{ formatMoney(goodsSku.salesPrice) }}</div> -->
        </template>
        <template v-else>
          <div class="sales">
            <span class="symbol">￥</span><span class="integer">{{ ~~goodsSku.salesPrice }}</span
            ><span v-if="(goodsSku.salesPrice + '').split('.')[1]" class="decimal"
              >.{{ (goodsSku.salesPrice + '').split('.')[1] }}</span
            >
          </div>
          <!-- <div class="market" v-if="goodsSku.salesPrice !== goodsSku.marketPrice">¥{{ formatMoney(goodsSku.marketPrice) }}</div> -->
        </template>
      </div>
      <div v-else-if="skuList.length > 0" class="left">
        <!-- 会员权益商品 -->
        <template v-if="shoppingFlag === 'V'">
          <div class="sales">
            <span class="symbol">￥</span
            ><span class="integer">{{ ~~skuList[0].vipBenefitPrice }}</span
            ><span v-if="(skuList[0].vipBenefitPrice + '').split('.')[1]" class="decimal"
              >.{{ (skuList[0].vipBenefitPrice + '').split('.')[1] }}</span
            >
          </div>
          <!-- <div class="market" v-if="skuList[0].salesPrice !== skuList[0].vipBenefitPrice">¥{{ formatMoney(skuList[0].salesPrice) }}</div> -->
        </template>
        <template v-else>
          <div class="sales">
            <span class="symbol">￥</span><span class="integer">{{ ~~skuList[0].salesPrice }}</span
            ><span v-if="(skuList[0].salesPrice + '').split('.')[1]" class="decimal"
              >.{{ (skuList[0].salesPrice + '').split('.')[1] }}</span
            >
          </div>
          <!-- <div class="market" v-if="skuList[0].salesPrice !== skuList[0].marketPrice">¥{{ formatMoney(skuList[0].marketPrice) }}</div> -->
        </template>
      </div>
      <vip-price v-if="shoppingFlag !== 'V' && goodsSku?.vipPrice" :value="goodsSku?.vipPrice" />
      <!-- <div v-if="shoppingFlag !== 'V' && goodsSku?.vipPrice" class="right">
        <img class="vip-bg" src="@/assets/images/goods/详情页会员价背景.png" />
        <div class="vip-price">
          <span class="symbol">￥</span><span class="integer">{{ ~~goodsSku?.vipPrice }}</span
          ><span v-if="(goodsSku?.vipPrice + '').split('.')[1]" class="decimal"
            >.{{ (goodsSku?.vipPrice + '').split('.')[1] }}</span
          >
        </div>
      </div> -->
      <!-- <div class="sale-count">已售{{ spuInfo?.saleNum }}+</div> -->
    </div>
    <div class="goods-name">
      {{ spuInfo.spuTitle }}
    </div>
    <div class="goods-details" v-if="spuInfo.detailTitle">{{ spuInfo.detailTitle }}</div>
    <div class="integral" v-if="integralFlag">
      <div class="integral-tag">
        <span class="text">积分抵</span>
        <span class="number">{{ goodsSku.integralRate }}%</span>
      </div>
      <div class="tips">
        最高使用{{ goodsSku.integralValue }}积分抵扣{{
          (goodsSku.salesPrice * goodsSku.integralRate) / 100
        }}元
      </div>
    </div>
  </div>
</template>

<script setup>
import VipPrice from '@/components/VipPrice.vue'
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const props = defineProps({
  goodsSku: {
    type: Object,
    default: () => {},
  },
  integralFlag: {
    type: Number,
    default: false,
  },
  shoppingFlag: {
    type: String,
    default: '',
  },
  spuInfo: {
    type: Object,
    default: () => {},
  },
  skuList: {
    type: Array,
    default: () => [],
  },
})
/**
 * 数据部分
 */
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang="scss">
.goods-base-info {
  border-radius: 15px 15px 0 0;

  // background: linear-gradient(180deg, #fedbd2 0%, #f6f6f6 50%);
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.73), #f6f6f6),
    linear-gradient(to bottom, #f6f6f6, #f6f6f6);
  background-size: 100% 15px, 100% calc(100% - 15px);
  background-repeat: no-repeat;
  background-position: left top, left 15px;
  // height: 133px;
  position: relative;
  padding-bottom: 10px;
  // background: #f6f6f6;

  .goods-price {
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    overflow: hidden;
    height: 54px;
    box-sizing: border-box;

    .left {
      display: flex;
      align-items: center;
      margin-left: 10px;

      .sales {
        font-weight: 600;
        font-size: 25px;
        color: #f43727;
        .symbol {
          font-size: 15px;
        }
        .decimal {
          font-size: 15px;
        }
      }

      // .sales::first-letter {
      //   font-size: 15px;
      //   font-weight: bold;
      // }

      .small {
        font-weight: 400;
        font-size: 12px;
        zoom: 0.83;
      }

      .market {
        font-weight: 400;
        font-size: 12px;
        zoom: 0.83;
        color: #ffffff;
        margin-bottom: 2px;
        margin: 18px 0 2px 10px;
      }
    }
    .vip-price {
      margin-left: 8px;
    }

    // .right {
    //   min-width: 160px;
    //   height: 28px;
    //   border-radius: 14px;
    //   background: linear-gradient(171.92deg, #fff4e9 15.23%, #ffecd1 116.86%);
    //   display: flex;
    //   align-items: center;
    //   margin-left: 10px;

    //   .vip-bg {
    //     width: 85px;
    //     height: 28px;
    //     display: inline-block;
    //   }

    //   .vip-price {
    //     font-size: 16px;
    //     color: #333;
    //     font-weight: bold;
    //     margin-left: -12px;
    //     margin-right: 8px;
    //     .symbol {
    //       font-size: 12px;
    //     }
    //     .decimal {
    //       font-size: 12px;
    //     }
    //   }

    //   // .vip-price::first-letter {
    //   //   font-size: 12px;
    //   //   font-weight: bold;
    //   // }
    // }

    .sale-count {
      font-size: 12px;
      font-weight: 400px;
      color: #999;
      margin-left: auto;
      margin-right: 18px;
    }
  }

  .goods-name {
    font-weight: bold;
    font-size: 15px;
    color: #333;
    line-height: 21px;
    padding: 0 14px;
  }

  .goods-details {
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    margin-top: 9px 14px 0;
    padding: 0 14px;
  }

  .integral {
    padding: 16px 14px 0;
    display: flex;
    align-items: center;
    color: #4671eb;

    .integral-tag {
      background: #4671eb;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2px;

      span {
        font-size: 12px;
      }

      span.text {
        color: #ffffff;
        margin-left: 5px;
        margin-right: 4px;
      }

      span.number {
        color: #4671eb;
        background: #ffffff;
        height: 16px;
        line-height: 16px;
        border-radius: 0 3px 3px 0;
        padding: 0 4px;
      }
    }

    .tips {
      font-size: 12px;
      margin-left: 8px;
    }
  }
}
</style>
