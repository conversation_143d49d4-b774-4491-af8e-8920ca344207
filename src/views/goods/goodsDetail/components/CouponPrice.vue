<template>
  <div class="coupon-price">
    <div class="cxhd">促销活动</div>
    <div class="price">
      <div class="sales">
        ￥{{ formatMoney(goodsSku.vipBenefitPrice, 0) }}
        <span class="small">优惠前¥{{ goodsSku.salesPrice }}</span>
      </div>
      <div class="stock">剩余{{ goodsSku.stock }}张</div>
    </div>
    <div class="seckill">
      <div class="day">距秒杀结束<span>10</span>天</div>
      <van-count-down :time="time" format="HH:mm:ss">
        <template #default="timeData">
          <span class="block">{{ supplementZero(timeData.hours) }}</span>
          <span class="colon">:</span>
          <span class="block">{{ supplementZero(timeData.minutes) }}</span>
          <span class="colon">:</span>
          <span class="block">{{ supplementZero(timeData.seconds) }}</span>
        </template>
      </van-count-down>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const time = ref(0)
const props = defineProps({
  goodsSku: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
const getTomorrowAtHour = (hour) => {
  const now = new Date();
  const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  tomorrow.setHours(hour, 0, 0, 0); // 设置指定小时，分钟、秒和毫秒为0
  return tomorrow.getTime();
}
const supplementZero = (number) => {
  return number > 9 ? number : ('0' + number)
}
onMounted(() => {
  time.value = getTomorrowAtHour(0) - new Date().getTime();
})
</script>
<style scoped lang='scss'>
  .coupon-price{
    background: #FF001A;
    height: 54px;
    display: flex;
    align-items: center;
    .cxhd{
      font-size: 16px;
      color: #FFE1E4;
      line-height: 19px;
      width: 32px;
      margin-left: 16px;
    }
    .price{
      margin-left: 16px;
      flex: 1;
      .sales{
        font-weight: DIN DIN;
        font-size: 26px;
        color: #FFFFFF;
        .small{
          font-size: 12px;
        }
      }
      .sales::first-letter{
        font-size: 14px;
      }
      .stock{
        font-size: 13px;
        color: #FFFFFF;
        line-height: 15px;
        margin-top: 2px;
      }
    }
    .seckill{
      background: url('@/assets/images/goods/seckill-bg.png') no-repeat;
      width: 181px;
      height: 54px;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-left: 20px;
      box-sizing: border-box;
      .day {
        font-size: 12px;
        color: #CB5511;
        line-height: 14px;
        margin-bottom: 5px;
        span{
          font-family: DIN, DIN;
          font-size: 16px;
          color: #4A2B19;
          line-height: 19px;
          display: inline-block;
        }
      }
      
      .colon {
        display: inline-block;
        margin: 0 4px;
        font-size: 14px;
        color: #191919;
      }
      .block {
        display: inline-block;
        width: 19px;
        height: 18px;
        line-height: 18px;
        color: #fff;
        font-size: 14px;
        text-align: center;
        background-color: #191919;
        border-radius: 2px;
      }
    }
  }
</style>