<!-- 商品详情页的会员秒杀提醒 -->
<template>
  <div class="card">
    <div class="left">
      <img class="logo" src="@/assets/images/time-limit/闹钟.png" />
      <img class="logo-title" src="@/assets/images/time-limit/详情页会员秒杀.png" />
    </div>
    <TimeLimitTimer inverseColor />
  </div>
</template>

<script setup>
import TimeLimitTimer from '@/components/TimeLimit/TimeLimitTimer.vue'
</script>

<style lang="scss" scoped>
.card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 11px 18px;
  width: 100%;
  height: 70px;
  background: linear-gradient(180deg, #4671eb 0%, #fe9469 100%);
  // border-radius: 30px 30px 0px 0px;

  .left {
    display: flex;
    align-items: center;

    .logo {
      width: 20px;
      height: 20px;
    }

    .logo-title {
      margin-left: 3px;
      height: 17px;
    }
  }
}
</style>