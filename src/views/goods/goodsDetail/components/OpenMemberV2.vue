<template>
  <div class="open-member" v-if="goodsSku?.vipPrice && (!user || !user.vipCust)">
    <img class="vip-logo" src="@/assets/images/my/皇冠.png" />
    <div class="member-text" v-if="goodsSku.salesPrice">
      开通会员，本单预计省{{ formatMoney(goodsSku.salesPrice - goodsSku.vipPrice) }}元
    </div>
    <div class="open-btn" @click="handleOpen">
      开通会员
      <!-- <img class="open-btn-icon" src="@/assets/icons/arrow-right-red.png" /> -->
      <van-icon name="arrow" class="open-btn-icon" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  goodsSku: {
    type: Object,
    default: () => {},
  },
  skuList: {
    type: Array,
    default: () => [],
  },
})
const handleOpen = () => {
  if (user.value) {
    router.push('/save-money')
    // emit('kefu')
    // router.push('/member')
  } else {
    proxy.appJS.appLogin()
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {})
</script>
<style scoped lang="scss">
.open-member {
  display: flex;
  align-items: center;
  padding: 8px 26px 36px 18px;
  background: var(--primary-linear-to-bottom);
  border-radius: 15px 15px 0 0;

  .vip-logo {
    width: 15px;
    height: auto;
    display: block;
    margin-right: 2px;
    margin-bottom: 2px;
  }

  .member-text {
    flex: 1;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    background: linear-gradient(180deg, #ffe9d3 9.52%, #fff6eb 86.6%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    position: relative;
  }

  .open-btn {
    width: 81px;
    height: 22px;
    background: #fff4e9;
    border-radius: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: var(--primary-color);

    .open-btn-icon {
      // width: 5px;
      // height: 8px;
      font-size: 0.8em;
      margin-left: 2px;
    }
  }
}
</style>
