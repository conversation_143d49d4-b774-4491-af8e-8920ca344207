<template>
  <div class="phone-context">
    <phone-header />
    <div class="sku-container">
      <div class="title">充值金额</div>
      <div class="sku-list" v-if="skuList.length > 0">
        <div
          :class="`sku-item ${item.skuId === checkedSku.skuId ? 'active' : ''}`"
          v-for="(item, index) in skuList"
          @click="handleSku(item)"
        >
          <div class="price">{{ item.salesPrice }}<span>元</span></div>
          <div class="integral">{{}}</div>
          <div class="recommend-label" v-if="item.salesPrice === 100">
            <div class="text">推荐</div>
          </div>
        </div>
        <div class="sku-item" v-if="advertises[$global.AD_POSITION_PHONE_SKU]">
          <my-swiper :swiperDatas="advertises[$global.AD_POSITION_PHONE_SKU]"></my-swiper>
        </div>
      </div>
      <coupon-container :order-info="orderInfo" @showCoupon="emit('showCoupon')" />
      <div v-if="advertises[$global.AD_POSITION_PHONE]" class="bannar-wrapper">
        <!-- <animation-img /> -->
        <my-swiper :swiperDatas="advertises[$global.AD_POSITION_PHONE]"></my-swiper>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import PhoneHeader from './PhoneHeader'
import CouponContainer from './CouponContainer'
import MySwiper from '@/components/MySwiper'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const props = defineProps({
  skuList: {
    type: Array,
    default: () => [],
  },
  orderInfo: {
    type: Object,
    default: () => {},
  },
  advertises: {
    type: Object,
    default: () => {},
  },
})
const checkedSku = inject('checkedSku')
const emit = defineEmits(['updateSku', 'showCoupon'])
const handleSku = (item) => {
  emit('updateSku', item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {})
</script>
<style scoped lang='scss'>
.phone-context {
  background: linear-gradient(90deg, #ffdbaa 0%, #ffca81 100%);
  border-radius: 16px;
  border: 1px solid #ffffff;
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  z-index: 222;
  margin-top: -35px;
  .sku-container {
    background: #ffffff;
    border-radius: 16px 16px 0 0;
    border: 1px solid #ffded0;
    flex: 1;
    padding: 12px 16px 0;
    .title {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.8);
      line-height: 21px;
      display: inline-block;
      position: relative;
      display: inline-flex;
      justify-content: center;
      font-weight: bold;
    }
    .title::after {
      content: ' ';
      position: absolute;
      bottom: -5px;
      width: 20px;
      height: 4px;
      background: #4671eb;
      border-radius: 16px;
    }
    .sku-list {
      margin-top: 12px;
      display: flex;
      flex-wrap: wrap;
      .sku-item.active {
        border: 1px solid #4671eb;
      }
      .sku-item {
        width: 109px;
        height: 64px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        margin-right: 6px;
        box-sizing: border-box;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .price {
          font-family: DIN, DIN;
          font-weight: bold;
          font-size: 26px;
          color: rgba(0, 0, 0, 0.8);
          line-height: 30px;
          span {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.8);
            line-height: 19px;
          }
        }
        .recommend-label {
          width: 28px;
          height: 13px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 0;
          top: 0;
          background: url('@/assets/images/goods/recommend-label.png') no-repeat;
          background-size: 100% 100%;
          .text {
            font-size: 12px;
            zoom: 0.83;
            color: #ffffff;
          }
        }
      }
      .sku-item:nth-of-type(3n + 0) {
        margin-right: 0;
      }
    }
  }
  .bannar-wrapper {
    margin-top: 8px;
  }
}
</style>