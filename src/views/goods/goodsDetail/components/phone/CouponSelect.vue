<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    :close-on-click-overlay="false"
    :style="{ 'border-radius': '12px 12px 0 0', background: '#F5F6F6' }"
  >
    <div class="coupon">
      <div class="coupon-header">
        <div class="title">
          <div class="text">优惠券</div>
          <van-icon class="close" name="cross" @click="show = false" />
        </div>
      </div>
      <div class="list">
        <div v-if="useCoupon.length > 0" class="coupon-list">
          <coupon-item v-for="item in useCoupon" :data="item" @click="handleCoupon(item)" />
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import CouponItem from './CouponItem'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const active = ref(0)
const show = ref(false)
const props = defineProps({
  useCoupon: {
    type: Array,
    default: () => []
  },
  unavailableCoupon: {
    type: Array,
    default: () => []
  }
})
const checkedCoupon = inject('checkedCoupon')
const updateCoupon = inject('updateCoupon')
const emit = defineEmits(['resetOrder'])
const handleCoupon = (item) => {
  updateCoupon(item)
  show.value = false
  // 更新预下单
  emit('resetOrder', item.ticketNo)
}
defineExpose({ show })
</script>
<style scoped lang='scss'>
  .coupon{
    padding-bottom: 34px;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #ffffff;
    .coupon-header{
      position: absolute;
      top: 0;
      width: 100%;
      z-index: 9999;
    }
    .title{
      padding: 24px 0 16px;
      text-align: center;
      position: relative;
      font-size: 18px;
      color: #0E0E0E;
      font-weight: bold;
      background: #ffffff;
      .close{
        position: absolute;
        right: 17px;
        top: 17px;
        width: 12px;
        height: 12px;
      }
    }
  }
  .tab-list{
    display: flex;
    align-items: center;
    background: #ffffff;
    height: 40px;
    .tab-item{
      font-weight: bold;
      font-size: 13px;
      color: #84888F;
      flex: 1;
      text-align: center;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tab-item.active{
      color: #FE2C57;
      position: relative;
    }
    .tab-item.active::after{
      content: ' ';
      position: absolute;
      bottom: 0;
      width: 49px;
      height: 2px;
      background: #FE2C57;
    }
  }
  .coupon-list{
    padding: 0 16px;
  }
  .list{
    height: 450px;
    overflow-y: auto;
    margin-top: 60px;
  }
</style>