<template>
  <div class="header">
    <div class="header-left">
      <div class="text">
        轻享花特权
        <img src="@/assets/images/goods/notice.png">
      </div>
      <van-notice-bar :delay="2" color="#4A2B19" :scrollable="false">
        <van-swipe
          vertical
          class="notice-swipe"
          :autoplay="3000"
          :show-indicators="false"
        >
          <van-swipe-item v-for="(item, index) in activeList" :key="index">
            <span>{{item.phone}}</span>
          </van-swipe-item>
        </van-swipe>
      </van-notice-bar>
    </div>
    <div class="header-right" @click="handleMemebr">
      <span>去领取</span><img src="@/assets/images/goods/chevron-right.png">
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const activeList = [
  { phone: '开通会员免费领豪礼' }
 ]
const handleMemebr = () => {
  proxy.$menuRouter('/member')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .header{
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    &-left{
      flex: 1;
      display: flex;
      align-items: center;
      .text{
        font-weight: bold;
        font-size: 14px;
        color: #4A2B19;
        img{
          width: 12px;
          height: 12px;
        }
      }
      .notice-swipe{
        height: 40px;
        line-height: 40px;
      }
      .van-swipe-item{
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .van-notice-bar{
        background: none;
        flex: 1;
        padding: 0 10px;
        font-weight: bold;
      }
    }
    &-right{
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #4A2B19;
      font-weight: bold;
      img{
        width: 18px;
        height: 18px;
      }
    }
  }
</style>
