<template>
  <div class="phone-account">
    <div class="input-container">
      <div class="input">
        <van-field v-model="account" placeholder="请输入充值手机号" maxlength="11" />
      </div>
      <div class="line"></div>
      <div class="member" @click="handleMemebr">
        特权
      <van-icon name="arrow"></van-icon>
      </div>
    </div>
    <div class="tequan"></div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const account = ref('')
const handleMemebr = () => {
  proxy.$menuRouter('/member')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ account })
</script>
<style scoped lang='scss'>
  .phone-account{
    margin: 16px 8px 0;
    position: relative;
    .tequan{
      position: absolute;
      right: 0;
      top: 0;
      width: 81px;
      height: 71px;
      background: linear-gradient( 90deg, #FF9768 0%, #FFE4C7 100%);
      border-radius: 16px 16px 16px 16px;
      border: 1px solid rgba(255,255,255,0.9);
      z-index: 9;
      font-size: 14px;
      color: #4A2B19;
      padding-left: 25px;
      padding-top: 7px;
      font-weight: bold;
      box-sizing: border-box;
    }
    .input-container{
      height: 124px;
      background: url('@/assets/images/goods/phone-bg.png') no-repeat;
      background-size: 100% 100%;
      padding: 20px;
      z-index: 10;
      position: relative;
      box-sizing: border-box;
      .member{
        position: absolute;
        right: 8px;
        top: 8px;
        font-size: 14px;
        color: #4A2B19;
        line-height: 16px;
        font-weight: bold;
      }
      input {
        color: #FFFFFF;
      }
      .line{
        height: 1px;
        background: rgba(255,255,255,0.3);
        margin-top: 11px;
      }
    }
    box-sizing: border-box;
    :deep(.van-cell) {
      background: none;
      padding: 3px 0;
    }
    :deep(.van-cell__value){
      font-size: 20px;
    }
    :deep(.van-field__control) {
      color: #FFFFFF;
    }
    ::-webkit-input-placeholder { /* Chrome/Opera/Safari */ 
      font-weight: 400;
      color: #FFFFFF;
    }
  }
</style>