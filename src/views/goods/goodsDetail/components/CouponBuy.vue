<template>
  <div class="coupon-buy" :class="{ 'iphonex-bottom': isIphoneX }">
    <div class="btn" @click="emit('submitBuy')">
      <span v-if="goodsSku.vipBenefitPrice > 0" class="unit">￥</span>
      <span v-if="goodsSku.vipBenefitPrice > 0" class="price">{{ formatMoney(goodsSku.vipBenefitPrice, 0) }}</span>
      <span class="text">{{ goodsSku.vipBenefitPrice > 0 ? '立即购买' : '立即领取' }}</span></div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const store = useStore();
const route = useRoute();
const router = useRouter();
const isIphoneX = window.isIphoneX
const props = defineProps({
  goodsSku: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['submitBuy'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .coupon-buy{
    position: fixed;
    bottom: 0;
    width: calc(100% - 32px);
    padding: 10px 16px 6px;
    background: #ffffff;
    .btn{
      height: 40px;
      background: linear-gradient( 180deg, #FC882F 0%, #F94B28 100%);
      border-radius: 80px;
      display: flex;
      justify-content: center;
      font-family: DIN, DIN;
      font-size: 24px;
      color: #FFFFFF;
      .text{
        font-size: 16px;
        margin-left: 4px;
        margin-top: 12px;
      }
      .price{
        margin-top: 7px;
      }
      .unit{
        font-size: 12px;
        margin-top: 16px;
      }
    }
  }
  .coupon-buy.iphonex-bottom{
    // padding-bottom: 40px;
    padding-bottom: var(--safe-area-inset-bottom);
  }
</style>