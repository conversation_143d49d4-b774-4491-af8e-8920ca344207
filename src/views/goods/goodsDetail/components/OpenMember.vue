<template>
  <div class="open-member" v-if="goodsSku?.vipPrice && (!user || !user.vipCust)">
    <img class="vip-logo" src="@/assets/images/goods/vip-logo.png">
    <div class="member-text" v-if="goodsSku.salesPrice">
      成为会员下单，预计省{{ formatMoney(goodsSku.salesPrice - goodsSku.vipPrice) }}元
    </div>
    <div class="open-btn" @click="handleOpen">
      成为会员
      <van-icon name="arrow"></van-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const user = computed(() => store.getters.userInfo)
const props = defineProps({
  goodsSku: {
      type: Object,
      default: () => {}
  },
  skuList: {
      type: Array,
      default: () => []
  }
})
const handleOpen = () => {
  if (user.value) {
    // router.push('/save-money')
    router.push('/member')
  } else {
    proxy.appJS.appLogin()
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {

})
</script>
<style scoped lang='scss'>
  .open-member{
    display: flex;
    height: 36px;
    align-items: center;
    padding: 15px 12px 0;
    margin: -15px 14px 0;
    background: linear-gradient( 90deg, #FFE2B0 0%, #F9E1C4 100%);
    border-radius: 8px;
    .vip-logo{
      width: 16px;
      height: 16px;
      display: block;
      margin-right: 6px;
    }
    .member-text {
      font-weight: 500;
      font-size: 14px;
      color: #422F1F;
      flex: 1;
    }
    .open-btn{
      width: 74px;
      height: 22px;
      background: #FA572A;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #ffffff;
    }
  }
</style>