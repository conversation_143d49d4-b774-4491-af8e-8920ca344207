<template>
  <div class="goods-info">
    <div class="goods-name">{{ spuInfo.spuTitle }}</div>
    <div class="goods-intr">{{ spuInfo.detailTitle }}</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  spuInfo: {
    type: Object,
    default: () => {}
  }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .goods-info{
    margin: 12px 16px 0;
    .goods-name{
      font-size: 16px;
      color: #0E0E0E;
      line-height: 24px;
    }
    .goods-intr{
      font-size: 12px;
      color: rgba(0,0,0,0.3);
      line-height: 14px;
      margin-top: 2px;
    }
  }
</style>