<template>
  <div class="goods-base-info">
    <div class="goods-price">
      <template v-if="timeLimit">
        <div class="time-limit-header">
          <div class="time-limit-left">
            <div class="tip">秒杀价</div>
            <div class="unit">￥</div>
            <div class="price">{{ formatMoney(seckillSpu?.seckillPrice) }}</div>
            <div class="origin-price">原价￥{{ formatMoney(seckillSpu.price) }}</div>
          </div>
          <div class="sale-count">已售{{ seckillSpu.seckillSales }}+</div>
        </div>
      </template>
      <template v-else>
        <div v-if="goodsSku.salesPrice" class="left">
          <!-- 会员权益商品 -->
          <template v-if="shoppingFlag === 'V'">
            <div class="sales">
              ￥{{ formatValue(goodsSku.vipBenefitPrice, 'split')[0]
              }}<span class="small">.{{ formatValue(goodsSku.vipBenefitPrice, 'split')[1] }}</span>
            </div>
            <!-- <div class="market" v-if="goodsSku.salesPrice !== goodsSku.vipBenefitPrice">¥{{ formatMoney(goodsSku.salesPrice) }}</div> -->
          </template>
          <template v-else>
            <div class="sales">
              ￥{{ formatValue(goodsSku.salesPrice, 'split')[0]
              }}<span class="small">.{{ formatValue(goodsSku.salesPrice, 'split')[1] }}</span>
            </div>
            <!-- <div class="market" v-if="goodsSku.salesPrice !== goodsSku.marketPrice">¥{{ formatMoney(goodsSku.marketPrice) }}</div> -->
          </template>
        </div>
        <div v-else-if="skuList.length > 0" class="left">
          <!-- 会员权益商品 -->
          <template v-if="shoppingFlag === 'V'">
            <div class="sales">
              ￥{{ formatValue(skuList[0].vipBenefitPrice, 'split')[0]
              }}<span class="small"
                >.{{ formatValue(skuList[0].vipBenefitPrice, 'split')[1] }}</span
              >
            </div>
            <!-- <div class="market" v-if="skuList[0].salesPrice !== skuList[0].vipBenefitPrice">¥{{ formatMoney(skuList[0].salesPrice) }}</div> -->
          </template>
          <template v-else>
            <div class="sales">
              ￥{{ formatValue(skuList[0].salesPrice, 'split')[0]
              }}<span class="small">.{{ formatValue(skuList[0].salesPrice, 'split')[1] }}</span>
            </div>
            <!-- <div class="market" v-if="skuList[0].salesPrice !== skuList[0].marketPrice">¥{{ formatMoney(skuList[0].marketPrice) }}</div> -->
          </template>
        </div>
        <div v-if="shoppingFlag !== 'V' && goodsSku?.vipPrice" class="right">
          <div class="vip-text">会员价:</div>
          <div class="vip-price">¥{{ formatMoney(goodsSku?.vipPrice) }}</div>
        </div>
      </template>
    </div>
    <div class="goods-name">
      {{ spuInfo.spuTitle }}
    </div>
    <div class="goods-details" v-if="spuInfo.detailTitle">{{ spuInfo.detailTitle }}</div>
    <div class="integral" v-if="integralFlag">
      <div class="integral-tag">
        <span class="text">积分抵</span>
        <span class="number">{{ goodsSku.integralRate }}%</span>
      </div>
      <div class="tips">
        最高使用{{ goodsSku.integralValue }}积分抵扣{{
          formatMoney((goodsSku.salesPrice * goodsSku.integralRate) / 100)
        }}元
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const props = defineProps({
  goodsSku: {
    type: Object,
    default: () => {},
  },
  seckillSpu: {
    type: Object,
    default: () => {},
  },
  integralFlag: {
    type: Number,
    default: false,
  },
  shoppingFlag: {
    type: String,
    default: '',
  },
  spuInfo: {
    type: Object,
    default: () => {},
  },
  skuList: {
    type: Array,
    default: () => [],
  },
  // 是否是秒杀活动
  timeLimit: {
    type: Boolean,
    default: false,
  },
})
/**
 * 数据部分
 */
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
.goods-base-info {
  background: #f6f6f6;
  border-radius: 15px 15px 0 0;
  padding-bottom: 10px;
  position: relative;

  .goods-price {
    border-radius: 15px 15px 0 0;
    background: linear-gradient(180deg, #fedbd2 25%, #f6f6f6 100%);
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    box-sizing: border-box;
    padding-top: 12px;

    .left {
      display: flex;
      align-items: center;
      margin-left: 10px;

      .sales {
        font-weight: 600;
        font-size: 30px;
        color: #ffffff;
      }

      .sales::first-letter {
        font-size: 12px;
        transform: scale(0.9);
        transform-origin: left top;
        font-weight: 400;
      }

      .small {
        font-weight: 400;
        font-size: 12px;
        zoom: 0.83;
      }

      .market {
        font-weight: 400;
        font-size: 12px;
        zoom: 0.83;
        color: #ffffff;
        margin-bottom: 2px;
        margin: 18px 0 2px 10px;
      }
    }

    .right {
      width: 151px;
      height: 54px;
      position: relative;
      color: #f9e1c4;
      background: url('@/assets/images/goods/goods-vip-bg.png') no-repeat;
      background-size: 100% 100%;

      .vip-text {
        font-size: 14px;
        margin-top: 5px;
        margin-left: 18px;
        line-height: 19px;
      }

      .vip-price {
        font-size: 18px;
        line-height: 21px;
        font-weight: bold;
        text-align: center;
        text-align: center;
      }

      .vip-price::first-letter {
        font-size: 12px;
        font-weight: 400;
      }
    }
  }

  .goods-name {
    font-weight: bold;
    font-size: 18px;
    color: #333;
    line-height: 21px;
    padding: 0 14px;
  }

  .goods-details {
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    margin-top: 9px 14px 0;
    padding: 0 14px;
  }

  .integral {
    padding: 16px 14px 0;
    display: flex;
    align-items: center;
    color: #4671eb;

    .integral-tag {
      background: #4671eb;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2px;

      span {
        font-size: 12px;
      }

      span.text {
        color: #ffffff;
        margin-left: 5px;
        margin-right: 4px;
      }

      span.number {
        color: #4671eb;
        background: #ffffff;
        height: 16px;
        line-height: 16px;
        border-radius: 0 3px 3px 0;
        padding: 0 4px;
      }
    }

    .tips {
      font-size: 12px;
      margin-left: 8px;
    }
  }
}

.time-limit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0 16px 0 12px;

  .time-limit-left {
    display: flex;
    align-items: center;

    .tip {
      font-size: 14px;
      background-color: #4671eb;
      color: #fff;
      padding: 1px 3px;
      border-radius: 8px 2px 2px 8px;
      margin-left: 4px;
      margin-right: 2px;
    }

    .unit {
      font-size: 16px;
      font-weight: bold;
      color: #4671eb;
    }

    .price {
      font-size: 30px;
      font-weight: bold;
      color: #4671eb;
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .origin-price {
      font-size: 14px;
      color: #999999;
    }
  }

  .sale-count {
    font-size: 14px;
    color: #999999;
  }
}
</style>