<template>
  <div class="goods-detail-page" @scroll="onScrollChange" ref="goodsRef">
    <goods-nav :scroll-top-value="scrollTopValue" :nav-bar-style="navBarStyle" @onLeftClick="onBackClick" />
    <div class="goods-detail-page-content" v-if="spuInfo">
      <div class="goods-imgs">
        <van-swipe lazy-render :autoplay="3000" style="height: 375px;">
          <van-swipe-item v-for="item in spuInfo.albumPicsArray" :key="item">
            <img style="width:100%" :src="item">
          </van-swipe-item>
          <template #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
          </template>
        </van-swipe>
        <img class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick">
      </div>
      <goods-detail-time-limit-tip v-if="timeLimit"/>
      <goods-base-info-seckill
        style="margin-top: -20px"
        :goods-sku="goodsSku"
        :seckill-spu="seckillSpu"
        :spu-info="spuInfo"
        :integral-flag="integralFlag"
        :shoppingFlag="shoppingFlag"
        :sku-list="skuList"
        :timeLimit="timeLimit"
      />
      <open-member v-if="!timeLimit" :goods-sku="goodsSku" />
      <div class="goods-select">
        <div class="goods-select-item" @click="handleSku">
          <div class="label">选择</div>
          <div class="content">
            <div class="checked-value flex">
              <div class="checked-item overflow-1" v-for="(item, index) in goodsSpecData" :key="index">
                <template v-if="item.checked">
                  <div v-for="(item2,index2) in item.leaf" :key="index2">
                    <div v-if="item.checked == item2.id">{{ item2.value }}/</div>
                  </div>
                </template>
              </div>
              <div class="goods-unit">{{ skuNum }}{{ spuInfo.unit }}</div>
            </div>
          </div>
          <van-icon color="#A8A8A8" name="arrow" />
        </div>
        <div class="goods-select-item" @click="handleAdrress">
          <div class="label">配送</div>
          <div class="content">{{ addressInfo.id ? 
          (addressInfo.province + ' ' + addressInfo.city + ' ' + addressInfo.district + ' ' + addressInfo.address) : '请选择收货地址' }}</div>
          <van-icon color="#A8A8A8" name="arrow" />
        </div>
      </div>
      <div class="divider-content">
        <van-divider :style="{ color: '#999999', borderColor: '#999999' }">详情介绍</van-divider>
      </div>
      <div v-html="content" class="details" />
      <div class="divider-content">
        <van-divider :style="{ padding: '0 0 90px 0', color: '#999999', borderColor: '#999999' }">到底了</van-divider>
      </div>
    </div>
    <action-bar :show-bag="false" :cart-num="cartNum" :saleable="spuInfo.saleable" :showBag="false" @handleCart="handleCart" @handleBuy="handleBuy" />
    <goods-sku-component
      ref="goodsSkuRef"
      :spu-info="spuInfo"
      :sku-list="skuList"
      :sku-num="skuNum"
      :goods-type="integralFlag ? true : false"
      :goods-sku="goodsSku"
      :goods-spec-data="spuInfo.specType === 'MORE' ? goodsSpecData : []"
      :modal-sku-type="modalSkuType"
      @updateSpecSku="updateSpecSku"
      @updateSkuNum="updateSkuNum"
      @onConfirm="onConfirm"
    />
    <address-popup
      v-if="user"
      ref="addressPopupRef"
      :address-info="addressInfo"
      @updateAddress="updateAddress"
      :goods-id="goodsId"
    />
  </div>
</template>

<script setup>
  import { goodsDetail } from '@/api/goods'
  import { goodsCartNumber, addShoppingCart } from '@/api/shopping-cart'
  import { getAddressDefault } from '@/api/address'
  import { formatHtml } from '@/utils/common'
  import { showToast, showSuccessToast } from 'vant'
  import ActionBar from '../components/ActionBar'
  import GoodsSkuComponent from '@/components/GoodsSku'
  import AddressPopup from '../components/AddressPopup'
  import GoodsBaseInfoSeckill from './components/GoodsBaseInfoSeckill'
  import GoodsNav from './components/GoodsNav'
  import OpenMember from './components/OpenMember'
  import GoodsDetailTimeLimitTip from './components/GoodsDetailTimeLimitTip'
  import { getSeckillGoodsDetail } from '@/api/seckill'
  import { useSeckillTimer } from '@/hooks/useSeckillTimer'
  import useNeedVip from '@/hooks/useNeedVip.js'

  const needVip = useNeedVip()
  const { currentSessionType } = useSeckillTimer() // 当前秒杀状态
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const { proxy } = getCurrentInstance()
  const route = useRoute()
  const router = useRouter()
  const integralFlag = Number(route.query.integralFlag)
  const { skuId, shoppingFlag, packageId } = route.query 
  const cartNum = ref(0)
  const skuNum = ref(0)
  const goodsId = route.query.goodsId ? route.query.goodsId : route.params.goodsId
  const sessionId = route.query.sessionId
  const timeLimit = ref(route.query.timeLimit === '1')
  const isVip = ref(route.query.isVip === '1')
  const content = ref('')
  const skuList = ref([])
  const goodsSkuRef = ref(null)
  const addressPopupRef = ref(null)
  const goodsSpecData = ref([])
  const modalSkuType = ref('select')
  const goodsRef = ref(null)
  const data = reactive({
    goodsInfo: {},
    spuInfo: {},
    addressInfo: {},
    goodsSku: {},
    seckillSpu: {},
    navBarStyle: {
      backgroundColor: '#ffffff',
      position: 'fixed'
    },
  })
  const { goodsInfo, spuInfo, addressInfo, goodsSku, seckillSpu, navBarStyle } = toRefs(data)
  const scrollTopValue= ref(-1)
  const ANCHOR_SCROLL_TOP = 64
  const onScrollChange = ($e) => {
    scrollTopValue.value = $e.target.scrollTop
    let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
    navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
  }
  watch(() => store.getters.userInfo, (newValue, oldValue) => { // 用户变更重新获取数据
    if (newValue) {
      if (!(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())) {
        initPage()
      }
    }
  });
  const onBackClick = () => {
    router.go(-1)
  }
  // 加入购物车
  const handleCart = () => {
    // if (user.value) { // 加入购物车需要登录
    //   if (!integralFlag && shoppingFlag !=='V') {
    //     handleSkuComm('cart')
    //   } else {
    //     showToast((shoppingFlag==='V' ?'权益商品':'积分商品')+'不支持加入购物车')
    //   }
      
    // } else {
    //   proxy.appJS.appLogin()
    // }
  }
  // 立即购买
  const handleBuy = () => {
    if (user.value) { // 加入购物车需要登录
      handleSkuComm('buy')
    } else {
      // 登录
      proxy.appJS.appLogin()
    }
  }
  const handleSku = () => {
    handleSkuComm('select')
  }
  // 规格处理
  const handleSkuComm = async (type) => {
    // 检查秒杀是否只限制vip购买
    if (isVip.value) {
      await needVip()
    }

    // 检查秒杀是否
    if (currentSessionType.value !== 'ongoing') {
      showToast('未在活动时间')
      return
    }

    modalSkuType.value = type
    if (goodsSku.value.skuId) {
      if (type === 'select') {
        goodsSkuRef.value.show = true
      } else {
        if (seckillSpu.value.isSellOut === '1') {
          showToast(spuInfo.value.specType === 'MORE' ? '当前所选规格暂无库存' : '当前商品暂无库存')
          return
        }
        onConfirm()
      }
    } else {
      goodsSkuRef.value.show = true
    }
  }
  // onConfirm提交
  const onConfirm = async () => {
    // console.log('确认订单，场次：', sessionId)
    // console.log('秒杀商品信息：', seckillSpu.value)
    // console.log('商品规格：', goodsSku.value)
    const params = [{
      spuId: spuInfo.value.spuId,
      skuId: goodsSku.value.skuId,
      quantity: skuNum.value,
    }]
    localStorage.setItem(proxy.$global.PARAM_ORDERCONFIRM, JSON.stringify({
      shoppingFlag: integralFlag ? 'I': shoppingFlag === 'V' ? 'V': 'N',
      packageId: packageId,
      msOrderSpus: params,
      addressInfo: addressInfo.value,
      isSeckill: '1',
      mmSeckillDTO: {
        seckillSessionDateRelationId: sessionId,
        seckillSpuId: seckillSpu.value.id,
      }
    }))
    router.push('/order-confirm-seckill')
  }
  // 选择收获地址
  const handleAdrress = () => {
    if (user.value) {
      addressPopupRef.value.show = true
    } else {
      props.appJS.appLogin()
    }
  }
  // 更新地址
  const updateAddress = (addr) => {
    sessionStorage.removeItem(proxy.$global.GOODS_DETAIL_ADDRESS)
    addressInfo.value = addr
  }
  // 更新规格/sku
  const updateSpecSku = (spec, sku) => {
    goodsSpecData.value = spec
    goodsSku.value = sku
  }
  // 更新购物车数据
  const updateSkuNum = (num) => {
    skuNum.value = num
  }
  const initPage = () => {
    let data = { spuId: goodsId }
    if(integralFlag) { // 积分模式
      data.point = 'INTEGRAL'
    }
    data.skuId = skuId
    data.shoppingFlag = shoppingFlag
    data.packageId = packageId
    getSeckillGoodsDetail(data).then(res => {
      goodsInfo.value = res.data
      spuInfo.value = res.data.spu
      skuList.value = res.data.skuList
      goodsSpecData.value = res.data.spu.specList
      skuNum.value = res.data.spu.minPurQty || 1
      goodsSku.value = res.data.skuList[0]
      seckillSpu.value = res.data.seckillSpu
      // console.log('商品信息：', goodsSku.value)
      // console.log('秒杀商品信息：', seckillSpu.value)
      content.value = formatHtml(res.data.spu.description)
      if(!spuInfo.value.albumPicsArray) {
        spuInfo.value.albumPicsArray = [spuInfo.value.thumbPics]
      }
      // 会员权益商品多规格，默认选中
      if ((shoppingFlag ==='V' || integralFlag) && res.data.spu.specType === 'MORE') {
        goodsSku.value = res.data.skuList[0]
        goodsSpecData.value[0].checked = goodsSpecData.value[0].leaf[0]?.id
      }
    })
    if (user.value) { // 登录状态下加载购物车数量和收获地址
      getShoppingCartNumber()
      if (sessionStorage.getItem(proxy.$global.GOODS_DETAIL_ADDRESS)) { // 选择地址返回
        addressInfo.value = JSON.parse(sessionStorage.getItem(proxy.$global.GOODS_DETAIL_ADDRESS))
      } else {
        getAddressDefault().then(res => {
          if(res.data && JSON.stringify(res.data) !== '{}') {
            addressInfo.value = res.data
          }
        })
      }
    }
  }
  onMounted(() => {
    initPage()
  })
  // 获取购物车数量
  const getShoppingCartNumber = () => {
    goodsCartNumber().then(res => {
      cartNum.value = res.data.total
    })
  }
</script>

<style lang="scss" scoped>
  .goods-detail-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    overflow: hidden;
    overflow-y: auto;
    &-content{
      .goods-imgs{
        position: relative;
        .back-img{
          width: 32px;
          height: 32px;
          position: absolute;
          left: 16px;
          top: 39px;
          display: block;
        }
      }
      .goods-select{
        margin: 10px;
        background: #ffffff;
        padding: 16px;
        border-radius: 8px;
        font-size: 14px;
        line-height: 20px;
        &-item{
          display: flex;
          align-items: center;
          .label{
            color: #666666;
            white-space:nowrap;
          }
          .content{
            flex: 1;
            margin-left: 16px;
            margin-right: 10px;
          }
          .goods-unit{
            white-space:nowrap;
          }
        }
        .goods-select-item +.goods-select-item{
          margin-top: 16px;
        }
      }
    }
    .divider-content{
      margin: 0 110px;
    }
    :deep(.details p){
      font-size: 15px;
    }
  }
  .custom-indicator {
    position: absolute;
    right: 10px;
    bottom: 10px;
    padding: 3px 11px;
    font-size: 12px;
    transform: scale(0.9);
    line-height: 16px;
    border-radius: 11px;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
  }
  .integral{
    background: #EAF5FF;
    padding: 4px 9px;
    display: flex;
    align-items: center;
    color: #4DA6FF;
    .integral-tag{
      background: #4DA6FF;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2px;
      span{
        font-size: 12px;
      }
      span.text{
        color: #FFFFFF;
        margin-left: 5px;
        margin-right: 4px;
      }
      span.number{
        color: #4DA6FF;
        background: #ffffff;
        height: 16px;
        line-height: 16px;
        border-radius: 0 3px 3px 0;
        padding: 0 4px;
      }
    }
    .tips{
      font-size: 12px;
      margin-left: 8px;
    }
  }
</style>