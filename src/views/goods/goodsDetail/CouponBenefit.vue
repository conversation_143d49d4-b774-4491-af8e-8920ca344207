<template>
  <div class="goods-detail-page" @scroll="onScrollChange" ref="goodsRef">
    <goods-nav :scroll-top-value="scrollTopValue" :nav-bar-style="navBarStyle" @onLeftClick="onBackClick" />
    <div class="goods-detail-page-content" v-if="spuInfo">
      <div class="goods-imgs">
        <van-swipe lazy-render :autoplay="3000">
          <van-swipe-item v-for="item in spuInfo.albumPicsArray" :key="item">
            <img style="width:100%" :src="item">
          </van-swipe-item>
          <template #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
          </template>
        </van-swipe>
        <img class="back-img" src="@/assets/images/goods/back-icon.png" @click="onBackClick">
      </div>
      <coupon-price :goods-sku="goodsSku"></coupon-price>
      <coupon-name :spu-info="spuInfo"></coupon-name>
      <div v-html="content" class="details" />
    </div>
    <coupon-buy @submitBuy="submitBuy" :goods-sku="goodsSku" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { goodsDetail } from '@/api/goods'
import GoodsNav from './components/GoodsNav'
import CouponPrice from './components/CouponPrice'
import CouponName from './components/CouponName'
import CouponBuy from './components/CouponBuy'
import { formatHtml } from '@/utils/common'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const goodsId = route.query.goodsId ? route.query.goodsId : route.params.goodsId
const { skuId, shoppingFlag, packageId } = route.query 
const content = ref('')
const goodsRef = ref(null)
  const data = reactive({
    goodsInfo: {},
    goodsSku: {},
    spuInfo: {},
    navBarStyle: {
      backgroundColor: '#ffffff',
      position: 'fixed'
    },
  })
  const { goodsInfo, goodsSku, spuInfo, navBarStyle } = toRefs(data)
  const scrollTopValue= ref(-1)
  const ANCHOR_SCROLL_TOP = 64
  const onScrollChange = ($e) => {
    scrollTopValue.value = $e.target.scrollTop
    let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
    navBarStyle.value.backgroundColor = 'rgba(255, 255, 255, ' + opacity + ')'
  }
  const initPage = () => {
    let data = { spuId: goodsId }
    data.skuId = skuId
    data.shoppingFlag = shoppingFlag
    data.packageId = packageId
    goodsDetail(data).then(res => {
      goodsInfo.value = res.data
      spuInfo.value = res.data.spu
      goodsSku.value = res.data.skuList[0]
      content.value = formatHtml(res.data.spu.description)
      if(!spuInfo.value.albumPicsArray) {
        spuInfo.value.albumPicsArray = [spuInfo.value.thumbPics]
      }
    })
  }
  const onBackClick = () => {
    router.go(-1)
  }
  // 下单
  const submitBuy = () => {
    const params = [{
      spuId: spuInfo.value.spuId,
      skuId: goodsSku.value.skuId,
      quantity: 1
    }]
    localStorage.setItem(proxy.$global.PARAM_ORDERCONFIRM, JSON.stringify({
      shoppingFlag: 'V',
      packageId: packageId,
      msOrderSpus: params
    }))
    router.push('/order-confirm')
  }
  onMounted(() => {
    initPage()
  })
</script>
<style scoped lang='scss'>
  .goods-detail-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    overflow: hidden;
    overflow-y: auto;
    background: #FFFFFF;
    &-content{
      padding-bottom: 100px;
      .goods-imgs{
        position: relative;
        .back-img{
          width: 32px;
          height: 32px;
          position: absolute;
          left: 16px;
          top: 39px;
          display: block;
        }
      }
    }
    .divider-content{
      margin: 0 110px;
    }
    .details{
      margin: 22px 16px 0;
    }
    :deep(.details p){
      font-size: 12px;
      line-height: 15px;
    }
  }
  .custom-indicator {
    position: absolute;
    right: 10px;
    bottom: 10px;
    padding: 3px 11px;
    font-size: 12px;
    transform: scale(0.9);
    line-height: 16px;
    border-radius: 11px;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
  }
</style>