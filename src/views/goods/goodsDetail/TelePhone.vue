<template>
  <scroll-nav-bar-flex :pageName="spuInfo.spuTitle" :page-style="pageStyle" rgb-color="255,255,255">
    <phone-account ref="phoneAccountRef" />
    <phone-context
      :sku-list="goodsInfo.skuList"
      :order-info="orderInfo"
      @updateSku="updateSku"
      @showCoupon="showCoupon"
      :advertises="advertises"
    />
    <coupon-select
      :use-coupon="orderInfo.tickets"
      ref="couponSelectRef"
      @resetOrder="orderParams"
    />
    <phone-buy :order-info="orderInfo" @submitBuy="submitBuy" />
  </scroll-nav-bar-flex>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import ScrollNavBarFlex from '@/components/ScrollNavBarFlex'
import PhoneAccount from './components/phone/PhoneAccount'
import PhoneContext from './components/phone/PhoneContext'
import CouponSelect from './components/phone/CouponSelect'
import PhoneBuy from './components/phone/PhoneBuy'
import { goodsDetail } from '@/api/goods'
import { goodsPreOrder, saveGoodsOrder } from '@/api/goods-order'
import { getAdList } from '@/api/base'
import { showToast, showConfirmDialog } from 'vant'
import { validPhone } from '@/utils/common'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const couponSelectRef = ref(null)
const goodsId = route.query.goodsId ? route.query.goodsId : route.params.goodsId
const { skuId, shoppingFlag, packageId } = route.query 
const phoneAccountRef = ref(null)
const rechargeAccount = ref('')
const goodsRef = ref(null)
  const data = reactive({
    goodsInfo: {},
    spuInfo: {},
    checkedSku: {},
    orderInfo: {},
    msOrder: {},
    checkedCoupon: {},
    advertises: {}
  })
  const { goodsInfo, spuInfo, checkedSku, orderInfo, checkedCoupon, msOrder, advertises } = toRefs(data)
  const pageStyle = {
    'background': 'linear-gradient( 134deg, #FFF1EB 0%, #FFD5C3 100%) no-repeat',
    'background-size': `100% ${proxy.$px2rem('247px')}`,
    'background-position': '0 0'
  }
  provide('checkedSku', checkedSku)
  provide('checkedCoupon', checkedCoupon)
  // 更新选中优惠券
  const updateCoupon = (item) => {
    checkedCoupon.value = item
  }
  provide('updateCoupon', updateCoupon)
  const initPage = async () => {
    let data = { spuId: goodsId }
    await goodsDetail(data).then(res => {
      goodsInfo.value = res.data
      spuInfo.value = res.data.spu
      checkedSku.value = res.data.skuList[0]
      orderParams()
    })
  }
  // 更新 sku
  const updateSku = (item) => {
    checkedSku.value = item
    orderParams()// 更新订单参数
  }
  const showCoupon = () => {
    couponSelectRef.value.show = true
  }
  const onBackClick = () => {
    router.go(-1)
  }
  // 下单
  const submitBuy = () => {
    rechargeAccount.value = phoneAccountRef.value.account
    if (!rechargeAccount.value) {
      showToast('请输入充值' + (goodsInfo.value.spu.categoryGroup === 'TELE' ? '手机号' : '账号'))
      return
    }
    if (!validPhone(rechargeAccount.value)) {
      showToast('请输入正确的手机号码')
      return
    }
    showConfirmDialog({
      title: '提示',
      message:
        '请确保手机号无误，充值后将无法退款',
    })
      .then(() => {
        orderSubmit()
      })
      .catch(() => {
        // on cancel
      });
  }
  //订单确认
const orderConfirm = async () => {
  msOrder.value.totalAmount = orderInfo.value.msOrder.totalAmount
  msOrder.value.payAmount = orderInfo.value.msOrder.payAmount
  const ress = await saveGoodsOrder({ msOrder: msOrder.value })
  const orderId = ress.data.orderId
  if (ress.data.cashier === 'Y') {
    router.push({path: '/cashier', query: { orderId: orderId }})
  } else {
    //不需要付款
    router.push({path: '/my/goods-order-detail', query: { orderId: orderId.value }})
  }
}
// 发起下单
const orderSubmit = async () => {
  msOrder.value.consumeType = goodsInfo.value.spu.categoryGroup
  msOrder.value.virtualSubmitMsg.chargeAccount = rechargeAccount.value
  if (orderInfo.value.tickets.length > 0 && !checkedCoupon.value.ticketNo) {
    showConfirmDialog({
      title: '提示',
      message:
        '您有可用优惠券未选择，确认不使用吗？',
    }).then(() => {
      orderConfirm()
    }).catch(() => {
      // on cancel
    });
  } else {
    orderConfirm()
  }
}
// 下单参数
const orderParams = () => {
  msOrder.value = {
    shoppingFlag: 'N',
    msOrderSpus: [{
      spuId: checkedSku.value.spuId,
      skuId: checkedSku.value.skuId,
      quantity: 1
    }],
    virtualSubmitMsg: {}
  }
  if (checkedCoupon.value?.ticketNo) { // 带上优惠券
    msOrder.value.ticketNo = checkedCoupon.value.ticketNo
  }
  preOrderSubmit()
}
  // 预下单
  const preOrderSubmit =async () => {
    let submitParam = {
      msOrder: msOrder.value
    }
    const ress = await goodsPreOrder(submitParam)
    orderInfo.value = ress.data
  }
  onMounted(() => {
    initPage()
    getAdList({regionType: [ proxy.$global.AD_POSITION_PHONE_SKU, 
      proxy.$global.AD_POSITION_PHONE ]}).then(res => {
      advertises.value = res.data
    })
  })
</script>
<style scoped lang='scss'>
</style>