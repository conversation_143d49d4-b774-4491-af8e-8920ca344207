<template>
  <div class="order-confirm-page">
    <navigation-bar
      @onLeftClick="onBackClick"
      pageName="订单确认"
      :navBarStyle="{ fontWeight: 600, backgroundColor: '#f9f9f9' }"
    />
    <div class="order-confirm-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <template v-if="orderInfo.categoryGroup === 'GENERAL'">
        <!-- 支持物流 -->
        <div v-if="addressInfo.id" class="address-info" @click="changeAddress">
          <div class="content">
            <div class="name-phone">
              <div class="name">{{ addressInfo.contactName }}</div>
              <div class="phone">{{ addressInfo.contactTel }}</div>
            </div>
            <div class="address overflow-1">
              {{
                addressInfo.province + addressInfo.city + addressInfo.district + addressInfo.address
              }}
            </div>
          </div>
          <van-icon name="arrow" :size="20" />
        </div>
        <div v-else class="address-info-empty" @click="changeAddress">
          <div class="content">
            <img src="@/assets/images/goods/empty-address-icon.png" />
            <div class="text">请选择收货地址</div>
          </div>
          <van-icon class="arrow-icon" color="#999999" :size="20" name="arrow" />
        </div>
      </template>
      <div
        v-else-if="orderInfo.categoryGroup === 'DIRECT_ADD' || orderInfo.categoryGroup === 'TELE'"
        class="direct-wrapper"
      >
        <van-cell-group inset>
          <van-field
            v-model="virtualSubmitMsg.chargeAccount"
            label="充值账号"
            placeholder="请输入充值账号"
          />
        </van-cell-group>
      </div>
      <div class="goods-info">
        <order-goods-row :goods-list="orderInfo.msOrderSpus" />
      </div>
      <div class="order-total">
        <div class="order-tell">
          <div class="label">商品总价</div>
          <div class="value">￥{{ orderInfo.totalAmount }}</div>
        </div>
        <div class="order-tell" v-if="orderInfo.mbAmount">
          <div class="label">会员优惠</div>
          <div class="value text-red">-￥{{ orderInfo.mbAmount }}</div>
        </div>
        <div class="order-tell" v-if="orderInfo.tvAmount">
          <div class="label">优惠券抵扣</div>
          <div class="value text-red">-￥{{ orderInfo.tvAmount }}</div>
        </div>
        <div class="order-tell" v-if="orderInfo.categoryGroup === 'GENERAL'">
          <div class="label">运费</div>
          <div class="value">￥0</div>
        </div>
      </div>
      <coupon-container :order-info="orderData" @showCoupon="showCoupon" />
      <div class="order-total" v-if="orderInfo.shoppingFlag === 'I'">
        <div class="order-tell">
          <div class="label">
            使用积分<span class="text-gray text-sm"
              >(可用积分<span class="text-red">{{ orderInfo.integralInfo.deductionIntegral }}</span
              >, 剩余积分<span class="text-red">{{ orderInfo.integralInfo.availableIntegral }}</span
              >)</span
            >
          </div>
          <div class="label text-red">
            <van-stepper
              v-model="useIntegral"
              @change="changeIntegral"
              :max="orderInfo.integralInfo.deductionIntegral"
              step="100"
              button-size="20px"
              input-width="44px"
              :disabled="orderInfo.integralInfo.integralType === 'ALL' ? true : false"
            />
          </div>
        </div>
        <div class="order-tell">
          <div class="label">积分抵扣</div>
          <div class="label text-red">-￥{{ orderInfo.integralInfo.deductionAmount }}</div>
        </div>
      </div>
    </div>
    <div class="footer" :class="{ 'iphonex-bottom': isIphoneX }">
      <span class="footer-total">
        合计：<span class="amount theme-text">￥{{ orderInfo.payAmount }}</span>
      </span>
      <div class="btn theme-linear-gradient" @click="handlePay">
        {{ orderInfo.payAmount > 0 ? '去付款' : '确认' }}
      </div>
    </div>
    <coupon-select
      :use-coupon="orderData.tickets"
      ref="couponSelectRef"
      @resetOrder="orderParams"
    />
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import OrderGoodsRow from '@/components/OrderGoodsRow'
import { goodsPreOrder, saveGoodsOrder } from '@/api/goods-order'
import CouponContainer from './components/CouponContainer'
import CouponSelect from './components/CouponSelect'
import { divide } from '@/utils/common'
import { showToast, showConfirmDialog } from 'vant'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const useIntegral = ref(0)
const couponSelectRef = ref(null)
const data = reactive({
  confirmData: {},
  orderData: {},
  orderInfo: {},
  addressInfo: {},
  virtualSubmitMsg: {
    chargeAccount: '',
  },
  checkedCoupon: {},
})
const { confirmData, orderData, orderInfo, addressInfo, virtualSubmitMsg, checkedCoupon } =
  toRefs(data)
provide('checkedCoupon', checkedCoupon)
// 更新选中优惠券
const updateCoupon = (item) => {
  checkedCoupon.value = item
}
provide('updateCoupon', updateCoupon)
const changeAddress = () => {
  router.push({ path: '/my/address', query: { type: 1 } })
}
//  积分修改
const changeIntegral = (val) => {
  if (val) {
    const deductionAmount = divide(val, orderInfo.value.integralInfo.integralRate)
    orderInfo.value.integralInfo.deductionAmount = deductionAmount
    orderInfo.value.payAmount =
      orderInfo.value.totalAmount - orderInfo.value.tvAmount - deductionAmount
  }
}
// 提交订单
const handlePay = () => {
  const msOrder = {
    categoryGroup: orderInfo.value.categoryGroup,
    totalAmount: orderInfo.value.totalAmount,
    payAmount: orderInfo.value.payAmount,
    point: orderInfo.value.shoppingFlag === 'I' ? useIntegral.value : 0,
    ...confirmData.value,
  }
  if (orderInfo.value.categoryGroup === 'GENERAL') {
    // 需要物流
    if (addressInfo.value.id) {
      msOrder['addressId'] = addressInfo.value.id
      submitOrder(msOrder)
    } else {
      showToast('请选择地址')
    }
  } else if (
    orderInfo.value.categoryGroup === 'DIRECT_ADD' ||
    orderInfo.value.categoryGroup === 'TELE'
  ) {
    if (virtualSubmitMsg.value.chargeAccount) {
      msOrder['virtualSubmitMsg'] = virtualSubmitMsg.value
      submitOrder(msOrder)
    } else {
      showToast('请正确填写充值账号')
    }
  } else {
    submitOrder(msOrder)
  }
}
// 提交订单
const submitOrder = (msOrder) => {
  if (orderData.value.tickets.length > 0 && !checkedCoupon.value.ticketNo) {
    showConfirmDialog({
      title: '提示',
      message: '您有可用优惠券未选择，确认不使用吗？',
    })
      .then(() => {
        orderConfirm(msOrder)
      })
      .catch(() => {
        // on cancel
      })
  } else {
    orderConfirm(msOrder)
  }
}
const orderConfirm = (msOrder) => {
  saveGoodsOrder({ msOrder }).then((res) => {
    if (res.data.cashier === 'Y') {
      const orderId = res.data.orderId
      router.push({ path: '/cashier', query: { orderId: orderId } })
    } else {
      const orderId = res.data.orderId
      router.replace({ name: 'MyGoodsOrderDetail', query: { orderId: orderId } })
    }
  })
}
const showCoupon = () => {
  couponSelectRef.value.show = true
}
// 下单参数
const orderParams = () => {
  confirmData.value = JSON.parse(localStorage.getItem('param-orderConfirm'))
  if (checkedCoupon.value?.ticketNo) {
    // 带上优惠券
    confirmData.value.ticketNo = checkedCoupon.value.ticketNo
  }
  preOrderSubmit()
}
const preOrderSubmit = () => {
  goodsPreOrder({ msOrder: confirmData.value }).then((res) => {
    orderInfo.value = res.data.msOrder
    orderData.value = res.data
    const checkedAddress = sessionStorage.getItem(proxy.$global.ADDRESS_CHECKED)
    if (checkedAddress) {
      // 选择地址返回
      addressInfo.value = JSON.parse(checkedAddress)
      sessionStorage.removeItem(proxy.$global.ADDRESS_CHECKED)
    } else if (confirmData.value.addressInfo && !checkedAddress) {
      // 选择商品并选择地址进入
      addressInfo.value = confirmData.value.addressInfo ? confirmData.value.addressInfo : {}
    } else {
      // 再次购买
      addressInfo.value = res.data.msOrder.deliveryAddress ? res.data.msOrder.deliveryAddress : {}
    }
    if (!res.data.msOrder.deliveryAddress) {
      // 删除地址返回订单页
      addressInfo.value = {}
    }
    if (orderInfo.value.shoppingFlag === 'I') {
      // 积分抵扣商品默认积分
      useIntegral.value = orderInfo.value.integralInfo.deductionIntegral
    }
  })
}
onMounted(() => {
  orderParams()
})
const onBackClick = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.order-confirm-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f9f9f9;
  .nav-bar {
    border-bottom: none;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    position: relative;
    .address-info {
      margin: 10px;
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      .name-phone {
        display: flex;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        .name {
          margin-right: 20px;
        }
      }
      .address {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
        margin-top: 4px;
      }
    }
    .address-info-empty {
      margin: 10px;
      padding: 19px 0 22px 0;
      position: relative;
      background: #ffffff;
      border-radius: 8px;
      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 48px;
          height: 48px;
          display: block;
        }
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 22px;
      }
      .arrow-icon {
        position: absolute;
        right: 16px;
        top: calc(50% - 10px);
      }
    }
    .goods-info {
      margin: 10px;
      background: #ffffff;
      border-radius: 8px;
      padding: 0 16px;
      padding-bottom: 20px;
      padding-top: 10px;
      &-title {
        display: flex;
        align-items: center;
        padding: 19px 0;
        img {
          width: 26px;
          height: 26px;
          margin-right: 6px;
        }
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 22px;
        }
      }
    }
    .order-total {
      margin: 10px;
      padding: 0 16px;
      border-radius: 8px;
      background: #ffffff;
      .order-tell {
        display: flex;
        justify-content: space-between;
        padding: 14px 0;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
        .value {
          font-size: 15px;
          font-weight: 600;
        }
      }
    }
  }
}
.footer {
  background: #ffffff;
  padding: 10px 10px;
  position: fixed;
  bottom: 0;
  width: calc(100% - 20px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .footer-total {
    font-size: 14px;
    display: flex;
    align-items: center;
    line-height: 22px;
    .amount {
      font-size: 16px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      line-height: 22px;
    }
  }
  .btn {
    width: 100px;
    border-radius: 7px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    margin-left: 11px;
  }
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}
:deep(.van-cell-group--inset) {
  margin: 10px;
}
</style>
