<template>
  <van-popup class="adress-popup" v-model:show="show" round position="bottom">
    <img class="close-img" @click="show = false" src="@/assets/images/goods/colse.png" />
    <div class="title">配送至</div>
    <div class="address-list">
      <div
        :class="`item ${item.id === addressInfo.id ? 'active' : ''}`"
        v-for="(item, index) in addressList"
        :key="index"
        @click="handleAddress(item)"
      >
        <img
          v-if="item.id === addressInfo.id"
          src="@/assets/images/goods/address-icon-active.png"
        />
        <img v-else src="@/assets/images/goods/address-icon.png" />
        <div class="text">{{ item.province + item.city + item.district + item.address }}</div>
      </div>
    </div>
    <div class="footer">
      <div class="btn theme-linear-gradient" @click="chooseOther">选择其他收货地址</div>
    </div>
  </van-popup>
</template>

<script setup>
import { listAddress } from '@/api/address'
const isIphoneX = window.isIphoneX
const router = useRouter()
const show = ref(false)
const addressList = ref([])
const props = defineProps({
  addressInfo: {
    type: Object,
    defalut: () => {},
  },
  goodsId: {
    type: String,
    defalut: '',
  },
})
const emit = defineEmits(['updateAddress'])
const handleAddress = (item) => {
  show.value = false
  emit('updateAddress', item)
}
const chooseOther = () => {
  router.push({ path: '/my/address', query: { type: '2', goodsId: props.goodsId } })
}
onMounted(() => {
  listAddress({ pageNum: 1, pageSize: 100 }).then((res) => {
    addressList.value = res.data
  })
})
const open = () => {
  addressList.value = []
  show.value = true
  listAddress({ pageNum: 1, pageSize: 100 }).then((res) => {
    addressList.value = res.data
  })
}
defineExpose({ show, open })
</script>

<style lang="scss" scoped>
.adress-popup {
  display: flex;
  height: 60%;
  flex-direction: column;
}
.title {
  height: 55px;
  line-height: 55px;
  text-align: center;
  border-bottom: 1px solid #ededee;
  font-size: 16px;
  font-family: PingFang-SC-Bold, PingFang-SC;
  font-weight: bold;
  color: #222222;
}
.address-list {
  flex: 1;
  overflow-y: scroll;
  padding: 0 16px;
  .item {
    display: flex;
    align-items: center;
    margin-top: 20px;
    img {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    font-size: 14px;
    color: #666666;
  }
  .item.active {
    color: #222222;
  }
}
.iphonex-bottom {
  // padding-bottom: 44px !important;
  // padding-bottom: var(--safe-area-inset-bottom);
}
.close-img {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 16px;
  right: 16px;
}
.footer {
  background: #ffffff;
  padding: 7px 16px;
  position: sticky;
  bottom: 0;
  width: calc(100% - 32px);
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
  .btn {
    border-radius: 7px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
  }
}
.footer.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(7px + var(--safe-area-inset-bottom));
}
</style>