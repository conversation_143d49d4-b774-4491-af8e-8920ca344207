<template>
  <van-popup
    v-model:show="show"
    position="right"
    round
    :style="{ height: '100%', width: '8.8rem' }"
  >
    <div class="filter-content">
      <!-- <div class="filter-row">
        <div class="title">价格区间</div>
        <div class="content">
          <div class=""></div>
        </div>
      </div> -->
      <div class="filter-row">
        <div class="title">品牌</div>
        <div class="content">
          <van-grid :column-num="3" :gutter="10" :border="false">
            <van-grid-item class="custom-grid-item" v-for="item in listBrand" :key="item.brandId">
              <div
                @click="queryParams.brandId = item.brandId"
                :class="`grid-col ${queryParams.brandId === item.brandId ? 'active' : ''}`"
              >
                {{ item.brandName }}
              </div>
            </van-grid-item>
          </van-grid>
        </div>
      </div>
      <div class="filter-row" v-if="listCategory.length > 0">
        <div class="title">分类</div>
        <div class="content">
          <van-grid :column-num="3" :gutter="10" :border="false">
            <van-grid-item
              class="custom-grid-item"
              v-for="item in listCategory"
              :key="item.categoryId"
            >
              <div
                @click="queryParams.categoryId = item.categoryId"
                :class="`grid-col ${queryParams.categoryId === item.categoryId ? 'active' : ''}`"
              >
                {{ item.categoryName }}
              </div>
            </van-grid-item>
          </van-grid>
        </div>
      </div>
      <div class="filter-btn">
        <div class="reset-btn btn" @click="handleReset">重置</div>
        <div class="confirm-btn btn theme-linear-gradient" @click="handleQuery">确认</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { categoryList, brandList } from '@/api/goods'
const route = useRoute()
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
})
const data = reactive({
  queryParams: {
    categoryId: undefined,
    brandId: undefined,
  },
})
const { queryParams } = toRefs(data)
const listCategory = ref([])
const listBrand = ref([])
const show = ref(false)
// 初始化分类数据
const checkedCategory = (categoryId, categoryName) => {
  listCategory.value.forEach(function (item, index) {
    let i = 11 //最多缓存12条
    if (item.categoryId == categoryId) {
      listCategory.value.splice(index, 1)
      i++
    }
    if (index >= i) {
      listCategory.value.splice(index, 1)
    }
  })
  listCategory.value.unshift({
    categoryId,
    categoryName,
  })
}
const checkedBrand = (brandId, brandName) => {
  listBrand.value.forEach(function (item, index) {
    let i = 11 //最多缓存12条
    if (item.brandId == brandId) {
      listBrand.value.splice(index, 1)
      i++
    }
    if (index >= i) {
      listBrand.value.splice(index, 1)
    }
  })
  listBrand.value.unshift({
    brandId,
    brandName,
  })
}
// 初始化品牌
const initBrand = () => {
  brandList({ pageNum: 1, pageSize: 12 }).then((res) => {
    listBrand.value = []
    if (res.data && res.data.length > 0) {
      res.data.forEach((item, index) => {
        if (index < 12) {
          // 最多取12条
          listBrand.value.push(item)
        }
      })
    }
    if (route.query.brandId) {
      if (route.query.brandName) {
        checkedBrand(route.query.brandId, route.query.brandId)
      } else {
        brandList({ pageNum: 1, pageSize: 1, brandId: route.query.brandId }).then((res) => {
          checkedBrand(route.query.brandId, res.data[0].brandName)
        })
      }
    }
  })
}
// 初始化分类数据
const initCategroy = () => {
  categoryList({ recommend: 'Y' }).then((res) => {
    listCategory.value = []
    if (res.data && res.data.length > 0) {
      res.data.forEach((item, index) => {
        if (index < 12) {
          // 最多取12条
          listCategory.value.push(item)
        }
      })
    }
    if (route.query.categoryId) {
      if (route.query.categoryName) {
        checkedCategory(route.query.categoryId, route.query.categoryName)
      } else {
        categoryList({ id: route.query.categoryId }).then((res) => {
          const item = res.data.find(
            (item) => item.categoryId.toString() === route.query.categoryId.toString()
          )
          checkedCategory(route.query.categoryId, item?.categoryName ?? '未知分类')
        })
      }
    }
  })
}
const emit = defineEmits(['updateQueryParams'])
onMounted(() => {
  queryParams.value.brandId = route.query.brandId
  queryParams.value.categoryId = route.query.categoryId
  initCategroy()
  initBrand()
})
// 确定查询
const handleQuery = () => {
  emit('updateQueryParams', queryParams.value)
  show.value = false
}
const handleReset = () => {
  queryParams.value = {
    categoryId: undefined,
    brandId: undefined,
  }
  emit('updateQueryParams', queryParams.value)
  show.value = false
}
defineExpose({
  show,
})
</script>

<style lang="scss" scoped>
.filter-content {
  position: relative;
  padding-top: 20px;
  .filter-row {
    margin-top: 38px;
    .title {
      padding: 0 15px;
      font-size: 14px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #222222;
      line-height: 20px;
    }
    .content {
      padding: 0 5px;
      margin-top: 10px;
      .grid-col {
        height: 30px;
        background: #f7f8fa;
        border-radius: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        width: 100%;
        border: 1px solid #f7f8fa;
      }
      .grid-col.active {
        height: 28px;
        background: #ecf1fd;
        border: 1px solid #4671eb;
        display: flex;
        align-items: center;
        color: #4671eb;
      }
    }
  }
  .filter-btn {
    width: calc(100% - 36px);
    position: fixed;
    bottom: 0;
    padding: 0 18px 46px 18px;
    display: flex;
    justify-content: space-between;
    .btn {
      width: 140px;
      height: 36px;
      border-radius: 7px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
    }
    .reset-btn {
      border: 1px solid #d5d5d5;
      height: 34px;
      line-height: 34px;
    }
    .confirm-btn {
      color: #ffffff;
    }
  }
}
</style>