<template>
  <div class="action-bar">
    <div class="action-index item" @click="handleAction('index')">
      <img src="@/assets/images/goods/action-index.png" />
      <div class="text">首页</div>
    </div>
    <!-- <div
      v-if="getAppVersion() !== 'VERIFY'"
      class="action-service item"
      @click="handleAction('service')"
    >
      <img src="@/assets/images/goods/action-service.png" />
      <div class="text">客服</div>
    </div> -->
    <!-- <div class="action-cart item" v-if="props.showBag" @click="handleAction('cart')">
      <img src="@/assets/images/goods/action-cart.png">
      <div class="badge-wrapper theme-bg">{{ cartNum }}</div>
      <div class="text">购物车</div>
    </div> -->
    <div class="btn-wrapper">
      <!-- <div :class="`cart btn ${ saleable === 'N' ? 'disabled' : '' }`" v-if="props.showBag" @click="handleCart">加入购物车</div> -->
      <div :class="`buy btn ${saleable === 'N' ? 'disabled' : ''}`" @click="handleBuy">
        {{ saleable === 'Y' ? '立即购买' : '已售罄' }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAppVersion } from '@/utils/auth'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const router = useRouter()
const props = defineProps({
  cartNum: {
    type: Number,
    default: 0,
  },
  saleable: {
    type: String,
    default: 'Y',
  },
  showBag: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['handleCart', 'handleBuy'])
const handleCart = () => {
  if (props.saleable === 'Y') {
    emit('handleCart')
  }
}
const handleBuy = () => {
  if (props.saleable === 'Y') {
    emit('handleBuy')
  }
}
// action
const handleAction = (action) => {
  switch (action) {
    case 'index':
      router.push({ name: 'Main', hash: '#home' })
      break
    case 'service':
      proxy.$customerService()
      //proxy.appJS.appOtherWebView(proxy.$global.CUSTOMER_SERVICE)
      //router.push('/iframe-view/webview?title=在线客服&url='+proxy.$global.CUSTOMER_SERVICE)
      break
    case 'cart':
      proxy.$menuRouter('/shopping-cart')
      break
  }
}
</script>

<style lang="scss" scoped>
.action-bar {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-top: 8px;
  padding-left: 11px;
  padding-right: 11px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 8px);
  // width: calc(100% - 20px);
  background: #ffffff;
  align-items: center;
  .item {
    // margin: 5px 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    padding: 0 10px;
    // margin-right: 20px;
    img,
    .img-badge {
      width: 20px;
      height: 20px;
      display: block;
      margin: 0 auto;
    }
    .text {
      font-size: 12px;
      transform: scale(0.85);
      white-space: nowrap;
      line-height: 14px;
      margin-top: 4px;
    }
  }
  .action-cart {
    position: relative;
    .badge-wrapper {
      position: absolute;
      top: -5px;
      right: 0px;
      width: 18px;
      height: 18px;
      line-height: 18px;
      border-radius: 500px;
      font-size: 12px;
      transform: scale(0.85);
      transform-origin: center center;
      color: #ffffff;
    }
  }
  .btn-wrapper {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    .btn {
      flex: 1;
    }
    .btn.disabled {
      background: #cecece;
    }
    .cart {
      height: 38px;
      border-radius: 999vw;
      border: 1px solid #4671eb;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #4671eb;
    }
    .cart.disabled {
      border: 1px solid #cecece;
      color: #ffffff;
    }
    .buy {
      // max-width: 116px;
      height: 38px;
      // background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
      background-image: var(--primary-linear-to-bottom);
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fefffe;
    }
    .btn + .btn {
      margin-left: 10px;
    }
  }
}
</style>
