<template>
    <div class="nav-bar z-index-max" :class="{ 'iphonex-top': isIphoneX }">
      <div class="left" @click="onLeftClick">
        <van-icon size="22" name="arrow-left" />
      </div>
      <div class="center">
        <div v-if="!modelValue" class="page-title">商品详情</div>
        <div v-else class="child-nav">
          <div :class="`child-nav-item ${ curNav === 0 ? 'active' : '' }`" @click="clickNav(0)">商品</div>
          <div :class="`child-nav-item ${ curNav === 1 ? 'active' : '' }`" @click="clickNav(1)">详情</div>
        </div>
      </div>
      <div class="right">
        <img src="@/assets/icons/share.png">
      </div>
    </div>
  </template>
  
  <script setup>
    const props = defineProps({
      modelValue: {
        type: Number,
        default: 0
      }
    })
    const curNav = ref(0)
    const store = useStore()
    const isIphoneX = window.isIphoneX
    const emit = defineEmits(['onLeftClick','changeScrollTop'])
    const clickNav = (_index, execute=true) => {
      if (curNav.value !== _index) {
        curNav.value = _index
        if (execute) {
          emit('changeScrollTop', _index)
        }
      }
    }
    const onLeftClick = () => {
      store.commit('SET_ISIOSMOVEBACK', false) // 重置变量isIosMoveBack
      emit('onLeftClick')
    }
    defineExpose({ clickNav })
  </script>
  
  <style lang="scss" scoped>
    .nav-bar {
      width: 100%;
      height: 44px;
      display: flex;
      justify-content: space-between;
      // 适配手机 stateBar
      padding-top: 25px;
      align-items: center;
      flex-shrink: 0;
      background: #ffffff;
      .left {
        padding-left: 6px;
        width: 32px;
        display: flex;
        align-items: center;
      }
      .right {
        display: flex;
        padding-right: 9px;
        align-items: center;
        position: relative;
        img{
          width: 30px;
          height: 30px;
        }
      }
  
      .center {
        display: flex;
        height: 100%;
        flex-grow: 1;
        padding: 0 5px;
        .page-title {
          align-self: center;
          margin: 0 auto;
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          white-space:nowrap;
        }
        .child-nav{
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
          &-item{
            font-size: 13px;
            color: #999999;
            line-height: 19px;
            margin: 0 10px;
            position: relative;
          }
          &-item.active{
            font-weight: bold;
            color: #222222;
          }
          &-item.active::after{
            position: absolute;
            bottom: -5px;
            left:50%;
            margin-left:-10px;
            transition: all .5s;
            content: ' ';
            width: 21px;
            height: 3px;
            background: #222222;
          }
        }
      }
    }
  </style>