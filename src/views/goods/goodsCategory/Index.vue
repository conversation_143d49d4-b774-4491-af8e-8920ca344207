<template>
  <div class="category-page">
    <div class="category-page-nav-bar">
      <img @click="onBackClick" src="@/assets/icons/white-back.png" />
      <!-- <div class="nav-bar-search" @click="routerSearch">
        <div class="text">搜索商品名称</div>
      </div> -->
      <search-bar></search-bar>
    </div>
    <div class="category-page-content">
      <div class="parent-category" :class="{ 'iphonex-bottom': isIphoneX }">
        <div
          :class="`parent-category-item ${selectedIndex === item.categoryId ? 'active' : ''}`"
          v-for="(item, index) in listCategory"
          :key="item.categoryId"
          @click="changeCategory(item, index)"
        >
          <!-- <img src="" alt="" /> -->
          <img class="parent-category-item-img" :src="item.icon" />
          {{ item.categoryName }}
          <!-- <template v-if="selectedIndex === item.categoryId">
            <img class="active-icon" src="@/assets/images/goods/category-active.png" />
            <div class="corner top-corner"></div>
            <div v-if="listCategory.length > index + 1" class="corner bottom-corner"></div>
          </template> -->
        </div>
      </div>
      <div class="category-content" ref="scrollWrapperRef">
        <div
          v-for="(item, index) in listCategory"
          :key="index"
          class="category-content-item"
          :ref="setItemRef"
        >
          <div v-if="item.subCategoryList.length" class="parent-name">{{ item.categoryName }}</div>
          <div v-if="item.subCategoryList.length" class="sub-category-list">
            <div
              v-for="(item2, index2) in item.subCategoryList"
              class="sub-category-item"
              :key="index2"
              @click="searchGoods(item2)"
            >
              <img class="category-img" :src="item2.icon" />
              <div class="name">{{ item2.categoryName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup name="GoodsCategory">
import { categoryList } from '@/api/goods'
import { showToast } from 'vant'
import SearchBar from '@/views/home/<USER>/SearchBar.vue'
import recommedIcon from '@/assets/icons/goods-category-recommed.png'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const listCategory = ref([])
const selectedIndex = ref(0)
const scrollWrapperRef = ref(null)
const user = computed(() => store.getters.userInfo)
const itemRefs = ref([])
onMounted(() => {
  // 分类
  categoryList({ parentId: '0', navStatus: 'Y', querySub: 'Y' }).then((res) => {
    let list = []
    list = res.data
    // 查询推荐分类
    categoryList({ recommend: 'Y' }).then((ress) => {
      const recommedObj = {
        categoryId: 0,
        categoryName: '推荐分类',
        subCategoryList: ress.data,
        icon: recommedIcon,
      }
      // 插入默认推荐
      list.unshift(recommedObj)
      listCategory.value = list
    })
  })
})
const setItemRef = (el) => {
  if (el && itemRefs.value.length < listCategory.value.length) {
    itemRefs.value.push(el)
  }
}
// 选择分类
const changeCategory = (item, index) => {
  selectedIndex.value = item.categoryId
  // 滚动
  const refTop = itemRefs.value[index].offsetTop
  scrollWrapperRef.value.scrollTo({
    top: refTop - scrollWrapperRef.value.offsetTop - 21,
    left: 0,
    behavior: 'smooth',
  })
}
const searchGoods = (item) => {
  router.push({
    path: '/goods-list',
    query: { categoryId: item.categoryId, categoryName: item.categoryName },
  })
}
// 搜索
const routerSearch = () => {
  router.push('/base-search')
}
const onBackClick = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.category-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: linear-gradient(
    180deg,
    #5581f6 0%,
    #aabefb 5%,
    #d6dffa 10%,
    #e8edfa 20%,
    #f4f6fa 30%
  );
  &-nav-bar {
    box-sizing: border-box;
    width: 100%;
    padding: 40px 14px 10px 14px;

    display: flex;
    align-items: center;
    img {
      width: 9px;
      display: block;
      margin-left: 10px;
      margin-right: 12px;
    }
    // .nav-bar-search {
    //   flex: 1;
    //   height: 28px;
    //   background: #ecedee;
    //   border-radius: 15px;
    //   display: flex;
    //   align-items: center;
    //   margin: 0 13px;
    //   padding-left: 20px;
    //   .text {
    //     font-size: 12px;
    //     color: #868686;
    //   }
    // }
  }
  &-content {
    flex-grow: 1;
    // background: linear-gradient(180deg, #d6dffa 0%, #f4f6fa 20%) no-repeat;
    display: flex;
    height: 100%;
    overflow: hidden;
    .parent-category {
      width: 90px;
      // height: 100%;
      overflow-y: auto;
      border-radius: 8px;
      &-item {
        // height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        line-height: 20px;
        background: #fff;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #b8b8b8;
        position: relative;
        flex-direction: column;
        padding: 12px 0;
        .active-icon {
          position: absolute;
          width: 9px;
          height: 9px;
          top: 28px;
          right: 12px;
        }
        // .corner {
        //   width: 8px;
        //   height: 8px;
        //   position: absolute;
        //   background: #fff;
        // }
        // .bottom-corner {
        //   position: absolute;
        //   top: 100%;
        //   right: 0;
        //   z-index: 1;
        // }
        // .top-corner {
        //   position: absolute;
        //   bottom: 100%;
        //   right: 0;
        // }
        .parent-category-item-img {
          width: 50px;
          height: 50px;
          object-fit: cover;
          border-radius: 5000px;
        }
      }
      &-item.active {
        color: #333333;
        font-weight: 600;
        // background: rgba(255, 255, 255, 0);
        background: none;
      }
      // &-item.active:before {
      //   top: -8px;
      //   border-bottom-right-radius: 100%;
      // }
      // &-item.active:after,
      // &-item.active:before {
      //   content: ' ';
      //   position: absolute;
      //   background: #f1f2f3;
      //   width: 8px;
      //   height: 8px;
      //   right: 0;
      //   z-index: 2;
      // }
      // &-item.active:after {
      //   bottom: -8px;
      //   border-top-right-radius: 100%;
      // }
    }
    .category-content {
      flex: 1;
      height: 100%;
      overflow-y: auto;
      padding-top: 10px;
      &-item {
        margin-bottom: 12px;
        overflow: auto;
        .parent-name {
          font-size: 15px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: bold;
          color: #333333;
          line-height: 21px;
          margin-left: 9px;
        }
        .sub-category-list {
          margin: 5px 9px;
          padding: 16px 4px;
          display: flex;
          flex-flow: row wrap;
          background: #fff;
          border-radius: 12px;
          .sub-category-item {
            text-align: center;
            margin-bottom: 12px;
            flex-basis: 33.33%;
            // padding-right: 20px;
            box-sizing: border-box;
            .category-img {
              width: 60px;
              height: 60px;
              display: block;
              margin: 0 auto;
              border-radius: 8px;
            }
            .name {
              margin-top: 7px;
              font-size: 15px;
              // transform: scale(0.9);
              // transform-origin: center center;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #333333;
              line-height: 21px;
            }
          }
        }
      }
    }
  }
}
</style>