<template>
  <div class="login-page text-center">
    <img class="img-phone-top" src="@/assets/images/login-phone-big.png">
    <div class="phone-number">185****3056</div>
    <div class="login-btn" @click="getSystem">本机号一键登录</div>
    <div class="agreement flex">
      <van-radio-group v-model="agreement">
        <van-radio name="1" icon-size="15px"></van-radio>
      </van-radio-group>
      <div class="text">
         <span>我已阅读并同意中国移动认证服务条款、 </span>
         <span class="deepen">XXXXX用户注册协议、隐私政策</span>
         <span>，并授权XXX获取本机号码</span>
      </div>
    </div>
    <van-divider :style="{ color: '#A8A8A8', 'margin-top': '40px' }">其他登录方式</van-divider>
    <div @click="toRegister">
      <img class="img-phone-mini" src="@/assets/images/login-phone.png">
      <div class="other-text">其他登录方式</div>
    </div>
  </div>
</template>

<script setup name="Login">
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const loading = ref(false)
  const agreement = ref(false)
  const data = reactive({
    loginForm: {
      username: '',
      password: ''
    }
  })
  const { loginForm } = toRefs(data)
  const onSubmit = (values) => {
    loading.value = true
  }
  const toRegister = () => {
    router.push({
      name: 'Register',
      params: {
        routerType: 'push'
      }
    })
  }
  onMounted(() => {
    // proxy.$jsbridge.registerHandler('getToken', (data) => {
    //   alert('app主动调用h5的方法', data)
    // })
  })
  const getSystem = () => {
    // proxy.$jsbridge.callHandler('idCardFront',{'param': "你好，这是我JS传递给你的数据"}, (data) => {
    //   alert('h5主动调用app的方法', data)
    // })
  }
</script>

<style lang="scss" scoped>
  .login-page{
    height: 100%;
    background: #ffffff;
    padding: 30px;
    .img-phone-top {
      margin-top: 100px;
      margin-bottom: 21px;
      width: 50px;
      height: 50px;
    }
    .phone-number{
      font-size: 32px;
      font-weight: bold;
      font-family: DINAlternate-Bold, DINAlternate;
      color: #363636;
    }
    .login-btn{
      width: 320px;
      height: 44px;
      line-height: 44px;
      background: #FF3355;
      border-radius: 22px;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      margin-top: 100px;
    }
    .agreement{
      font-size: 13px;
      color: #A8A8A8;
      text-align: left;
      margin-top: 19px;
      .text{
        margin-top: -2px;
        margin-left: 5px;
        line-height: 18px;
        .deepen{
          color: #363636;
        }
      }
    }
    .img-phone-mini{
      width: 28px;
      height: 28px;
    }
    .other-text{
      font-size: 12px;
      color: #A8A8A8;
      margin-top: 9px;
    }
  }
</style>