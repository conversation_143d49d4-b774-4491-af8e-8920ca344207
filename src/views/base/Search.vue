<template>
  <div class="search-page">
    <navigation-bar :isDefault="false">
      <template #nav-left>
        <img class="back-img" src="@/assets/icons/back.png" @click="onBackClick" />
      </template>
      <template #nav-center>
        <form action="/">
          <van-search
            v-model="keywords"
            shape="round"
            placeholder="请输入搜索关键词"
            @search="onSearch"
            @cancel="onCancel"
            clearable
          >
            <template #left-icon>
              <img src="@/assets/icons/search-gray.png" />
            </template>
          </van-search>
        </form>
      </template>
      <template #nav-right>
        <div class="btn-cancel" @click="onBackClick">取消</div>
      </template>
    </navigation-bar>
    <div class="search-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div v-if="searchAction" class="keyword-list">
        <div
          v-for="item in searchList"
          :key="item.keys"
          class="keyword-item solid-bottom"
          @click="searchHandle(item.keywords)"
        >
          {{ item.keywords }}
        </div>
      </div>
      <div v-else class="content">
        <div v-if="searchHistory.length > 0" class="history-search">
          <div class="title">
            <span>搜索历史</span>
            <div @click="onClearHistory"><van-icon name="delete-o" color="#4671eb" /></div>
          </div>
          <div class="list">
            <div
              class="item"
              v-for="item in searchHistory"
              :key="item.name"
              @click="searchHandle(item.name)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="hot-search" v-if="hotKeywords.length > 0">
          <div class="title">
            <span>热门搜索</span>
          </div>
          <div class="list">
            <div
              :class="`item ${index < 2 ? 'hot theme-text' : ''}`"
              v-for="(item, index) in hotKeywords"
              :key="index"
              @click="searchHandle(item.keywords)"
            >
              {{ item.keywords }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import NavigationBar from '@/components/NavigationBar'
import { keywordsSearch } from '@/api/base'
import { showConfirmDialog, showToast } from 'vant'
const { proxy } = getCurrentInstance()
const router = useRouter()
const isIphoneX = window.isIphoneX
const SEARCH_HISTORY = 'xm-searchHistory'
const store = useStore()
const user = computed(() => store.getters.userInfo)
const searchHistory = ref([])
const hotKeywords = ref([])
let reloadTimes = null
const onBackClick = () => {
  router.go(-1)
}
const keywords = ref('')
const searchAction = ref(false)
const searchList = ref([])
const onSearch = () => {
  searchHandle(keywords.value)
}
watch(
  keywords,
  (newVal, oldVal) => {
    if (newVal.length > 0) {
      searchAction.value = true
      clearTimeout(reloadTimes)
      reloadTimes = null
      reloadTimes = setTimeout(() => {
        search()
      }, 500)
    } else {
      searchAction.value = false
    }
  },
  { immediate: true, deep: true }
)
const onCancel = () => {
  keywords.value = ''
  searchAction.value = false
}
const searchHandle = (value) => {
  if (value.trim()) {
    searchHistory.value.forEach(function (item, index) {
      let i = 9 //最多缓存10条
      if (item.name == value) {
        searchHistory.value.splice(index, 1)
        i++
      }
      if (index >= i) {
        searchHistory.value.splice(index, 1)
      }
    })
    searchHistory.value.unshift({
      name: value,
    })
    localStorage.setItem(SEARCH_HISTORY, JSON.stringify(searchHistory.value))
    // 跳转
    router.push({ path: '/goods-list', query: { keywords: value } })
  } else {
    showToast('请输入关键字')
  }
}
const onClearHistory = () => {
  showConfirmDialog({
    title: '提示',
    message: '确认删除全部历史记录？',
  })
    .then(() => {
      searchHistory.value = []
      localStorage.removeItem(SEARCH_HISTORY)
    })
    .catch(() => {
      // on cancel
    })
}
const search = () => {
  if (user.value) {
    keywordsSearch({ keywords: keywords.value }).then((res) => {
      searchList.value = res.data
    })
  }
}
onMounted(() => {
  if (user.value) {
    keywordsSearch().then((res) => {
      hotKeywords.value = res.data
    })
  }
  searchHistory.value = localStorage.getItem(SEARCH_HISTORY)
    ? JSON.parse(localStorage.getItem(SEARCH_HISTORY))
    : []
})
</script>

<style lang="scss" scoped>
.search-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    padding: 20px;
    .keyword-list {
      .keyword-item {
        font-size: 13px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 18px;
        color: #222222;
        padding: 15px 0;
      }
    }
    .content {
      .hot-search {
        margin-top: 30px;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: bold;
        line-height: 22px;
        color: #222222;
        img {
          width: 24px;
          height: 24px;
        }
      }
      .list {
        display: flex;
        flex-wrap: wrap;
      }
      .item {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 17px;
        color: #222222;
        padding: 4px 10px;
        border-radius: 13px;
        background: #f6f6f6;
        margin-top: 10px;
      }
      .item + .item {
        margin-left: 10px;
      }
    }
  }
  .van-search {
    width: 100%;
  }
  :deep(.van-search__content) {
    border: 1px solid #d5d5d5;
    background: none;
  }
  .btn-cancel {
    font-size: 13px;
  }
}
</style>