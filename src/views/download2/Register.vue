<template>
  <div class="register-container">
    <div class="header">
      <img class="logo" src="@/assets/images/download/logo2.png" />
      <span>轻享花如意会员 尽享更好精彩</span>
    </div>
    <div class="main">
      <div class="right-label">优质用户专享</div>
      <div class="main-tips">最高可借额度(元）</div>
      <div class="main-quota">200,000</div>
      <div class="mian-tips2">年利率7.2%起，借1万元一天息费低至1.9元</div>
    </div>
    <div class="form-wrapper">
      <div class="form-group">
        <input v-model="form.phone" type="tel" placeholder="输入本人手机号领取额度" />
      </div>
      <div class="form-group">
        <div class="input-code">
          <input
            ref="codeRef"
            type="tel"
            v-model="form.code"
            @input="resetInputVal"
            placeholder="请输入验证码"
          />
          <div class="line"></div>
          <div class="code-text" :class="`${times > 0 ? 'disabled' : ''}`" @click="getCode">
            {{ codeText }}
          </div>
        </div>
      </div>
      <div class="register-btn" @click="handleRegister">激活额度</div>
      <agreement-content v-model="form.checked" />
    </div>
    <div class="reason">
      <div class="reason-title">
        <div class="text">选择极享金的理由</div>
        <div class="line"></div>
      </div>
      <div class="reason-list">
        <div class="reason-item">
          <img src="@/assets/images/download/zgqd.png" />
          <div class="text">正规机构</div>
        </div>
        <div class="reason-item">
          <img src="@/assets/images/download/xxtm.png" />
          <div class="text">信息透明</div>
        </div>
        <div class="reason-item">
          <img src="@/assets/images/download/aqkk.png" />
          <div class="text">安全可靠</div>
        </div>
      </div>
    </div>
    <FooterInfo></FooterInfo>
  </div>
</template>

<script setup>
import { smsCode } from '@/api/base'
import { login } from '@/api/login'
import { showToast } from 'vant'
import { pvuvOutside } from '@/utils/pvuv'
import AgreementContent from './components/AgreementContent'
import FooterInfo from './components/FooterInfo'
import localforage from 'localforage'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const codeText = ref('发送验证码')
const disabled = ref(true)
const times = ref(0)
const codeRef = ref(null)
const iosExplain = ref(false)
const data = reactive({
  form: {
    phone: '',
    requestType: 'H5',
    partnerCode: '',
    code: '',
    checked: true,
  },
})
const { form } = toRefs(data)
watch(form.value, (newVal) => {
  if (
    newVal.checked &&
    newVal.phone &&
    newVal.phone.length === 11 &&
    newVal.code &&
    ('' + newVal.code).length === 6
  ) {
    disabled.value = false
  } else {
    disabled.value = true
  }
})
const getCode = () => {
  if (times.value === 0) {
    if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
      showToast('请填写正确手机号码')
      return
    }
    times.value = 60
    codeText.value = times.value + 's重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '发送验证码'
      }
    }, 1000)
    codeRef.value.focus()
    smsCode({ phone: form.value.phone, smsType: 'VERIFY_JYQ' })
  }
}
const handleRegister = () => {
  if (disabled.value) return
  login(form.value).then((res) => {
    window._czc.push(['_trackEvent', 'download', 'register', form.value.phone, 1, undefined])
    proxy.onToastSucc(() => {
      router.push('/download2/product')
    }, '激活成功，快去下载使用吧')
  })
}
const resetInputVal = (value) => {
  const code = '' + form.value.code
  if (code > 6) {
    form.value.code = code.substring(0, 6)
  }
}

onMounted(async () => {
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  form.value.partnerCode = route.query.channel
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  document.title = '轻享花'
})
</script>

<style lang="scss" scoped>
.register-container {
  background: linear-gradient(180deg, #ff571a 0%, rgba(255, 87, 26, 0) 100%);
  background-size: 100% 358px;
  background-position: 0 0;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: absolute;
  .header {
    display: flex;
    padding: 13px 0 0 19px;
    align-items: center;
    .logo {
      width: 56px;
      height: 18px;
      display: block;
    }
    span {
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      margin-left: 10px;
    }
  }
  .main {
    background: linear-gradient(180deg, #ffe7de 0%, #ffffff 23%);
    border-radius: 12px;
    margin: 14px 15px 0;
    position: relative;
    padding-bottom: 26px;
    .right-label {
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient(93deg, #583d10 0%, #201502 100%);
      border-radius: 0px 12px 0px 12px;
      width: 89px;
      height: 24px;
      line-height: 24px;
      display: flex;
      text-align: center;
      justify-content: center;
      font-size: 12px;
      color: #d7c6ac;
    }
    .main-tips {
      padding-top: 30px;
      text-align: center;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }
    .main-quota {
      font-size: 55px;
      color: #333333;
      margin-top: 23px;
      font-family: DINAlternate-Bold, DINAlternate;
      text-align: center;
    }
    .mian-tips2 {
      margin: 10px 30px;
      background: #fff8f2;
      border-radius: 20px;
      font-size: 13px;
      color: #ae8241;
      line-height: 15px;
      padding: 5px 0;
      text-align: center;
    }
  }
  .form-wrapper {
    padding: 20px;
    margin: 10px 15px 0;
    background: #ffffff;
    border-radius: 12px;
    .form-group {
      background: #f4f4f4;
      border-radius: 100px;
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      input {
        background: transparent;
        font-size: 14px;
        width: 100%;
      }
      .input-code {
        display: flex;
        align-items: center;
        flex: 1;
        input {
          flex: 1;
        }
        .line {
          height: 23px;
          width: 1px;
          background: #e7e7e7;
        }
        .code-text {
          font-size: 14px;
          color: #2f80ed;
          line-height: 20px;
          margin-left: 20px;
        }
      }
    }
    .form-group + .form-group {
      margin-top: 10px;
    }
  }
  .register-btn {
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    background: linear-gradient(174deg, #feaa39 0%, #ff5620 100%);
    box-shadow: 0px 4px 0px 0px #e03f07;
    border-radius: 60px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
  }
  .reason {
    background: #ffffff;
    border-radius: 12px;
    margin: 10px 15px 0;
    padding: 20px 0 30px 0;
    .reason-title {
      margin: 0 46px;
      position: relative;
      height: 42px;
      .text {
        width: 155px;
        background: #ffffff;
        position: absolute;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        color: #333333;
        left: 45px;
        top: -10px;
      }
      .line {
        background: linear-gradient(
          90deg,
          rgba(240, 240, 240, 0) 0%,
          #bfbfbf 36%,
          rgba(138, 138, 138, 0) 100%
        );
        height: 1px;
        margin-top: 20px;
      }
    }
    .reason-list {
      display: flex;
      justify-content: space-between;
      padding: 0 30px;
      .reason-item {
        text-align: center;
        img {
          width: 50px;
          height: 50px;
          display: block;
        }
        .text {
          margin-top: 10px;
          font-size: 14px;
          color: #333333;
          line-height: 16px;
        }
      }
    }
  }
}
</style>
