<template>
  <div class="download-container">
    <div class="header">
      <img class="logo" src="@/assets/images/download/logo2.png" />
      <span>轻享花如意会员 尽享更好精彩</span>
    </div>
    <div class="main">
      <div class="right-label">优质用户专享</div>
      <div class="main-tips">初审成功,额度估计(元)</div>
      <div class="main-quota">165,000</div>
      <div class="mian-tips2">年利率7.2%起，借1万元一天息费低至1.9元</div>
      <div class="download-btn" @click="handleDownload">
        立即下载领取<span v-if="countDown">（{{ countDown }}s）</span>
      </div>
      <div class="more-quota solid-top" @click="handleDownload">
        <img src="@/assets/images/download/product-logo.png" class="product-img" />
        <div class="text-info">
          <div class="p1">您的额度已下发！</div>
          <div class="p2">请下载APP领取更多额度</div>
        </div>
        <van-icon color="#B2B2B2" name="arrow" />
      </div>
    </div>
    <footer-info />
  </div>
</template>

<script setup>
import onDownload from '@/utils/downloadApp'
import FooterInfo from './components/FooterInfo'
import { pvuvOutside } from '@/utils/pvuv'
const route = useRoute()
const { proxy } = getCurrentInstance()
const iosExplain = ref(false)
const countDown = ref(5)
let interval = null
const handleDownload = () => {
  window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'download-btn'])
  onDownload()
  countDown.value = 0
  clearInterval(interval)
}
onMounted(async () => {
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  document.title = '轻享花'
  if (u.toLowerCase().indexOf('micromessenger') > -1 || u.toLowerCase().indexOf(' qq/') > -1) {
    document.getElementById('mask').style.display = 'block'
  } else {
    countDown.value = 5
    interval = setInterval(() => {
      countDown.value--
      if (countDown.value === 0) {
        clearInterval(interval)
        window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'auto'])
        onDownload()
      }
    }, 1000)
  }
})
</script>

<style lang="scss" scoped>
.download-container {
  background: linear-gradient(180deg, #ff571a 0%, rgba(255, 87, 26, 0) 100%);
  background-size: 100% 358px;
  background-position: 0 0;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .header {
    display: flex;
    padding: 13px 0 0 19px;
    align-items: center;
    .logo {
      width: 56px;
      height: 18px;
      display: block;
    }
    span {
      font-size: 14px;
      color: #ffffff;
      line-height: 16px;
      margin-left: 10px;
    }
  }
  .main {
    background: linear-gradient(180deg, #ffe7de 0%, #ffffff 23%);
    border-radius: 12px;
    margin: 14px 15px 0;
    position: relative;
    .right-label {
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient(93deg, #583d10 0%, #201502 100%);
      border-radius: 0px 12px 0px 12px;
      width: 89px;
      height: 24px;
      line-height: 24px;
      display: flex;
      text-align: center;
      justify-content: center;
      font-size: 12px;
      color: #d7c6ac;
    }
    .main-tips {
      padding-top: 30px;
      text-align: center;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }
    .main-quota {
      font-size: 55px;
      color: #333333;
      margin-top: 23px;
      font-family: DINAlternate-Bold, DINAlternate;
      text-align: center;
    }
    .mian-tips2 {
      margin: 10px 30px;
      background: #fff8f2;
      border-radius: 20px;
      font-size: 13px;
      color: #ae8241;
      line-height: 15px;
      padding: 5px 0;
      text-align: center;
    }
    .download-btn {
      margin: 30px 30px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48px;
      background: linear-gradient(174deg, #feaa39 0%, #ff5620 100%);
      box-shadow: 0px 4px 0px 0px #e03f07;
      border-radius: 60px;
      font-weight: 500;
      font-size: 18px;
      color: #ffffff;
    }
    .more-quota {
      margin-top: 30px;
      padding: 17px 15px;
      display: flex;
      align-items: center;
      .product-img {
        width: 36px;
        height: 36px;
        display: block;
      }
      .text-info {
        margin-left: 10px;
        flex: 1;
        .p1 {
          font-size: 14px;
          color: #000000;
          line-height: 16px;
          font-weight: bold;
        }
        .p2 {
          font-size: 12px;
          color: #a6a6a6;
          line-height: 14px;
          margin-top: 2px;
        }
      }
    }
  }
}
</style>
