<template>
  <div class="time-limit-page" @scroll="onScrollChange" ref="pageRef">
    <van-pull-refresh v-model="loading" @refresh="onRefresh">
      <div class="gradient-container">
        <van-sticky>
          <div class="time-limit-top" :class="{ iphonex: isIphoneX }">
            <div
              class="time-limit-top-nav"
              :class="{ 'iphonex-top': isIphoneX }"
              :style="navBarStyle"
            >
              <div class="time-limit-top-nav-left">
                <van-icon class="back-button" size="22" name="arrow-left" @click="onBackClick" />
                <img class="time-limit-title-image" src="@/assets/images/time-limit/会员秒杀.png" />
              </div>
              <div class="time-limit-top-nav-right">
                <Search v-model="searchText" @search="onSearch" @clear="onClear" />
              </div>
            </div>
          </div>
        </van-sticky>
        <div class="time-limit-page-content">
          <!-- 正品好物,隐私保护,极速送达,售后无忧 -->
          <header-words />
          <!-- 今日抄底 -->
          <today-card ref="todayCardRef" />
          <time-tip />
        </div>
      </div>
      <time-limit-goods ref="timeLimitGoodsRef" :searchText="searchText" />
    </van-pull-refresh>
  </div>
</template>
<script setup name="Home">
import HeaderWords from './components/HeaderWords.vue'
import TodayCard from './components/TodayCard'
import TimeTip from './components/TimeTip.vue'
import TimeLimitGoods from './components/TimeLimitGoods'
import Search from './components/Search'
import { useSeckillStore } from '@/store/seckill'
import { showDialog } from 'vant'
import { nextTick, watch } from 'vue'

const isIphoneX = window.isIphoneX
const store = useStore()
const router = useRouter()
const pageRef = ref()
const todayCardRef = ref()
const timeLimitGoodsRef = ref()
const data = reactive({
  navBarStyle: {
    backgroundColor: '',
    position: 'fixed',
  },
})
const { navBarStyle } = toRefs(data)
const seckillStore = useSeckillStore()
const searchText = ref('')

// 滚动值
const scrollTopValue = ref(-1)

watch(
  () => store.getters.userInfo,
  (newValue, oldValue) => {
    // 用户变更重新获取数据
    if (newValue) {
      if (
        !(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())
      ) {
        initPage()
      }
    }
  }
)

watch(
  () => seckillStore.currentSession,
  (val) => {
    if (!val) {
      showDialog({
        title: '提示',
        message: '秒杀活动已结束',
      })
        .then(() => {
          router.replace('/')
        })
        .catch(() => {})
    }
  }
)

onMounted(async () => {
  await initPage()
})

onActivated(() => {
  pageRef.value.scrollTop = scrollTopValue.value
})

const initPage = async () => {
  await todayCardRef.value.reload()
  await timeLimitGoodsRef.value.reload()
}

// 搜索
const onSearch = async () => {
  await timeLimitGoodsRef.value.reloadGoods()
}

// 清空搜索
const onClear = async () => {
  await nextTick()
  await timeLimitGoodsRef.value.reloadGoods()
}

// 滚动
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
}

// 下拉刷新
const loading = ref(false)
const onRefresh = async () => {
  await initPage()
  loading.value = false
}

const onBackClick = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.time-limit-page {
  width: 100%;
  min-height: 100vh;
  min-height: 100dvh;
  overflow: hidden;
  overflow-y: auto;

  .gradient-container {
    background: linear-gradient(
      180deg,
      #4671eb 0%,
      #f5b8b3 55%,
      #f6c7c3 64%,
      #f6d6d3 73%,
      #f6e6e5 85%,
      #f6f6f6 100%
    );
  }

  .time-limit-top.iphonex {
    // height: 88px;
    height: calc(69px + var(--safe-area-inset-top));
  }

  .time-limit-title-image {
    height: 21.5px;
  }

  .time-limit-top {
    box-sizing: border-box;
    height: 69px;
    background: linear-gradient(180deg, #4671eb, #f46e63);

    .time-limit-top-nav {
      width: 100%;
      height: 44px;
      display: flex;
      justify-content: space-between;
      padding-top: 25px;
      align-items: center;
      flex-shrink: 0;
      z-index: 1999;
      position: fixed;
      top: 0;

      .time-limit-top-nav-left {
        display: flex;
        align-items: center;
        color: #fff;

        .back-button {
          padding: 8px;
        }
      }

      .time-limit-top-nav-right {
        flex: 1;
        font-size: 16px;
        color: #ffffff;
        width: 220px;
        margin: 0 16px;

        .search {
          background: rgb(255, 255, 255, 0.3);
          border: 2px solid #fff;
          border-radius: 16px;
          font-size: 14px;
          display: flex;
          align-items: center;
          height: 32px;

          img {
            width: 19px;
            height: 19px;
            margin-left: 12px;
            margin-right: 12px;
          }
        }
      }
    }
  }

  .time-limit-page-content {
    box-sizing: border-box;

    .bannar-wrapper {
      background: linear-gradient(to bottom, #4671eb 0%, #4671eb 50%, #f5f5f5 50%, #f5f5f5 100%);
      margin-top: -1px;
      position: relative;
    }

    .bannar2-wrapper {
      margin: 15px 12px;
    }

    .icon-group {
      background: #ffffff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.02);
      border-radius: 8px;
      margin: 12px;
      padding: 13px 0;
    }

    .swiper-wrapper {
      margin: 12px 10px;
    }

    .recommend-title {
      text-align: center;
      margin: 14px;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
    }
  }
}

.ad-popup {
  position: relative;
  font-size: 0;

  .popup-img {
    max-width: 100%;
    height: auto;
  }

  .ad-popup-close {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 26px;
  }
}

.skeleton-swiper-img {
  width: 100%;
  height: 150px;
}

.skeleton-func {
  flex-direction: column;
  height: 180px;
  justify-content: space-between;
}

.skeleton-welfare {
  flex-direction: column;
}

:deep(.van-pull-refresh__head) {
  color: #4671eb !important;
}
</style>
