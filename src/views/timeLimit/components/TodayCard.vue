<template>
  <div class="today-card">
    <div class="header">
      <span class="today">今日抄底</span>
      <img style="width: 17px; height: 16.5px;" src="@/assets/images/time-limit/向下箭头.png" />
    </div>
    <div class="goods-list">
      <div class="goods" v-for="item of list" @click="() => onClickGoods(item)">
        <img class="goods-image" :src="item.thumbPics" />
        <div class="buy-button">
          <span class="currency">￥</span>
          <span class="price">{{ item.seckillPrice }}</span>
          <img class="tip" src="@/assets/images/time-limit/loot.png" />
        </div>
        <div class="origin">
          <span class="origin-currency">￥</span>
          <span class="origin-price">{{ item.price }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useSeckillStore } from '@/store/seckill'
import { getSeckillRecommandGoodsList } from '@/api/seckill'

const seckillStore = useSeckillStore()
const router = useRouter()
const list = ref([])
const loading = ref(false)
const myRoutePath = ref('')

watch(() => seckillStore.currentSession, async () => {
  if (router.currentRoute.value.path !== myRoutePath.value) {
    return
  }
  if (seckillStore.currentSession) {
    console.log('秒杀今日抄底卡片监听到当前场次变化', seckillStore.currentSession.id)
    await onCurrentSessionChanged()
  } else {
    list.value = []
    loading.value = false
  }
})

onMounted(async () => {
  myRoutePath.value = router.currentRoute.value.path
})

const onCurrentSessionChanged = async () => {
  await getList()
}

const getList = async () => {
  try {
    loading.value = true
    // 获取今日抄底的物品列表
    const res = await getSeckillRecommandGoodsList(seckillStore.currentSession.seckillSessionId)
    list.value = Array.isArray(res.data) ? res.data : []
  } finally {
    loading.value = false
  }
}

const reload = async () => {
  await getList()
}

const onClickGoods = async (item) => {
  // 跳转到商品详情页面，是秒杀活动
  router.push({
    path: '/goods-detail-seckill',
    query: {
      goodsId: item.id,
      sessionId: seckillStore.currentSession.id,
      timeLimit: '1',
      isVip: seckillStore.currentSession.isVip,
    }
  })
}

defineExpose({
  reload,
})
</script>

<style lang="scss" scoped>
.today-card {
  height: 155px;
  background: linear-gradient(180deg, #ffd3d1, #fff 25%);
  margin: 10px;
  border-radius: 8px;
  padding: 10px;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .today {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }

  .goods-list {
    display: flex;
    gap: 16px;
    overflow-x: auto;

    .goods {
      .goods-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
      }

      .buy-button {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fde9dd;
        border-radius: 12px;
        overflow: hidden;

        .currency {
          font-size: 10px;
          font-weight: bold;
          color: #f03927;
          margin-left: 6px;
          margin-bottom: -2px;
          margin-right: -2px;
        }

        .price {
          font-size: 14px;
          font-weight: bold;
          color: #f03927;
          flex: 1;
        }

        .tip {
          height: 20px;
        }
      }

      .origin {
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: line-through #CFCFCF;
        margin-top: 4px;

        .origin-currency {
          font-size: 10px;
          font-weight: bold;
          color: #ccc;
          margin-bottom: -2px;
          margin-right: -2px;
        }

        .origin-price {
          font-size: 13px;
          font-weight: bold;
          color: #ccc;
        }
      }
    }
  }
}
</style>