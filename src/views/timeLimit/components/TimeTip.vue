<template>
  <div class="time-tip">
    <div class="background">
      <img class="image" src="@/assets/images/time-limit/时间提示背景.png">
    </div>
    <div class="content">
      <div class="left">
        <div class="start-time">{{ currentTime }}</div>
        <TimeLimitTimer />
      </div>
      <div class="right">
        <div class="end-time">{{ nextTime }}</div>
        <div class="end-time-tip">{{ nextLabel }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import TimeLimitTimer from '@/components/TimeLimit/TimeLimitTimer.vue'
import { useSeckillStore } from '@/store/seckill'
import dayjs from 'dayjs'
import { computed } from 'vue'

const seckillStore = useSeckillStore()

const currentTime = computed(() => {
  if (seckillStore.currentSession) {
    const time = seckillStore.currentSession.startTime
    return time !== null ? dayjs(time).format('HH:mm') : ''
  } else {
    return ''
  }
})

const nextTime = computed(() => {
  if (seckillStore.nextSessionStartTime) {
    const time = seckillStore.nextSessionStartTime
    return time !== null ? dayjs(time).format('HH:mm') : ''
  } else {
    return ''
  }
})

const nextLabel = computed(() => {
  const now = seckillStore.now
  if (seckillStore.nextSessionStartTime) {
    const time = seckillStore.nextSessionStartTime
    if (time) {
      if (dayjs(time).isSame(now, 'day')) {
        return '即将开抢'
      } else if (dayjs(time).isSame(now.add(1, 'day'), 'day')) {
        return '明日秒杀'
      } else {
        return '下一场' + dayjs(time).format('M月D日')
      }
    } else {
      return ''
    }
  } else {
    return ''
  }
})
</script>

<style lang="scss" scoped>
.time-tip {
  position: relative;

  .background {
    // background-color: #ffd9d6;
    // height: 74px - 16px;
    // border-radius: 12px 12px 0 0;
    // margin-top: 16px;

    .image {
      width: 100%;
      // margin-top: -8px;
    }
  }

  .content {
    position: absolute;
    left: 0;
    top: 20px;
    display: flex;
    width: 100%;

    .left {
      flex: 1;

      .start-time {
        font-size: 17px;
        font-weight: bold;
        color: #F43F30;
        text-align: center;
        margin-left: 8px;
        margin-bottom: 4px;
      }
    }

    .right {
      flex: 1;

      .end-time {
        font-size: 17px;
        font-weight: bold;
        color: #333;
        text-align: center;
        margin-left: 8px;
        margin-bottom: 4px;
      }

      .end-time-tip {
        font-size: 12px;
        color: #6A4B48;
        opacity: 50%;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
}
</style>