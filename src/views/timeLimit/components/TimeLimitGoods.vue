<template>
  <van-list ref="listRef" v-model:loading="loading" :finished="finished" finished-text="——没有更多了——" @load="getList"
    :immediate-check="false">
    <time-limit-goods-card :goods-list="list" :round="true" />
  </van-list>
</template>

<script setup>
import TimeLimitGoodsCard from '@/components/TimeLimit/TimeLimitGoodsCard'
import { useSeckillStore } from '@/store/seckill'
import { getSeckillGoodsList } from '@/api/seckill'

const router = useRouter()
const seckillStore = useSeckillStore()
const props = defineProps({
  searchText: {
    type: String,
    default: '',
  }
})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  }
})
const { queryParams } = toRefs(data)
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const myRoutePath = ref('')
const listRef = ref()

watch(() => seckillStore.currentSession, async () => {
  if (router.currentRoute.value.path !== myRoutePath.value) {
    return
  }
  if (seckillStore.currentSession) {
    // console.log('秒杀商品页监听到当前场次变化', seckillStore.currentSession.id)
    await onCurrentSessionChanged()
  } else {
    list.value = []
    loading.value = false
    finished.value = true
  }
})

onMounted(async () => {
  myRoutePath.value = router.currentRoute.value.path
})

const onCurrentSessionChanged = async () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10
  }
  await getList()
}

const getList = async () => {
  try {
    const session = seckillStore.currentSession
    const res = await getSeckillGoodsList({
      ...queryParams.value,
      seckillSessionId: session.seckillSessionId,
      spuTitle: props.searchText,
    })
    const data = Array.isArray(res.data) ? res.data : []
    if (queryParams.value.pageNum === 1) {
      list.value = data
    } else {
      list.value = [...list.value, ...data]
    }
    if (list.value.length >= res.total) {
      finished.value = true
    } else {
      queryParams.value.pageNum++
    }
  } catch(err) {
    finished.value = true // 防止死循环
    throw err
  } finally {
    loading.value = false
  }
}

const reload = async () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  finished.value = false
  loading.value = true
  await seckillStore.reload()
}

const reloadGoods = async () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  finished.value = false
  loading.value = true
  await getList()
}

defineExpose({
  reload,
  reloadGoods,
})
</script>

<style lang="scss" scoped>
.goods-list-style-item {
  margin: 10px;
}

.goods-price {
  margin-top: 6px;

  .sale-price {
    font-size: 12px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #E9362E;

    .big-number {
      font-size: 18px;
    }
  }

  .market-price {
    margin-left: 5px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    text-decoration: line-through;
  }
}

.goods-info {
  margin-left: 14px;
}

.goods-info-top .goods-label {
  display: flex;

  .scale {
    border-radius: 2px;
    border: 1px solid #E9362E;
    font-size: 12px;
    transform: scale(0.8);
    transform-origin: left top;
    padding: 1px 3px;
    color: #E9362E;
    margin-top: 4px;
  }
}

.goods-sales-price {
  .big-number {
    font-size: 18px !important;
  }
}

.goods-title {
  .member-price {
    display: inline-block;
    font-size: 12px;
    transform: scale(0.8);
    transform-origin: left center;
    font-weight: 400;
    color: #FFFFFF;
    background: #E9362E;
    border-radius: 2px;
    padding: 1px 3px;
  }
}
</style>