<template>
  <div class="register-container">
    <img class="logo" src="@/assets/images/download/logo.png" />
    <div class="product-introduction">
      <div class="title">最高可借额度</div>
      <img class="quota-img" src="@/assets/images/download/quota.png" />
      <div class="decs">年利率7.2%起，借1万元一天息费低至1.9元</div>
      <img class="rate-img" src="@/assets/images/download/7.2.png" />
    </div>
    <div class="form-wrapper">
      <div class="form-group">
        <img src="@/assets/images/download/phone-icon.png" />
        <input v-model="form.phone" type="tel" placeholder="请输入手机号码" />
      </div>
      <div class="form-group">
        <img src="@/assets/images/download/code-icon.png" />
        <div class="input-code">
          <input
            ref="codeRef"
            type="tel"
            v-model="form.code"
            @input="resetInputVal"
            placeholder="请输入验证码"
          />
          <div class="code-text" :class="`${times > 0 ? 'disabled' : ''}`" @click="getCode">
            {{ codeText }}
          </div>
        </div>
      </div>
    </div>
    <div :class="`register-btn ${disabled ? 'disabled' : ''}`" @click="handleRegister">
      立即激活额度
    </div>
    <agreement-content v-model="form.checked" />
    <div class="reason">
      <img src="@/assets/images/download/reason.png" />
    </div>
    <footer-info />
  </div>
</template>

<script setup>
import { smsCode } from '@/api/base'
import { login } from '@/api/login'
import { showToast } from 'vant'
import { pvuvOutside } from '@/utils/pvuv'
import FooterInfo from './components/FooterInfo'
import AgreementContent from './components/AgreementContent'
import localforage from 'localforage'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const codeText = ref('发送验证码')
const disabled = ref(true)
const times = ref(0)
const codeRef = ref(null)
const iosExplain = ref(false)
const data = reactive({
  form: {
    phone: '',
    requestType: 'H5',
    partnerCode: '',
    code: '',
    checked: true,
  },
})
const { form } = toRefs(data)
watch(form.value, (newVal) => {
  if (
    newVal.checked &&
    newVal.phone &&
    newVal.phone.length === 11 &&
    newVal.code &&
    ('' + newVal.code).length === 6
  ) {
    disabled.value = false
  } else {
    disabled.value = true
  }
})
const getCode = () => {
  if (times.value === 0) {
    if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
      showToast('请填写正确手机号码')
      return
    }
    times.value = 60
    codeText.value = times.value + 's重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '发送验证码'
      }
    }, 1000)
    codeRef.value.focus()
    smsCode({ phone: form.value.phone, smsType: 'VERIFY_JYQ' })
  }
}
const handleRegister = () => {
  if (disabled.value) return
  login(form.value).then((res) => {
    window._czc.push(['_trackEvent', 'download', 'register', form.value.phone, 1, undefined])
    proxy.onToastSucc(() => {
      localforage.setItem('register-succcess', 1)
      router.replace('/download/product')
    }, '激活成功，快去下载使用吧')
  })
}
const resetInputVal = (value) => {
  const code = '' + form.value.code
  if (code > 6) {
    form.value.code = code.substring(0, 6)
  }
}

onMounted(async () => {
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  form.value.partnerCode = route.query.channel
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  document.title = '轻享花'
  const registered = await localforage.getItem('register-succcess')
  if (registered) {
    router.replace('/download/product')
  }
})
</script>

<style lang="scss" scoped>
.register-container {
  background: linear-gradient(180deg, #ffd8c2 0%, #ffe5c6 79%, #f3f3f3 100%);
  background-size: 100% 650px;
  background-position: 0 0;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .logo {
    width: 254px;
    height: 30px;
    display: block;
    margin: 34px 16px 0;
  }
  .product-introduction {
    width: 363px;
    height: 278px;
    margin: 24px auto 0;
    background: url('@/assets/images/download/content-bg.png') no-repeat;
    background-size: 100% 100%;
    .title {
      text-align: center;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      padding-top: 32px;
    }
    .quota-img {
      display: block;
      width: 181px;
      height: auto;
      margin: 22px auto 0;
    }
    .decs {
      margin-top: 22px;
      font-size: 13px;
      color: #ffffff;
      line-height: 18px;
      text-align: center;
    }
    .rate-img {
      margin: 39px 0 0 34px;
      width: 256px;
      height: 67px;
    }
  }
  .form-wrapper {
    padding: 18px 22px 0;
    .form-group {
      background: #fffafa;
      border-radius: 4px;
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 14px;
      img {
        width: 16px;
        height: 16px;
        display: block;
        margin-right: 10px;
      }
      input {
        background: transparent;
        font-size: 14px;
        width: 100%;
      }
      .input-code {
        display: flex;
        align-items: center;
        flex: 1;
        input {
          flex: 1;
        }
        .code-text {
          font-size: 14px;
          color: #4671eb;
          line-height: 20px;
        }
      }
    }
    .form-group + .form-group {
      margin-top: 14px;
    }
  }
  .register-btn {
    margin: 22px 22px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    background: linear-gradient(180deg, #4671eb 0%, #ff4019 100%);
    border-radius: 27px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
  }
  .register-btn.disabled {
    background: #abb5c4;
  }
  .reason {
    margin: 32px 16px 0;
    padding: 11px 16px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.03);
    border-radius: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
