<template>
  <div class="download-container">
    <div class="mask" id="mask">
      <div class="info">
        <span>点击右上角的</span>
        <div class="bg">
          <img src="@/assets/images/download/dian.png" />
        </div>
        <div class="flex align-center margin-top-xs">
          <span>选择在浏览器打开</span>
          <img src="@/assets/images/download/browser.png" class="browser" />
        </div>
        <img class="arrow" src="@/assets/images/download/arrow.png" />
      </div>
    </div>
    <img class="logo" src="@/assets/images/download/logo.png" />
    <div class="product-introduction">
      <div class="title">初审成功 额度估计</div>
      <img class="quota-img" src="@/assets/images/download/quota2.png" />
      <div class="decs1">您的额度已下发！</div>
      <div class="decs2">请<span>下载APP</span>领取更多额度</div>
      <div class="download-btn" @click="handleDownload">
        立即下载领取<span v-if="countDown > 0">（{{ countDown }}s）</span>
      </div>
    </div>
    <footer-info />
  </div>
</template>

<script setup>
import onDownload from '@/utils/downloadApp'
import FooterInfo from './components/FooterInfo'
import { pvuvOutside } from '@/utils/pvuv'
const route = useRoute()
const { proxy } = getCurrentInstance()
const iosExplain = ref(false)
const countDown = ref(5)
let interval = null
const handleDownload = () => {
  window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'download-btn'])
  onDownload()
  countDown.value = 0
  clearInterval(interval)
}
onMounted(async () => {
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  document.title = '轻享花'
  if (u.toLowerCase().indexOf('micromessenger') > -1 || u.toLowerCase().indexOf(' qq/') > -1) {
    document.getElementById('mask').style.display = 'block'
  } else {
    countDown.value = 5
    interval = setInterval(() => {
      countDown.value--
      if (countDown.value === 0) {
        clearInterval(interval)
        window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'auto'])
        onDownload()
      }
    }, 1000)
  }
})
</script>

<style lang="scss" scoped>
.download-container {
  background: linear-gradient(180deg, #ffd8c2 0%, #ffe5c6 79%, #f3f3f3 100%);
  background-size: 100% 498px;
  background-position: 0 0;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
  .mask {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    background: rgba($color: #000000, $alpha: 0.7);
    z-index: 9999;
    .info {
      position: relative;
      color: #ffffff;
      padding-top: 40px;
      padding-left: 30px;
      font-size: 18px;
      .bg {
        background: #ffffff;
        border-radius: 2px;
        color: #000;
        padding: 0 3px;
        display: inline-block;
        height: 15px;
        margin-left: 2px;
        img {
          width: 15px;
        }
      }
      .browser {
        width: 18px;
        height: 18px;
        display: inline-block;
        margin-left: 2px;
      }
      .arrow {
        width: 80px;
        position: absolute;
        right: 10px;
        top: 20px;
      }
    }
  }
  .logo {
    width: 292px;
    height: 30px;
    display: block;
    margin: 54px 16px 0;
  }
  .product-introduction {
    width: 363px;
    height: 378px;
    margin: 24px auto 0;
    background: url('@/assets/images/download/product-content-bg.png') no-repeat;
    background-size: 100% 100%;
    .title {
      text-align: center;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      padding-top: 55px;
    }
    .quota-img {
      display: block;
      width: 181px;
      height: auto;
      margin: 22px auto 0;
    }
    .decs1 {
      text-align: center;
      margin-top: 67px;
      font-weight: 600;
      font-size: 20px;
      color: #353535;
      line-height: 28px;
    }
    .decs2 {
      font-weight: 600;
      font-size: 16px;
      color: #353535;
      line-height: 22px;
      margin-top: 11px;
      text-align: center;
    }
    .download-btn {
      height: 44px;
      background: linear-gradient(180deg, #4671eb 0%, #ff4019 100%);
      border-radius: 27px;
      line-height: 44px;
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      margin: 20px 32px 0;
    }
  }
}
</style>
