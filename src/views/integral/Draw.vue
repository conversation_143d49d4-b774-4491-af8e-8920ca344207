<template>
  <div class="integral-draw-page">
    <navigation-bar pageName="" @onLeftClick="onBackClick"></navigation-bar>
    <div class="integral-draw-page-content">
      <div class="main-bg">
        <div class="tips">
          <div class="text">点击金蛋抽奖</div>
          <div class="triangle"></div>
        </div>
        <div class="golden-egg" @click="onDraw()">
          <img src="@/assets/images/integral/draw-egg.png">
          <div :class="`egg-animation ${active ? 'active': ''}`"></div>
          <img src="@/assets/images/integral/draw-egg.png">
        </div>
        <div class="draw-ask">（每次抽奖消耗{{ IntegralValue }}积分）</div>
        <draw-goods :integral="integralNumber" />
      </div>
      <div class="position">
        <img src="@/assets/images/integral/draw-rule.png" @click="goDrawRules">
        <img src="@/assets/images/integral/draw-log.png" @click="goDrawLog">
      </div>
    </div>
    <draw-result ref="drawResultRef" title="恭喜中奖" :lottery-item="lotteryItem" @onAgain="onDraw" />
  </div>
</template>

<script setup>
  import DrawResult from './components/DrawResult'
  import { showFailToast } from 'vant'
  import DrawGoods from './components/DrawGoods'
  import { getDrawIntegral, submitDraw } from '@/api/integral'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const active = ref(false)
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const integralNumber = computed(() => store.getters.integralValue)
  const drawResultRef = ref(null)
  const IntegralValue = ref(0)
  const data = reactive({
    lotteryItem: {},
    integralData: {}
  })
  const { lotteryItem, integralData } = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  const onDraw = () => {
    if(integralNumber.value >= IntegralValue.value) {
      active.value = true
      setTimeout(() => {
        submitDraw({ sessionType: 'GLOD_EGG' }).then(res => {
          store.dispatch('IntegralData') // 更新积分
          lotteryItem.value = res.data
          drawResultRef.value.show = true
          active.value = false
        }).catch(() => {
          active.value = false
        })
      }, 1000)
      
    } else {
      showFailToast('当前积分不足')
    }
  }
  const goDrawLog = () => {
    router.push('/integral/draw/log')
  }
  const goDrawRules = () => {
    router.push('/integral/draw/rules')
  }
  onMounted(() => {
    if (user.value) {
      getDrawIntegral().then(res => {
        IntegralValue.value = res.data.IntegralValue
      })
      store.dispatch('IntegralData') // 更新积分
    }
  })
</script>

<style lang="scss" scoped>
  @keyframes egg {
    0% {
      background: url('@/assets/images/integral/big-egg.png') no-repeat;
      background-size: 100% 100%;
    }
    50% {
      background: url('@/assets/images/integral/big-egg-open.png') no-repeat;
      background-size: 100% 100%;
    }
    100% {
      background: url('@/assets/images/integral/open-egg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .integral-draw-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #CE0B00;
    :deep(.nav-bar .left) {
      color: #ffffff;
    }
    .nav-bar {
      background: #FF4F3F !important;
      border-bottom: none;
    }
    &-content{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      position: relative;
      .main-bg{
        background: url('@/assets/images/integral/draw-bg.png') no-repeat;
        background-size: 100% 100%;
        padding-top: 160px;
        padding-bottom: 46px;
        .tips{
          display: flex;
          flex-direction: column;
          align-items: center;
          .text{
            width: 102px;
            height: 28px;
            background: #F63220;
            border-radius: 6px;
            font-size: 13px;
            color: #FFFFFF;
            height: 28px;
            text-align: center;
            line-height: 28px;
          }
          .triangle{
            width: 0px;
            height: 0px;
            border: 8px solid transparent;
            border-top-color: #F63220;
            margin-top: -1px;
          }
        }
        .golden-egg{
          display: flex;
          justify-content: center;
          margin-top: -20px;
          align-items: flex-end;
          img{
            width: 88px;
            height: 108px;
            margin-bottom: 20px;
          }
          .egg-animation{
            width: 200px;
            height: 200px;
            background: url('@/assets/images/integral/big-egg.png') no-repeat;
            background-size: 100% 100%;
            margin: 0 -30px;
          }
          .egg-animation.active{
            animation: egg 2s;
            animation-fill-mode: forwards;
          }
        }
        .draw-ask{
          font-size: 12px;
          color: #82400B;
          line-height: 17px;
          text-align: center;
        }
      }
      .position {
        position: absolute;
        top: 31px;
        right: -1px;
        img{
          display: block;
          width: 41px;
          height: 60px;
        }
        img +img{
          margin-top: 7px;
        }
      }
    }
  }
</style>