<template>
  <div class="integral-page">
    <navigation-bar pageName="积分明细" @onLeftClick="onBackClick"></navigation-bar>
    <div class="main-content">
      <div class="label">
        <img src="@/assets/icons/my/integral-icon.png"><span>积分额度</span>
      </div>
      <div class="integral-num">
        {{ integralData.avaliableIntegral ? integralData.avaliableIntegral : 0 }}
      </div>
    </div>
    <div class="list">
      <div class="list-title">
        <span class="line"></span><span>收支明细</span>
      </div>
      <div v-if="list.length >0" class="item-content">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="——没有更多了——"
          @load="getList"
          :immediate-check="false"
        >
          <div class="item solid-bottom" v-for="item in list" :key="item.id">
            <img v-if="item.direct === 'ADD'" src="@/assets/images/integral/integral-plus.png">
            <img v-else src="@/assets/images/integral/integral-consume.png">
            <div class="info">
              <div class="info-left">
                <div class="p1">
                  <!-- {{ item.ruleType === 'VIP_BUY' ? 
                  '会员购买' : item.ruleType === 'SIGN' ? 
                  '签到' : item.ruleType === 'INVALID' ? '过期' : item.ruleType === 'CASH_APPLY' ? 
                  '备用金申请' : item.ruleType === 'CASH_FAIL' ? '备用金审核失败' : 
                  item.ruleType === 'VIP_ADD' ? '新人福利' : 
                  item.ruleType === 'GLOD_EGG_AWARD' ? '砸金蛋': '' }} -->
                  {{ item.ruleDesc }}
                </div>
                <div class="p2">{{ parseTime(item.createTime, '{y}.{m}.{d} {h}:{i}:{s}') }}</div>
              </div>
              <div class="info-right">{{ item.direct === 'ADD' ? '+': '-' }}{{ item.integralValue }}</div>
            </div>
          </div>
        </van-list>
      </div>
      <van-empty v-else description="暂无数据"></van-empty>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { getIntegral, listIntegralDetail } from '@/api/customer'
  const { proxy } = getCurrentInstance()
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const store = useStore()
  const list = ref([]) 
  const loading = ref(false)
  const finished = ref(false)
  const data = reactive({
    integralData: {},
    queryParams: {
      pageNum: '1',
      pageSize: '20'
    }
  })
  const { queryParams, integralData }  = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(() => {
    integral()
  })
  const integral = () => {
    // 个人积分
    getIntegral().then(res => {
      integralData.value = res.data
    })
    getList()
  }
  const getList = () => {
    listIntegralDetail(queryParams.value).then(res => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }
</script>

<style lang="scss" scoped>
  .integral-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    :deep(.nav-bar .left) {
      color: #ffffff;
    }
    :deep(.nav-bar .center .page-title){
      color: #ffffff;
    }
  }
  .nav-bar {
    background: #FF671A !important;
    border-bottom: none;
  }
  .main-content{
    height: 282px;
    background: linear-gradient( 180deg, #FD6908 0%, #FD7710 71%, #FFFFFF 100%);
    margin-top: -1px;
    .label{
      display: flex;
      align-items: center;
      margin-left: 38px;
      margin-top: 33px;
      img{
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
      span{
        font-size: 13px;
        color: #FFFFFF;
      }
    }
    .integral-num{
      font-size: 36px;
      font-weight: bold;
      color: #222222;
      margin-top: 9px;
      margin-left: 38px;
      color: #FFFFFF;
    }
  }
  .list{
    flex: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
    border-radius: 12px;
    margin: 12px;
    margin-top: 15px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-top: -160px;
    &-title{
      padding: 20px 0 2px 20px;
      font-size: 16px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #363636;
      display: flex;
      align-items: center;
      .line{
        width: 3px;
        height: 15px;
        background: #FF5F3F;
        border-radius: 2px;
        margin-right: 11px;
      }
    }
    .item-content{
      flex: 1;
      overflow-y: auto;
      .item{
        display: flex;
        align-items: center;
        padding: 18px 0;
        margin: 0 16px;
        img{
          width: 40px;
          height: 40px;
          display: block;
          margin-right: 12px;
        }
        .info{
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &-left{
            .p1{
              font-size: 15px;
              font-weight: bold;
              color: #363636;
              line-height: 21px;
            }
            .p2{
              margin-top: 1px;
              font-size: 12px;
              font-weight: 400;
              color: #A8A8A8;
              line-height: 17px;
            }
          }
          &-right{
            font-size: 16px;
            font-weight: bold;
            color: #363636;
          }
        }
      }
    }
  }
</style>