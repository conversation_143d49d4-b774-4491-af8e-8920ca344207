<template>
  <div class="integral-sign-page">
    <navigation-bar pageName="签到" @onLeftClick="onBackClick"></navigation-bar>
    <div class="integral-sign-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="bg"></div>
      <div class="sign-content">
        <integral-title title="每日签到领积分" tips="连续签到可额外获取50积分" />
        <sign-in-wrapper />
      </div>
      <sign-task />
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import IntegralTitle from './components/IntegralTitle'
  import SignInWrapper from './components/SignInWrapper'
  import SignTask from './components/SignTask'
  const isIphoneX = window.isIphoneX
  const { proxy } = getCurrentInstance()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const store = useStore()
  const onBackClick = () => {
    router.go(-1)
  }
</script>

<style lang="scss" scoped>
  .integral-sign-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow-y: auto;
      .bg{
        height: 256px;
        background: url("@/assets/images/integral/sign-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .sign-content{
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
        border-radius: 12px;
        margin: 0 12px;
        margin-top: -100px;
        padding-bottom: 20px;
      }
    }
  }
</style>