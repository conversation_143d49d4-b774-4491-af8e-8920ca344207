<template>
  <div class="draw-rules-page">
    <navigation-bar pageName="抽奖规则" @onLeftClick="onBackClick"></navigation-bar>
    <div class="draw-rules-page-content">
      <div class="desc" v-dompurify-html="desc"></div>
      <!-- <div class="nav">
        <div class="title">一、活动规则</div>
        <p>1.进入活动页，您可消耗积分参与抽奖，通过完成任务可获得积分，具体任务可在[积分]板块查看;</p>
        <p>2.消耗10积分可以进行1次抽奖，抽奖获得的奖品有效使用时间见下方说明;</p>
        <p>3.用户的中奖奖品，可在抽奖记录中查看</p>
        <p>4.中奖奖品为活动免费赠送，因此不支持退换、兑现</p>
      </div>
      <div class="nav">
        <div class="title">二、活动奖品</div>
        <p>1.华为手环1部 (奖池总计5部，颜色随机，价值799元)</p>
        <p>2.积分增送(1-1000积不等);</p>
      </div>
      <div class="nav">
        <div class="title">三、其他说明</div>
        <p>若用户存在违法违规行为 (包括但不限于患意刷单、虚假交易、实施网络攻击、机器模拟用户等行为)，平台有权取消用户的活动资格，并有权采取撤销相关违规交易、收回奖励等措施，必要时追究用户法律责任。
活动期间，如出现不可抗力或情势变更的情况(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动遭受严重网络攻击或因系统故障需要暂停举办的），主办方可依相关法律的规定主张免责。</p>
        <p>(* 以上活动最终解释要归平台所有)</p>
        <p>(* 本活动Apple不是赞助者，也没有以任何形式参与活动)</p>
      </div> -->
    </div>
  </div>
</template>

<script setup>
  import { getIntegralRule } from '@/api/integral'
  import { onMounted } from 'vue'
  const desc = ref('')
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(() => {
    getIntegralRule({ type: 'GLOD_EGG' }).then(res => {
      desc.value = res.data.sessionDesc
    })
  })
</script>

<style lang="scss" scoped>
  .draw-rules-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      background: #ffffff;
      padding: 20px;
      :deep(.title){
        font-size: 16px;
        margin: 10px 0;
        font-weight: bold;
      }
      :deep(p){
        line-height: 18px;
        font-size: 14px;
        margin-top: 8px;
      }
      .nav{
        margin-top: 20px;
        
        
      }
    }
  }
</style>