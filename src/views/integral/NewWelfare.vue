<template>
  <div class="integral-welfare-page">
    <navigation-bar pageName="新人福利" @onLeftClick="onBackClick"></navigation-bar>
    <div class="integral-welfare-page-content">
      <div class="top-wrapper">
        <img class="new-text-img" src="@/assets/images/integral/new-text.png">
        <div class="number"><span class="num">{{ integralNum }}</span><span class="text">积分</span></div>
      </div>
      <div class="bottom-wrapper">
        <img class="explain" src="@/assets/images/integral/integral-explain.png">
        <img class="btn" @click="onReceive" src="@/assets/images/integral/receive-btn.png">
        <div class="tips">新用户可免费领取积分，每位用户限1次</div>
      </div>
    </div>
    <popup-tips ref="popupTipsRef" title="领取成功" :result="`您获取${ integralNum }积分`" @onResult="onResult" />
  </div>
</template>

<script setup>
  import PopupTips from './components/PopupTips'
  import { getNewPIntegral, getNewPreceive } from '@/api/integral'
  import { Toast } from 'vant'
  const { proxy } = getCurrentInstance()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const store = useStore()
  const popupTipsRef = ref(null)
  const integralNum = ref(0)
  const onBackClick = () => {
    router.go(-1)
  }
  const onReceive = () => {
    if (user.value) {
      getNewPreceive().then(res => {
        store.dispatch('IntegralData') // 更新积分
        popupTipsRef.value.show = true
      })
    } else {
      proxy.appJS.appLogin()
    }
  }
  const onResult = () => {
    popupTipsRef.value.show = false
  }
  onMounted(()=> {
    getNewPIntegral().then(res => {
      integralNum.value = res.data.IntegralValue
    })
  })
</script>

<style lang="scss" scoped>
  .integral-welfare-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    background: #FF3445;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .top-wrapper{
        height: 310px;
        background: url("@/assets/images/integral/welfare-bg.png") no-repeat;
        background-size: 100% 100%;
        padding-top: 50px;
        box-sizing: border-box;
        .new-text-img{
          display: block;
          width: 186px;
          height: 81px;
          margin: 0 auto;
        }
        .number{
          text-align: center;
          margin-top: 55px;
          color: #FF3445;
          font-size: 44px;
          font-weight: bold;
          span.num{
            margin-left: 25px;
          }
          span.text{
            font-size: 12px;
            font-weight: bold;
            margin-left: 2px;
          }
        }
      }
      .bottom-wrapper{
        flex: 1;
        background: linear-gradient( 180deg, #FF3445 0%, #FF9130 30%, #FF671A 100%);
      }
      img.explain{
        width: 337px;
        height: 274px;
        margin: 0 auto;
      }
      img.btn{
        width: 100px;
        height: 100px;
        margin: 0 auto;
        margin-top: -60px;
      }
      .tips{
        margin-top: 30px;
        font-size: 12px;
        transform: scale(0.85);
        color: #FFFFFF;
        line-height: 14px;
        text-align: center;
      }
    }
  }
  img {
    display: block;
  }
</style>