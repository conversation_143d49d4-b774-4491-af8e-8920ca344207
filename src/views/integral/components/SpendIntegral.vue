<template>
  <div class="spend-integral">
    <integral-title title="花积分" tips="直接当钱花" />
    <div class="mofang">
        <img class="golden-egg" src="@/assets/images/integral/golden-egg.png" @click="handleDraw">
        <div class="mofang-right">
            <div class="item" @click="handleExchange">
                <img class="item-img" src="@/assets/images/integral/jifenhaoli.png">
                <div class="item-label">
                    <div class="flex align-center">
                        <span class="text">积分兑礼</span>
                        <van-icon color="#666666" size="16" name="arrow"></van-icon>
                    </div>
                    <div class="tips">免费换</div>
                </div>
            </div>
            <div class="item-line"></div>
            <div class="item" @click="handleHome">
                <img class="item-img" src="@/assets/images/integral/shangchenghaowu.png">
                <div class="item-label">
                    <div class="flex align-center">
                        <span class="text">商城好物</span>
                        <van-icon color="#666666" size="16" name="arrow"></van-icon>
                    </div>
                    <div class="tips">热门购</div>
                </div>
            </div>
        </div>
    </div>
    <exchange-goods></exchange-goods>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import IntegralTitle from './IntegralTitle';
import ExchangeGoods from './ExchangeGoods'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const handleDraw = () => {
    router.push('/integral/draw')
}
const handleExchange = () => {
    router.push('/integral/mall')
}
const handleHome = () => {
    proxy.$menuRouter('/')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .spend-integral{
        margin: 35px 12px 0;
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
        border-radius: 12px;
        position: relative;
        z-index: 44;
        padding-bottom: 21px;
        .mofang{
            display: flex;
            justify-content: space-between;
            padding: 0 16px;
            .golden-egg{
                width: 154px;
                height: 130px;
                display: block;
            }
            &-right{
                width: 154px;
                height: 130px;
                background: linear-gradient( 180deg, #FFF2E3 0%, #FFF7ED 53%, #FEF6EB 100%);
                border-radius: 12px;
                padding-top: 19px;
                box-sizing: border-box;
                .item{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .item-img{
                        width: 38px;
                        height: 38px;
                        margin-right: 11px;
                    }
                    .text{
                        font-weight: 600;
                        font-size: 15px;
                        color: #333333;
                        line-height: 21px;
                        margin-right: 4px;
                    }
                    .tips{
                        font-size: 12px;
                        color: #666666;
                        line-height: 17px;
                    }
                }
                .item-line{
                    height: 1px;
                    background: #FFDABD;
                    margin: 11px;
                }
            }
        }
    }
</style>