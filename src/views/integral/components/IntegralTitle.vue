<template>
  <div class="title">
    <span class="line"></span>
    <span class="text">{{ title }}</span>
    <span class="tips">{{ tips }}</span>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    tips: {
        type: String,
        default: ''
    }
})
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    
    .title{
        display: flex;
        align-items: center;
        padding: 20px 19px;
        .line{
          width: 3px;
          height: 15px;
          background: #FF5F3F;
          border-radius: 2px;
        }
        .text{
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          line-height: 22px;
          margin-left: 12px;
        }
        .tips{
          margin-left: 10px;
          font-size: 12px;
          color: #999999;
          line-height: 17px;
          margin-top: 4px;
        }
      }
</style>