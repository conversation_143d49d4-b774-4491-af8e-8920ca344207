<template>
  <van-popup
    class="popup-tips"
    v-model:show="show"
    :style="{ 
      width: $px2rem('300px'), 
      height: $px2rem('360px')
    }">
    <div class="popup-tips-text">{{ title }}</div>
    <img class="popup-tips-img" src="@/assets/images/integral/popup-img.png">
    <div class="popup-tips-result">{{ result }}</div>
    <div class="popup-tips-btn" @click="emit('onResult')">好的</div>
  </van-popup>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    result: {
      type: String,
      default: ''
    }
  })
  const show = ref(false)
  const emit = defineEmits(['onResult'])
  defineExpose({ show })
</script>

<style lang="scss">
  .popup-tips{
    background: url('@/assets/images/integral/popup-bg.png') no-repeat;
    background-size: 100% 100%;
    &-text{
      text-align: center;
      margin-top: 47px;
      font-size: 18px;
      color: #FF4019;
      font-weight: bold;
    }
    &-img{
      width: 83px;
      height: 91px;
      display: block;
      margin: 0 auto;
      margin-top: 36px;
    }
    &-result{
      margin-top: 26px;
      color: #666666;
      font-size: 14px;
      text-align: center;
    }
    &-btn{
      width: 140px;
      height: 40px;
      background: linear-gradient( 180deg, #FF671A 0%, #FF4019 100%);
      border-radius: 20px;
      font-size: 16px;
      color: #FFFFFF;
      margin: 0 auto;
      text-align: center;
      line-height: 40px;
      margin-top: 33px;
    }
  }
</style>