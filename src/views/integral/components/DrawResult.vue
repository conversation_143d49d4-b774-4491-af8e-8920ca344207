<template>
  <van-popup
    class="draw-result"
    v-model:show="show"
    :style="{ 
      width: $px2rem('300px'), 
      height: $px2rem('360px')
    }">
    <div class="draw-result-text">{{ title }}</div>
    <img class="draw-result-img" :src="lotteryItem.image">
    <div class="draw-result-result">
      {{ lotteryItem.type === 'INTEGRAL' ? ('恭喜获得'+ lotteryItem.integralValue +'积分') : lotteryItem.awardName}}
    </div>
    <div class="draw-result-btn">
      <div class="btn again" @click="againDraw">再来一次</div>
      <div class="btn" @click="show=false">开心收下</div>
    </div>
  </van-popup>
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    lotteryItem: {
      type: Object,
      default: () => {}
    }
  })
  const againDraw = () => {
    show.value = false
    emit('onAgain')
  }
  const show = ref(false)
  const emit = defineEmits(['onAgain'])
  defineExpose({ show })
</script>

<style lang="scss">
  .draw-result{
    background: url('@/assets/images/integral/popup-bg.png') no-repeat;
    background-size: 100% 100%;
    &-text{
      text-align: center;
      margin-top: 47px;
      font-size: 18px;
      color: #FF4019;
      font-weight: bold;
    }
    &-img{
      width: 112px;
      height: 112px;
      display: block;
      margin: 0 auto;
      margin-top: 36px;
    }
    &-result{
      margin-top: 16px;
      color: #666666;
      font-size: 14px;
      text-align: center;
    }
    &-btn{
      display: flex;
      margin: 0 44px;
      .btn{
        width: 100px;
        height: 40px;
        border-radius: 20px;
        border: 1px solid #FF5533;
        font-size: 16px;
        text-align: center;
        line-height: 40px;
        margin-top: 30px;
        color: #FF5533;
      }
      .btn.again {
        background: #FF5533;
        color: #ffffff;
      }
      .btn +.btn{
        margin-left: 16px;
      }
    }
  }
</style>