<template>
  <div class="sign-task">
    <integral-title title="做任务领积分" />
    <div class="task-list">
      <div v-for="(item, index) in list" :key="index" class="task-item dashed-top">
        <div class="left">
          <div class="p1">{{ item.title }}</div>
          <div class="p2">{{ item.desc }}</div>
        </div>
        <div class="right">
          <div class="number">+{{ item.integral }}</div>
          <div class="btn" v-if="!item.done" @click="onClick(item)">
            <div class="text">去完成</div>
          </div>
          <div v-else class="no-btn">已完成</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import IntegralTitle from './IntegralTitle'
  import { getIntegralTask } from '@/api/integral'
  import { getMemberInfo } from '@/api/member'
  import { Toast } from 'vant'
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const router = useRouter()
  const user = computed(() => store.getters.userInfo)
  const list = ref([])
  onMounted(async () => {
    await getIntegralTask().then(res => {
      list.value = res.data
    })
    getMemberInfo().then(res => {
      res.data.map(item =>{
        if (item.vipPrice.vipType !== 'DISCOUNT') {
          list.value.push({
            type: item.vipPrice.vipCode,
            title: item.vipPrice.vipName+'积分权益',
            integral: item.vipPrice.integral,
            done: item.vipCust ? true : false,
            desc: '成功开通'+ item.vipPrice.vipName +'可得'
          })
        }
      })
    })
  })
  const onClick = (item) => {
    switch(item.type) {
      case 'REGISTER':
      router.push('/integral/new-welfare')
        return
      case 'EXCHANGE_GOODS':
      router.push('/integral/mall')
      return
      case 'FIRST_GOLD_EGG':
      router.push('/integral/draw')
      return
      case 'YJAK':
      router.push('/save-money')
      return
      case 'YJBK':
      router.push('/member')
      return
    }
  }
</script>

<style lang="scss" scoped>
  .sign-task{
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
    border-radius: 12px;
    margin: 12px;
    .task-list{
      padding: 0 20px;
      .task-item{
        padding: 10px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .p1 {
          font-size: 15px;
          color: #333333;
          line-height: 21px;
        }
        .p2 {
          margin-top: 2px;
          font-size: 12px;
          transform: scale(0.9);
          transform-origin: left center;
          color: #999999;
          line-height: 16px;
        }
        .right{
          display: flex;
          align-items: center;
          .number{
            font-size: 14px;
            color: #F58523;
            line-height: 20px;
            font-weight: bold;
          }
          .btn{
            width: 56px;
            height: 25px;
            background: #FF5533;
            border-radius: 13px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 14px;
            color: #FFFFFF;
            .text{
              font-size: 12px;
              zoom: 0.93;
            }
          }
          .no-btn{
            font-size: 12px;
            color: #999999;
            margin-left: 14px;
            width: 56px;
            height: 25px;
            background: #E8E8E8;
            border-radius: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      
    }
  }
</style>