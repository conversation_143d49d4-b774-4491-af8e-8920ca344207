<template>
  <div class="integral-goods">
    <div class="title">
      积分好物推荐
    </div>
    <div class="tab" v-if="tabNav">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        :class="`tab-item ${index === curTab ? 'active': ''}`"
        @click="onChange(item, index)"
      >
        {{ item.dictLabel }}
      </div>
    </div>
    <div v-if="list.length>0" class="list">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="——没有更多了——"
        @load="getList"
        :immediate-check="false"
      >
        <goods-list :list="list" />
      </van-list>
    </div>
    <div v-else class="empty">
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
  import { integralGoods } from '@/api/goods'
  import { getDictData } from '@/api/base'
  import GoodsList from './GoodsList'
  const store = useStore()
  const curTab = ref(0)
  const tabList = ref([])
  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const refreshing = ref(false)
  const props = defineProps({
    tabNav: {
      type: Boolean,
      default: true
    }
  })
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      integralGradientLower: undefined,
      integralGradientUpper: undefined
    }
  })
  const { queryParams }  = toRefs(data)
  const onChange = (item, index) => {
    curTab.value = index
    queryParams.value.pageNum = 1
    if (item.dictValue) {
      const values = item.dictValue.split(',')
      queryParams.value.integralGradientLower = values[0]
      queryParams.value.integralGradientUpper = values[1] || undefined
    } else {
      queryParams.value.integralGradientLower = undefined
      queryParams.value.integralGradientUpper = undefined
    }
    
    list.value = []
    finished.value = false
    getList()
  }
  const getList = () => {
    integralGoods(queryParams.value).then(res => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }
  watch(() => store.getters.userInfo, (newValue, oldValue) => {
    if (newValue) {
      if (!(oldValue && Object.entries(oldValue).toString() === Object.entries(newValue).toString())) {
        list.value = []
        queryParams.value.pageNum = 1
        getList()
      }
    }
  });
  // onActivated(() => {
  //   list.value = []
  // })
  onMounted(() => {
    getList()
    getDictData({ dictType: 'INTEGRAL_RANGE' }).then(res => {
      tabList.value = res.data
      tabList.value.unshift({dictLabel: '全部', dictValue: ''})
    })
  })
</script>

<style lang="scss" scoped>
  .integral-goods{
    margin-top: 38px;
    .title{
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      text-align: center;
    }
    .tab{
      margin: 20px 12px 0;
      display: flex;
      .tab-item{
        height: 28px;
        text-align: center;
        font-size: 13px;
        padding: 0 12px;
        color: #808080;
        margin-right: 10px;
        display: flex;
        align-items: center;
      }
      .tab-item:first-child {
        padding: 0 18px;
      }
      .tab-item.active{
        font-size: 14px;
        color: #FF4019;
        font-weight: bold;
        background: rgba(255,85,51,0.2);
        border-radius: 14px;
      }
    }
  }
</style>