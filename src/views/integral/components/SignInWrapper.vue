<template>
  <div class="sign-wrapper">
    <div class="list" v-if="user">
      <div :class="`item ${item.flag === 'Y' ? 'active' : ''}`" v-for="item in signList" :key="item.day">
        <img class="sign-img" :src="item.flag === 'Y' ? activeImg : inactiveImg">
        <div class="day mini-text">
          {{ item.integral }}
        </div>
      </div>
    </div>
    <div class="sign-btn">
      <div v-if="!isSign || !user" class="btn theme-linear-gradient" @click="handleSignIn">立即签到</div>
      <div v-else class="text">已签到，明日再来哦~</div>
    </div>
  </div>
</template>

<script setup>
  import { getIntegralSign, submitSign } from '@/api/customer'
  import activeImg from '@/assets/images/integral/integral-active.png'
  import inactiveImg from '@/assets/images/integral/integral-inactive.png'
  import { showToast, showSuccessToast } from 'vant'
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const signNum = ref(0)
  const isSign = ref(false)
  const data = reactive({
    bonusSign: {}
  })
  const signList = ref([])
  const { bonusSign } = toRefs(data)
  const emit = defineEmits(['updateIntegral'])
  onMounted(() => {
    if (user.value) {
      integralSign()
    }
  })
  const integralSign = () => {
    getIntegralSign().then(res => {
      const today = new Date()
      const week = today.getDay() === 0 ? 7 : today.getDay()
      let list = res.data.signList.map(item => {
        if (item.day < week) { // 今日之前的不可再签到
          item.disabled = true
        } else if (item.day === week) { // 今天
          item.today = 'Y'
          isSign.value = item.flag === 'Y' ? true : false
        }
        if (item.flag ==='Y') { // 累计签到
          signNum.value ++
        }
        return item
      })
      signList.value = list
      bonusSign.value = res.data.bonusSign
    })
  }
  const handleSignIn = () => {
    if (user.value) {
      if(!isSign.value) {
        submitSign().then(res => {
          showSuccessToast('签到成功！')
          store.dispatch('IntegralData') // 更新积分
          integralSign()
          emit('updateIntegral')
        })
      } else {
        showToast('今日已签到')
      }
    } else {
      proxy.appLogin()
    }
  }
</script>

<style lang="scss" scoped>
  .sign-wrapper{
    .list {
      display: flex;
      justify-content: center;
      .item {
        width: 40px;
        padding-top: 7px;
        padding-bottom: 5px;
        background: #F7F7F7;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 7px;
        .mini-text{
          font-size: 12px;
          transform: scale(0.9);
          transform-origin: center center;
          color: #FFF7D8;
          line-height: 16px;
          margin-top: 4px;
        }
        .day{
          color: #EF8F39;
        }
        .sign-img{
          width: 25px;
          height: 25px;
          display: block;
          margin: 0 auto;
        }
      }
      .item.active{
        background: linear-gradient(180deg, #F8B01B 0%, #FF7E2C 100%);
      }
      .item.active .day{
        color: #FFFFFF;
      }
      .item:last-child {
        margin-right: 0;
      }
    }
  }
  .sign-btn{
    display: flex;
    justify-content: center;
    .btn{
      width: 160px;
      height: 40px;
      border-radius: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 15px;
      font-weight: bold;
      color: #FFFFFF;
      margin-top: 22px;
    }
    .text{
      margin-top: 21px;
      margin-bottom: 16px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #A8A8A8;
      line-height: 20px;
    }
  }
</style>