<template>
  <div class="hot-convert">
    <img class="hot-bg" src="@/assets/images/integral/hot-bg.png">
    <div class="list" v-if="regionGoodsList[$global.GOODS_REGION_CULLING_CONVERT]">
      <template v-for="(item, index) in regionGoodsList[$global.GOODS_REGION_CULLING_CONVERT]"
        :key="index">
        <div
          v-if="index < 3"
          class="item"
          @click="goodsRouter(item)"
        >
          <img :src="item.image" class="img">
          <div class="goods-name overflow-1">
            {{ item.spuTitle }}
          </div>
          <div class="integral-num">{{ item.integral }}积分</div>
          <div class="btn">去兑换</div>
        </div>
      </template>
      
    </div>
  </div>
</template>

<script setup>
  import { regionGoods } from '@/api/goods'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const data = reactive({
    regionGoodsList: {}
  })
  const { regionGoodsList } = toRefs(data)
  const getRegionGoods = () => {
    regionGoods({ 'regionTypeList': [proxy.$global.GOODS_REGION_CULLING_CONVERT ]}).then(res => {
      regionGoodsList.value = res.data
    })
  }
  getRegionGoods()
  const goodsRouter = (item) => {
    router.push({path: '/goods-detail', query: {goodsId: item.spuId, integralFlag: 1} })
  }
</script>

<style lang="scss" scoped>
  .hot-convert{
    margin: -47px 12px 0 12px;
    .hot-bg {
      width: 100%;
      display: block;
    }
    .list{
      background: #ffffff;
      border-radius: 0 0 12px 12px;
      padding: 10px 20px 25px 20px;
      display: flex;
      justify-content: space-between;
      .item{
        width: 90px;
        text-align: center;
        img{
          width: 90px;
          height: 90px;
          display: block;
        }
        .goods-name{
          margin-top: 15px;
          font-size: 13px;
          color: #4A280C;
          line-height: 18px;
        }
        .integral-num{
          margin-top: 8px;
          font-size: 13px;
          color: #F54E4E;
          line-height: 18px;
          font-weight: bold;
        }
        .btn {
          width: 66px;
          margin: 0 auto;
          height: 28px;
          background: #FD5023;
          border-radius: 16px;
          line-height: 28px;
          text-align: center;
          margin-top: 14px;
          font-size: 13px;
          font-weight: bold;
          color: #FFFFFF;
        }
      }
    }
  }
</style>