<template>
    <div class="earn-integral">
        <integral-title title="赚积分" tips="轻松领积分" />
        <div class="list">
            <div class="earn-item" @click="handleNew">
                <img class="item-img" src="@/assets/images/integral/xinrenfuli.png">
                <img class="item-bg" src="@/assets/images/integral/xinrenfuli-bg.png">
                <div class="label">
                    <div class="p1">新人福利</div>
                    <div class="p2">送{{ integralNum }}积分</div>
                </div>
                <div class="btn">领取</div>
            </div>
            <div class="earn-item" @click="handleSign">
                <img class="item-img" src="@/assets/images/integral/meirihaoli.png">
                <img class="item-bg" src="@/assets/images/integral/meirihaoli-bg.png">
                <div class="label">
                    <div class="p1">每日好礼</div>
                    <div class="p2">签到领积分</div>
                </div>
                <div class="btn">打卡</div>
            </div>
        </div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
  import { getNewPIntegral } from '@/api/integral'
  import IntegralTitle from './IntegralTitle';
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const route = useRoute();
  const router = useRouter();
  const integralNum = ref(0)
  const handleNew = () => {
    router.push('/integral/new-welfare')
  }
  const handleSign = () => {
    router.push('/integral/sign')
  }
  onBeforeMount(() => {
    //console.log('2.组件挂载页面之前执行----onBeforeMount')
  })
  onMounted(() => {
    getNewPIntegral().then(res => {
      integralNum.value = res.data.IntegralValue
    })
  })
  </script>
  <style scoped lang='scss'>
      .earn-integral{
        margin: 20px 12px 0;
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.02);
        border-radius: 12px;
        position: relative;
        z-index: 44;
        padding-bottom: 23px;
        .list{
            display: flex;
            justify-content: space-between;
            padding: 0 16px;
            .earn-item{
                width: 154px;
                height: 120px;
                position: relative;
                .item-img{
                    width: 74px;
                    height: 74px;
                    position: absolute;
                    left: 5px;
                    top: -20px;
                    z-index: 20;
                }
                .item-bg{
                    width: 154px;
                    height: 120px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 10;
                }
                .btn{
                    position: absolute;
                    bottom: 20px;
                    right: 16px;
                    width: 48px;
                    height: 26px;
                    background: linear-gradient( 180deg, #FF671A 0%, #FF4019 100%);
                    border-radius: 19px;
                    font-size: 12px;
                    color: #FFFFFF;
                    z-index: 30;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .label{
                    position: absolute;
                    left: 18px;
                    top: 58px;
                    z-index: 30;
                    .p1{
                        font-weight: 600;
                        font-size: 15px;
                        color: #333333;
                        line-height: 21px;
                    }
                    .p2{
                        margin-top: 4px;
                        font-size: 12px;
                        color: #666666;
                        line-height: 17px;
                    }
                }
            }
        }
      }
  </style>