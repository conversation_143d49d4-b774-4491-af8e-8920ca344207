<template>
  <div class="exchange-goods">
    <div class="list" v-if="regionGoodsList[proxy.$global.GOODS_REGION_INTEGRAL_EXCHANGE]">
        <div
            class="item"
            v-for="item in regionGoodsList[proxy.$global.GOODS_REGION_INTEGRAL_EXCHANGE]"
            :key="item.spuId"
            @click="handleDetail(item)"
        >
            <img :src="item.image" class="item-img">
            <div class="btn">兑换</div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { regionGoods } from '@/api/goods'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
/**
* 数据部分
*/
const data = reactive({
    regionGoodsList: {}
})
const { regionGoodsList } = toRefs(data)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
const handleDetail = (item) => {
    router.push({path: '/goods-detail', query: {goodsId: item.spuId, integralFlag: 1} })
}
onMounted(() => {
    regionGoods({ 'regionTypeList': [proxy.$global.GOODS_REGION_INTEGRAL_EXCHANGE]}).then(res => {
      regionGoodsList.value = res.data
    })
})
</script>
<style scoped lang='scss'>
    .exchange-goods{
        background: #FFF8ED;
        border-radius: 12px;
        padding: 16px 0;
        margin: 12px 16px 0 16px;
        .list{
            display: flex;
            padding-left: 14px;
            overflow-x: auto; /* 水平滚动条 */
            .item{
                width: 62px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .item-img{
                    width: 62px;
                    height: 62px;
                    display: block;
                    border-radius: 5000px;
                }
                .btn{
                    display: inline-block;
                    margin-top: 12px;
                    background: #FFE9D7;
                    border-radius: 11px;
                    font-size: 12px;
                    color: #322B27;
                    line-height: 17px;
                    padding: 2px 11px;
                    text-align: center;
                }
            }
            .item{
                margin-right: 14px;
            }
        }
    }
</style>