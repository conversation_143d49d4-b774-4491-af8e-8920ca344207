<template>
  <div class="draw-goods">
    <div class="goods-list">
      <div class="item" v-for="(item, index) in goodsList" :key="index">
        <img :src="item.image">
        <div class="name">{{ item.awardName }}</div>
      </div>
    </div>
    <div class="integral-btn">
      <div class="btn">我的积分：{{ integral }}积分</div>
    </div>
  </div>
</template>

<script setup>
  import { getDrawGoods } from '@/api/integral'
  const store = useStore()
  const goodsList =ref([])
  const props = defineProps({
    integral: {
      type: Number,
      default: 0
    }
  })
  onMounted(() => {
    getDrawGoods({ sessionType: 'GLOD_EGG' }).then(res => {
      goodsList.value = res.data
    })
  })
</script>

<style lang="scss" scoped>
  .draw-goods{
    width: 290px;
    height: 250px;
    margin: 0 auto;
    background: url('@/assets/images/integral/draw-goods-bg.png');
    background-size: 100% 100%;
    margin-top: 20px;
    padding: 0 22px;
    overflow-x: auto;
    .goods-list{
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      padding-top: 78px;
      .item{
        flex: none;
        img{
          width: 70px;
          height: 70px;
          display: block;
          border-radius: 12px;
        }
        .name{
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
          margin-top: 4px;
          text-align: center;
        }
      }
      .item +.item{
        margin-left: 6px;
      }
    }
    .integral-btn{
      display: flex;
      justify-content: center;
      margin-top: 30px;
      .btn{
        height: 32px;
        background: linear-gradient(180deg, #FF8E7C 0%, #FF0000 100%);
        border-radius: 16px;
        padding: 0 22px;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 32px;
      }
    }
  }
</style>