<template>
  <div class="nav-wrapper theme-bg" :class="{ 'iphonex': isIphoneX }">
        <div class="nav-content" :class="{ 'iphonex-top': isIphoneX }" :style="navBarStyle">
          <div class="left" @click="emit('onLeftClick')">
            <!-- 默认状态 -->
            <van-icon v-if="isDefault && isShowBack" size="22" name="arrow-left" />
            <!-- 定制具名插槽 -->
            <slot name="nav-left"></slot>
          </div>
            <div class="center">
                <!-- 默认状态 -->
                <span class="page-title">发现</span>
            </div>
            <div class="right">
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    navBarStyle: {
        backgroundColor: '',
        position: 'fixed'
    },
    isDefault: {
      default: true,
      type: Boolean
    },
    isShowBack: {
      default: true,
      type: Boolean
    },
})
const emit = defineEmits(['onLeftClick'])
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .nav-wrapper.iphonex{
        // height: 88px;
        height: calc(69px + var(--safe-area-inset-top));
    }
    .nav-wrapper{
        box-sizing: border-box;
        height: 69px;
    }
    .nav-content{
        width: 100%;
        height: 44px;
        display: flex;
        justify-content: space-between;
        padding-top: 25px;
        align-items: center;
        flex-shrink: 0;
        z-index: 1999;
        position: fixed;
        top: 0;
        color: #FFFFFF;
        font-size: 18px;
        .left {
            padding-left: 6px;
            width: 32px;
            display: flex;
            align-items: center;
        }
        .right {
            display: flex;
            padding-right: 9px;
            align-items: center;
            width: 32px;
            position: relative;
            font-size: 14px;
        }
        .center {
            display: flex;
            height: 100%;
            flex-grow: 1;
            padding: 0 5px;
            .page-title {
                align-self: center;
                margin: 0 auto;
                font-size: 18px;
                color: #ffffff;
                white-space:nowrap;
            }
        }
    }

</style>