<template>
  <div class="list">
    <div
      class="item"
      v-for="(item, index) in list"
      :key="index"
      @click="handleDetail(item)"
    >
      <img :src="item.image">
      <div class="info">
        <div class="name overflow-2">
          {{ item.spuTitle }}
        </div>
        <div class="integral">
          <span v-if="item.integralPrice">{{ item.integralPrice }}元 + </span> {{ item.integralValue }}积分
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const router = useRouter()
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })
  const handleDetail = (item) => {
    router.push({path: '/goods-detail', query: {goodsId: item.spuId, integralFlag: 1} })
  }
</script>

<style lang="scss" scoped>
  .list{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 14px 12px 0;
    .item {
      width: 170px;
      overflow: hidden;
      border-radius: 8px;
      background: #ffffff;
      margin-bottom: 12px;
      img{
        width: 170px;
        height: 170px;
        display: block;
      }
      .info {
        padding: 12px;
        display: flex;
        flex-direction: column;
        .name{
          font-size: 13px;
          color: #333333;
          line-height: 18px;
        }
        .integral{
          font-size: 15px;
          color: #FF3355;
          line-height: 21px;
          font-weight: bold;
          margin-top: 7px;
        }
      }
    }
  }
</style>