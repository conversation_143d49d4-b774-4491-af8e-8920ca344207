<template>
  <div class="info">
    <div class="quota">
        <img src="@/assets/images/integral/small-cion.png">
        <span>积分额度</span>
    </div>
    <div class="number-wrapper">
        <div class="number">
            {{ integral }}
        </div>
        <div class="other" @click="handleIntegralDetal">
            <div class="text">明细</div>
            <div class="arrow-wrapper">
                <div class="arrow"></div>
            </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    integral: {
        type: Number,
        default: 0
    }
})
const handleIntegralDetal = () => {
    router.push('/integral/detail')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .info{
        padding-top: 28px;
        .quota{
            display: flex;
            align-items: center;
            margin-left: 28px;
            font-size: 13px;
            color: #FFFFFF;
            line-height: 20px;
            img{
                width: 30px;
                height: 30px;
                display: block;
                margin-right: 5px;
            }
        }
        .other{
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            .text{
                font-size: 12px;
                transform: scale(0.85);
                transform-origin: left center;
                color: #FFFFFF;
            }
        }
        .number-wrapper{
            margin-left: 34px;
            margin-top: 4px;
            display: flex;
            align-items: self-end;
            .number{
                min-width: 50px;
                font-family: DIN, DIN;
                font-weight: bold;
                font-size: 36px;
                color: #FFFFFF;
            }
            .text{
                margin-left: 10px;
            }
            .arrow-wrapper{
                width: 16px;
                height: 16px;
                border-radius: 5000px;
                background: linear-gradient( 90deg, rgba(255,255,255,0.58) 0%, #FFFFFF 100%);
                position: relative;
                .arrow{
                    position: absolute;
                    left:3px;
                    top: 5px;
                    overflow: hidden;
                    zoom: 1;
                    width: 6px;
                    height: 6px;
                    text-indent: -99999px;
                    border-left: 1px solid #FF671A;
                    border-top: 1px solid #FF671A;
                    transform: rotate(135deg);
                }
            }
        }
    }
</style>