<template>
  <div class="draw-log-page">
    <navigation-bar pageName="抽奖记录" @onLeftClick="onBackClick"></navigation-bar>
    <div class="draw-log-page-content">
      <div v-if="list.length>0" class="list-content">
        <van-pull-refresh v-model="refreshing">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="——没有更多了——"
            @load="getList"
            :immediate-check="false"
          >
            <div class="list">
              <div class="item" v-for="(item, index) in list" :key="index">
                <div class="title solid-bottom">
                  <span class="time">{{ item.operTime }}</span>
                  <span :class="`state ${ item.exchangeFlag === 'Y' ? 'used' : 'wait' }`" v-if="item.type === 'GOODS'">
                    {{ item.exchangeFlag === 'Y' ? '已兑换' : '待兑换' }}
                  </span>
                </div>
                <div class="content">
                  <div class="img">
                    <img :src="item.image">
                  </div>
                  <div class="name">
                    获得 {{ item.type === 'INTEGRAL' ? (item.integralValue + '积分') : item.awardName }}
                  </div>
                  <div class="exchange-btn"
                    v-if="item.type === 'GOODS' && item.exchangeFlag === 'N'"
                    @click="handleExchange(item)"
                  >
                    去兑换
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else class="empty">
        <van-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  import { getDrawLog } from '@/api/integral'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const refreshing = ref(false)
  const data = reactive({
    queryParams: {
      pageNum: '1',
      pageSize: '10'
    }
  })
  const { queryParams }  = toRefs(data)
  const onBackClick = () => {
    router.go(-1)
  }
  const getList = () => {
    getDrawLog(queryParams.value).then(res => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total || res.data.length === 0) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }
  const handleExchange = (item) => {
    localStorage.setItem(proxy.$global.PARAM_ORDERCONFIRM, JSON.stringify({
      shoppingFlag: 'A',
      awardId: item.id,
      msOrderSpus: {
        spuId: item.spuId,
        skuId: item.skuId,
        quantity: 1
      }
    }))
    router.push('/order-confirm')
  }
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .draw-log-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      .list-content{
        .list{
          .item{
            margin-top: 12px;
            background: #ffffff;
            .title{
              display: flex;
              justify-content: space-between;
              height: 40px;
              align-items: center;
              padding: 0 15px;
              font-size: 13px;
              .time{
                color: #999999;
              }
              .state{
                color: #06BB8D;
              }
              .state.used{
                color: #06BB8D;
              }
              .state.wait{
                color:#FF4F3F;
              }
            }
            .content{
              display: flex;
              padding: 15px;
              align-items: center;
              justify-content: space-between;
              .img{
                width: 60px;
                height: 60px;
                background: rgb(216,216,216, 0.25);
                border-radius: 7px;
                display: flex;
                align-items: center;
                justify-content: center;
                img{
                  width: 50px;
                  height: 50px;
                  display: block;
                }
              }
              .name{
                font-size: 16px;
                color: #333333;
                flex: 1;
                margin-left: 20px;
              }
            }
            .exchange-btn{
              width: 67px;
              height: 33px;
              background: #FF4F3F;
              border-radius: 17px;
              font-size: 13px;
              color: #ffffff;
              line-height: 33px;
              text-align: center;
            }
          }
        }
      }
    }
  }
</style>