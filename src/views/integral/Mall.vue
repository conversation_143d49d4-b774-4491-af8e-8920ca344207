<template>
  <div class="integral-mall-page">
    <navigation-bar pageName="积分超值兑" @onLeftClick="onBackClick"></navigation-bar>
    <div class="integral-mall-page-content">
      <div class="mall-main">
        <img class="mall-title" src="@/assets/images/integral/title.png">
        <div class="integral-data">
          <span>你的积分：</span>
          <img src="@/assets/images/integral/integral-icon.png">
          <span class="number">{{ integralValue }}</span>
        </div>
      </div>
      <hot-convert />
      <integral-goods :tab-nav="false" />
    </div>
  </div>
</template>

<script setup>
  import HotConvert from './components/HotConvert'
  import IntegralGoods from './components/IntegralGoods'
  const router = useRouter()
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const integralValue = computed(() => store.getters.integralValue)
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(() => {
    if (user.value) {
      store.dispatch('IntegralData') // 获取积分
    }
  })
</script>

<style lang="scss" scoped>
  .integral-mall-page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-content{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      .mall-main{
        height: 180px;
        background: url('@/assets/images/integral/mall-bg.png') no-repeat;
        background-size: 100% 100%;
        padding-left: 41px;
        .mall-title{
          width: 135px;
          height: 27px;
          margin-top: 48px;
        }
        .integral-data{
          margin-top: 14px;
          font-size: 12px;
          color: #611D0B;
          display: flex;
          align-items: center;
          img{
            width: 16px;
            height: 16px;
            display: block;
            margin-right: 4px;
          }
          .number{
            font-size: 13px;
            color: #F94800;
          }
        }
      }
    }
  }
</style>