<template>
  <div class="page" @scroll="onScrollChange" ref="pageRef">
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="theme-text">
      <integral-header
        :isShowBack="isMenuPath() ? false: true" pageName="轻享花"
        :nav-bar-style="navBarStyle"
        @onLeftClick="onBackClick"
      ></integral-header>
      <div class="integral-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
          <img class="big-cion" src="@/assets/images/integral/big-cion.png">
          <img class="bg-line" src="@/assets/images/integral/bg-line.png">
          <integral-data :integral="integralValue"></integral-data>
          <spend-integral></spend-integral>
          <earn-integral></earn-integral>
          <integral-goods></integral-goods>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script setup name="Integral">
  import IntegralData from './components/IntegralData'
  import SpendIntegral from './components/SpendIntegral'
  import EarnIntegral from './components/EarnIntegral'
  import IntegralGoods from './components/IntegralGoods'
  import IntegralHeader from './components/IntegralHeader'
  import { isMenuPath } from '@/utils/common'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const store = useStore()
  const isIphoneX = window.isIphoneX
  const loading = ref(false)
  const pageRef = ref(null)
  const user = computed(() => store.getters.userInfo)
  const integralValue = computed(() => store.getters.integralValue)
  // 滚动值
  const scrollTopValue= ref(-1)
  const ANCHOR_SCROLL_TOP = 64
  const data = reactive({
    navBarStyle: {
      backgroundColor: '',
      position: 'fixed'
    }
  })
  const { navBarStyle } = toRefs(data)
  const onBackClick = () => {
    if(!isMenuPath()) {
      router.go(-1)
    }
  }
  const initPage = () => {
    if (user.value) {
      store.dispatch('IntegralData').then(res => {
        loading.value = false
      }) // 获取积分
    } else {
      loading.value = false
    }
  }
  const onRefresh = () => {
    initPage()
  }
  onActivated(() => {
      pageRef.value.scrollTop = scrollTopValue.value
  })
  // 滚动
  const onScrollChange = ($e) => {
    scrollTopValue.value = $e.target.scrollTop
    let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
    navBarStyle.value.backgroundColor = 'rgba(255,103,26, ' + opacity + ')'
  }
  onMounted(() => {
    initPage()
  })
</script>

<style lang="scss" scoped>
  .page{
    height: 100%;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;
    .integral-page-content {
      background: linear-gradient( 180deg, #FF671A 0%, #FD7710 71%, #FFFFFF 100%);
      background-size: 100% 465px;
      background-position: 0 0;
      background-repeat: no-repeat;
      position: relative;
      margin-top: -1px;
      .big-cion{
        position: absolute;
        width: 68px;
        height: 89px;
        top: 28px;
        right: 51px;
        z-index: 23;
      }
      .bg-line{
        position: absolute;
        width: 130px;
        height: 233px;
        right: 0;
        top: 35px;
        z-index: 22;
      }
      .recommed{
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        text-align: center;
        margin-top: 38px;
      }
    }
  }
</style>
