<template>
  <div class="nav-bar z-index-max" :class="[{ 'iphonex-top': isIphoneX }, 
  { 'bottom-line': pageName },{'no-padd-top': noPaddTop}]" :style="navBarStyle">
    <div v-if="isShowBack" class="left" @click="onLeftClick">
      <!-- 默认状态 -->
      <van-icon size="22" name="arrow-left" />
      <!-- 定制具名插槽 -->
      <slot name="nav-left"></slot>
    </div>
    <div class="center">
      <!-- 默认状态 -->
      <!-- <span class="page-title" v-if="isDefault">{{ pageName }}</span> -->
      <!-- 定制具名插槽 -->
      <slot name="nav-center"></slot>
    </div>
    <div v-if="!isSearch" class="right">
      <!-- 定制具名插槽 -->
      <slot name="nav-right"></slot>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    // 是否展示默认状态
    isDefault: {
      default: true,
      type: Boolean
    },
    isShowBack: {
      default: true,
      type: Boolean
    },
    isSearch: {
      default: false,
      type: Boolean
    },
    // 默认状态下页面标题的名称
    pageName: {
      default: '',
      type: String
    },
    // navBar样式
    navBarStyle: {
      type: Object,
      default: function() {
        return {
          backgroundColor: 'white'
        };
      }
    },
    noPaddTop: {
      default: false,
      type: Boolean
    }
  })
  const store = useStore()
  const isIphoneX = window.isIphoneX
  const emit = defineEmits(['onLeftClick'])
  const onLeftClick = () => {
    store.commit('SET_ISIOSMOVEBACK', false) // 重置变量isIosMoveBack
    emit('onLeftClick')
  }
</script>

<style lang="scss" scoped>
  .nav-bar {
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: space-between;
    // 适配手机 stateBar
    padding-top: 25px;
    align-items: center;
    flex-shrink: 0;
    .left {
      padding-left: 6px;
      width: 32px;
      display: flex;
      align-items: center;
      color: #ffffff;
    }
    .right {
      display: flex;
      padding-right: 9px;
      align-items: center;
      width: 32px;
      position: relative;
      font-size: 14px;
    }

    .center {
      display: flex;
      height: 100%;
      flex-grow: 1;
      padding-right: 5px;
      .page-title {
        align-self: center;
        margin: 0 auto;
        font-size: 18px;
        color: #333333;
        white-space:nowrap;
      }
    }
  }

  .bottom-line {
    border-bottom: 1px solid #e5e5e5;
  }
  .no-padd-top{
    padding-top: 0 !important;
  }
</style>