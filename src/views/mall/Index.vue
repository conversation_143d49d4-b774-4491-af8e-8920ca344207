<template>
  <div class="mall-page" @scroll="onScrollChange" ref="installmentRef">
    <nav-bar
      :isShowBack="isMenuPath() ? false : true"
      :isSearch="true"
      :navBarStyle="navBarStyle"
      @onLeftClick="onBackClick"
    >
      <template #nav-center>
        <search
          :icon="searchIcon"
          @click="routerSearch"
        />
      </template>
    </nav-bar>
    <div class="mall-page-content">
      <div :class="`bg-top ${isIphoneX?'iphonex' : ''}`">
        <div class="category-wrapper">
          <div class="category-tab">
            <div :class="`item ${categoryActive === 0 ? 'active' : ''}`" @click="handleCategory(0)">推荐</div>
            <div
              v-for="item in categoryTop"
              :key="item.categoryId"
              :class="`item ${categoryActive === item.categoryId ? 'active' : ''}`"
              @click="handleCategory(item.categoryId)"
            >
              {{ item.categoryName }}
            </div>
          </div>
        </div>
      </div>
      <div class="installment-content" v-if="categoryActive===0" :class="{ 'iphonex-bottom': isIphoneX }">
        <div class="index-ad-position" v-if="advertises[$global.AD_POSITION_INSTALLMENT_PRODUCT]">
          <my-swiper :swiperDatas="advertises[$global.AD_POSITION_INSTALLMENT_PRODUCT]"></my-swiper>
        </div>
        <div class="func-group">
          <div class="func-item" v-for="item in funcGroup" :key="item.id" @click="funcRouter(item)">
            <img :src="item.icon">
            <div class="text">{{ item.name }}</div>
          </div>
        </div>
        <div class="recommend">
          <div class="recommend-title">新品推荐</div>
          <goods-row :goods-list="recommendGoods"></goods-row>
        </div>
      </div>
      <goods-category v-else v-model="categoryActive" ref="goodsCategoryRef"></goods-category>
    </div>
  </div>
</template>
<script setup name="Installment">
  import NavBar from './components/NavBar'
  import GoodsRow from '@/components/GoodsRow'
  import GoodsCategory from './components/GoodsCategory'
  import { getAdList } from '@/api/base'
  import { getFunGroup, getMessageList } from '@/api/home'
  import { categoryList, regionGoodsPage } from '@/api/goods'
  import { adRouter, funcRouter } from '@/utils/toRouter'
  import MySwiper from '@/components/MySwiper'
  import Search from '@/components/Currency/Search'
  import searchIcon from '@/assets/icons/search.png'
  import { isMenuPath } from '@/utils/common'
  import { showToast } from 'vant'
  const  isIphoneX = window.isIphoneX
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const router = useRouter()
  const homeRef = ref(null)
  const categoryTop = ref([])
  const goodsCategoryRef = ref(null)
  const categoryActive = ref(0)
  const funcGroup = ref([])
  const user = computed(() => store.getters.userInfo)
  const installmentRef = ref(null)
  const keywords = ref('')
  const msgNum = ref(0)
  const data = reactive({
    navBarStyle: {
      backgroundColor: '#FF671A',
      position: 'fixed'
    },
    navBarCurrentSlot: {},
    advertises: {},
    queryParams: {
      pageNum: '1',
      pageSize: '10'
    }
  })
  const { navBarStyle, navBarCurrentSlot, advertises, queryParams } = toRefs(data)
  // 滚动值
  const scrollTopValue= ref(-1)
  const ANCHOR_SCROLL_TOP = 160
  const recommendGoods = ref([])
  const onBackClick = () => {
    if (!isMenuPath()) {
      router.go(-1)
    }
  }
  onMounted(() => {
    // 分类
    categoryList({ parentId: '0', navStatus: 'Y' }).then(res => {
      categoryTop.value = res.data
    })
    // 广告
    getAdList({regionType: [
    proxy.$global.AD_POSITION_INSTALLMENT_PRODUCT,
    proxy.$global.AD_POSITION_INSTALLMENT_WELFARE]}).then(res => {
      advertises.value = res.data
    })
    // 功能组
    getFunGroup({ funcRegion: proxy.$global.ICON_FUN_SHOP_HOME }).then(res => {
      funcGroup.value = res.data
    })
    // 推荐商品
    getRegionGoods()
    // 消息信息
    messageGet()
  })
  onActivated(() => {
    installmentRef.value.scrollTop = scrollTopValue.value
  })
  const getRegionGoods = () => {
    regionGoodsPage({ 'regionType': proxy.$global.GOODS_REGION_NEW,
    ...queryParams.value }).then(res => {
      recommendGoods.value = res.data
    })
  }
  const routerSearch = () => {
    router.push('/base-search')
  }
  const handleCategory = (value) => {
    categoryActive.value = value
    if(value) {
      nextTick(() => {
        goodsCategoryRef.value.getSubcategory()
      })
    } else {
      // 刷新首页
    }
  }
  // 滚动
  const onScrollChange = ($e) => {
    scrollTopValue.value = $e.target.scrollTop
    let opacity = scrollTopValue.value / ANCHOR_SCROLL_TOP;
    navBarStyle.value.backgroundColor = 'rgba(255,103,26, ' + opacity + ')'
  }
  // 消息列表获取
  const messageGet = () => {
    getMessageList({pageNum: 1, pageSize: 100}).then(res => {
      const localMsg = localStorage.getItem('msg-read')
      let num = 0
      if(localMsg) {
        let msgIds = localMsg.split(',')
        if(res.data.length > 0) {
          res.data.map(item => {
            if(!msgIds.includes(''+item.id)) {
              ++ num
            }
          })
        }
      } else {
        res.data.map(item => {
          ++ num
        })
      }
      msgNum.value = num
    })
  }
  // 跳转消息页面
  const toMessageNofiy = () => {
    router.push('/message-notify')
  }
  // 下拉刷新
  const loading = ref(false)
  const onRefresh = () => {
    setTimeout(() => {
      Toast('刷新成功')
      loading.value = false
    }, 1000);
  };
</script>

<style lang="scss" scoped>
  .mall-page{
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
    :deep(.nav-bar){
      z-index: 9999;
    }
    .msg-wrap{
      position: relative;
      display: flex;
      align-items: center;
      img{
        width: 32px;
        height: 32px;
      }
      .badge{
        position: absolute;
        top: 5px;
        right: -2px;
        width: 15px;
        height: 15px;
        background: #FFFFFF;
        border-radius: 500px;
        font-size: 12px;
        transform: scale(0.9);
        transform-origin: left center;
        line-height: 15px;
        text-align: center;
        font-weight: bold;
      }
      .text{
        position: absolute;
        bottom: -10px;
        font-size: 18px;
        width: 40px;
        transform: scale(0.5);
        transform-origin: left;
        color: #ffffff;
        left: 7px;
      }
    }
    &-content{
      width: 375px;
      height: 100%;
      .bg-top.iphonex{
        // padding-top: 90px;
        padding-top: calc(68px + var(--safe-area-inset-top));
      }
      .bg-top{
        height: 86px;
        margin-bottom: -38px;
        padding-top: 68px;
        background: #FF671A;
        .category-wrapper{
          padding-right: 16px;
          padding-left: 15px;
        }
        .category-tab{
          display: flex;
          overflow-x: auto;
          overflow-y: hidden;
          margin-top: 10px;
          .item{
            word-break:keep-all;
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 21px;
          }
          .item +.item{
            margin-left: 25px;
          }
          .item.active {
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
          }
          .item.active::after{//主要是这个
            content: '';
            width: 20px;
            height: 2px;
            display: block;
            margin: 0 auto;
            margin-top: 6px;
            background: #FFFFFF;
            border-radius: 2px;
          }
        }
      }
      .index-ad-position{
        margin: 0 10px;
        border-radius: 8px;
      }
      .func-group{
        display: flex;
        flex-wrap: wrap;
        margin: 10px;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 20px 0 15px 0;
        .func-item{
          flex-basis: 25%;
          text-align: center;
          img{
            width: 44px;
            height: 44px;
          }
          .text{
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000000;
            line-height: 17px;
            margin-top: 12px;
          }
        }
      }
      .welfare{
        background: #FFFFFF;
        border-radius: 8px;
        padding: 0 15px;
        margin: 10px;
        margin-top: 8px;
        .welfare-title {
          font-size: 16px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #000000;
          line-height: 22px;
          padding-top: 20px;
          padding-bottom: 12px;
        }
        .welfare-item{
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          img{
            width: 158px;
            height: 76px;
            margin-bottom: 10px;
          }
        }
      }
      .recommend{
        padding-bottom: 10px;
        .recommend-title{
          margin: 14px;
          font-size: 15px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #000000;
          line-height: 21px;
        }
      }
    }
  }
</style>