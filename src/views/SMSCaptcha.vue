<template>
  <div class="page">
    <div class="close" @click="onClose"><van-icon name="cross" /></div>
    <div class="title">请输入短信验证码</div>
    <div class="desc">已发送短信验证码至 {{ phoneFormat(phone) }}</div>
    <van-password-input
      :value="value"
      :mask="false"
      :gutter="10"
      :error-info="errorInfo"
      :focused="showKeyboard"
      @focus="showKeyboard = true"
    />
    <van-number-keyboard
      v-model="value"
      :show="showKeyboard"
      @blur="showKeyboard = false"
      safe-area-inset-bottom
    />
    <div class="send-btn" v-if="modelValue === 0" @click="getCode">重新发送</div>
    <div class="send-btn reset" v-if="modelValue > 0">
      重新获取（{{ modelValue > 0 ? modelValue + 's' : '' }}）
    </div>
  </div>
</template>

<script setup>
import { phoneFormat } from '@/utils/common'
import { setToken } from '../utils/auth'
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  phone: {
    type: String,
    default: '',
  },
})
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const store = useStore()
const times = ref(route.params.times)
const value = ref('')
const errorInfo = ref('')
const showKeyboard = ref(true)
watch(value, (newVal) => {
  if (newVal.length === 6) {
    store
      .dispatch('Login', { phone: props.phone, code: value.value, requestType: 'APP' })
      .then((res) => {
        showKeyboard.value = false
        store.dispatch('GetInfo').then(() => {
          router.replace(route.query?.redirect || '/')
        })
      })
      .catch((err) => {
        errorInfo.value = err
      })
  }
})
const emit = defineEmits(['get-code', 'on-close'])
// 获取短信验证码
const getCode = () => {
  emit('get-code')
}
const onClose = () => {
  emit('on-close')
}
defineExpose({
  value,
})
</script>

<style lang="scss" scoped>
.page {
  padding: 25px;
  padding-top: 50px;
  .close {
    color: #c8c9cc;
    font-size: 22px;
    margin-top: 10px;
  }
  .title {
    margin-top: 80px;
    font-size: 26px;
    color: #363636;
    font-weight: bold;
    line-height: 37px;
  }
  .desc {
    font-size: 14px;
    color: #868686;
    margin-bottom: 40px;
  }
  .send-btn {
    color: #0864fe;
    margin-top: 26px;
    font-family: PingFangSC-Regular, PingFang SC !important;
    font-size: 14px;
  }
  .send-btn.reset {
    color: #a8a8a8;
  }
}
.van-password-input {
  margin: 0;
}
:deep(.van-password-input__item) {
  border-bottom: 1px solid #d1d1d3;
}
:deep(.van-password-input__item--focus) {
  border-bottom: 1px solid #363636;
}
</style>
