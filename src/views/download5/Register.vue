<template>
  <div class="register-container">
    <img class="logo" src="@/assets/images/download/five/register-bg.png">
    <div class="register-btn" @click="handleRegister">立即提现</div>
    <div class="main-bottom">
      <van-icon name="fire" color="#FF6700" />
      <div class="">{{ personNum }}人已领取</div>
    </div>
    <img class="icon-group" src="@/assets/images/download/five/icon-group.png">
    <img class="img-reason" src="@/assets/images/download/five/reason.png">
    <footer-info></footer-info>
    <form-context ref="formPopupRef"></form-context>
  </div>
</template>

<script setup>
  import { smsCode } from '@/api/base'
  import { login } from '@/api/login'
  import { showToast } from 'vant'
  import { pvuvOutside } from '@/utils/pvuv'
  import AgreementContent from './components/AgreementContent'
  import FooterInfo from './components/FooterInfo'
  import FormContext from './components/FormContext'
  import localforage from 'localforage'
  import { CountTo } from 'vue3-count-to'
  import { onBeforeMount } from 'vue'
  const route = useRoute()
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const codeText = ref('发送验证码')
  const disabled = ref(true)
  const times = ref(0)
  const codeRef = ref(null)
  const formPopupRef = ref(null)
  const iosExplain = ref(false)
  const personNum = ref(230200)

  const handleRegister = () => {
    formPopupRef.value.show = true
  }
  const resetInputVal = (value) => {
    const code = '' + form.value.code
    if (code > 6) {
      form.value.code = code.substring(0,6)
    }
  }
  const timerId = ref(null);

  const numberMethod = () => {
    personNum.value += Math.floor(Math.random() * 10 + 1)
    localStorage.setItem('personNum', personNum.value)
  }
  onUnmounted(() => {
    // 清除定时器
    if (timerId.value) {
      clearInterval(timerId.value);
      timerId.value = null;
    }
  });

  onMounted(async () => {
    let _personNum = localStorage.getItem('personNum')
    if (_personNum) {
      personNum.value = Number(_personNum)
    }
    timerId.value = setInterval(numberMethod, 1000); // 每1000毫秒调用一次yourMethod方法
    var u = navigator.userAgent;
    if(!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
      iosExplain.value = true
    }
    if (route.query.channel) {
      await pvuvOutside(route.query.channel)
    }
    document.title = '轻享花'
  })

</script>

<style lang="scss" scoped>
  .register-container{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    position: absolute;
    .logo{
      width: 100%;
      height: auto;
      display: block;
      margin-bottom: -56px;
    }
    .register-btn{
      margin: 0 35px;
      background: #FD493E;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      border-radius: 24px;
      font-weight: 500;
      font-size: 18px;
      height: 45px;
      position: relative;
    }
    .main-bottom{
      width: 343px;
      height: 31px;
      background: #FFF3F0;
      border-radius: 24px;
      margin: 20px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 14px;
      color: #A0A0A0;
      img{
        width: 17px;
        height: 17px;
        display: block;
        margin-right: 5px;
      }
    }
    .icon-group, .img-reason{
      width: 100%;
      height: auto;
      display: block;
      margin-top: 11px;
    }
  }
</style>
