<template>
  <van-popup
    v-model:show="show"
    safe-area-inset-bottom
    :style="{ height: '100%', width: '100%' }"
    class="full-screen"
    closeable
  >
    <div class="form">
      <div class="title">登录<span>/</span>注册</div>
      <div class="form-wrapper">
        <div class="form-group solid-bottom">
          <input v-model="form.phone" maxlength="11" type="tel" placeholder="输入本人常用手机号" />
        </div>
        <div class="form-group solid-bottom">
          <div class="input-code">
            <input
              ref="codeRef"
              type="tel"
              v-model="form.code"
              @input="resetInputVal"
              placeholder="请输入验证码"
            />
            <div class="line"></div>
            <div class="code-text" :class="`${times > 0 ? 'disabled' : ''}`" @click="getCode">
              {{ codeText }}
            </div>
          </div>
        </div>
        <div class="register-btn" @click="handleRegister">完成</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { smsCode } from '@/api/base'
import { login } from '@/api/login'
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const show = ref(false)
const times = ref(0)
const codeRef = ref(null)
const codeText = ref('发送验证码')
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  form.value.partnerCode = route.query.channel
})
const data = reactive({
  form: {
    phone: '',
    requestType: 'H5',
    partnerCode: '',
    code: '',
    checked: true,
  },
})
const { form } = toRefs(data)
const getCode = () => {
  if (times.value === 0) {
    if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
      showToast('请填写正确手机号码')
      return
    }
    times.value = 60
    codeText.value = times.value + 's重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '发送验证码'
      }
    }, 1000)
    codeRef.value.focus()
    smsCode({ phone: form.value.phone, smsType: 'VERIFY_JYQ' })
  }
}
const handleRegister = () => {
  if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
    showToast('请填写正确手机号码')
    return
  }
  if (!form.value.code) {
    showToast('请填写验证码')
    return
  }
  login(form.value).then((res) => {
    window._czc.push(['_trackEvent', 'download', 'register', form.value.phone, 1, undefined])
    proxy.onToastSucc(() => {
      show.value = false
      router.push('/download5/product')
    }, '激活成功，快去下载使用吧')
  })
}
defineExpose({ show })
</script>
<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/download/five/form-bg.png') no-repeat;
  background-size: 100% 300px;
  .title {
    font-size: 22px;
    text-align: center;
    font-weight: bold;
    padding-top: 200px;
    span {
      font-weight: 400;
    }
  }
  .form-wrapper {
    padding: 0 35px;
    margin-top: 50px;
    .form-group {
      padding: 10px;
      input {
        background: transparent;
        font-size: 16px;
        width: 100%;
      }
      .input-code {
        display: flex;
        align-items: center;
        flex: 1;
        margin-top: 5px;
      }
      .line {
        height: 23px;
        width: 1px;
        background: #e7e7e7;
      }
      .code-text {
        font-size: 14px;
        color: #ff4119;
        line-height: 20px;
        margin-left: 20px;
        flex-shrink: 0;
      }
    }
  }
  .register-btn {
    margin-top: 40px;
    font-size: 18px;
    color: #ffffff;
    font-weight: bold;
    background: #ff4119;
    border-radius: 22px;
    height: 44px;
    line-height: 44px;
    text-align: center;
  }
}
</style>
