<template>
  <div class="shopping-cart-page">
    <navigation-bar pageName="购物车" :isShowBack="false">
      <template #nav-right>
        <div @click="isManage=!isManage">
          {{ isManage ? '管理' : '完成' }}
        </div>
      </template>
    </navigation-bar>
    <cart-page-content :is-manage="isManage" />
  </div>
</template>

<script setup name="ShpppingCart">
import NavigationBar from '@/components/NavigationBar'
import CartPageContent from './components/CartPageContent'
const  isIphoneX = window.isIphoneX
const isManage = ref(true)
</script>

<style lang="scss" scoped>
  .shopping-cart-page{
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
</style>