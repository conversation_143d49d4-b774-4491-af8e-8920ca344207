<template>
  <div class="shopping-cart-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
    <div class="flex-sub">
      <div class="empty-cart" v-if="list.length === 0">
        <img src="@/assets/images/goods/cart-empty.png" />
        <div class="text">您还没有选购商品</div>
        <div class="text">赶紧去挑选喜欢的商品吧</div>
        <div class="empty-btn theme-bg" @click="goIndex">去挑选商品</div>
      </div>
      <div class="list" v-else>
        <component
          :is="isManage ? 'van-radio-group' : 'van-checkbox-group'"
          :model-value="isManage ? checkedList?.[0] : checkedList"
          @update:model-value="isManage ? (checkedList = [$event]) : (checkedList = $event)"
          @change="checkChange"
        >
          <!-- <van-radio-group v-model="checkedList" @change="checkChange"> -->

          <div class="item" v-for="(item, index) in list" :key="index">
            <van-swipe-cell>
              <div class="item-content">
                <component
                  :is="isManage ? 'van-radio' : 'van-checkbox'"
                  :name="item.cartId"
                  :checked-color="$global.THMEM"
                />
                <div class="goods-info">
                  <img class="goods-img" :src="item.albumPics" />
                  <div class="goods-other">
                    <div class="goods-name overflow-2">{{ item.spuName }}</div>
                    <div class="goods-spec overflow-1">{{ item.spec }}</div>
                    <div class="goods-price">
                      <div class="price">￥{{ item.salesPrice }}</div>
                      <van-stepper
                        v-model="item.quantity"
                        @change="changeNum($event, item)"
                        integer
                        :max="item.stock"
                        button-size="24px"
                        input-width="40px"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <template #right>
                <van-button
                  square
                  text="删除"
                  type="danger"
                  class="delete-button"
                  @click="itemDeleteCart(item, index)"
                />
              </template>
            </van-swipe-cell>
          </div>
          <!-- </van-radio-group> -->
        </component>
      </div>
      <GuessYouLike />
      <GoodsList :listType="listType" />
    </div>
    <div class="footer-total" :class="{ 'iphonex-bottom': isIphoneX }" v-if="list.length > 0">
      <van-checkbox
        v-if="!isManage"
        v-model="allChecked"
        :checked-color="$global.THMEM"
        @update:model-value="allChange"
        >全选</van-checkbox
      >
      <div class="footer-total-right">
        <template v-if="isManage">
          <div class="text">
            合计：<span class="amount">￥{{ totalAmount }}</span>
          </div>
          <div class="btn theme-linear-gradient" @click="handlePay">
            去结算({{ checkedList.length }})
          </div>
        </template>
        <div v-else class="delete-btn" @click="delShow = true">删除({{ checkedList.length }})</div>
      </div>
    </div>
    <van-popup round v-model:show="delShow">
      <div class="warning-popup">
        <div class="title">确定移除选择的商品吗？</div>
        <div class="content">
          <div class="btn text-black" @click.stop="delShow = false">取消</div>
          <div class="btn theme-bg text-white" @click.stop="submitDelete">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { listShoppingCart, updateNum, deleteShppingCart } from '@/api/shopping-cart'
import GoodsList from '@/components/GoodsList/GoodsList.vue'
import { showToast, showSuccessToast } from 'vant'
import { computed, onActivated } from 'vue'
import GuessYouLike from '@/components/GoodsList/GuessYouLike.vue'
import { Decimal } from 'decimal.js'

const isIphoneX = window.isIphoneX
const store = useStore()
const { proxy } = getCurrentInstance()
const user = computed(() => store.getters.userInfo)
const router = useRouter()
const delShow = ref(false)
const checkedList = ref([])
const allChecked = computed(() => {
  return checkedList.value.length === list.value.length
})
const list = ref([])
const totalAmount = computed(() => {
  return checkedList.value
    .reduce((acc, cur) => {
      const item = list.value.find((item) => item.cartId === cur)
      return acc.add(new Decimal(item.salesPrice).mul(item.quantity))
    }, new Decimal(0))
    .toNumber()
})
const listType = { type: 'region', value: proxy.$global.GOODS_REGION_FOLLOW }
let timer = null
const props = defineProps({
  isManage: {
    type: Boolean,
    default: true,
  },
})
watch(
  () => props.isManage,
  (val) => {
    checkedList.value = []
    // checkChange([])
    // if (val) {
    // } else {
    //   checkedList.value = null
    // }
  }
)
onActivated(() => {
  // allChecked.value = false
  checkedList.value = []
  getList()
})
// 清除选中
onMounted(() => {
  // allChecked.value = false
  checkedList.value = []
  getList()
})
const goIndex = () => {
  proxy.$menuRouter('/')
}
// 购物车数量修改
const changeNum = (event, item) => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(function () {
    timer = null
    updateNumFun(event, item)
  }, 200)
}
const updateNumFun = (event, item) => {
  updateNum({ id: item.cartId, quantity: event }).then((res) => {
    // showSuccessToast('操作成功！')
    computedTotal(checkedList.value)
  })
}
// 删除购物车
const itemDeleteCart = (item, index) => {
  deleteShppingCart({ idList: [item.cartId] }).then((res) => {
    proxy.onToastSucc(() => {
      list.value.splice(index, 1)
      checkedList.value = checkedList.value.filter((checked) => checked != item.cartId)
      computedTotal(checkedList.value)
    }, '删除成功！')
  })
}
// 批量删除
const submitDelete = () => {
  delShow.value = false
  deleteShppingCart({ idList: checkedList.value }).then((res) => {
    proxy.onToastSucc(() => {
      getList()
      checkedList.value = []
      // allChecked.value = false
      // totalAmount.value = 0
    }, '删除成功！')
  })
}
// 勾选
const checkChange = (val) => {
  if (props.isManage) {
    val = [val]
  }

  // if (val.length === list.value.length) {
  //   // 修改全选复选框
  //   allChecked.value = true
  // } else {
  //   allChecked.value = false
  // }
  if (val.length > 0) {
    // computedTotal(val)
  } else {
    totalAmount.value = 0
  }
}
// 重新计算总额
// const computedTotal = (val) => {
//   let amount = 0
//   for (let i = 0; i <= val.length; i++) {
//     list.value.forEach((item) => {
//       if (item.cartId === val[i]) {
//         amount += item.salesPrice * item.quantity
//       }
//     })
//   }
//   totalAmount.value = amount
// }
// 全选/反选
const allChange = (val) => {
  checkedList.value = val ? list.value.map((item) => item.cartId) : []

  // checkedList.value = []
  // if (allChecked.value) {
  //   let amount = 0
  //   list.value.forEach((item) => {
  //     amount += item.salesPrice * item.quantity
  //     checkedList.value.push(item.cartId)
  //   })
  //   totalAmount.value = amount
  // } else {
  //   totalAmount.value = 0
  // }
}
const getList = () => {
  if (user.value) {
    listShoppingCart({ pageNum: 1, pageSize: 100 }).then((res) => {
      list.value = res.data
    })
  }
}
const handlePay = () => {
  let params = []
  if (checkedList.value.length > 0) {
    for (let i = 0; i <= checkedList.value.length; i++) {
      list.value.forEach((item) => {
        if (item.cartId === checkedList.value[i]) {
          params.push({
            spuId: item.spuId,
            skuId: item.skuId,
            quantity: item.quantity,
          })
        }
      })
    }
    localStorage.setItem(
      proxy.$global.PARAM_ORDERCONFIRM,
      JSON.stringify({
        shoppingFlag: 'Y',
        msOrderSpus: params,
      })
    )
    router.push('/order-confirm')
  } else {
    showToast('请选择商品')
  }
}
</script>

<style lang="scss" scoped>
.shopping-cart-page-context {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.empty-cart {
  background: #ffffff;
  padding: 35px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  img {
    width: 182px;
    height: 135px;
    display: block;
    margin-bottom: 8px;
  }

  .text {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 17px;
  }

  .empty-btn {
    margin-top: 21px;
    text-align: center;
    width: 120px;
    height: 36px;
    border-radius: 18px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 36px;
  }
}

.list {
  margin: 10px;
  background: #ffffff;
  border-radius: 4px;
  padding-top: 12px;

  .title {
    display: flex;
    align-items: center;
    padding: 19px 13px;

    img {
      width: 26px;
      height: 26px;
      margin-right: 8px;
    }

    .text {
      font-size: 14px;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #333333;
      line-height: 20px;
    }
  }

  .item {
    padding-bottom: 20px;
    padding-left: 14px;

    &-content {
      display: flex;
      padding-right: 14px;
      align-items: center;

      .van-checkbox {
        overflow: inherit;
      }

      .goods-info {
        display: flex;
        margin-left: 6px;
        flex: 1;

        .goods-img {
          width: 80px;
          height: 80px;
          flex-shrink: 0;
          border-radius: 4px;
          margin-right: 14px;
        }

        .goods-other {
          flex: 1;

          .goods-name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 20px;
          }

          .goods-spec {
            font-size: 12px;
            transform: scale(0.85);
            transform-origin: left top;
            color: #999999;
            line-height: 14px;
            margin-top: 4px;
          }

          .goods-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 6px;

            .price {
              font-size: 16px;
              font-family: PingFang-SC-Bold, PingFang-SC;
              font-weight: bold;
              color: #ff4019;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
}

.goods-recommend {
  margin-top: 30px;
  margin-bottom: 100px;

  .title {
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    line-height: 22px;
    margin-bottom: 10px;
    text-align: center;
  }
}

.footer-total {
  position: fixed;
  width: calc(100% - 32px);
  padding: 10px 16px;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0;

  &-right {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .text {
      font-size: 14px;
      color: #666666;
      line-height: 20px;

      .amount {
        font-size: 16px;
        font-weight: bold;
        color: #ff4019;
      }
    }

    .btn {
      width: 100px;
      height: 40px;
      border-radius: 20px;
      font-size: 14px;
      color: #ffffff;
      text-align: center;
      line-height: 40px;
      margin-left: 7px;
    }
  }

  .delete-btn {
    width: 100px;
    height: 38px;
    border-radius: 20px;
    font-size: 14px;
    text-align: center;
    line-height: 40px;
    color: #999999;
    border: 1px solid #d5d5d5;
  }
}

.footer-total.iphonex-bottom {
  // padding-bottom: 51px !important;
  padding-bottom: calc(10px + var(--safe-area-inset-bottom));
}

.delete-button {
  height: 100%;
}

.warning-popup {
  width: 235px;
  background: #ffffff;

  .title {
    text-align: center;
    margin-top: 19px;
    font-size: 16px;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
  }

  .content {
    display: flex;
    justify-content: space-between;
    margin: 27px 20px 16px;

    .btn {
      width: 90px;
      height: 32px;
      background: #eeeeee;
      border-radius: 16px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
  }
}
.footer-total-right {
  margin-left: auto;
}
</style>
