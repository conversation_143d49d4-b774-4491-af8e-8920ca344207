<template>
  <div ref="downloadRef" class="download-container">
    <div class="mask" id="mask">
      <div class="info">
        <span>点击右上角的</span>
        <div class="bg">
          <img src="@/assets/images/download/dian.png" />
        </div>
        <div class="flex align-center margin-top-xs">
          <span>选择在浏览器打开</span>
          <img src="@/assets/images/download/browser.png" class="browser" />
        </div>
        <img class="arrow" src="@/assets/images/download/arrow.png" />
      </div>
    </div>
    <img class="download-bg" src="@/assets/images/download/six/download-bg.png" />
    <div class="download-main">
      <div class="number-wrapper">
        ¥
        <van-rolling-text
          :start-num="100000"
          :target-num="188888"
          direction="up"
          stop-order="rtl"
          :height="rollingHeight"
        />
      </div>
      <div class="download-btn theme-linear-gradient" @click="handleDownload()">
        下载APP·立即提现
      </div>
      <div class="hint">请使用注册手机号{{ phone }}登录</div>
    </div>
  </div>
</template>

<script setup>
import onDownload from '@/utils/downloadApp'
import { pvuvOutside } from '@/utils/pvuv'
import localforage from 'localforage'
import { CountTo } from 'vue3-count-to'
import { useWindowSize } from '@vueuse/core'
import Decimal from 'decimal.js'

const { width } = useWindowSize()
const rollingHeight = computed(() => new Decimal(64).div(375).mul(width.value).toDP(0).toNumber())
const iosExplain = ref(false)
const downloadRef = ref(null)
const route = useRoute()
const phone = ref(null)
const handleDownload = () => {
  var u = navigator.userAgent
  if (u.toLowerCase().indexOf('micromessenger') > -1 || u.toLowerCase().indexOf(' qq/') > -1) {
    document.getElementById('mask').style.display = 'block'
    downloadRef.value.style.overflowY = 'hidden'
  }
  window._czc.push(['_trackEvent', 'download', 'download', 'download', 1, 'download-btn'])
  onDownload()
}
const getPhone = async () => {
  let phoneNum = await localforage.getItem('register-phone')
  phone.value = phoneNum.substring(0, 3) + '*'.repeat(6) + phoneNum.substring(9)
}
onMounted(async () => {
  getPhone()
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  // if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
  //   iosExplain.value = true
  // }
  document.title = '轻享花'
})
</script>

<style lang="scss" scoped>
.download-container {
  width: 100%;
  overflow-y: auto;
  position: relative;
  .mask {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    background: rgba($color: #000000, $alpha: 0.7);
    z-index: 9999;
    .info {
      position: relative;
      color: #ffffff;
      padding-top: 40px;
      padding-left: 30px;
      font-size: 18px;
      .bg {
        background: #ffffff;
        border-radius: 2px;
        color: #000;
        padding: 0 3px;
        display: inline-block;
        height: 15px;
        margin-left: 2px;
        img {
          width: 15px;
        }
      }
      .browser {
        width: 18px;
        height: 18px;
        display: inline-block;
        margin-left: 2px;
      }
      .arrow {
        width: 80px;
        position: absolute;
        right: 10px;
        top: 20px;
      }
    }
  }
  .download-bg {
    display: block;
    width: 100%;
    height: auto;
  }
  .download-main {
    width: 345px;
    height: 391px;
    background-image: url('@/assets/images/download/six/download-main-bg.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    position: absolute;
    top: 121px;
    left: 15px;
    .number-wrapper {
      text-align: center;
      margin-top: 72px;
      color: #4671eb;
      font-size: 55px;
      font-weight: bold;
      font-family: DIN !important;
      position: relative;
      text-align: center;
      transform: translate3d(0px, 0px, 0px);
      .van-rolling-text {
        --van-rolling-text-color: #4671eb;
        --van-rolling-text-font-size: 55px;
        --van-rolling-text-item-width: 30px;
        // height: 64px;
        :deep() {
          .van-rolling-text-item {
            // height: 64px !important;
          }
          .van-rolling-text-item__item {
            font-family: DIN !important;
            // line-height: 64px !important;
          }
        }
      }
    }
    .number-wrapper::first-letter {
      font-size: 40px;
      font-weight: normal;
    }
    .download-btn {
      width: 311px;
      height: 47px;
      border-radius: 24px;
      line-height: 47px;
      text-align: center;
      font-size: 17px;
      margin: 12px 0 0 17px;
      color: #ffffff;
    }
    .hint {
      font-size: 14px;
      color: #ff6d1c;
      text-align: center;
      margin-top: 157px;
    }
  }
}
</style>
