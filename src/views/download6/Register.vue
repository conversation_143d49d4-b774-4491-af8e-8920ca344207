<template>
  <div class="register-container">
    <img class="register-bg" src="@/assets/images/download/six/register-bg.png" />
    <div class="register-main">
      <div class="number-wrapper">
        ¥
        <van-rolling-text
          :start-num="100000"
          :target-num="200000"
          direction="up"
          stop-order="rtl"
          :height="rollingHeight"
        />
      </div>
      <div class="form-wrapper">
        <div class="form-group">
          <img src="@/assets/images/download/six/phone-icon.png" />
          <input v-model="form.phone" type="tel" placeholder="请输入手机号码" />
        </div>
        <div class="form-group">
          <img
            src="@/assets/images/download/six/code-icon.png"
            style="width: 20px; height: auto; margin: 0 8px 0 -1px"
          />
          <div class="input-code">
            <input
              ref="codeRef"
              type="tel"
              v-model="form.code"
              @input="resetInputVal"
              placeholder="请输入验证码"
            />
            <div class="code-text" :class="`${times > 0 ? 'disabled' : ''}`" @click="getCode">
              {{ codeText }}
            </div>
          </div>
        </div>
        <div
          class="btn theme-linear-gradient"
          :class="{ 'btn-disabled': disabled }"
          @click="handleRegister()"
        >
          查看我的额度
        </div>
        <agreement-content v-model="form.checked" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { smsCode } from '@/api/base'
import { login } from '@/api/login'
import { showToast } from 'vant'
import { pvuvOutside } from '@/utils/pvuv'
import AgreementContent from './components/AgreementContent'
import localforage from 'localforage'
import { CountTo } from 'vue3-count-to'
import { useWindowSize } from '@vueuse/core'
import Decimal from 'decimal.js'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const codeText = ref('获取验证码')
const disabled = ref(true)
const times = ref(0)
const codeRef = ref(null)
const iosExplain = ref(false)
const { width } = useWindowSize()
const rollingHeight = computed(() => new Decimal(64).div(375).mul(width.value).toDP(0).toNumber())
const data = reactive({
  form: {
    phone: '',
    requestType: 'H5',
    partnerCode: '',
    code: '',
    checked: true,
  },
})
const { form } = toRefs(data)
watch(form.value, (newVal) => {
  if (
    newVal.checked &&
    newVal.phone &&
    newVal.phone.length === 11 &&
    newVal.code &&
    ('' + newVal.code).length === 6
  ) {
    disabled.value = false
  } else {
    disabled.value = true
  }
})
const getCode = () => {
  if (times.value === 0) {
    if (!form.value.phone || !proxy.validPhone(form.value.phone)) {
      showToast('请填写正确手机号码')
      return
    }
    times.value = 60
    codeText.value = times.value + 's重新发送'
    const timer = setInterval(function () {
      times.value--
      codeText.value = times.value + 's重新发送'
      if (times.value === 0) {
        clearInterval(timer)
        codeText.value = '发送验证码'
      }
    }, 1000)
    codeRef.value.focus()
    smsCode({ phone: form.value.phone, smsType: 'VERIFY_JYQ' })
  }
}
const resetInputVal = (value) => {
  const code = '' + form.value.code
  if (code > 6) {
    form.value.code = code.substring(0, 6)
  }
}
const handleRegister = () => {
  if (disabled.value) return
  login(form.value).then((res) => {
    window._czc.push(['_trackEvent', 'download6', 'register', form.value.phone, 1, undefined])
    proxy.onToastSucc(() => {
      localforage.setItem('register-succcess', 1)
      localforage.setItem('register-phone', form.value.phone)
      router.replace('/download6/product')
    }, '激活成功，快去下载使用吧')
  })
}

onMounted(async () => {
  var u = navigator.userAgent
  if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    iosExplain.value = true
  }
  form.value.partnerCode = route.query.channel
  if (route.query.channel) {
    await pvuvOutside(route.query.channel)
  }
  document.title = '轻享花'
  const registered = await localforage.getItem('register-succcess')
  const phone = await localforage.getItem('register-phone')
  if (registered && phone) {
    router.replace('/download6/product')
  }
})
</script>

<style lang="scss" scoped>
.register-container {
  width: 100%;
  overflow: auto;
  position: relative;
  .register-bg {
    display: block;
    width: 100%;
    height: auto;
  }
  .register-main {
    width: 345px;
    height: 391px;
    background-image: url('@/assets/images/download/six/register-main-bg.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    position: absolute;
    top: 121px;
    left: 15px;
    .number-wrapper {
      text-align: center;
      margin-top: 72px;
      color: #4671eb;
      font-size: 55px;
      font-weight: bold;
      font-family: DIN !important;
      position: relative;
      text-align: center;
      transform: translate3d(0px, 0px, 0px);
      .van-rolling-text {
        --van-rolling-text-color: #4671eb;
        --van-rolling-text-font-size: 55px;
        --van-rolling-text-item-width: 30px;
        // height: 64px;
        :deep() {
          .van-rolling-text-item {
            // height: 64px !important;
          }
          .van-rolling-text-item__item {
            font-family: DIN !important;
            // line-height: 64px !important;
          }
        }
      }
    }
    .number-wrapper::first-letter {
      font-size: 40px;
      font-weight: normal;
    }
    .form-wrapper {
      margin-top: 12px;
      padding: 0 17px;
      .form-group {
        background: #fffafa;
        border-radius: 22px;
        height: 47px;
        display: flex;
        align-items: center;
        padding: 0 19px;
        img {
          width: 17px;
          height: auto;
          display: block;
          margin-right: 10px;
        }
        input {
          background: transparent;
          font-size: 16px;
          width: 100%;
        }
        .input-code {
          display: flex;
          align-items: center;
          flex: 1;
          input {
            flex: 1;
          }
          .code-text {
            font-size: 16px;
            font-weight: 600;
            color: #ff321e;
            line-height: 23px;
          }
        }
      }
      .form-group + .form-group {
        margin-top: 14px;
      }
      .btn {
        width: 100%;
        height: 47px;
        border-radius: 24px;
        line-height: 47px;
        text-align: center;
        font-size: 17px;
        margin-top: 13px;
        color: #ffffff;
      }
      .btn.btn-disabled {
        background: #ccc;
      }
    }
  }
}
</style>
