<template>
  <div class="coupon-page">
    <navigation-bar pageName="我的优惠券" @onLeftClick="onBackClick" />
    <!-- tabs -->
    <div v-if="navList.length > 0" class="nav-list">
      <div
        :class="`nav-item ${item.state === queryParams.filter ? 'active' : ''}`"
        v-for="(item, index) in navList"
        :key="index"
        @click="handleNav(item)"
      >
        {{ item.stateDesc }}({{ item.quantity }})
      </div>
    </div>
    <div class="coupon-page-content" :class="{ 'iphonex-bottom': isIphoneX }">
      <div v-if="list.length > 0" class="list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <Coupon :list="list" :queryParams="queryParams" />

          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="——没有更多了——"
            @load="getList"
            :immediate-check="false"
          >
          </van-list>
        </van-pull-refresh>
      </div>
      <van-empty v-else description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import Coupon from './components/Coupon.vue'
import { getCoupons, couponsNumber } from '@/api/market'

const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const store = useStore()
const route = useRoute()
const router = useRouter()
const user = computed(() => store.getters.userInfo)
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const navList = ref([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: 'DISCOUNTS', // 优惠劵
    filter: 'UNUSED', // UNUSED：未使用 USED：已使用 EXPIRE：过期
  },
})
const { queryParams } = toRefs(data)
// 重置查询
const resetQuery = () => {
  queryParams.value.pageNum = 1
  list.value = []
  getList()
}
const handleNav = (item) => {
  queryParams.value.filter = item.state
  resetQuery()
}
// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
}

// 获取优惠劵列表
const getList = () => {
  finished.value = false
  getCoupons(queryParams.value)
    .then((res) => {
      loading.value = false
      list.value = [...list.value, ...res.data]
      if (list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum++
      }
    })
    .catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
}

// 获取优惠卷数量
const getCouponsNumber = () => {
  couponsNumber().then((res) => {
    navList.value = res.data
  })
}

const onBackClick = () => {
  router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')

  if (user.value) {
    // 获取优惠券数据
    getList()
    getCouponsNumber()
  }
})
</script>
<style scoped lang='scss'>
.coupon-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #ffffff;
  .nav-list {
    height: 44px;
    display: flex;
    align-items: center;
    background: #ffffff;
    font-size: 14px;
    margin-top: 10px;
    .nav-item {
      height: 44px;
      flex: 1;
      font-weight: 400;
      color: #999999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .nav-item.active {
      font-weight: bold;
      color: #4671eb;
      position: relative;
    }
    .nav-item.active::after {
      //主要是这个
      content: '';
      display: block;
      margin: 0 auto;
      width: 22px;
      height: 4px;
      background: #4671eb;
      border-radius: 8px;
      position: absolute;
      left: calc(50% - 11px);
      bottom: 6px;
    }
  }
  .coupon-page-content {
    overflow-y: scroll;
  }
}
</style>