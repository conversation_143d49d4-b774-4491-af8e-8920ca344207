<template>
    <!-- 优惠券列表 -->
    <div class="coupon" v-for="(item,index) in list" :key="index">
       <div class="coupon-item">
            <div class="coupon-ticket" :class="queryParams.filter === 'UNUSED' ? 'isUse' : 'diabled'">
              <div class="coupon-amount-container" v-if="item.ticketDefine">
                  <!-- 固定金额 -->
                  <div class="coupon_amount" v-if="item.ticketDefine.discountType == 'FIXED'">
                      <span>￥</span>{{  item.ticketDefine.ticketDiscount && item.ticketDefine.ticketDiscount.value }}
                  </div>
                  <!-- 折扣 -->
                  <div class="coupon_amount" v-else-if="item.ticketDefine.discountType == 'PERCENT'">
                      {{  item.ticketDefine.ticketDiscount && item.ticketDefine.ticketDiscount.value }}<span>折</span>
                  </div>
                  <div class="coupon_limit">{{ item.ticketDefine.ruleDesc }}</div>
              </div>
              <div class="coupon-content-container">
                  <div class="coupon-content" v-if="item.ticketDefine">
                    <div class="name">{{ item.name }}</div>
                    <div class="remark" v-if="item.ticketDefine.ticketThreshold && item.ticketDefine.ticketThreshold.type === 'MIN_AMOUNT'">满{{ item.ticketDefine.ticketThreshold.amount }}元可用</div>
                    <div class="time">{{ secondsToDate(item.startTime) }}~{{ secondsToDate(item.endTime) }}</div>
                    <div class="srouce">
                      来源于{{ item.receiveSource === 'TAKE' ? '客户主动领取': 
                      item.receiveSource === 'GRANT' ? '系统发放' : item.receiveSource === 'ORDER' ? '普通商品购买' : 
                      item.receiveSource === 'BENEFIT' ? '会员权益购买' : item.receiveSource === 'AUTO' ? '系统赠送' : 
                      item.receiveSource ==='VIP' ? '' : '会员权益领取' }}
                    </div>
                  </div>
              </div>
            </div>
       </div>
    </div>
</template>
<script>
import { defineComponent, ref } from 'vue'
import { secondsToDate } from '@/utils/date'

export default defineComponent({
    props:{
      list:{
          default:[],
          type: Array
      },
      queryParams:{
          default:{},
          type: Object
      }
    },
    setup(props,{emit}){
        // 规则
        const currentIndex = ref()
        const isShowRules = ref(false)
        const changeRules = (index) => {
          if( currentIndex.value === index){
              isShowRules.value = !isShowRules.value
          }else{
              currentIndex.value = index
              isShowRules.value = true
          }
        }
        // 使用优惠卷
        const router = useRouter()
        const handleUse = () => {
            router.push({path:'/'})
        }

        return{
            currentIndex,
            isShowRules,
            changeRules,
            handleUse,
            secondsToDate
        }
    }
})

</script>

<style lang="scss" scoped>
.coupon{
    width: calc(100% - 20px);
    margin: 12px 16px 0;
    
    .coupon-item{
        margin-bottom: 12px;
    }
    .coupon-ticket.diabled{
      filter: grayscale(1);
    }
    .coupon-ticket{
        width: 343px;
        height: 95px;
        background: url('@/assets/images/market/coupon-bg.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        box-sizing: border-box;
        .coupon-amount-container{
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
          width: 114px;
          height: 100%;
          .coupon_amount{
            font-family: DIN, DIN;
            font-size: 32px;
            color: #704B35;
            font-weight: bold;
            span{
              font-size: 16px;
            }
          }
          .coupon_limit{
            font-size: 14px;
            color: #704B35;
            line-height: 16px;
            margin-top: 5px;
          }
        }

        .coupon-content-container{
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            padding: 10px 20px;

            .coupon-content{
                display: flex;
                flex-flow: column;
                justify-content: center;
                height: 100%;
                .name{
                  font-size: 14px;
                  color: #704B35;
                  font-weight: bold;
                  line-height: 16px;
                }
                .time{
                  font-size: 12px;
                  color: rgba(112,75,53,0.6);
                  line-height: 14px;
                  margin-top: 2px;
                }
                .srouce,.remark{
                  margin-top: 4px;
                  font-size: 12px;
                  color: rgba(112,75,53,0.6);
                  margin-top: 4px;
                  line-height: 14px;
                }
            }

            .coupon-btn{
                font-weight: bold;
                font-size: 12px;
                color: #FFFFFF;
                text-align: center;
                button{
                    min-width: 55px;
                    height: 24px;
                    line-height: 24px;
                    background: #C2C2C2;
                    border-radius: 12px;
                    border: none;
                }
            }

            .coupon-tag{
                position: absolute;
                top: 0;
                right: 0;
                width: 54px;
                height: 43px;
                
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }

        &.isUse{
            .coupon_amount{
              color: #704B35;
            }
            .coupon_limit{
              color: #704B35;
            }
        }


    }

    .coupon-rule{
        background: #F7F7F7;
        border-radius: 0px 0px 8px 8px;
        padding: 10px;
        div{
            font-weight: 400;
            font-size: 12px;
            color: #7B7B7B;
            line-height: 18px;
            text-align: left;
            word-wrap: break-word;
        }
        &.isUse{
            background: #FDF8F9;
        }
    }
}
</style>