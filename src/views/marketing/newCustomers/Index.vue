<template>
  <div class="new-cust-page">
    <navigation-bar pageName="新人福利" @onLeftClick="onBackClick" />
    <div class="new-cust-page-content">
        <div class="new-cust-banner">
          <img  class="newbron-img">
          <div class="time">
            <span>福利有效时间</span>
            <van-count-down class="count-down" v-if="expirationTime" :time="expirationTime * 1000" />
          </div>

          <img class="rule" @click="showActivityRules">
        </div>
        <div class="goods-info bg-white">
          <div class="goods-category">
            <van-tabs @change="changeCategory" v-model:active="categoryId" title-inactive-color="#222222" title-active-color="#F9433C" color="#F9433C" line-width="20px">
              <van-tab
                v-for="(item,index) in categoryList"
                :title="item.categoryName"
                :name="item.categoryId"
                :key="item.categoryId"
                />
            </van-tabs>
          </div>
          <div class="goods-list">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
              <van-list
                  v-model:loading="loading"
                  :finished="finished"
                  finished-text="——没有更多了——"
                  @load="getList"
                  :immediate-check="false"
                  :class="{ 'iphonex-bottom': isIphoneX }"
              >
                <goods-list :data="list" />
              </van-list>
            </van-pull-refresh>
          </div>
        </div>
    </div>
      <PopupTips
      v-model:show="isShow"
      title="新人活动规则"
      btnText="我知道了"
      :popupWidth="$px2rem(280 + 'px')"
      btnBackground="linear-gradient( 92deg, #FE722A 0%, #FD3B4B 100%)"
      @onClick="showActivityRules"
    >
      <template v-slot:content>
        1、新人是指注册并首次登录48小时内的新用户，超48小时后不能参与新人限时福利活动。<br>
        2、页面展示为实际补贴金额，改选其他品类价格变化，以实际补贴金额为准。<br>
        3、新人专区商品会随时更新品种，欢迎抢购。
      </template>
    </PopupTips>
  </div>

</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { regionGoodsPage } from '@/api/goods'
import GoodsList from './components/GoodsList'
import { getCategory } from '@/api/market'
import PopupTips from '@/components/PopupTips'

const { proxy } = getCurrentInstance();
const isIphoneX = window.isIphoneX
const store = useStore();
const route = useRoute();
const router = useRouter();
const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      categoryId: null
    }
  })
  const { queryParams } = toRefs(data)
  const list = ref([])

  const loading = ref(false)
  const finished = ref(false)
  const refreshing = ref(false)

  // 获取分类列表
  let categoryList = ref([])
  const getCategoryList = () => {
    let params = {
      regionType: "NEW_CUSTOMER" // 新人专区
    }
    getCategory(params).then(res => {
      categoryList.value = res.data
    })
  }

  // 选择商品分类
  const categoryId = ref()
  const changeCategory = (val) => {
    queryParams.value.categoryId = val
    resetQuery()
  }

  const resetQuery = () => {
    queryParams.value.pageNum = 1
    list.value = []
    getList()
  }

  // 获取商品
  const getList = () => {
    regionGoodsPage({
      regionType: proxy.$global.GOODS_REGION_NEW_CUSTOMER,
      categoryId: categoryId.value,
    ...queryParams.value }).then(res => {
      loading.value = false
      refreshing.value = false
      list.value = [...list.value, ...res.data]
      if(list.value.length >= res.total) {
        finished.value = true
      } else {
        queryParams.value.pageNum ++
      }
    }).catch(() => {
      loading.value = false
      finished.value = true // 防止死循环
    })
  }


  // 展示活动规则
  let isShow = ref(false)
  const showActivityRules = () => {
    isShow.value = !isShow.value
  }

  // 获取用户信息
  let expirationTime = ref(null)
  const getUserInfo = () => {
    store.dispatch('GetInfo').then(res => {
      expirationTime.value = res.data.expirationTime
    })
  }


// 下拉刷新
const onRefresh = () => {
  finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  resetQuery()
};
const onBackClick = () => {
    router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  getUserInfo()
  getCategoryList()
})
</script>

<style scoped lang='scss'>
    .new-cust-page{
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        position: absolute;
        background: #FAD6C6;
        &-content{
          flex: 1;
          display: flex;
          flex-direction: column;
          .newbron-img{
            width: 100%;
            height: 129px;
          }
          .new-cust-banner{
            position: relative;
            .time{
              position: absolute;
              bottom: 19px;
              width: 100%;
              font-weight: 400;
              font-size: 12px;
              color: #FFFFFF;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              .count-down{
                color: #FFFFFF !important;
                font-size: 12px !important;
                margin-left: 5px;
              }
            }
            .rule{
              position: absolute;
              top: 44px;
              right: 0px;
              width: 23px;
              height: 61px;
            }
          }
          .goods-info{
            flex: 1;
            margin: 12px;
            border-radius: 7px;
            .goods-list{
              margin: 12px;
              overflow-y: scroll;
              height: calc(100vh - 320px);
              height: calc(100dvh - 320px);

            }
          }
        }
    }
</style>