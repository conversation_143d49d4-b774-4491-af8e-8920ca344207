<template>
  <div>
    <div class="goods-list-item" v-for="item in data" :key="item.spuId">
        <img :src="item.image" class="goods-img">
        <div class="goods-name-price">
        <div class="name-label">
            <div class="name overflow-1">{{ item.spuTitle }}</div>
            <div class="label">
            <van-tag plain type="danger">新人补贴</van-tag>
            </div>
        </div>
        <div class="price-wrapper">
            <div class="newbron-price">
                <span class="label">新人价</span>
                <span class="price">¥{{ item.priceDown }}</span>
                <span class="del-price">¥{{ item.price }}</span>
            </div>
            <div class="buy-btn">
                <div class="text" @click="$goodsRouter(item)">立即抢购</div>
            </div>
        </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    }
})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .goods-list-item {
        padding: 11px;
        background: #FBF5EE;
        border-radius: 7px;
        display: flex;
        .goods-img{
            width: 94px;
            height: 94px;
            display: block;
            flex-shrink: 0;
            border-radius: 5px;
        }
        .goods-name-price{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 9px;
            flex: 1;
            .name-label{
            .name{
                font-size: 13px;
                color: #222222;
                line-height: 18px;
            }
            }
        }
        .price-wrapper{
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            .newbron-price{
            display: flex;
            align-items: flex-end;
            .label{
                font-weight: 500;
                color: #222222;
                font-size: 12px;
                transform: scale(0.9);
                transform-origin: left top;
            }
            .price{
                font-weight: 500;
                font-size: 15px;
                color: #F9433C;
            }
            .price::first-letter{
                font-size: 12px;
            }
            .del-price{
                font-size: 12px;
                transform: scale(0.85);
                color: #979797;
                transform-origin: left;
                margin-left: 5px;
                text-decoration: line-through;
            }
            }
            .buy-btn{
            width: 65px;
            height: 26px;
            background: #F9433C;
            border-radius: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFFFFF;
            .text{
                font-size: 12px;
                transform: scale(0.9);
            }
            }
        }
        }
    .goods-list-item +.goods-list-item{
        margin-top: 11px;
    }
</style>