<template>
    <swiper
        class="swiperBox"
        :slides-per-view="4"
        @slideChange="onSlideChange"
    >
        <swiper-slide v-for="(item, index) in data" :key="index">
            <div :class="`category-item ${item.categoryId === categoryId ? 'active': ''}`" @click="onSlideClick(item)">
                <div :class="`label ${item.categoryId === categoryId ? 'active': ''}`">{{ item.categoryName }}</div>
            </div>
        </swiper-slide>
    </swiper>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    categoryId: {
        type: Number,
        default: null
    }
})
const emit = defineEmits(['changeCategory'])
const onSlideClick = (item) => {
    emit('changeCategory', item.categoryId)
}
const onSlideChange = (val) => {
    emit('changeCategory', props.data[val.activeIndex].categoryId)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
    .swiperBox{
        padding: 0 11px;
        .category-item{
            height: 48px;
            display: flex;
            align-items: center;
            .label{
                font-weight: 500;
                font-size: 13px;
                color: #222222;
                text-align: center;
            }
            .label.active{
                font-size: 14px;
                color: #F9433C;
            }
        }
    }

</style>
