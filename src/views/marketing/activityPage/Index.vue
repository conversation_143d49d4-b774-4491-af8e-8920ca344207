<template>
  <div class="activity-page">
    <navigation-bar :page-name="activityInfo.title" @onLeftClick="onBackClick" />
    <div class="activity-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <div class="main-image">
        <img v-if="activityInfo.rotatePicture" :src="activityInfo.rotatePicture" />
      </div>
      <div
        class="goods-list"
        :style="{
          backgroundImage: activityInfo.backgroundimage
            ? `url(${activityInfo.backgroundimage})`
            : 'none',
        }"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="——没有更多了——"
          :immediate-check="false"
          @load="getList"
        >
          <goods-row :goods-list="list" />
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import GoodsRow from '@/components/GoodsRow'
import { getActivityPage } from '@/api/market'
import { goodsList, regionGoodsPage } from '@/api/goods'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const id = route.query.id
const loading = ref(false)
const finished = ref(false)
const list = ref([])
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  activityInfo: {},
})
const { queryParams, activityInfo } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const getList = () => {
  loading.value = true
  if (activityInfo.value.spuSourceType === 'SPECIAL') {
    // 普通
    regionGoodsPage(queryParams.value)
      .then((res) => {
        listData(res)
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  } else {
    goodsList(queryParams.value)
      .then((res) => {
        listData(res)
      })
      .catch(() => {
        loading.value = false
        finished.value = true // 防止死循环
      })
  }
}
const listData = (res) => {
  loading.value = false
  list.value = [...list.value, ...res.data]
  if (list.value.length >= res.total) {
    finished.value = true
  } else {
    queryParams.value.pageNum++
  }
}
onMounted(() => {
  getActivityPage({ id }).then((res) => {
    activityInfo.value = res.data
    if (res.data.spuSourceType === 'BRAND') {
      // 品牌
      queryParams.value.brandId = res.data.spuSourceValue
    } else if (res.data.spuSourceType === 'SPECIAL') {
      queryParams.value.regionType = res.data.spuSourceValue
    } else if (res.data.spuSourceType === 'CATEGORY') {
      queryParams.value.categoryId = res.data.spuSourceValue
    } else if (res.data.spuSourceType === 'SPU') {
      queryParams.value.spuIds = res.data.spuSourceValue.split(',')
    }
    getList()
  })
})
</script>

<style lang="scss" scoped>
.activity-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  &-context {
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    .main-image {
      // margin: 12px 10px;
      img {
        width: 100%;
        // border-radius: 8px;
        display: block;
      }
    }
    .goods-list {
      // margin-top: 12px;
      padding-top: 12px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}
</style>
