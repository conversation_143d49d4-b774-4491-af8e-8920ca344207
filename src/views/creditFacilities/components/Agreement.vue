<template>
  <div class="agreement">
    <img class="success-img" src="@/assets/images/credit/success.png">
    <div class="success-text">人脸识别成功</div>
    <div class="agreement-content">
        <div class="title">个人征信授权书与用户信息授权书</div>
        <img class="line" src="@/assets/images/credit/agreement-line.png">
        <div class="p indent">
            重要提示:本次授权文件包含《个人征信授权书》、《用户信息授权书》为了保障您的合法权益、，请您务必审慎阅读、充分理解授权书条款内容，特别是您在授权书范围内的权利和义务。
        </div>
        <div class="p indent margin-top">
            您的点击确认及业务申请使用等行为即视为您已阅读、理解并同意签署《个人征信授权书》、《用户信息授权书》，并授权众安在线财产保险股份有限公司或服务方(深圳众联商务有限公司)向合作的电子合同服务商代您申请开通及调用您的电子签名，以签署授权书。您认可电子签名签署授权书的法律效力，并受签署的授权书约束。如授权书包含至相应业务协议中，对相应业务协议接受同意即视为您已阅读、理解并同意授权书内容。
        </div>
    </div>
    <div class="h100"></div>
    <footer-fiexd>
      <div class="footer-btn theme-linear-gradient" @click="emit('onSubmit')">阅读全文并拉至底部</div>
    </footer-fiexd>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
const emit = defineEmits(['onSubmit'])
</script>
<style scoped lang='scss'>
    .agreement{
        background: #ffffff;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding-bottom: 120px;
        .success-img{
            width: 60px;
            height: 60px;
            display: block;
            margin: 0 auto;
            margin-top: 60px;
        }
        .success-text{
            font-weight: 600;
            font-size: 21px;
            color: #222222;
            line-height: 29px;
            text-align: center;
            margin-top: 23px;
        }
        .agreement-content{
            margin: 45px 35px 0;
            background: #FFF8F5;
            border-radius: 4px;
            border: 1px solid #E2CDB1;
            flex: 1;
            overflow-y: scroll; /*y轴滚动*/
            padding: 23px 15px;
            .title{
                font-size: 15px;
                color: #85601F;
                line-height: 21px;
                text-align: center;
            }
            .p{
                font-size: 13px;
                color: #85601F;
                line-height: 18px;
            }
            .line{
                width: 214px;
                height: 9px;
                display: block;
                margin: 16px auto;
            }
            .indent{
                text-indent: 2em;
            }
        }
    }
</style>