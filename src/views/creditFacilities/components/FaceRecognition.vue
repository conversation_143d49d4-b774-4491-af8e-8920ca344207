<template>
  <div class="page-face">
    <div class="tips-title-wrapper">
      <div class="tips-title">
        <span>完成人脸验证</span><span class="hightlight">获最高100元现金</span>
      </div>
      <div class="page-num">
        <span class="hightlight">2</span>/4
      </div>
    </div>
    <div class="tips-info">验证本人操作并用于您的借款额度审批</div>
    <div class="face-warpper">
      <img src="@/assets/images/credit/face-bg.png" />
    </div>
    <div class="user-flex">
      <div class="user-name" v-if="custIdCard.name">{{ showFirstName(custIdCard.name) }}</div>
      <div class="user-text">请保证人脸在取景框中</div>
    </div>
    <div class="psxz">拍摄须知</div>
    <div class="face-tips">
      <div class="face-tip">
        <img src="@/assets/images/credit/face-tip-1.jpg" />
        <div class="info">
          <img src="@/assets/images/credit/error.png" />
          <span>遮挡面部</span>
        </div>
      </div>
      <div class="face-tip">
        <img src="@/assets/images/credit/face-tip-2.jpg" />
        <div class="info">
          <img src="@/assets/images/credit/error.png" />
          <span>拍摄不全</span>
        </div>
      </div>
      <div class="face-tip">
        <img src="@/assets/images/credit/face-tip-3.jpg" />
        <div class="info">
          <img src="@/assets/images/credit/error.png" />
          <span>光线不足</span>
        </div>
      </div>
    </div>
    <footer-fiexd>
      <div class="footer">
        <div class="tip"><img src="@/assets/images/credit/safe.png" />轻享花智能加密，实时保障你的信息安全</div>
        <div class="btn" @click="onNext">开始识别</div>
      </div>
    </footer-fiexd>
  </div>
</template>

<script setup>
  // import { uploadBase64 } from '@/api/base'
  import { showFirstName } from '@/utils/common'
  import { showFailToast, showDialog } from 'vant'
  import { getRealnameFaceRecognitionUrl } from '@/api/customer'
  import { useRouter } from 'vue-router'
  const props = defineProps({
    modelValue: {
      type: Object
    },
    custIdCard: {
      type: Object,
      default: () => {}
    }
  })
  const { proxy } = getCurrentInstance()
  const validShow = ref(false)
  const emit = defineEmits(['on-submit'])
  const store = useStore()
  const user = computed(() => store.getters.userInfo)
  const router = useRouter()
  const loading = ref(false)
  const motion = (params) => {
    const data = JSON.parse(params.replace(/\r|\n/g,''))
    if(data.faceStatus ==='PASS') {
      props.modelValue.faceScore = data.faceScore
      props.modelValue.faceStatus = data.faceStatus
      emit('on-submit')
      // const imgBase64 = data.faceImg.replace(/\n/g, '')
      // uploadBase64({ base64: imgBase64, fileName: 'faceImg' }).then(res => {
      //   props.modelValue.faceImg = res.link
      //   props.modelValue.faceScore = data.faceScore
      //   props.modelValue.faceStatus = data.faceStatus
      //   emit('on-submit')
      // })
    } else {
      showFailToast('识别失败，请重试！')
    }
  }
  const appMotion = () => {
    uni.postMessage({
      data: {
        action: 'motion',
        data: { idcard: props.custIdCard.idcard, name: props.custIdCard.name }
      }
    })
  }
  const onNext = async () => {
    if (!user.value) {
      return
    }

    if (loading.value) {
      return
    }

    const custIdCard = JSON.parse(localStorage.getItem('custIdCard'))
    if (!custIdCard) {
      showDialog({
        message: '身份证信息缓存失效',
      }).then(() => {
        router.replace({ path: '/cash-loan' })
      })
      return
    }

    try {
      loading.value = true
      const data = {
        custId: user.value.id,
        phone: user.value.phone,
        idCard: custIdCard.idcard,
        name: custIdCard.name,
        returnUrl: window.location.origin + '/face-recognition-result' // 重定向URL
      }
      const res = await getRealnameFaceRecognitionUrl(data)
      // flowId用于查询人脸识别结果
      const flowId = res.data ? res.data.flowId : ''
      if (!flowId) {
        showFailToast('获取人脸识别链接失败')
        return
      }
      localStorage.setItem('faceRecognitionFlowId', flowId)
      // 跳转到人脸识别url
      const url = res.data.originalUrl
      window.location.href = url
    } finally {
      loading.value = false
    }
  }
  onMounted(() => {
    window.motion = motion
  })
</script>

<style lang="scss" scoped>
  .page-face{
    background: #ffffff;
    height: 100%;

    .tips-title-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 18px 16px 0;

      .tips-title{
        font-size: 16px;
        font-weight: 600;
        color: #333;
        .hightlight{
          color: #FF551A;
          margin-left: 2px;
        }
      }

      .page-num {
        font-size: 13px;
        font-weight: 600;
        color: #cfcfcf;
        .hightlight {
          font-size: 15px;
          color: #333;
        }
        margin-right: 3px;
      }
    }
    .tips-info{
      margin: 7px 15px 0 15px;
      font-size: 13px;
      font-weight: 400;
      color: #cfcfcf;
      line-height: 17px;
    }
  }
  .face-warpper{
    margin-top: 38px;
    margin-bottom: 19px;
    img {
      width: 149px;
      height: 144px;
      display: block;
      margin: 0 auto;
    }
  }
  .user-flex{
    display: flex;
    justify-content: center;
    align-items: center;
    .user-name{
      font-size: 20px;
      color: #FF551A;
      line-height: 28px;
      font-weight: bold;
      margin-right: 12px;
    }
    .user-text{
      font-size: 15px;
      font-weight: 600;
      color: #333333;
      line-height: 22px;
    }
  }
  .psxz{
    margin-top: 59px;
    font-size: 15px;
    font-weight: 600;
    color: #333;
    line-height: 20px;
    text-align: left;
    margin-left: 17px;
  }
  .face-tips{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 34px 0 34px;

    .face-tip {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #f6f6f6;
      border-radius: 10px;
      width: 87px;
      height: 87px;

      img {
        width: 100%;
        height: 100%;
      }

      .info {
        display: flex;
        align-items: center;
        margin-top: 7px;

        img {
          width: 18px;
          height: 18px;
          margin-right: 2px;
        }

        span {
          font-size: 13px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .footer {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 10px;
      box-sizing: border-box;

      .tip {
        font-size: 13px;
        font-weight: 400;
        color: #bcbcbc;
        display: flex;
        align-items: center;
        white-space: nowrap;

        img {
          height: 16px;
          margin-right: 5px;
        }
      }

      .btn {
        margin-top: 5px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: linear-gradient(180deg, #FF3927 0%, #FF8C64 100%);
        border-radius: 20px;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
        text-align: center;
      }
    }
</style>
