<script setup>
import { uploadBase64 } from '@/api/base'
import { useId } from 'vue'

const { proxy } = getCurrentInstance()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const id = useId()

const props = defineProps({
  front: {
    type: Boolean,
    default: false,
  },
})

const imageInfo = defineModel({ default: null })

const onClose = () => {
  imageInfo.value = null
}

const onClick = () => {
  if (!user.value) {
    proxy.appJS.appLogin()
  }
}

const onInputChange = (event, fileName) => {
  const file = event.target.files[0]
  compressImage(file, 2560, 1440, 0.8, (blob) => {
    const reader = new FileReader()
    reader.onload = async function (e) {
      // 去掉base64,前面的部分
      const base64 = e.target.result.replace(/^data:image\/[a-zA-Z]+;base64,/, '')
      const res = await uploadBase64({ base64, fileName })
      if (res.link) {
        imageInfo.value = {
          link: res.link,
          base64: base64,
          src: e.target.result,
        }
      }
    }
    reader.readAsDataURL(blob)
  })
}

// 压缩函数（参考之前的回答）
function compressImage(file, maxWidth, maxHeight, quality, callback) {
  const reader = new FileReader()
  reader.onload = function (event) {
    const img = new Image()
    img.src = event.target.result
    img.onload = function () {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      let width = img.width
      let height = img.height
      if (width > height) {
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width)
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = Math.round((width * maxHeight) / height)
          height = maxHeight
        }
      }
      canvas.width = width
      canvas.height = height
      ctx.drawImage(img, 0, 0, width, height)
      canvas.toBlob(callback, 'image/jpeg', quality)
    }
  }
  reader.readAsDataURL(file)
}
</script>

<template>
  <div
    class="idcard-uploader"
    :class="{
      'is-front': front,
      'is-back': !front,
    }"
  >
    <div v-if="imageInfo" class="preview">
      <img class="image" :src="imageInfo.src" />
      <van-icon class="close" size="20" name="clear" @click="onClose" />
    </div>
    <div v-else class="uploader">
      <label v-if="user" class="input-label" :for="id" />
      <div v-else class="input-label" @click="onClick" />
      <input
        class="input"
        :id="id"
        type="file"
        accept="image/*"
        :disabled="!user"
        hidden
        @change="onInputChange"
      />
      <!-- <img class="bg" src="@/assets/images/credit/idcard-front.png" /> -->
      <div class="tips">
        <span>拍摄/身份证</span><b>{{ front ? '头像面' : '国徽面' }}</b>
      </div>
      <!-- <template v-if="props.front">
        <input
          class="input"
          id="idcardInput"
          type="file"
          accept="image/*"
          :disabled="!user"
          @change="(event) => onInputChange(event, 'idcardfront')"
        />
        <img class="bg" src="@/assets/images/credit/idcard-front.png" />
        <div class="tips">拍摄/上传身份证头像面</div>
      </template>
      <template v-else>
        <input
          class="input"
          id="idcardInput"
          type="file"
          accept="image/*"
          :disabled="!user"
          @change="(event) => onInputChange(event, 'idcardback')"
        />
        <img class="bg" src="@/assets/images/credit/idcard-back.png" />
        <div class="tips">拍摄/上传身份证国徽面</div>
      </template> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.idcard-uploader {
  // margin: 10px 23px;
  background: #f7f8fa;
  // width: 330px;
  // height: 170px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  position: relative;

  .preview {
    .image {
      width: 330px;
      height: 170px;
    }

    .close {
      position: absolute;
      right: 10px;
      top: 10px;
      padding: 10px;
      color: #fff;
    }
  }
  &.is-front {
    .uploader {
      background-image: url('@/assets/images/credit/idcard-front.png');
    }
  }
  &.is-back {
    .uploader {
      background-image: url('@/assets/images/credit/idcard-back.png');
    }
  }

  .uploader {
    // margin: auto;
    margin: 4px;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    // width: auto;
    // height: auto;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: 100% 100%;

    .input {
      display: none;
    }

    .input-label {
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translate(-50%, -50%);
      display: block;
      width: 32px;
      height: 32px;
      padding: 10px;
      box-sizing: content-box;
      background-repeat: no-repeat !important;
      background-size: contain !important;
      background-origin: content-box !important;
      background: url('@/assets/images/credit/camera.png');
    }

    // .bg {
    //   width: 221px;
    //   height: 125px;
    // }

    .tips {
      position: absolute;
      bottom: 8px;
      left: 0;
      right: 0;
      text-align: center;
      // left: 50%;
      // transform: translateX(-50%);
      white-space: nowrap;
      font-size: 14px;
      font-weight: 400;
      color: #333;
      font-weight: normal;
      b {
        color: #3f58d6;
        font-weight: inherit;
      }
    }
  }
}
</style>
