<template>
  <div class="result-page">
    <navigation-bar
      :pageName="pageName"
      :navBarStyle="{
        fontSize: '16px',
        fontWeight: 600,
        color: '#000',
        backgroundColor: '#fff',
        border: 'none',
      }"
      @onLeftClick="onBackClick"
    ></navigation-bar>
    <div class="context">
      <img class="icon" src="@/assets/images/credit/compute.png" />
      <div class="title">授信额度计算中，请您耐心等待</div>
      <div class="desc">
        该额度仅为平台智能评估额度，本平台仅提供信息展示，不参与任何放款行为，最终审核放款以资方机构为准
      </div>
      <div class="btn" @click="onViewResult">查看结果</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const pageName = ref('')

onMounted(() => {
  pageName.value = '授信额度已提交'
})

const onBackClick = () => {
  router.replace({ path: '/cash-loan' })
}

const onViewResult = () => {
  router.replace({ path: '/my/quota' })
}
</script>

<style lang="scss" scoped>
.result-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #fff;

  .context {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin: 0 10px;

    .icon {
      width: 128px;
      height: 116px;
      margin-top: 60px;
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-top: 7px;
    }

    .desc {
      font-size: 14px;
      font-weight: 500;
      color: #cfcfcf;
      margin-top: 13px;
    }

    .btn {
      margin-top: 37px;
      width: 240px;
      height: 40px;
      line-height: 40px;
      background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
      border-radius: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
