<template>
  <div class="idcard-page">
    <div class="tips-title-wrapper">
      <div class="tips-title">
        <span>请上传身份证的正反面</span><!-- <span class="hightlight">获最高100元现金</span> -->
      </div>
      <div class="page-num"><span class="hightlight">1</span>/4</div>
    </div>
    <div class="tips-info">应监督要求，证件与人脸信息将通过合作机构核验您的身份</div>
    <div class="content">
      <div class="card-wrap">
        <IdcardUploader v-model="idcardFrontInfo" front />
        <IdcardUploader v-model="idcardBackInfo" />
      </div>
      <div class="pic-tips">确保证件边框完整，文字清晰可见</div>
      <div class="icard-info">
        <div class="title">请确认您的身份证件信息准确无误</div>
        <div class="valid-info">
          <span class="label">真实姓名</span>
          <span :class="`value ${modelValue.name ? '' : 'placeholder'}`">{{
            modelValue.name ? modelValue.name : '请上传身份证识别'
          }}</span>
        </div>
        <div class="valid-info">
          <span class="label">身份证号</span>
          <span :class="`value ${modelValue.idcard ? '' : 'placeholder'}`">{{
            modelValue.idcard ? idCardFormat(modelValue.idcard) : '请上传身份证识别'
          }}</span>
        </div>
      </div>
      <div style="height: 150px"></div>
    </div>
    <footer-fiexd>
      <div class="footer">
        <div class="tip">
          <img src="@/assets/images/credit/safe.png" />轻享花智能加密，实时保障你的信息安全
        </div>
        <div class="btn" @click="onNext">下一步</div>
      </div>
    </footer-fiexd>
  </div>
</template>

<script setup>
import { countValid } from '@/api/base'
import { ocr } from '@/api/customer'
import { showDialog, showToast } from 'vant'
import { watchEffect } from 'vue'
import IdcardUploader from './IdcardUploader.vue'
// const  isIphoneX = window.isIphoneX
// const { proxy } = getCurrentInstance()
// const validShow = ref(false)
// const imgData = ref('')
const router = useRouter()
// const agreement = ref(true)
// const showAgreement = ref(false)
const idcardFrontInfo = ref(null)
const idcardBackInfo = ref(null)
const modelValue = defineModel({ default: {} })
const emit = defineEmits(['on-next', 'updateAgreement'])
// 正面图片和背面图片链接都有了，就进行ocr识别
watchEffect(async () => {
  if (idcardFrontInfo.value && idcardBackInfo.value) {
    console.log('两张图片都准备好了')
    try {
      const res = await ocr({
        infoImg: idcardFrontInfo.value.base64,
        emblemImg: idcardBackInfo.value.base64,
      })
      const data = res.data
      if (!data) {
        reset()
        showToast('身份证识别失败')
        return
      }

      // 获取身份证号码
      if (!data.idNo) {
        reset()
        showToast('获取身份证号码失败')
        return
      }

      // 检查年龄
      if (!checkAgeByIdCard(data.idNo)) {
        reset()
        showToast('您未满18岁，不符合申请条件！')
        return
      }

      // 将身份信息保存
      const validityPeriod = data.validityPeriod.split('-')
      modelValue.value = {
        name: data.name,
        idcard: data.idNo,
        gender: data.gender,
        nation: data.nation,
        birthday: data.birthDay,
        idcardAddr: data.address,
        office: data.issuedBy,
        startTime: validityPeriod[0],
        endTime: validityPeriod[1],
        idcardFrontImg: idcardFrontInfo.value.link,
        idcardBackImg: idcardBackInfo.value.link,
      }
    } catch (err) {
      reset()
    }
  } else {
    modelValue.value = {}
  }
})
const reset = () => {
  idcardFrontInfo.value = null
  idcardBackInfo.value = null
  modelValue.value = {}
}
const updateAgreement = () => {
  emit('updateAgreement')
}
const idCardFormat = (idcard) => {
  return idcard.substring(0, 6) + ' ' + idcard.substring(6, 14) + ' ' + idcard.substring(14, 18)
}
const onNext = async () => {
  if (modelValue.value && modelValue.value.name && modelValue.value.idcard) {
    localStorage.setItem('custIdCard', JSON.stringify(modelValue.value))
    emit('on-next')
  } else {
    showToast('请识别本人身份证')
  }
}
// 校验年龄
const checkAgeByIdCard = (idCard) => {
  let birthDate
  if (idCard.length === 18) {
    birthDate = idCard.substr(6, 8)
  } else if (idCard.length === 15) {
    birthDate = '19' + idCard.substr(6, 6)
  } else {
    return false
  }
  const birthYear = parseInt(birthDate.substr(0, 4), 10)
  const birthMonth = parseInt(birthDate.substr(4, 2), 10) - 1
  const birthDay = parseInt(birthDate.substr(6, 2), 10)
  const birth = new Date(birthYear, birthMonth, birthDay)
  const now = new Date()
  const ageDiff = now - birth
  const ageDate = new Date(ageDiff)
  const age = Math.abs(ageDate.getUTCFullYear() - 1970)
  return age >= 18
}
// // APP调用H5 idCardFront
// const idCardFront = async (params) => {
//   const data = JSON.parse(params.replace(/\r|\n/g,''))
//   const imgBase64 = data.idcardFrontImg.replace(/\n/g, '')
//   if(data.idcard) {
//     if (checkAgeByIdCard(data.idcard)) {
//       await uploadBase64({ base64: imgBase64, fileName: 'idcardfront' }).then(res => {
//         props.modelValue.idcardFrontImg = res.link
//         idcardFrontImg.value = imgBase64
//       })
//     } else {
//       showToast('您未满18岁，不符合申请条件！')
//       return
//     }
//   } else {
//     showToast('获取身份证号码失败')
//   }
//   props.modelValue.idcard = data.idcard
//   props.modelValue.name = data.name
//   props.modelValue.gender = data.gender
//   props.modelValue.nation = data.nation
//   props.modelValue.birthday = data.birthday
//   props.modelValue.idcardAddr = data.idcardAddr
// }
// // APP调用H5 idCardBack
// const idCardBack = async (params) => {
//   const data = JSON.parse(params.replace(/\r|\n/g,''))
//   const imgBase64 = data.idcardBackImg.replace(/\n/g, '')
//   if(data.office) {
//     await uploadBase64({ base64: imgBase64, fileName: 'idcardback' }).then(res => {
//       props.modelValue.idcardBackImg = res.link
//       idcardBackImg.value = imgBase64
//     })
//   } else {
//     showToast('获取签发机构失败')
//   }
//   props.modelValue.office = data.office
//   props.modelValue.startTime = data.startTime
//   props.modelValue.endTime = data.endTime
// }
// 调用APP指令
// const appFront = () => {
//   if(!props.modelValue.idcardFrontImg) {
//     uni.postMessage({
//       data: {
//         action: 'idCardFront'
//       }
//     })
//   }
// }
// const appBack = () => {
//   if(!props.modelValue.idcardBackImg) {
//     uni.postMessage({
//       data: {
//         action: 'idCardBack'
//       }
//     })
//   }
// }
onMounted(() => {
  countValid().then((res) => {
    if (res.data !== 'Y') {
      showDialog({
        message: '今日实名次数已超限，请明天再尝试。',
      }).then(() => {
        router.go(-1)
      })
    }
  })
  // window.idCardFront = idCardFront
  // window.idCardBack = idCardBack
})
</script>

<style lang="scss" scoped>
.idcard-page {
  background: #ffffff;
  // height: 100%;
  overflow-y: scroll;

  .tips-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 16px 0;

    .tips-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      .hightlight {
        color: #ff551a;
        margin-left: 2px;
      }
    }

    .page-num {
      font-size: 13px;
      font-weight: 600;
      color: #cfcfcf;
      .hightlight {
        font-size: 15px;
        color: #333;
      }
      margin-right: 3px;
    }
  }
  .tips-info {
    margin: 7px 15px 0 15px;
    font-size: 13px;
    font-weight: 400;
    color: #cfcfcf;
    line-height: 17px;
  }
  .content {
    .pic-tips {
      font-size: 13px;
      color: #0000004d;
      line-height: 17px;
      text-align: center;
      margin-top: 8px;
    }
    .divide-line {
      margin-top: 28px;
      height: 5px;
      background: #f2f2f2;
    }
    .icard-info {
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 18px;
        margin: 28px 0 15px 14px;
      }
      .valid-info {
        margin: 0 15px;
        font-size: 14px;
        color: #000000;
        line-height: 20px;
        padding: 10px 0;
        border-bottom: 1px solid #efefef;
        display: flex;
        .label {
          width: 80px;
        }
        .placeholder {
          color: #969799;
        }
      }
    }
  }
  .next-btn.disabled {
    background: #abb5c4 !important;
  }

  .footer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;

    .tip {
      font-size: 13px;
      font-weight: 400;
      color: #bcbcbc;
      display: flex;
      align-items: center;
      white-space: nowrap;

      img {
        height: 16px;
        margin-right: 5px;
      }
    }

    .btn {
      margin-top: 5px;
      width: 100%;
      height: 40px;
      line-height: 40px;
      // background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
      // border-radius: 20px;
      border-radius: 8px;
      background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-align: center;
    }
  }
}

.card-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  // grid-row: 103px;
  // grid-auto-flow: 103px;
  height: 103px;
  gap: 6px;
  padding: 16px;
  .idcard-uploader {
    width: 100%;
    height: 100%;
  }
}
</style>
