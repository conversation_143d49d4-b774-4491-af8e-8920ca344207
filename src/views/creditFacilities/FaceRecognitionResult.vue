<template>
  <div class="result-page">
    <navigation-bar :isShowBack="false"
      :navBarStyle="{ fontSize: '16px', fontWeight: 600, color: '#000', backgroundColor: '#fff', border: 'none' }"></navigation-bar>
    <div class="context">
      <template v-if="loading">
      </template>
      <template v-else>
        <template v-if="success">
          <img class="icon" src="@/assets/images/credit/success.png" />
          <div class="title">人脸识别成功</div>
          <div class="desc">{{ seconds }}秒后自动跳转请填写本人资料</div>
        </template>
        <template v-else>
          <img class="icon-fail" src="@/assets/images/credit/fail.png" />
          <div class="title">人脸识别未通过</div>
          <div class="message">{{ message }}</div>
          <div class="desc">{{ seconds }}秒后自动返回</div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { queryFaceRecognitionResult, saveCustFace } from '@/api/customer'
import { useStore } from 'vuex'

const router = useRouter()
const store = useStore()

const timer = ref('')
const seconds = ref(3)
const success = ref(false)
const loading = ref(false)
const message = ref('')

const queryResult = async () => {
  const resultRes = await queryFaceRecognitionResult()
  if (resultRes && resultRes.data && resultRes.data.status === 'SUCCESS') {
    success.value = true
    message.value = ''
  } else {
    success.value = false
    message.value = resultRes.data.message
  }
}

const submitRealname = async () => {
  const flowId = localStorage.getItem('faceRecognitionFlowId')
  if (flowId) {
    // 提交审批中
    const custIdCard = JSON.parse(localStorage.getItem('custIdCard'))
    const res = await saveCustFace({
      flowId, // 相对于原来，增加了人脸识别id
      custIdCard,
      // 后端接口修改，废弃旧的人脸识别参数
      // custFace: {
      //   faceScore: score,
      //   faceStatus: 'PASS',
      // }
    })
    const errMsg = res.data
    success.value = !errMsg
    if(success.value){
      store.dispatch('GetInfo')
    }
    message.value = errMsg || ''
  } else {
    success.value = false
    message.value = ''
  }
}

const tick = () => {
  clearTimeout(timer.value)
  if (seconds.value <= 1) {
    leave()
  } else {
    timer.value = setTimeout(() => {
      seconds.value -= 1
      tick()
    }, 1000)
  }
}

const leave = () => {
  if (success.value) {
    router.replace({ path: '/credit-facilities', query: { faceRecognition: 'success' } })
  } else {
    if (message.value === '该身份证已注册') {
      // 注册时可能已实名（未复现此bug，暂时跳转到借钱首页，避免重复实名）
      router.replace({ path: '/cash-loan' })
    } else {
      router.replace({ path: '/credit-facilities', query: { faceRecognition: 'fail' } })
    }
  }
}

onMounted(async () => {
  try {
    loading.value = true
    await queryResult()
    if (success.value) {
      await submitRealname()
    }
  } finally {
    loading.value = false
    tick()
  }
})
</script>

<style lang="scss" scoped>
.result-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #fff;

  .context {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin: 0 10px;

    .icon {
      width: 64px;
      height: 64px;
      margin-top: 88px;
    }

    .icon-fail {
      width: 43px;
      height: 45px;
      margin-top: 88px;
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-top: 17px;
    }

    .message {
      font-size: 14px;
      font-weight: 500;
      color: #cfcfcf;
      margin-top: 13px;
    }

    .desc {
      font-size: 14px;
      font-weight: 500;
      color: #cfcfcf;
      margin-top: 13px;
    }
  }
}
</style>