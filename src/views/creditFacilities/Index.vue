<template>
  <section class="page credit-page">
    <header>
      <navigation-bar :title="pageName" />
    </header>
    <main>
      <div class="progress" v-show="setpsActive !== 3">
        <Steps
          class="steps"
          :step="setpsActive"
          :items="['上传身份证', '人脸验证', '填写信息', '查看结果']"
        />
      </div>
      <div class="credit-page-context" v-show="setpsActive !== 3">
        <id-card
          v-model="form.custIdCard"
          @updateAgreement="showAgreement = true"
          v-show="setpsActive === 0"
          @on-next="onNext"
        />
        <face-recognition
          v-model="form.custFace"
          :cust-id-card="form.custIdCard"
          v-show="setpsActive === 1"
          @on-submit="onSubmit"
        />
        <CustInfo v-show="setpsActive === 2" @on-next="onNext" />
      </div>
      <Result v-show="setpsActive === 3" />
    </main>
    <van-popup
      v-model:show="showAgreement"
      safe-area-inset-bottom
      :style="{ height: '100%', width: '100%' }"
    >
      <div class="flex-popup">
        <navigation-bar :isShowBack="false" pageName="授信额度服务协议">
          <template #nav-left>
            <van-icon name="cross" size="22" class="text-gray" @click="showAgreement = false" />
          </template>
        </navigation-bar>
        <iframe
          class="external-links"
          src="/agreement/product-service.htm"
          scrolling="auto"
          frameborder="0"
          id="iframe"
        ></iframe>
      </div>
    </van-popup>
  </section>
</template>

<script setup name="CreditFacilities">
import { saveCustFace } from '@/api/customer'
import IdCard from './components/IdCard'
import FaceRecognition from './components/FaceRecognition'
import Agreement from './components/Agreement'
import Steps from '@/components/Steps/Steps.vue'
import CustInfo from './components/CustInfo.vue'
import Result from './components/Result.vue'
import NavigationBar from '@/components/NavigationBar/index2.vue'
import { onMounted } from 'vue'
const { proxy } = getCurrentInstance()
const isIphoneX = window.isIphoneX
const showAgreement = ref(false)
const store = useStore()
const route = useRoute()
const router = useRouter()
const setpsActive = ref(0)
const pageName = ref('实名认证')
const title = ref('')
const data = reactive({
  form: {
    custIdCard: {},
    custFace: {},
  },
})
const { form } = toRefs(data)
watch(setpsActive, (val) => {
  updateTitle()
})
// 更新标题
const updateTitle = () => {
  switch (setpsActive.value) {
    case 0:
      pageName.value = '实名认证'
      break
    case 1:
      pageName.value = '人脸验证'
      break
    case 2:
      pageName.value = '基本信息'
      break
    case 3:
      pageName.value = '授信额度已提交'
      break
  }
}
const onSubmit = () => {
  saveCustFace({ custIdCard: form.value.custIdCard, custFace: form.value.custFace }).then((res) => {
    store.dispatch('GetInfo').then((res) => {
      proxy.onToastSucc(() => {
        router.replace({ name: 'CustInfo' })
      }, '提交成功')
    })
  })
}
const onNext = () => {
  setpsActive.value += 1
}
const onBackClick = () => {
  router.go(-1)
}
onMounted(() => {
  // 如果存在faceRecognition，表明是从人脸识别结果页面跳转回来的
  if (route.query.faceRecognition === 'success') {
    // 人脸识别成功后，进入下一步
    setpsActive.value = 2
  } else if (route.query.faceRecognition === 'fail') {
    // 人脸识别成功后，返回到人脸识别页面
    setpsActive.value = 1
  } else {
    // 默认进入页面时从第一步开始
    setpsActive.value = 0
  }
})
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background-color: #f6f6f6;
  font-size: 14px;
  header {
    flex: none;
    background-color: #fff;
  }
  .navigation-bar {
    background-color: #fff;
  }
  main {
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    background-color: #f6f6f6;
    position: relative;
  }
  &-context {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .progress {
    margin-bottom: 10px;
    // position: sticky;
    // top: 0;
    // left: 0;
    // right: 0;

    .steps {
      padding: 13px 19px 10px;
    }
  }
}
</style>
