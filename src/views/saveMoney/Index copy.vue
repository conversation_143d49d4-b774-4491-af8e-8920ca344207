<template>
  <div class="page">
    <navigation-bar pageName="轻享花省钱卡" @onLeftClick="onBackClick"></navigation-bar>
    <div class="page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <van-pull-refresh class="text-white" v-model="loading" @refresh="initPage">
        <template v-if="checkedMember.vipPrice && !checkedMember.vipCust">
          <img src="@/assets/images/jsk/title.png" class="title-img">
        </template>
        <increase-quota :data="checkedMember" @handleConfirm="handleConfirm" @agreementShow="agreementRef.show=true"></increase-quota>
        <div v-if="advertises[$global.AD_POSITION_SAVE_MONEY]" class="bannar-wrapper">
          <my-swiper :swiperDatas="advertises[$global.AD_POSITION_SAVE_MONEY]"></my-swiper>
        </div>
        <div class="benefit-wrapper">
          <benefit-item @buyTips="signRef.show = true"></benefit-item>
        </div>
      </van-pull-refresh>
    </div>
    <footer-fiexd v-if="!checkedMember.vipCust">
      <div class="flex-sub">
        <div class="agreement">
          <van-checkbox class="flex0" v-model="checked" icon-size="16px" :checked-color="$global.THMEM" />
          <span class="agree">我已阅读<span class="highlight theme-text" @click="agreementRef.show=true">《权益服务协议》</span>，理解并接受相关条款并同意自动续费，续费可随时取消</span>
        </div>
        <div class="buy-btn theme-linear-gradient" @click="handleConfirm">立即开通</div>
      </div>
    </footer-fiexd>
    <sign-popup ref="signRef" @handleBuy="handleBuy" />
    <agreement-popup url="/agreement/member-service.htm" title="权益服务协议" ref="agreementRef"></agreement-popup>
    <not-bank-tips
      ref="notBankTipsRef"
      @add-bank="memberAddBankRef.show=true"
      title="绑定银行卡即可快速开通权益"
    ></not-bank-tips>
    <member-add-bank ref="memberAddBankRef" :bind-params="payInfo" @bindSuccess="launchPay"></member-add-bank>
    <open-success title="开通成功" tips="成功开通轻享花省钱卡，享优先推荐特权" ref="openSuccRef"></open-success>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { submitMemberOrder, bankCardList, payProduct, submitPay } from '@/api/member-bank'
import { getMemberInfo } from '@/api/member'
import AgreementPopup from '@/components/AgreementPopup'
import IncreaseQuota from './components/IncreaseQuota';
import { getAdList } from '@/api/base'
import MySwiper from '@/components/MySwiper'
import BenefitItem from '../member/components/BenefitItem'
import SignPopup from './components/SignPopup'
import NotBankTips from '../member/components/memberBank/NotBankTips'
import MemberAddBank from '../member/components/memberBank/AddBank'
import OpenSuccess from '../member/components/memberBank/OpenSuccess'
import { showLoadingToast, closeToast, showDialog, showConfirmDialog } from 'vant'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checked = ref(true)
const signRef = ref(null)
const agreementRef = ref(null)
const notBankTipsRef = ref(null)
const memberAddBankRef = ref(null)
const loading = ref(false)
const openSuccRef = ref(null)
const payStatus = ref(null)
const data = reactive({
    advertises: {},
    checkedMember: {},
    payInfo: {}
  })
const { advertises, checkedMember, payInfo } = toRefs(data)
const onBackClick = () => {
    router.go(-1)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
provide('checkedMember', checkedMember)
const handleConfirm = () => {
  if (!checkedMember.value.vipCust) {
    signRef.value.show = true
  } else {
    router.push('/my/quota')
  }
}
// 购买
const handleBuy = async () => {
  const payableTimes = localStorage.getItem('vip_quickpay')
  if(payableTimes && payableTimes > Date.now()) { // 5分钟内多次点击购买
    showConfirmDialog({
      title: '提示',
      message: '您5分钟内发起过权益开通，是否已扣款成功？如已扣款成功，请稍后下拉刷新页面',
      confirmButtonText: '扣款失败,重新开通',
      cancelButtonText: '已扣款成功'
    })
      .then(() => {
        localStorage.removeItem('vip_quickpay')
        submitVipOrder()
      })
      .catch(() => {
        initPage()// 购买前先判断是否已购买
        return
      });
  } else {
    submitVipOrder()
  }
}
const submitVipOrder = async () => {
  try {
    showLoadingToast({
      duration: 0,
      message: '加载中...',
      forbidClick: true
    })
    await initPage()// 购买前先判断是否已购买
    if (!checkedMember.value.vipCust) {
      // 会员卡下单
      const orderRes = await submitMemberOrder({ virtualMsg: { vipPriceId: checkedMember.value.vipPrice.id } })
      if(orderRes) {
        const orderId = orderRes.data?.orderId
        // 获取支付产品信息
        const payRes = await payProduct({ orderId })
        payInfo.value = payRes.data.cashiers[0]
        payInfo.value.orderId = orderId
        const bankRes = await bankCardList({ pageNum:1, pageSize:100, channelId: payInfo.value.bankChannelId,
            orderId: orderId,
            bankCardType: payInfo.value.cardBindType, cmId: payInfo.value.cmId })
        if (bankRes.data?.length > 0) {
          // 提交支付
          await launchPay(bankRes.data[0].id)
        } else {
          notBankTipsRef.value.show = true
        }
      }
    } else {
      showDialog({
        title: '提示',
        message: '会员已购买成功！',
      }).then(() => {
        // on close
      });
    }
  } catch (error) {
    console.log('会员购买失败')
  }
  closeToast() // 清除加载中
}
// 发起支付
const launchPay = async (bankId) => {
  await submitPay({ cashierId: payInfo.value.id,
      orderId: payInfo.value.orderId, id: bankId }).then(res => {
    const currentTimestamp = Date.now() + 5 * 60 * 1000;
    localStorage.setItem('vip_quickpay', currentTimestamp) // 5分钟内发起再次支付
    openSuccRef.value.show = true
  })
}
const initPage = async () => {
  await getMemberInfo({ vipType: 'DISCOUNT' }, false).then(res => {
    checkedMember.value = res.data?.[0] // 默认第一条会员
    loading.value = false
  })

}
onMounted(() => {
  getAdList({regionType: [proxy.$global.AD_POSITION_SAVE_MONEY]}).then(res => {
    advertises.value = res.data
  })
  initPage()
})
</script>
<style scoped lang='scss'>
  .page{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    &-context{
      flex-grow: 1;
      overflow: hidden;
      overflow-y: auto;
      background: linear-gradient( 180deg, #FD6908 0%, #FD7710 71%, #FFFFFF 100%) no-repeat;
      background-size: 100% 500px;
      padding-bottom: 150px;
      .title-img {
        width: 258px;
        height: 33px;
        display: block;
        margin: 30px auto 0;
      }
      .bannar-wrapper{
        margin: 16px 16px 0;
      }
      .benefit-wrapper {
        margin-top: 16px;
        padding: 0 16px 10px;
        background: linear-gradient( 180deg, #FDF6EC 0%, #FAF8F4 5%, #FFFFFF 100%);
        border-radius: 19px 19px 0px 0px;
        overflow: hidden;
      }
    }
    .agreement{
      display: flex;
      align-items: center;
      width: 100%;
      .flex0 {
        flex-shrink: 0;
      }
      .agree{
        font-weight: 400;
        font-size: 12px;
        transform: scale(0.9);
        color: #999999;
        transform-origin: left center;
        margin-left: 8px;
        line-height: 15px;
      }
    }
    .buy-btn{
      height: 44px;
      border-radius: 22px;
      margin: 16px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #FFFFFF;
      span{
        font-weight: 600;
        font-size: 24px;
        color: #FFFFFF;
        margin-left: 5px;
      }
    }
  }
</style>
