<template>
  <div class="page">
    <!-- <navigation-bar pageName="轻享花省钱卡" @onLeftClick="onBackClick"></navigation-bar> -->
    <member-header
      title="极省VIP"
      :rootStyle="{ background: 'linear-gradient(180deg, #f43828, #f57165)' }"
      background="none"
      color="#fff"
      :is-member="checkedMember.vipCust ? true : false"
      :order-info="orderInfo"
    />
    <div
      class="page-context"
      @scroll="onScrollChange"
      :class="{ 'iphonex-bottom': isIphoneX, member: checkedMember.vipCust }"
    >
      <van-pull-refresh class="text-white" v-model="loading" @refresh="initPage">
        <template v-if="loaded">
          <!-- 避免查询显示非会员模块 -->

          <div class="user-info">
            <div
              class="user-info__body"
              :class="{
                'is-credit': user?.creditIntentionFlag === 'Y',
              }"
            >
              <user-avatar class="user-info__avatar" />
              <div class="user-info__content">
                <div class="user-info__phone">
                  {{ user?.phone?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}
                </div>
                <div class="user-info__desc">
                  <template v-if="checkedMember.vipCust">
                    <span
                      >到期时间：{{
                        dayjs(checkedMember.vipCust.invalidTime).format('YYYY-MM-DD')
                      }}</span
                    >
                    <button class="user-info__btn" @click="$router.push('/member/equity-order')">
                      权益订单<van-icon name="arrow" />
                    </button>
                  </template>
                  <span v-else>开通即可享受会员权益</span>
                </div>
              </div>
            </div>
          </div>
          <increase-quota
            v-if="user?.creditIntentionFlag === 'Y'"
            class="increase-quota"
            @handleConfirm="handleConfirm"
            @agreementShow="agreementShow"
            @updateBenfit="updateBenfit"
          ></increase-quota>

          <div v-if="checkedMember?.benefitList?.length" class="benefit-list">
            <div class="benefit-list__title">
              尊享{{ '零一二三四五六七八九十'[checkedMember.benefitList.length] }}大特权
            </div>
            <div class="benefit-list__items">
              <div
                v-for="item in checkedMember?.benefitList"
                :key="item.id"
                class="benefit-list__item"
              >
                <img :src="item.benefitImg" alt="" class="benefit-list__item__icon" />
                <div class="benefit-list__item__title">{{ item.benefitName }}</div>
              </div>
            </div>
          </div>
        </template>
        <div v-if="advertises[$global.AD_POSITION_SAVE_MONEY]" class="bannar-wrapper">
          <my-swiper :swiperDatas="advertises[$global.AD_POSITION_SAVE_MONEY]"></my-swiper>
        </div>
        <benefit-item ref="benefitList" @buyTips="signRef.show = true"></benefit-item>
      </van-pull-refresh>
    </div>
    <div
      class="member-footer"
      :class="{ 'iphonex-bottom': isIphoneX }"
      v-if="!checkedMember.vipCust"
    >
      <div class="flex-sub">
        <div class="buy-btn-bar">
          <div class="content" v-if="checkedMember?.vipPrice">
            <span class="price"
              ><span>￥</span><span>{{ checkedMember.vipPrice.discountPrice }}</span></span
            >
            <span class="price-desc">/连续包月</span>
            <span class="price-desc2"
              >折合约¥{{
                new Decimal(checkedMember.vipPrice.discountPrice)
                  .div(checkedMember.vipPrice.duration)
                  .div(checkedMember.vipPrice.durationUnit === 'MONTH' ? 30 : 1)
                  .toDP(2)
                  .toString()
              }}/天</span
            >

            <span class="tip">开通包回本</span>
          </div>
          <button class="buy-btn" @click="handleConfirm">立即开通</button>
        </div>
        <div class="agreement">
          <van-checkbox
            class="flex0"
            v-model="checked"
            icon-size="16px"
            :checked-color="$global.THMEM"
          />
          <div class="agree">
            <div class="agree-text">
              我已阅读并同意
              <span class="highlight theme-text" @click="agreementShow('/member-service')"
                >《会员综合服务协议》</span
              >、 理解并接受相关条款并同意<span
                class="highlight theme-text"
                @click="agreementShow('/member-renew')"
                >《自动续费服务协议》</span
              >，续费可随时取消
            </div>
          </div>
        </div>
      </div>
    </div>
    <sign-popup ref="signRef" @handleBuy="handleBuy" />
    <agreement-popup
      :url="agreementUrl"
      :title="agreementTitle"
      ref="agreementRef"
    ></agreement-popup>
    <not-bank-tips
      ref="notBankTipsRef"
      @add-bank="memberAddBankRef.show = true"
      title="绑定银行卡即可快速开通权益"
    ></not-bank-tips>
    <member-add-bank
      ref="memberAddBankRef"
      :bind-params="payInfo"
      @bindSuccess="launchPay"
    ></member-add-bank>
    <open-success
      title="开通成功"
      tips="成功开通轻享花省钱卡，享优先推荐特权"
      ref="openSuccRef"
    ></open-success>
    <van-popup round v-model:show="vipDescPopupShow" teleport="body" class="vip-desc-popup">
      <div class="header">
        <span class="desc-title">规则说明</span>
        <van-icon
          color="#cccccc"
          name="close"
          class="close-icon"
          @click="toggleVipDescPopupShow(false)"
        ></van-icon>
      </div>
      <div class="body" v-if="checkedMember?.vipPrice">
        <div class="vip-desc" v-dompurify-html="checkedMember?.vipPrice.vipDesc"></div>
      </div>
    </van-popup>
    <button @click="toggleVipDescPopupShow(true)" class="vip-desc-btn">规则</button>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  toRefs,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
  computed,
  useTemplateRef,
} from 'vue'

// if (import.meta.env.DEV) {
//   onMounted(() => {
//     openSuccRef.value.reveal()
//   })
// }

import { submitMemberOrder, bankCardList, payProduct, submitPay } from '@/api/member-bank'
import { getMemberInfo, memberOrderInfo } from '@/api/member'
import MySwiper from '@/components/MySwiper'
import AgreementPopup from '@/components/AgreementPopup'
import IncreaseQuota from './components/IncreaseQuotaV2'
import { getAdList } from '@/api/base'
import BenefitItem from './components/BenefitItem'
import SignPopup from './components/SignPopup'
import NotBankTips from '../member/components/memberBank/NotBankTips'
import MemberAddBank from '../member/components/memberBank/AddBank'
import OpenSuccess from './components/OpenSuccessPopup.vue'
import MemberTop from './components/MemberTop'
import MemberHeader from '../member/components/MemberHeader'
import { showLoadingToast, closeToast, showDialog, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import Decimal from 'decimal.js'
import UserAvatar from '@/components/UserAvatar'
const isIphoneX = window.isIphoneX
const { proxy } = getCurrentInstance()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const router = useRouter()
const checked = ref(true)
const signRef = ref(null)
const agreementRef = ref(null)
const notBankTipsRef = ref(null)
const memberAddBankRef = ref(null)
const agreementUrl = ref('')
const agreementTitle = ref('')
const loading = ref(false)
const openSuccRef = ref(null)
const payStatus = ref(null)
const scrollTopValue = ref(0)
const loaded = ref(false)
const data = reactive({
  advertises: {},
  checkedMember: {},
  payInfo: {},
  couponGoods: {},
  orderInfo: {},
})
const { advertises, checkedMember, payInfo, couponGoods, orderInfo } = toRefs(data)
const onBackClick = () => {
  router.go(-1)
}
const onScrollChange = ($e) => {
  scrollTopValue.value = $e.target.scrollTop
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
const agreementShow = (url) => {
  if (url === '/member-service') {
    agreementUrl.value = '/agreement/member-service.htm'
    agreementTitle.value = '权益服务协议'
  }

  if (url === '/member-renew') {
    agreementUrl.value = '/agreement/member-renew.htm'
    agreementTitle.value = '自动续费服务协议'
  }

  if (url === '/digital-certificate-protocol') {
    agreementUrl.value = '/agreement/digital-certificate-protocol.htm'
    agreementTitle.value = '数字证书协议'
  }

  agreementRef.value.show = true
}
// 更新优惠券商品
const updateCouponGoods = (data) => {
  couponGoods.value = data
}
provide('checkedMember', checkedMember)
provide('couponGoods', couponGoods)
provide('updateCouponGoods', updateCouponGoods)
const handleGoods = (benefit, item) => {
  if (checkedMember.value.vipCust) {
    const params = {
      goodsId: item.spuId,
      skuId: item.skuId,
      packageId: benefit.id,
      shoppingFlag: 'V',
    }
    if (item.categoryGroup === 'COUPON') {
      router.push({ path: '/goods-coupon', query: params })
    } else {
      router.push({ path: '/goods-detail', query: params })
    }
  } else {
    signRef.value.show = true
  }
}
provide('handleGoods', handleGoods)
const handleConfirm = () => {
  if (!checkedMember.value.vipCust) {
    signRef.value.show = true
  } else {
    router.push('/my/quota')
  }
}

// 购买
const handleBuy = async () => {
  const payableTimes = localStorage.getItem('vip_quickpay')
  if (payableTimes && payableTimes > Date.now()) {
    // 5分钟内多次点击购买
    showConfirmDialog({
      title: '提示',
      message: '您5分钟内发起过权益开通，是否已扣款成功？如已扣款成功，请稍后下拉刷新页面',
      confirmButtonText: '扣款失败,重新开通',
      cancelButtonText: '已扣款成功',
    })
      .then(() => {
        localStorage.removeItem('vip_quickpay')
        submitVipOrder()
      })
      .catch(() => {
        initPage() // 购买前先判断是否已购买
        return
      })
  } else {
    submitVipOrder()
  }
}
const submitVipOrder = async () => {
  try {
    showLoadingToast({
      duration: 0,
      message: '加载中...',
      forbidClick: true,
    })
    await initPage() // 购买前先判断是否已购买
    if (!checkedMember.value.vipCust) {
      // 会员卡下单
      const orderRes = await submitMemberOrder({
        virtualMsg: { vipPriceId: checkedMember.value.vipPrice.id },
      })
      if (orderRes) {
        const orderId = orderRes.data?.orderId
        // 获取支付产品信息
        const payRes = await payProduct({ orderId })
        payInfo.value = payRes.data.cashiers[0]
        payInfo.value.orderId = orderId
        const bankRes = await bankCardList({
          pageNum: 1,
          pageSize: 100,
          channelId: payInfo.value.bankChannelId,
          orderId: orderId,
          bankCardType: payInfo.value.cardBindType,
          cmId: payInfo.value.cmId,
        })
        if (bankRes.data?.length > 0) {
          // 提交支付
          // closeToast() // 清除加载中

          await launchPay(bankRes.data[0].id)
        } else {
          notBankTipsRef.value.show = true
        }
      }
    } else {
      closeToast() // 清除加载中

      await openSuccRef.value.reveal()

      // showDialog({
      //   title: '提示',
      //   message: '会员已购买成功！',
      // }).then(() => {
      //   // on close
      // })
    }
  } catch (error) {
    console.log('会员购买失败')
  }
  closeToast() // 清除加载中
}
// 发起支付
const launchPay = async (bankId) => {
  const {
    data: { status },
  } = await submitPay({ cashierId: payInfo.value.id, orderId: payInfo.value.orderId, id: bankId })
  const currentTimestamp = Date.now() + 5 * 60 * 1000
  localStorage.setItem('vip_quickpay', currentTimestamp) // 5分钟内发起再次支付
  // 延迟等待支付回调完成
  await new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, 3000)
  })
  closeToast() // 清除加载中
  await openSuccRef.value.reveal()
  initPage()
}
const initPage = async () => {
  await getMemberInfo({ vipType: 'DISCOUNT' }, false).then((res) => {
    checkedMember.value = res.data?.[0] // 默认第一条会员
    if (checkedMember.value.vipCust) {
      memberOrderInfo({ id: checkedMember.value.vipPrice.id }).then((res) => {
        orderInfo.value = res.data
      })
    }
    loading.value = false
    loaded.value = true
  })
}
onMounted(() => {
  getAdList({ regionType: [proxy.$global.AD_POSITION_SAVE_MONEY] }).then((res) => {
    advertises.value = res.data
  })
  initPage()
})
const benefitListRef = useTemplateRef('benefitList')
const benefitList = computed(() => benefitListRef.value?.benefitList)
provide('benefitList', benefitList)
function updateBenfit() {
  initPage()
}
import { useToggle } from '@vueuse/core'
const [vipDescPopupShow, toggleVipDescPopupShow] = useToggle(false)
</script>
<style scoped lang="scss">
.page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: absolute;
  background: #f6f6f6;
  .page-context.member {
    padding-bottom: 44px;
  }
  &-context {
    margin-top: -1px;
    flex-grow: 1;
    overflow: hidden;
    overflow-y: auto;
    padding-bottom: 150px;
    .van-pull-refresh {
      background: linear-gradient(180deg, #f57165 0%, #f6f6f6 250px) no-repeat;
      background-size: 100% 250px;
      background-color: #f6f6f6;
    }
    .title-img {
      width: 258px;
      height: 33px;
      display: block;
      margin: 30px auto 0;
    }
    .bannar-wrapper {
      margin: 10px;
    }
  }
  .member-footer.iphonex-bottom {
    // padding-bottom: 44px;
    padding-bottom: var(--safe-area-inset-bottom);
  }
  .member-footer {
    position: fixed;
    bottom: 0;
    // width: 335px;
    width: 100%;
    padding: 22px 10px 0 10px;
    box-sizing: border-box;
    z-index: 1200;
    background: #ffffff;
  }
  .agreement {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    margin-top: 9px;
    .flex0 {
      flex-shrink: 0;
    }
    .agree {
      margin-left: 8px;
      flex: 1;
      min-width: 0;
      position: relative;
      font-size: 12px;
      // width: 100%;
      .agree-text {
        width: 100%;
        white-space: nowrap; /* 确保文本不换行 */
        overflow-x: auto; /* 左右滚动 */
        overflow-y: hidden; /* 隐藏垂直滚动条 */
        touch-action: pan-x; /* 仅允许横向滑动 */
        -webkit-overflow-scrolling: touch; /* 对于移动设备使用滚动回弹效果 */
        font-size: 12px;
        // zoom: 0.93;
        color: #999999;
        position: relative;
      }
      &::after {
        content: '';
        height: 100%;
        width: 2em;
        // background-color: #f6f6f6;
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0), rgb(255, 255, 255));
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
  // .buy-btn {
  //   height: 44px;
  //   border-radius: 22px;
  //   margin: 0 auto;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   font-size: 16px;
  //   color: #ffffff;
  //   span {
  //     font-weight: 600;
  //     font-size: 24px;
  //     color: #ffffff;
  //     margin-left: 5px;
  //   }
  // }
}

.user-info {
  background-image: url('@/assets/images/member/user-info-bg.png');
  background-size: 100% 100%;
  width: 352px;
  height: 102px;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  // margin-bottom: -20px;
  + :deep(.increase-quota) {
    margin-top: -10px;
    // margin-top: 200px;
  }
  .user-info__body {
    margin-left: 10px;
    margin-top: 40px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    &.is-credit {
      // margin-top: 36px;
    }
    .user-info__content {
      margin-left: 6px;
      flex: 1;
      min-width: 0;
    }
    .user-info__avatar {
      width: 50px;
      height: 50px;
      border-radius: 999vw;
      border: 2px solid #fff;
      background-color: #fff;
    }
    .user-info__phone {
      font-size: 18px;
      color: #bb2518;
    }
    .user-info__desc {
      font-size: 15px;
      color: #bb251880;
      margin-top: 4px;
      display: flex;
      align-items: center;
    }
  }
  .user-info__btn {
    font-size: 15px;
    color: #fff;
    // position: absolute;
    // right: 10px;
    // top: 70px;
    margin-left: auto;
    border: none;
    background: none;
    padding: 0;
  }
}
:deep(.increase-quota) {
  // margin-top: -10px;
  // background-color: red;
  // display: none;
}
.benefit-list {
  margin: 10px;
  background-color: #fff;
  border-radius: 10px;
  padding: 11px 8px 22px;
  .benefit-list__title {
    font-size: 15px;
    color: #4f0601;
    font-weight: 600;
  }
  .benefit-list__items {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 14px;
    .benefit-list__item {
      .benefit-list__item__icon {
        width: 44px;
        height: 44px;
        display: block;
        margin: 0 auto;
      }
      .benefit-list__item__title {
        margin-top: 4px;
        font-size: 14px;
        color: #ab4d0c;
        text-align: center;
      }
    }
  }
}
.member-footer {
}
.buy-btn-bar {
  border-radius: 999vw;
  background: linear-gradient(180deg, #432811 16.11%, #26221f 100%);
  height: 44px;
  width: 355px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  // padding: 0 0 0 ;
  padding-left: 28px;
  box-sizing: border-box;
  font-size: 14px;
  .content {
    position: relative;
    line-height: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  .price {
    color: #ffe9d3;
    font-size: 20px;
    font-weight: 600;
  }
  .price-desc {
    color: #ffe9d380;
    font-size: 15px;
    margin-left: 4px;
  }
  .price-desc2 {
    color: #ffffff80;
    font-size: 14px;
    margin-left: 4px;
  }
  .tip {
    position: absolute;
    display: block;
    // top: 100%;
    bottom: calc(100% - 2px);
    // bottom: 100%;
    // bottom: calc(-100% + 0px);

    left: 70px;
    background-color: #ff671b;
    border-radius: 999vw 999vw 999vw 0;
    font-size: 13px;
    padding: 4px 10px;
    color: #fff;
    // display: flex;
  }
  .buy-btn {
    height: 100%;
    border-radius: 999vw;
    border: none;
    display: block;
    margin-left: auto;
    width: 115px;
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    background: linear-gradient(180deg, #ff3927 0%, #ff8c64 100%);
  }
}
.vip-desc-btn {
  position: fixed;
  right: 0;
  top: 200px;
  background-color: #0000004d;
  border-radius: 4px 0 0 4px;
  color: #fff;
  font-size: 14px;
  padding: 8px 4px;
  z-index: 100;
  border: none;
  writing-mode: vertical-lr;
}
.vip-desc-popup {
  // margin: 10px;
  // height: 100%;
  // max-height: calc(100vh - 44px);
  max-height: calc(80dvh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
  // width: 100%;
  display: flex;
  flex-direction: column;
  .header {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    height: 44px;
    position: relative;
    text-align: center;
    flex: none;
    .desc-title {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
    .close-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  .body {
    padding: 10px;
    flex: 1;
    overflow-y: auto;
    .vip-desc {
      font-size: 14px;
      color: #333;
      line-height: 1.5;
    }
  }
}
</style>
