<template>
  <van-popup v-model:show="show" :close-on-click-overlay="false" class="open-success-popup">
    <div class="body">
      <button class="confirm-btn" @click="confirm()">享用特权</button>
      <div class="tips">
        <span>详情请查看</span>
        <button class="order-btn" @click="openOrder()">权益服务订单</button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
defineExpose({
  reveal,
  cancel,
})
const show = ref(false)
onReveal(() => {
  show.value = true
})
onConfirm(() => {
  show.value = false
})
onCancel(() => {
  show.value = false
})
const router = useRouter()
function openOrder() {
  cancel()
  router.push('/member/equity-order')
}
</script>

<style lang="scss" scoped>
.open-success-popup {
  background-color: transparent;
  margin-top: -40px;
  .body {
    background-image: url('@/assets/images/member/open-success-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    width: 286px;
    height: 406px;
    box-sizing: border-box;
    padding: 91px 0 0 0;
    .confirm-btn {
      background: linear-gradient(2deg, #ffa375 1.66%, #ff2419 98.94%);
      width: 234.5px;
      height: 40px;
      border-radius: 999vw;
      color: #fff;
      font-size: 18px;
      font-weight: 500;
      display: block;
      border: none;
      margin: 236px auto 0;
    }
    .tips {
      color: #201616;
      font-size: 14px;
      font-weight: 500;
      margin-top: 7.5px;
      text-align: center;
      .order-btn {
        color: #e30000;
        border: none;
        background-color: transparent;
      }
    }
  }
}
</style>
