<template>
  <div v-if="couponGoods && couponGoods.vipBenefitGoodsInfo?.length > 0" class="coupon-goods">
    <div v-for="item in couponGoods.vipBenefitGoodsInfo" @click="onGoods(item)">
      <img class="goods-img" :src="item.imgUrl">
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const couponGoods = inject('couponGoods')
const handleGoods = inject('handleGoods')
const onGoods = (item) => {
  handleGoods(couponGoods.value, item)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .goods-img{
    width: 100%;
    height: auto;
    margin-top: 10px;
  }
</style>