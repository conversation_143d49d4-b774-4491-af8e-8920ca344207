<template>
  <div>
    <van-popup
      :close-on-click-overlay="false"
      class="overflow-inherit sign-popup"
      round
      v-model:show="show"
    >
      <div class="body">
        <!-- <div class="title">再次确认激活省钱卡特权</div>
        <div class="tips" v-if="checkedMember.vipPrice">
          我已阅读《权益服务协议》，理解并接受相关条款并同意以{{
            checkedMember.vipPrice.discountPrice
          }}元续费省钱卡，可随时取消
        </div> -->
        <!-- <van-icon
        class="close"
        size="0.6rem"
        color="#cccccc"
        name="close"
        @click="onCancel"
        ></van-icon> -->
        <div class="content">
          <ul class="benefit-list">
            <li v-for="item in checkedMember.benefitList" class="item">
              <img src="@/assets/images/member/sign-popup-item-icon.png" alt="" class="icon" />
              <span>
                {{ item.benefitName }}
              </span>
            </li>
          </ul>
          <div class="price">
            <span class="prefix">仅需</span><span class="symbol">￥</span
            ><span class="value">{{ checkedMember.vipPrice.discountPrice }}</span>
          </div>
        </div>
        <button class="close-btn" @click="onCancel"></button>
      </div>
      <button class="btn" @click="handleBuy">确认开通</button>
    </van-popup>
    <cancel-open ref="cancelOpenRef"></cancel-open>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import CancelOpen from './CancelOpenPopup.vue'
const { proxy } = getCurrentInstance()
const store = useStore()
const user = computed(() => store.getters.userInfo)
const route = useRoute()
const router = useRouter()
const show = ref(false)
const cancelOpenRef = ref(null)
const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleBuy'])
const handleBuy = () => {
  show.value = false
  emit('handleBuy')
}
const onCancel = async () => {
  // if (user.value?.creditIntentionFlag === 'Y') {
  show.value = false
  const { data, isCanceled } = await cancelOpenRef.value.reveal()
  if (isCanceled) {
    return
  }
  handleBuy()
  // }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
defineExpose({ show })
</script>
<style scoped lang="scss">
:deep(.van-popup) {
  overflow-y: inherit !important;
}
.sign-popup {
  // background-image: url('@/assets/images/member/sign-popup-bg.png');
  // background-size: 100% 100%;
  // width: ;
  background-color: transparent;
  .body {
    padding: 120px 22px 20px 22px;
    box-sizing: border-box;
    position: relative;
    width: 286px;
    height: 279px;
    background-image: url('@/assets/images/member/sign-popup-bg.png');
    background-size: 100% 100%;
    position: relative;
    .close-btn {
      border: none;
      position: absolute;
      top: 13px;
      right: 8px;
      width: 30px;
      height: 30px;
      opacity: 0;
      // background-color: red;
    }
    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      .benefit-list {
        margin-right: 120px;
        .item {
          font-size: 13px;
          color: #5d19144d;
          line-height: 18px;
          height: 18px;
          .icon {
            width: 10px;
            height: 9px;
            margin-right: 4px;
            vertical-align: middle;
          }
        }
      }
      .price {
        margin-top: auto;
        color: #4671eb;
        font-family: 'DIN';
        font-weight: 600;
        text-shadow: -2px -2px 0 #fff, 2px -2px 0 #fff, -2px 2px 0 #fff, 2px 2px 0 #fff;

        :deep(*) {
          font-family: inherit;
        }
        .prefix {
          font-size: 13px;
        }
        .symbol {
          font-size: 26px;
        }
        .value {
          font-size: 42px;
        }
      }
    }
    // .title {
    //   font-weight: 500;
    //   font-size: 18px;
    //   color: #000000;
    //   line-height: 25px;
    //   text-align: center;
    // }
    // .tips {
    //   font-size: 14px;
    //   color: #666666;
    //   line-height: 20px;
    //   margin-top: 18px;
    // }
    // .benefit {
    //   margin-top: 17px;
    //   display: flex;
    //   justify-content: space-between;
    //   &-item {
    //     display: flex;
    //     flex-direction: column;
    //     align-items: center;
    //     flex: 1;
    //     img {
    //       width: 40px;
    //       height: 40px;
    //       display: block;
    //     }
    //     .text {
    //       font-size: 12px;
    //       color: #666666;
    //       line-height: 17px;
    //       margin-top: 6px;
    //     }
    //   }
    // }

    // .close {
    //   display: block;
    //   position: absolute;
    //   bottom: -40px;
    //   left: calc(50% - 10px);
    // }
  }
  .btn {
    border: none;
    width: 234px;
    height: 40px;
    margin: 15px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    border-radius: 999vw;
    background: linear-gradient(2deg, #ffa375 1.66%, #ff2419 98.94%);
  }
}
</style>
