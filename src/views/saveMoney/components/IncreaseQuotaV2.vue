<template>
  <div class="quota" v-bind="$attrs">
    <!-- <div v-if="checkedMember.vipCust" class="title">限时免息 | 循环额度 | 灵活消费</div> -->
    <template v-if="!checkedMember.vipCust">
      <div class="title-1">专属提额</div>
      <div class="title-2">
        最高可提升<img src="@/assets/images/member/up.png" alt="UP" class="icon" />
      </div>
      <img src="@/assets/images/member/激活立即生效icon.png" alt="" class="stamp" />
    </template>

    <template v-if="!checkedMember.vipCust">
      <div class="number-wrapper">
        <van-rolling-text
          ref="rollingText"
          :start-num="0"
          :target-num="5000"
          direction="up"
          stop-order="rtl"
          :auto-start="false"
          :height="rollingHeight"
        />
      </div>

      <!-- <div v-if="checkedMember.vipCust" class="tip">激活极省VIP，以实际获得额度为准</div> -->

      <div class="tip">提额结果以最终审批为准</div>
      <button class="apply-btn" @click="handleConfirm">立即提额</button>
      <div class="agreement">
        <van-checkbox v-model="checked" icon-size="16px" :checked-color="$global.THMEM" /><span
          >我已阅读并同意</span
        ><strong @click="emit('agreementShow', '/member-service')">《会员综合服务协议》</strong>
      </div>
    </template>

    <div v-if="checkedMember.vipCust && voucher" class="voucher">
      <div class="voucher-title-wrapper">
        <!-- <div class="voucher-name">{{ voucher.name }}</div> -->
        <!-- <div class="voucher-title"> -->
        <div class="voucher-title-1">{{ voucher.title1 }}</div>
        <div class="voucher-title-2">{{ voucher.title2 }}</div>
        <div class="voucher-title-3">{{ voucher.title3 }}</div>
        <!-- </div> -->
      </div>
      <div class="voucher-list-wrapper">
        <div class="voucher-list">
          <div
            v-for="item in voucher.vipBenefitGoodsInfo.slice(0, 2)"
            :key="item.id"
            class="voucher-item"
            :class="item.voucherUseType"
          >
            <div class="voucher-item-title">
              {{ item.voucherUseType === 'CASH_QUOTA' ? '极享金' : '极刻花' }}
            </div>
            <div class="voucher-item-desc">
              {{ item.voucherUseType === 'CASH_QUOTA' ? '轻松提借钱额度' : '轻松提分期额度' }}
            </div>
            <button
              v-if="item.receiveNum === 0"
              class="voucher-item-btn"
              @click="handleReceive(item)"
            >
              立即领取
            </button>
            <button v-else class="voucher-item-btn" @click="goVoucherUse(item)">
              <span v-if="item.voucherUseType === 'CASH_QUOTA'">立即借钱</span>
              <span v-else>立刻提额</span>
            </button>
            <template v-if="item.receiveNum > 0">
              <img
                v-if="item.voucherUseType === 'CASH_QUOTA'"
                class="receive-icon"
                src="@/assets/images/member/quota-receive-cash2.png"
              />
              <img
                v-else
                class="receive-icon"
                src="@/assets/images/member/quota-receive-consume2.png"
              />
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <van-popup
    :show="receivedSuccessDialog.isRevealed"
    close-on-popstate
    class="received-success-dialog"
    :class="{
      'cash-quota': receivedSuccessDialogData?.voucherUseType === 'CASH_QUOTA',
      'consume-quota': receivedSuccessDialogData?.voucherUseType === 'CONSUME_QUOTA',
    }"
    @close="receivedSuccessDialog.cancel('close')"
    @touchmove.stop
  >
    <!-- <div class="header"></div> -->
    <div class="body">
      <div class="title">不错哦，领取到了隐藏攻略</div>

      <div class="voucher-wrapper">
        <div class="voucher-title">
          <div class="">
            <span class="symbol">￥</span>
            <span class="value">{{ receivedSuccessDialogData?.price ?? '?' }}</span>
          </div>
        </div>
        <div class="voucher-desc">
          <div class="title">大额提额券</div>
          <div class="sub-title">限量前1000名领取</div>
        </div>
      </div>
      <div class="desc">
        <ul>
          <li>
            1.有效期：提额券自领取后自动激活生效，对应提升额度有效期与用户现有额度保持一致，额度到期时，提额权益自动清零。​
          </li>
          <li>
            2.适用范围：该提额券仅限用于平台指定金融产品 "{{
              receivedSuccessDialogData?.voucherUseType === 'CASH_QUOTA' ? '极享金' : '极刻花'
            }}"，不可用于其他产品或业务场景。​
          </li>
          <li>
            3.使用说明：该权益激活后即视为已使用，权益不可撤回、不可退款，且无法转让给其他用户。
          </li>
        </ul>
      </div>
    </div>
    <div class="footer">
      <button class="confirm-btn" @click="receivedSuccessDialog.confirm()">
        <span v-if="receivedSuccessDialogData?.voucherId">立即激活</span>
        <span v-else>立即查看</span>
      </button>
    </div>
  </van-popup>
</template>

<script setup>
import { useTemplateRef, nextTick, reactive } from 'vue'
import { useToggle, unrefElement, useConfirmDialog, useWindowSize } from '@vueuse/core'
// import { CountUp } from 'countup.js'
// import { Odometer } from 'odometer_countup'
import { showToast, showDialog, showSuccessToast, showConfirmDialog } from 'vant'
import Decimal from 'decimal.js'

import { receiveSubmit } from '@/api/member'
import { writtenOff } from '@/api/voucher'

const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleConfirm', 'agreementShow'])

const { width } = useWindowSize()
const rollingHeight = computed(() => new Decimal(54).div(375).mul(width.value).toDP(0).toNumber())

function handleConfirm() {
  if (!checked.value) {
    showToast('请先阅读并同意《极省VIP协议》')
    return
  }
  emit('handleConfirm')
}

const [checked, toggleChecked] = useToggle(true)
const [show, toggleShow] = useToggle(false)
const numberAnimationWrapperRef = useTemplateRef('numberAnimationWrapper')

const limit = ref(5000)
const benefitList = inject('benefitList')

const voucher = computed(() => {
  return benefitList.value?.find((item) => item.packageType === 'VOUCHER')
})

let countUp = null
const rollingTextRef = useTemplateRef('rollingText')

onMounted(() => {
  // limit.value = 5000
  // if (!numberAnimationWrapperRef.value) return
  // countUp = new CountUp(unrefElement(numberAnimationWrapperRef), 0, {
  //   startVal: 0,
  //   plugin: new Odometer({ duration: 2, lastDigitDelay: 0 }),
  //   duration: 1,
  //   useGrouping: false,
  // })
  // countUp.update(limit.value)
  // countUp.start()
  rollingTextRef.value?.start()
})

watch(
  checkedMember,
  async (val) => {
    if (!rollingTextRef.value) return
    rollingTextRef.value.reset()
    // XXX: 这里需要等待一下才能播放动画
    await new Promise((resolve) => setTimeout(resolve, 100))
    rollingTextRef.value?.start()
    // countUp?.reset()
    // countUp?.update(limit.value)
    // countUp?.start()
  },
  {
    // immediate: true,
  }
)

const router = useRouter()
function goVoucherUse(item) {
  console.log('item>>>', item)
  if (item.voucherUseType === 'CASH_QUOTA') {
    router.push({
      path: '/cash-loan',
      query: {},
    })
    return
  }
  router.push('/installment')
}
const receivedSuccessDialogData = ref()
const receivedSuccessDialog = reactive(useConfirmDialog())

receivedSuccessDialog.onReveal((data) => {
  receivedSuccessDialogData.value = data
})
receivedSuccessDialog.onConfirm(async () => {
  if (receivedSuccessDialogData.value?.voucherId) {
    const res = await writtenOff({
      voucherId: receivedSuccessDialogData.value?.voucherId,
    })
    if (res.data?.creditFlag === 'Y') {
      showSuccessToast('激活成功')
    } else {
      await showConfirmDialog({
        title: '提示',
        message: '当前产品授信额度未可用，暂不能使用提额卡',
        confirmButtonText: '去看看',
        cancelButtonText: '好的',
      })
      goVoucherUse(receivedSuccessDialogData.value)
      // router.push('/cash-loan')
    }
  } else {
    goVoucherUse(receivedSuccessDialogData.value)
  }
})

function handleReceive(item) {
  // receivedSuccessDialog.reveal(item)

  // return
  if (checkedMember.value.vipCust) {
    if (item.receiveNum > 0) {
      showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
      return
    }
    if (item.receiveNum === 0) {
      receiveSubmit({ id: item.id }).then((res) => {
        // console.log('res', res)
        // showSuccessToast('领取成功')
        receivedSuccessDialog.reveal({ ...item, voucherId: res.data })
        item.receiveNum = 1
      })
    }
  } else {
    emit('buyTips')
  }
}
</script>

<style lang="scss" scoped>
.quota {
  background-image: url('@/assets/images/member/quota-bg.png');
  background-size: 100% auto;
  padding: 12px 15px;
  position: relative;
  color: #333;
  .title {
    background-image: url('@/assets/images/member/quota-title-bg.png');
    background-size: 100% 100%;
    width: 208px;
    height: 27px;
    text-align: center;
    line-height: 27px;
    font-size: 14px;
    color: #974f33;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  // height: ;
  .title-1 {
    font-size: 15px;
    color: #4f0601;
    position: absolute;
    top: 12px;
    left: 15px;
    font-weight: 600;
  }
  .title-2 {
    text-align: center;
    font-size: 18px;
    color: #333;
    margin-top: 10px;
    .icon {
      width: 11px;
      height: 15px;
    }
  }
  .number-wrapper {
    text-align: center;
    margin-top: 33px;
    // display: inline-flex;
    color: #f43929;
    font-size: 54px;
    font-weight: bold;
    font-family: DIN !important;
    position: relative;
    // display: inline-block;
    text-align: center;
    transform: translate3d(0px, 0px, 0px);
    .van-rolling-text {
      --van-rolling-text-color: #f43929;
      --van-rolling-text-font-size: 55px;
      --van-rolling-text-item-width: 30px;
      // height: 54px;
      :deep() {
        .van-rolling-text-item {
          // height: 54px !important;
        }
        .van-rolling-text-item__item {
          font-family: DIN !important;
          // line-height: 54px !important;
        }
      }
    }

    // .value {
    // }
  }
  .tip {
    font-size: 14px;
    color: #4f06014d;
    text-align: center;
    margin-top: 15px;
  }
  .apply-btn {
    width: 234.5px;
    height: 40px;
    border-radius: 999vw;
    background: linear-gradient(2deg, #ffa375 1.66%, #ff2419 98.94%);
    border: none;
    font-size: 18px;
    color: #fff;
    margin: 7px auto 0;
    display: block;
  }
  .stamp {
    display: block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 10px;
    right: 3px;
  }
  .agreement {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #4f06014d;
    .van-checkbox {
      display: inline-flex;
      margin-right: 4px;
    }
    strong {
      color: #4671eb;
      font-weight: inherit;
    }
  }
}
.agreement-popup .popup-content {
  padding: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  height: 60vh;
  .desc-title {
    text-align: right;
    padding-bottom: 10px;
  }
  .vip-desc {
    flex: 1;
    overflow-y: auto;
  }
  :deep(p) {
    font-size: 13px;
    color: #000000;
    line-height: 18px;
  }
}

.voucher {
  display: flex;
  width: 354px;
  height: 104px;
  margin: 0px auto 0;
  border-radius: 15px;
  // background: linear-gradient(250deg, #ffc3ae 72.89%, rgba(246, 246, 246, 0.3) 98.22%);
  background-image: url('@/assets/images/member/voucher-bg.png');
  background-size: 100% 100%;
  overflow: hidden;
  .voucher-title-wrapper {
    flex: 1;
    margin-top: 22px;
    margin-left: 10px;
    line-height: 1.2;
    color: #5a161180;
    font-size: 13px;
    line-height: 1;

    .voucher-name {
    }
    // .voucher-title {
    .voucher-title-1 {
      color: #5a1611;
      font-size: 16px;
      font-weight: 600;
    }
    .voucher-title-2 {
      margin-top: 8px;
    }
    // }
  }
  .voucher-list-wrapper {
    // flex: 1;
    width: 253px;
    flex: none;
    margin: 5px 4px;
    background-color: #fff;
    border-radius: 10px;
    .voucher-list {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 10px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      .voucher-item {
        width: 100%;
        height: 100%;
        border-radius: 10px;
        font-size: 14px;
        background-size: 100% 100%;
        position: relative;
        .receive-icon {
          position: absolute;
          top: -6px;
          right: -6px;
          width: 44px;
          height: 44px;
        }
        &.CASH_QUOTA {
          background: linear-gradient(180deg, #9b9de8 -20.02%, #fff 60.79%);
          --color: 83, 88, 225;
          // .voucher-item-title {
          //   color: #5358e1;
          // }
          // .voucher-item-desc {
          //   color: #5358e14d;
          // }
          // .voucher-item-btn {
          //   background-color: #5358e1;
          // }
        }
        &.CONSUME_QUOTA {
          background: linear-gradient(180deg, #f5d5e5 -20.02%, #fff 57.87%);

          --color: 226, 86, 156;
          // .voucher-item-title {
          // }
          // .voucher-item-desc {
          //   color: #e2569c4d;
          // }
          // .voucher-item-btn {
          //   background-color: #e2569c;
          // }
        }
        .voucher-item-title {
          font-size: 15px;
          color: rgb(var(--color));
          text-align: center;
          margin-top: 10px;
          // color: #fff;
        }
        .voucher-item-desc {
          font-size: 12px;
          color: rgba(var(--color), 0.3);
          text-align: center;
          margin-top: 3px;
        }
        .voucher-item-btn {
          width: 82px;
          height: 23px;
          border-radius: 999vw;
          color: #fff;
          font-size: 12px;
          font-weight: 600;
          background-color: rgb(var(--color));
          border: none;
          display: block;
          margin: 5px auto 0;
        }
      }
    }
  }
}
.received-success-dialog {
  border-radius: 25px;
  width: calc(572px / 2);

  &.cash-quota {
    .body {
      background-image: url('@/assets/images/member/received-success-bg.png'),
        linear-gradient(178deg, #595fff -36.35%, #fff 32.82%);
      .title {
        color: #2224ae;
      }
    }
    .footer {
      .confirm-btn {
        background: linear-gradient(180deg, #8c91f2 0%, #5458e1 100%);
      }
    }
  }
  &.consume-quota {
    .body {
      background-image: url('@/assets/images/member/received-success-bg.png'),
        linear-gradient(178deg, #fe9acc -36.35%, #fff 32.82%);
      .title {
        color: #8c2659;
      }
    }
    .footer {
      .confirm-btn {
        background: linear-gradient(180deg, #f38ecb 0%, #e2569c 100%);
      }
    }
  }

  .body {
    padding: 20px 20px 0;
    box-sizing: border-box;
    height: calc(552px / 2);
    overflow: hidden;
    margin-top: 6px;
    border-radius: 25px;
    // background: url('@/assets/images/member/received-success-bg.png'),
    // linear-gradient(178deg, #fe9acc -36.35%, #fff 32.82%);
    background-size: 84px 96px, 100% 100%;
    background-repeat: no-repeat;
    background-position: right 9px top 6px, top left;
    // opacity: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 80px;
      background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
      pointer-events: none;
    }

    .title {
      // color: #8c2659;
      font-size: 20px;
      text-align: center;
      flex: none;
    }
    .voucher-wrapper {
      flex: none;
      width: 240px;
      height: 82px;
      box-sizing: border-box;
      font-size: 14px;
      background-image: url('@/assets/images/member/received-success-dialog-voucher-bg.png');
      background-size: 100% 100%;
      margin: 14px auto;
      display: flex;
      color: var(--primary-color);
      .voucher-title {
        flex: none;
        width: 87px;
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: center;
        text-align: center;
        font-size: calc(56px / 2);

        .symbol {
          font-size: calc(34px / 2);
        }
        .value {
          // font-size: calc(56px / 2);
        }
      }
      .voucher-desc {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        .title {
          font-size: 14px;
          color: var(--primary-color);
          font-size: 20px;
          font-weight: 600;
          text-align: left;
        }
        .sub-title {
          font-size: 12px;
          color: #33333380;
          font-size: 14px;
          margin-top: 4px;
        }
      }
    }
    .desc {
      color: #999;
      font-size: 14px;
      line-height: 1.2;
      overflow-x: hidden;
      overflow-y: scroll;
      flex: 1;
      height: 0;
      position: relative;
      padding-bottom: 20px;
    }
  }
  .footer {
    padding: 8px 20px 20px;
    .confirm-btn {
      width: 100%;
      height: 40px;
      border-radius: 999vw;
      border: none;
      // background: linear-gradient(180deg, #f38ecb 0%, #e2569c 100%);
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
