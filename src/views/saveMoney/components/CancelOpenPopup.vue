<template>
  <van-popup v-model:show="visible" :close-on-click-overlay="false" class="cancel-open-popup">
    <div class="popup-content">
      <div class="header">
        <div class="title">真的要放弃么</div>
        <div class="sub-title">90%的人都已选择开通</div>
      </div>
      <div class="body">
        <div class="title">
          极省会员尊享{{
            '零一二三四五六七八九十'[checkedMember?.benefitList?.length ?? 0]
          }}大项特权
        </div>
        <div class="list-wrapper">
          <div class="list">
            <div v-for="item in checkedMember?.benefitList" :key="item.id" class="item">
              <img :src="item.benefitImg" alt="" class="icon" />
              <div class="name">{{ item.benefitName }}</div>
            </div>
          </div>
        </div>
      </div>
      <button class="close-btn" @click="cancel('close')">
        <van-icon name="cross" />
      </button>
    </div>
    <button class="continue-btn" @click="confirm('continue')">继续开通</button>
  </van-popup>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onConfirm, onCancel } = useConfirmDialog()
const visible = ref(false)
const checkedMember = inject('checkedMember')

defineExpose({
  reveal,
  cancel,
})
onReveal(() => {
  visible.value = true
})
onConfirm(() => {
  visible.value = false
})
onCancel(() => {
  visible.value = false
})
</script>

<style lang="scss" scoped>
.cancel-open-popup {
  background-color: transparent;
  font-size: 12px;

  .popup-content {
    background-image: url('@/assets/images/member/cancel-open-popup-bg.png');
    background-size: 100% 100%;
    width: 286px;
    height: 347px;
  }

  .header {
    // padding-top: 100px;
    padding: 60px 20px 20px;
    height: 124px;
    // background: red;
    // opacity: 0.5;
    box-sizing: border-box;
    .title {
      font-size: 20px;
      font-weight: 700;
      color: #bb4d12;
    }
    .sub-title {
      font-size: 20px;
      font-weight: 700;
      color: #461d07;
      margin-top: 6px;
    }
  }
  .close-btn {
    position: absolute;
    top: 44px;
    right: 8px;
    width: 22px;
    height: 22px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    // opacity: 0;
    border: none;
    background: transparent;
  }
  .body {
    position: relative;
    box-sizing: border-box;
    padding: 20px 30px 30px;

    .title {
      text-align: center;
      color: var(--primary-color);
      font-size: 17px;
      margin-top: 10px;
      font-weight: 600;
    }
    .list-wrapper {
      margin-top: 10px;
      .list {
        display: flex;
        flex-wrap: wrap;
        gap: 6px 20px;
        align-items: center;
        justify-content: center;
        .item {
          display: flex;
          flex-direction: column;

          .icon {
            display: block;
            width: 44px;
            height: 44px;
            margin: 0 auto;
          }
          .name {
            color: #ab4d0c;
            font-size: 14px;
            margin-top: 4px;
            text-align: center;
          }
        }
      }
    }
  }
  .continue-btn {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    width: 234px;
    height: 40px;
    border-radius: 999vw;
    background: linear-gradient(2deg, #ffa375 1.66%, #ff2419 98.94%);
    display: block;
    border: none;
    margin: 15px auto 0;
  }
}
</style>
