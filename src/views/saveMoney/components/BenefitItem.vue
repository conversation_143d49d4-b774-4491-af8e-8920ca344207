<template>
  <div>
    <!-- <voucher-item
      v-if="voucherData && checkedMember.vipCust"
      :data="voucherData"
      @updateBenfit="queryBenefitList"
    ></voucher-item> -->
    <div class="benefit-wrapper">
      <template v-for="(benefit, index) in benefitList" :key="index">
        <!-- 实体商品 -->
        <div v-if="benefit.packageType === 'GOODS'" class="goods" :data-type="benefit.packageType">
          <div class="body">
            <div class="title">
              <img :src="benefit.imgUrl" alt="" class="icon" />
              <div class="title1">
                {{ benefit.title1 }}
              </div>
            </div>
            <div class="content">
              <div class="list">
                <div
                  v-for="item in benefit.vipBenefitGoodsInfo"
                  :key="item.id"
                  class="item"
                  @click="onGoods(benefit, item)"
                >
                  <img :src="item.imgUrl" alt="" class="img" />
                  <div class="label">
                    仅需<span class="price"
                      ><span class="symbol">￥</span><span class="integer">{{ ~~item.price }}</span
                      ><span v-if="item.price.toString().includes('.')" class="decimal"
                        >.{{ item.price.toString().split('.')[1] }}</span
                      ></span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 虚拟商品 -->
        <div v-else-if="benefit.packageType === 'VIRTUAL_GOODS'" class="virtual-goods">
          <div class="header">
            <span class="title1">{{ benefit.title1 }}</span>
            <span class="title2">{{ benefit.title2 }}</span>
            <span class="title3">{{ benefit.title3 }}</span>
          </div>
          <div class="body">
            <div class="list">
              <div v-for="item in benefit.vipBenefitGoodsInfo" :key="item.id" class="item">
                <div class="img-wrapper">
                  <img src="@/assets/images/logo.png" alt="" class="img" />
                  <img :src="item.imgUrl" alt="" class="img" />
                </div>
                <div class="label">
                  <div class="name">{{ item.name }}</div>
                  <div class="price-wrapper">
                    <span>仅需</span
                    ><span class="price"
                      ><span class="symbol">￥</span><span class="integer">{{ ~~item.price }}</span
                      ><span v-if="item.price.toString().includes('.')" class="decimal"
                        >.{{ item.price.toString().split('.')[1] }}</span
                      ></span
                    >
                  </div>
                  <button class="btn" @click="onGoods(benefit, item)">领取</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <coupon-benefit
          v-else-if="benefit.packageType === 'COUPON' || benefit.packageType === 'COUPON_PACKAGE'"
          :benifit="benefit"
          @handleReceive="handleReceive"
        /> -->
        <!-- 优惠券/优惠券包 -->
        <div
          v-else-if="benefit.packageType === 'COUPON' || benefit.packageType === 'COUPON_PACKAGE'"
          class="coupon"
          :class="{ 'coupon-package': benefit.packageType === 'COUPON_PACKAGE' }"
        >
          <div class="bg"></div>
          <div class="header">
            <span class="title1">{{ benefit.title1 }}</span>
            <span class="title2">{{ benefit.title2 }}</span>
            <span class="title3">{{ benefit.title3 }}</span>
          </div>
          <div class="body">
            <div class="list">
              <div
                v-for="item in benefit.packageType === 'COUPON_PACKAGE'
                  ? benefit.vipBenefitGoodsInfo
                  : [
                      {
                        id: 0,
                        ticketList: benefit.vipBenefitGoodsInfo,
                      },
                    ]"
                :key="item.id"
                class="item"
              >
                <div class="ticket-list-wrapper">
                  <div class="ticket-list">
                    <div v-for="ticket in item.ticketList" :key="ticket.id" class="ticket-item">
                      <div class="ticket-value">
                        <span>￥</span
                        ><span>{{
                          benefit.packageType === 'COUPON_PACKAGE'
                            ? ticket?.ticketDiscount?.value
                            : ticket?.ticketDefine?.ticketDiscount?.value
                        }}</span>
                      </div>
                      <div class="ticket-name">
                        <!-- <span v-if="ticket.ticketThreshold.type === 'MIN_AMOUNT'"
                          >平台满减券</span
                        > -->

                        <template
                          v-if="
                            (benefit.packageType === 'COUPON_PACKAGE'
                              ? ticket
                              : ticket.ticketDefine
                            ).ticketSpuRel.type === 'NONE'
                          "
                        >
                          <template
                            v-if="
                              (benefit.packageType === 'COUPON_PACKAGE'
                                ? ticket
                                : ticket.ticketDefine
                              ).ticketThreshold.type === 'NONE'
                            "
                          >
                            无门槛
                          </template>
                          <template v-else>
                            满{{
                              (benefit.packageType === 'COUPON_PACKAGE'
                                ? ticket
                                : ticket.ticketDefine
                              ).ticketThreshold.amount
                            }}元可用
                          </template>
                        </template>
                        <template
                          v-else-if="
                            (benefit.packageType === 'COUPON_PACKAGE'
                              ? ticket
                              : ticket.ticketDefine
                            ).ticketSpuRel.type === 'SPU'
                          "
                        >
                          指定商品可用
                        </template>

                        <template
                          v-else-if="
                            (benefit.packageType === 'COUPON_PACKAGE'
                              ? ticket
                              : ticket.ticketDefine
                            ).ticketSpuRel.type === 'CATEGORY'
                          "
                        >
                          指定分类可用
                        </template>
                        <template v-else>
                          {{ ticket.name }}
                        </template>
                      </div>
                      <button
                        class="ticket-btn"
                        @click="handleReceive(benefit, ticket)"
                        :disabled="
                          (benefit.packageType === 'COUPON_PACKAGE'
                            ? item.receiveNum
                            : ticket.receiveNum) > 0
                        "
                      >
                        <span
                          v-if="
                            (benefit.packageType === 'COUPON_PACKAGE'
                              ? item.receiveNum
                              : ticket.receiveNum) > 0
                          "
                          >已领取</span
                        >
                        <span v-else>立即领取</span>
                      </button>
                    </div>
                  </div>
                </div>
                <button
                  v-if="benefit.packageType === 'COUPON_PACKAGE'"
                  class="package-btn"
                  @click="handleReceive(benefit, item)"
                  :disabled="item.receiveNum > 0"
                >
                  一键领取
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他 -->
        <div v-else class="other" hidden :data-type="benefit.packageType">
          {{ benefit.name }}
        </div>
        <!-- <div class="benefit-item">
          <div class="clamp">
            <div class="content">{{benefit.name}}</div>
            <div class="diagonal-half"></div>
          </div>
          <div class="item-content">
            <div class="title2">{{benefit.title1}} {{benefit.title2}}</div>
            <div class="title3">{{benefit.title3}}</div>
            <div class="box-list" v-if="benefit.packageType === 'GOODS'">
              <Sku v-for="item in benefit.vipBenefitGoodsInfo"
                :key="item.skuId" class="item" :data="item"
                @click="onGoods(benefit, item)"
              />
            </div>
            <coupon-benefit
              v-else-if="benefit.packageType === 'COUPON' || benefit.packageType === 'COUPON_PACKAGE'"
              :benifit="benefit"
              @handleReceive="handleReceive"
            />
          </div>
        </div> -->
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { memberBenefitList, receiveSubmit } from '@/api/member'
import Sku from '../../member/components/Sku'
import { Toast, showLoadingToast, closeToast, showSuccessToast, showDialog } from 'vant'
import VoucherItem from './VoucherItem'
import CouponBenefit from '../../member/components/CouponBenefit'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const vipId = ref(0)
const benefitList = ref([])
const data = reactive({
  voucherData: {},
})
const { voucherData } = toRefs(data)
const checkedMember = inject('checkedMember')
const updateCouponGoods = inject('updateCouponGoods')
const handleGoods = inject('handleGoods')
watch(checkedMember, (val) => {
  vipId.value = val.vipPrice.id
  queryBenefitList()
})
const emit = defineEmits(['buyTips'])
const handleVoucher = () => {
  router.push('/my/voucher?tab=QUOTA')
}
const onGoods = (benefit, item) => {
  handleGoods(benefit, item)
}
// 领取
const handleReceive = (benefit, item) => {
  if (checkedMember.value.vipCust) {
    if (benefit.periodNum === benefit.receiveNum) {
      const textMsg =
        benefit.packageType === 'COUPON' || benefit.packageType === 'COUPON_PACKAGE'
          ? '领取次数已达上限'
          : '领取次数已达上限，请前往我的订单查看'
      showDialog({ message: textMsg })
      return
    }
    if (item.receiveNum === 0) {
      receiveSubmit({ id: item.id }).then((res) => {
        showSuccessToast('领取成功')
        queryBenefitList()
      })
    }
  } else {
    emit('buyTips')
  }
}
const queryBenefitList = () => {
  let newBenefitList = []
  memberBenefitList({ id: vipId.value }).then((res) => {
    // res.data.map((item) => {
    //   if (item.packageType === 'VOUCHER') {
    //     // 代金券、提额券
    //     voucherData.value = item
    //   } else if (item.packageType === 'GOODS') {
    //     let isCouponGoods = false
    //     item.vipBenefitGoodsInfo.map((item2) => {
    //       if (item2.categoryGroup === 'COUPON') {
    //         isCouponGoods = true
    //       }
    //     })
    //     if (isCouponGoods) {
    //       updateCouponGoods(item)
    //     } else {
    //       newBenefitList.push(item)
    //     }
    //   } else {
    //     newBenefitList.push(item)
    //   }
    // })
    benefitList.value = res.data
  })
}
defineExpose({ benefitList })
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang="scss">
.benefit-wrapper {
  margin-top: 12px;
  color: #bc6d38;
  font-size: 12px;
  // padding: 0 16px 10px;
  // background: linear-gradient(180deg, #fdf6ec 0%, #faf8f4 5%, #ffffff 100%);
  // border-radius: 19px 19px 0px 0px;
  // overflow: hidden;
}
.benefit-item {
  background: #fffaf5;
  border-radius: 12px;
  border: 1px solid #f1dcbf;
  position: relative;
  padding-top: 17px;
  margin-top: 17px;
  .clamp {
    position: absolute;
    left: -9px;
    top: 20px;
    .content {
      width: 58px;
      height: 28px;
      background: #ffc25f;
      border-radius: 0px 100px 100px 0px;
      font-size: 13px;
      color: #6c3f02;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .diagonal-half {
      width: 9px;
      height: 12px;
      background-color: #d89f3b;
      clip-path: polygon(100% 100%, 0 0, 100% 0);
    }
  }
  .item-content {
    .title2 {
      font-weight: 600;
      font-size: 16px;
      color: #231318;
      line-height: 22px;
      text-align: center;
    }
    .title3 {
      font-size: 12px;
      color: #4a280c;
      line-height: 17px;
      text-align: center;
      margin-top: 6px;
    }
    .box-list {
      display: flex;
      flex-wrap: wrap;
      margin: 38px 10px 0 10px;
      .item {
        margin: 0 7px 24px;
      }
    }
  }
}

.goods {
  background-color: #fff;
  padding: 10px;
  color: #bc6d38;
  margin: 10px;
  border-radius: 10px;
  .body {
    // height: 122px;
    display: flex;
    border-radius: 10px;
    overflow: hidden;
    background-color: #fdf7f4;
    .title {
      width: 51px;
      flex: none;
      background-color: #fee4db;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .icon {
        width: 25px;
        height: 25px;
        display: block;
        margin-bottom: 5px;
      }
      .title1 {
        font-size: 12px;
        color: #bc6d38;
        width: 24px;
        text-align: center;
        line-height: 1.2;
        font-weight: 600;
      }
    }
  }
  .content {
    flex: 1;
    overflow-x: scroll;
    .list {
      padding: 10px;
      display: flex;
      gap: 10px;
      width: fit-content;
      .item {
        border-radius: 10px;
        overflow: hidden;
        font-size: 12px;
        flex: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;
        .img {
          width: 80px;
          height: 80px;
          background: #fff;
        }
        .label {
          height: 22px;
          font-size: 12px;
          color: #bc6d38;
          width: 100%;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #feece6;
          .price {
            .symbol {
              font-size: 11px;
            }
            .integer {
              font-size: 16px;
            }
            .decimal {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}
.virtual-goods {
  margin: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  color: #bc6d38;
  .header {
    font-size: 15px;
    color: #4f06014d;
    .title1 {
      font-size: 17px;
      color: #4f0601;
      margin-right: 4px;
    }
    .title2 {
    }
    .title3 {
    }
  }
  .body {
    margin-top: 10px;
    .list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      .item {
        width: 100%;
        background-color: #fee4db;
        overflow: hidden;
        border-radius: 8px;
        .img-wrapper {
          margin-top: 22px;
          display: flex;
          gap: 12px;
          align-items: center;
          justify-content: center;
          .img {
            width: 53px;
            height: 53px;
            display: block;
            border-radius: 4px;
            background-color: #fff;
          }
        }
        .label {
          margin-top: 6px;
          // background-color: #fff3ef;
          overflow: hidden;
          // border-radius: 50% 50% 0px 0px / 14px 14px 0px 0px;
          background-image: url('@/assets/images/member/virtual-goods-bg.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;
        }
        .name {
          color: #4f0601;
          font-size: 13px;
          text-align: center;
          line-height: 1;
          margin-top: 20px;
        }
        .price-wrapper {
          text-align: center;
          font-size: 15px;
          color: #ab4d0c;
          margin-top: 5px;
        }
        .btn {
          border: none;
          background-color: #fff;
          border-radius: 999vw;
          font-size: 15px;
          color: #4f0601;
          width: 104px;
          height: 30px;
          display: block;
          margin: 6px auto 8px;
        }
      }
    }
  }
}
.coupon {
  margin: 10px;
  // padding: 10px;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  // .bg {
  //   margin: 4px;
  //   left: 0px;
  //   top: 0px;
  //   right: 0px;
  //   position: absolute;
  //   background: linear-gradient(180deg, rgba(254, 225, 214, 0.95) 0%, #fff 68.56%);
  //   // width: 100%;
  //   height: 82px;
  //   z-index: 0;
  //   border-radius: 10px;
  //   // background-color: red;
  // }
  .header {
    // position: relative;
    // z-index: 0;
    border-radius: 10px;
    margin: 4px;
    padding: 11px 11px 0px;
    background: linear-gradient(180deg, rgba(254, 225, 214, 0.95) 0%, #fff 68.56%);
    height: 82px;
    margin-bottom: -44px;
    font-size: 15px;
    color: #4f06014d;
    .title1 {
      font-size: 17px;
      color: #4f0601;
      margin-right: 4px;
    }
    .title2 {
    }
    .title3 {
    }
  }
  .body {
    margin: 0 4px;
    // padding: 10px;
    // position: relative;
    // z-index: 0;
    padding-bottom: 10px;
    .list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      // padding: 10px 0;
      .item {
        .ticket-list-wrapper {
          overflow-x: scroll;
        }
        .ticket-list {
          display: flex;
          gap: 10px;
          padding: 0 12px;
          width: fit-content;
          .ticket-item {
            width: 88px;
            height: 85px;
            background-image: url('@/assets/images/member/ticket-bg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            overflow: hidden;
            flex: none;
            .ticket-value {
              margin-top: 13px;
              font-size: 20px;
              color: #4f0601;
              font-weight: 600;
              text-align: center;
              .symbol {
                font-size: 11px;
              }
            }
            .ticket-name {
              margin-top: 3px;
              text-align: center;
              font-size: 10px;
              color: #4f060180;
              font-weight: 600;
            }
            .ticket-btn {
              margin: 20px auto 0;
              font-size: 14px;
              font-weight: 600;
              color: #fff;
              border: none;
              background: none;
              display: block;
            }
          }
        }
        .package-btn {
          border: none;
          border-radius: 999vw;
          // width: ;
          // margin: 10px 10px 0;
          display: block;
          background: linear-gradient(2deg, #ff8c64 1.66%, #ff2419 98.94%);
          width: 334px;
          height: 35px;
          margin: 10px auto 0;
          color: #fff;
          font-size: 17px;
          // font-weight: 600;
          &:disabled {
            background: none;
            background-color: #cfcfcf;
          }
        }
      }
    }
  }
}
</style>
