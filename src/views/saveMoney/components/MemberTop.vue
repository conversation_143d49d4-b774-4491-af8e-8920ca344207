<template>
  <div class="member">
    <div class="order-icon" @click="handleMemberOrder">
      权益订单
      <van-icon name="arrow"></van-icon>
    </div>
    <div class="valid" v-if="checkedMember.vipCust">有效期至：{{ parseTime(checkedMember.vipCust?.invalidTime, '{y}-{m}-{d}') }}</div>
    <div class="valid" v-else>暂未开通，开通即可享受会员权益</div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const checkedMember = inject('checkedMember')
const handleMemberOrder = () => {
  router.push('/member/equity-order')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .member.not{
    background: url('@/assets/images/member/save-money-no.png') no-repeat;
    background-size: 100% 100%;
  }
  .member{
    margin: 15px 16px -15px;
    background: url('@/assets/images/member/save-money-member.png') no-repeat;
    background-size: 100% 100%;
    height: 83px;
    box-sizing: border-box;
    .order-icon{
      margin-right: 12px;
      text-align: right;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 16px;
      padding-top: 18px;
      font-weight: bold;
    }
    .valid{
      font-weight: bold;
      font-size: 12px;
      color: #FFFFFF;
      line-height: 14px;
      margin: 8px 12px;
    }
  }
</style>