<template>
  <div class="increase-quota">
    <div class="label-img">特权1</div>
    <div class="title theme-text">省钱达人 月月提额</div>
    <div class="rules-explan" @click="show = true">规则说明</div>
    <div class="contnet">
      <div class="info">
        <van-circle
          class="van-circle"
          v-model:current-rate="currentRate"
          :rate="40"
          :speed="20"
          :size="$px2rem('246px')"
          :stroke-width="50"
          :color="gradientColor"
          layer-color="#FEE1D4"
          start-position="left"
        />
        <div class="text">最高可提升额度(元)</div>
        <div class="number">
          <count-to :start-val="1000" :end-val="5000" :duration="2000" separator=""></count-to>
          <img class="highest-img" src="@/assets/images/jsk/highest.png" />
        </div>
      </div>
      <div class="apply-btn theme-linear-gradient" @click="emit('handleConfirm')">
        {{ checkedMember.vipCust ? '查看额度' : '立即提额' }}
      </div>
      <div class="tips" v-if="!checkedMember.vipCust">
        激活省钱卡 <span>提额立即生效</span>，以实际获得额度为准
      </div>
      <div v-else class="tips">
        省钱卡 <span>激活成功</span>，有效期至：{{
          parseTime(checkedMember.vipCust.invalidTime, '{y}-{m}-{d}')
        }}
      </div>
      <div class="agreement" v-if="!checkedMember.vipCust">
        <van-checkbox
          class="flex0"
          v-model="checked"
          icon-size="16px"
          :checked-color="$global.THMEM"
        />
        <div class="agree">
          我已阅读
          <span class="highlight theme-text" @click="agreementShow('/member-service')"
            >《权益服务协议》</span
          >、 理解并接受相关条款并同意<span
            class="highlight theme-text"
            @click="agreementShow('/member-renew')"
            >《自动续费服务协议》</span
          >
          ，续费可随时取消
        </div>
      </div>
      <div class="coupon-goods-wrapper">
        <coupon-goods />
      </div>
    </div>
    <van-popup round v-model:show="show" teleport="body">
      <div class="popup-content" v-if="checkedMember.vipPrice">
        <div class="desc-title">
          <van-icon size="0.6rem" color="#cccccc" name="close" @click="show = false"></van-icon>
        </div>
        <div class="vip-desc" v-dompurify-html="checkedMember.vipPrice.vipDesc"></div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import CouponGoods from './CouponGoods'
import { CountTo } from 'vue3-count-to'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute()
const router = useRouter()
const checked = ref(true)
const show = ref(false)
const checkedMember = inject('checkedMember')
const emit = defineEmits(['handleConfirm', 'agreementShow'])
const currentRate = ref(0)
const gradientColor = {
  '0%': '#FF460E',
  '30%': '#FF8B56',
  '100%': '#FF8B56',
}
const agreementShow = (url) => {
  emit('agreementShow', url)
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
.increase-quota {
  margin: 18px 16px 0;
  background: #ffffff;
  border-radius: 10px;
  position: relative;
  padding-bottom: 23px;
  .title {
    text-align: center;
    font-size: 18px;
    padding-top: 10px;
  }
  .rules-explan {
    position: absolute;
    right: 10px;
    top: 40px;
    font-size: 12px;
    border-radius: 8px;
    width: 58px;
    height: 26px;
    background: #fee1d4;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.7);
  }
  .order-explan {
    position: absolute;
    right: 0;
    top: 45px;
    background: #666666;
    color: #ffffff;
    font-size: 12px;
    padding: 5px;
    border-radius: 8px 0 0 8px;
  }
  .label-img {
    position: absolute;
    left: -9px;
    top: 19px;
    width: 58px;
    height: 40px;
    font-size: 14px;
    background: url('@/assets/images/jsk/label.png') no-repeat;
    background-size: 100% 100%;
    padding-top: 5px;
    padding-left: 9px;
    color: #6c3f02;
    line-height: 20px;
    box-sizing: border-box;
  }
  .contnet {
    text-align: center;
    .info {
      width: 274px;
      height: 136px;
      background: url('@/assets/images/jsk/scale.png') no-repeat;
      background-size: 100% 100%;
      text-align: center;
      margin: 14px auto 0;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      padding-top: 56px;
      .van-circle {
        position: absolute;
        left: 14px;
        top: 14px;
      }
      .text {
        font-size: 14px;
        color: #333333;
        line-height: 16px;
        font-weight: bold;
      }
      .number {
        color: #4671eb;
        margin-top: 8px;
        position: relative;
        display: inline-block;
        span {
          font-weight: bold;
          font-family: DIN, DIN;
          font-size: 52px;
        }
        .highest-img {
          width: 31px;
          height: 15px;
          position: absolute;
          right: -28px;
          top: -3px;
        }
      }
    }
    .apply-btn {
      border-radius: 27px;
      margin-top: 24px;
      height: 44px;
      margin: 24px 42px 0;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 44px;
      text-align: center;
    }
    .tips {
      margin-top: 24px;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      span {
        color: #e33c43;
      }
    }
    .agreement {
      margin: 10px 20px 0 40px;
      display: flex;
      align-items: center;
      .flex0 {
        flex-shrink: 0;
      }
      .van-checkbox {
        margin-right: 5px;
      }
      .agree {
        font-weight: 400;
        font-size: 12px;
        zoom: 0.93;
        color: #999999;
        margin-left: 1px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow-x: auto; /* 左右滚动 */
        overflow-y: hidden; /* 隐藏垂直滚动条 */
        touch-action: pan-x; /* 仅允许横向滑动 */
        -webkit-overflow-scrolling: touch; /* 对于移动设备使用滚动回弹效果 */
      }
    }
  }
}
.popup-content {
  padding: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  height: 60vh;
  .desc-title {
    text-align: right;
    padding-bottom: 10px;
  }
  .vip-desc {
    flex: 1;
    overflow-y: auto;
  }
  :deep(p) {
    font-size: 13px;
    color: #000000;
    line-height: 18px;
  }
}
.coupon-goods-wrapper {
  margin: 0 12px;
}
</style>