<template>
  <div class="quota-content">
    <div class="clamp">
      <div class="content">{{data.name}}</div>
      <div class="diagonal-half"></div>
    </div>
    <div class="title">{{data.title1}}</div>
    <div class="title2">{{data.title2}}</div>
    <div class="quota-list">
      <div class="quota-item" v-for="item in data.vipBenefitGoodsInfo">
        <template v-if="item.receiveNum > 0">
          <img v-if="item.voucherUseType ==='CASH_QUOTA'" class="bg" src="@/assets/images/member/quota-bg-cash.png">
          <img v-else class="bg" src="@/assets/images/member/quota-bg-consume.png">
          <img v-if="item.voucherUseType ==='CASH_QUOTA'" class="receive-sign" src="@/assets/images/member/quota-receive-cash.png">
          <img v-else class="receive-sign" src="@/assets/images/member/quota-receive-consume.png">
          <div :class="`text vip ${item.voucherUseType}`">
            <div class="p1">{{ item.voucherUseType === 'CASH_QUOTA' ? '极享金' : '极刻花' }}</div>
            <div class="p2">轻松提额度</div>
          </div>
          <div :class="`btn list ${item.voucherUseType}`" @click="handleVoucher(item)">去提额</div>
        </template>
        <template v-else>
          <img class="bg" v-if="item.voucherUseType ==='CASH_QUOTA'" src="@/assets/images/member/quota-cash.png">
          <img class="bg" v-else src="@/assets/images/member/quota-consume.png">
          <div class="text">
            <div class="p1">{{ item.voucherUseType === 'CASH_QUOTA' ? '极享金' : '' }}</div>
            <div class="p2">轻松提额度</div>
          </div>
          <div :class="`btn ${item.voucherUseType}`" @click="handleReceive(item)">立即领取</div>
        </template>
      </div>
    </div>
    <coupon-goods />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, getCurrentInstance } from 'vue'
import { Toast, showLoadingToast, closeToast, showSuccessToast, showDialog } from 'vant';
import { receiveSubmit } from '@/api/member'
import CouponGoods from './CouponGoods'
const { proxy } = getCurrentInstance();
const store = useStore();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
})
const checkedMember = inject('checkedMember')
const emit = defineEmits(['updateBenfit'])
// 领取
const handleReceive = (item) => {
  if(checkedMember.value.vipCust) {
    if (props.data.periodNum === props.data.receiveNum) {
      showDialog({ message: '领取次数已达上限，请前往我的订单查看' })
      return
    }
    if (item.receiveNum === 0) {
      receiveSubmit({ id: item.id }).then(res => {
        showSuccessToast('领取成功')
        emit('updateBenfit')
      })
    }
  } else {
    emit('buyTips')
  }
}
const handleVoucher = () => {
  router.push('/my/voucher?tab=QUOTA')
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style scoped lang='scss'>
  .quota-content{
    padding: 22px 13px 20px;
    margin: 0 15px;
    background: #ffffff;
    border-radius: 12px;
    position: relative;
    border: 1px solid #F1DCBF;
    .clamp{
      position: absolute;
      left: -9px;
      top: 20px;
      .content{
        width: 58px;
        height: 28px;
        background: #FFC25F;
        border-radius: 0px 100px 100px 0px;
        font-size: 14px;
        color: #6C3F02;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .diagonal-half {
        width: 9px;
        height: 12px;
        background-color: #D89F3B;
        clip-path: polygon(100% 100%, 0 0, 100% 0);
      }
    }
    .title{
      font-weight: bold;
      font-size: 17px;
      color: #000000;
      line-height: 24px;
      text-align: center;
    }
    .title2{
      font-size: 13px;
      color: #4A280C;
      line-height: 15px;
      margin-top: 2px;
      text-align: center;
    }
    .quota-list{
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      .quota-item {
        width: 150px;
        height: 112px;
        position: relative;
        .bg {
          position: absolute;
          width: 150px;
          height: 112px;
          display: block;
        }
        .receive-sign{
          width: 71px;
          height: 71px;
          position: absolute;
          top: 10px;
          right: 6px;
        }
        .text{
          position: absolute;
          top: 19px;
          left: 18px;
          color: #ffffff;
          .p1{
            font-size: 18px;
            line-height: 25px;
            height: 25px;
            font-weight: bold;
          }
          .p2{
            font-size: 12px;
            line-height: 16px;
            zoom:0.9;
          }
        }
        .text.vip{
          color: #FF4419;
        }
        .text.CONSUME_QUOTA{
          color: #1E4EFC;
        }
        .btn{
          position: absolute;
          width: 130px;
          height: 30px;
          background: linear-gradient( 180deg, #FFFFFF 0%, #FFE8DB 100%);
          border-radius: 16px;
          border: 1px solid #FF9C9C;
          font-size: 13px;
          font-weight: bold;
          color: #FF5723;
          line-height: 30px;
          text-align: center;
          bottom: 14px;
          left: 10px;
        }
        .btn.list{
          left: 12px;
          bottom: 15px;
          width: 80px;
        }
        .btn.CONSUME_QUOTA{
          background: linear-gradient( 180deg, #FFFFFF 0%, #DBFCFF 100%);
          border-radius: 16px;
          border: 1px solid #1A3FFF;
          color: #1A3FFF;
        }
      }
    }
  }
</style>