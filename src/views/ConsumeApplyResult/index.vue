<template>
  <section class="consume-apply-result page">
    <header>
      <NavigationBar title="额度申请结果" />
    </header>
    <main>
      <img src="./assets/icon.png" alt="" class="result-icon" />
      <p class="title">额度申请提交成功</p>
      <p class="sub-title">
        当前排队人数较多，审核成功后将会短信通知<br />可多购物提升信用度哦（文案要再定）
      </p>

      <button v-if="adInfo" class="ad-btn" @click="handleAdClick()">
        <img :src="adInfo?.pic" alt="" />
      </button>
    </main>
  </section>
</template>

<script setup>
defineOptions({
  name: 'ConsumeApplyResult',
})

import NavigationBar from '@/components/NavigationBar/index2.vue'
// import MySwiper from '@/components/MySwiper/index.vue'
import { until, useAsyncState } from '@vueuse/core'
import { getAdList } from '@/api/base'
import { filter } from '@/utils/adIsShow.js'
import { onMounted } from 'vue'
import { add } from '@/utils/adEventRecord.js'
import { adRouter } from '@/utils/toRouter.js'

const {
  state: adInfo,
  isLoading: adIsLoading,
  isReady: adIsReady,
} = useAsyncState(
  async () => {
    const { data } = await getAdList({
      regionType: ['ConsumeApplyResult'],
    })
    return (await filter(data?.ConsumeApplyResult ?? []))?.[0]
  },
  null,
  {
    resetOnExecute: false,
  }
)
onMounted(async () => {
  await until(adIsReady).toBe(true)
  if (adInfo.value) {
    add(adInfo.value, 'EXPOSURE')
  }
})

function handleAdClick() {
  adRouter(adInfo.value)
}
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #333;
  font-size: 14px;
  line-height: 1.2;
  header {
    flex: none;
  }
  main {
    flex: 1;
  }
}
.result-icon {
  width: 74px;
  height: 74px;
  display: block;
  margin: 80px auto 0;
}
.title {
  font-size: 18px;
  text-align: center;
  margin-top: 12px;
}
.sub-title {
  text-align: center;
  color: #00000066;
  font-size: 13px;
  margin-top: 4px;
  line-height: 1.4;
}
.ad-btn {
  display: block;
  padding: 0;
  margin: 26px auto 0;
  border: none;
  background: none;
  width: 320px;
  height: auto;
  img {
    width: 100%;
    height: auto;
  }
}
</style>
