<template>
  <div class="norem page" :class="currentView">
    <header>header</header>
    <main>
      <!-- <div class="">屏幕尺寸：{{ width }} * {{ height }}</div>
      <div class="">文档尺寸：{{ { outerWidth, outerHeight, innerWidth, innerHeight } }}</div> -->

      <div class="">
        {{
          {
            inner,
            outer,
            visual,
          }
        }}
      </div>

      <div class="">
        安全区
        {{ screenSafeArea }}
      </div>
    </main>
    <footer>footer</footer>
  </div>
</template>

<script setup>
const data = ref({})

const inner = useWindowSize({
  // listenOrientation: false,
  type: 'inner',
})
const outer = useWindowSize({
  // listenOrientation: false,
  type: 'outer',
})
const visual = useWindowSize({
  // listenOrientation: false,
  type: 'visual',
})

// const { availWidth, availHeight, availTop, availLeft } = window.screen

// const { outerWidth, outerHeight, innerWidth, innerHeight } = window

const list = [
  {
    value: 'max',
    label: '全屏幕',
    content: '全屏幕尺寸',
  },
  {
    value: 'main',
    label: '内容区域',
    content: '全屏幕尺寸剑去浏览器的工具栏的尺寸',
  },
  {
    value: 'safe',
    label: '安全区',
    content: '屏幕安全区',
  },
]

const currentView = ref('max')

import { useScreenSafeArea, useWindowSize } from '@vueuse/core'
import { onMounted } from 'vue'
const screenSafeArea = useScreenSafeArea()
onMounted(() => {
  // console.log(screenSafeArea)
  screenSafeArea.update()
})

function handleClick() {}
</script>

<style lang="scss" scoped>
.norem.page {
  box-sizing: border-box;
  background: #ddd;
  font-size: 16px;
  flex: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100dvw;
  height: 100vh;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  // padding: env(safe-area-inset-top, 0px) env(safe-area-inset-right, 0px)
  // env(safe-area-inset-bottom, 0px) env(safe-area-inset-left, 0px);
  border: 0 solid red;
  border-width: env(safe-area-inset-top, 0px) env(safe-area-inset-right, 0px)
    env(safe-area-inset-bottom, 0px) env(safe-area-inset-left, 0px);
  box-sizing: border-box;
  header {
    height: 40px;
    background: #ccc;
  }
  main {
    background: #999;
    flex: 1;
    // height: 100dvh;
  }
  footer {
    height: 50px;
    background: #ccc;
    // position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    bottom: env(safe-area-inset-bottom, 0px);
  }
}
</style>

<style lang="scss"></style>
