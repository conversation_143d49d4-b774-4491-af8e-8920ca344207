<template>
  <div class="iframe-page">
    <navigation-bar pageName="在线咨询" @onLeftClick="onBackClick"></navigation-bar>
    <div class="iframe-page-context" :class="{ 'iphonex-bottom': isIphoneX, 'oppo': isOppo }">
      <iframe class="external-links" :src="url" scrolling="auto" frameborder="0" id="iframe"></iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const isIphoneX = window.isIphoneX
  const router = useRouter()
  const route = useRoute()
  const url = ref('')
  const isOppo = ref(false)
  const { metadata } = route.query
  const _userAgent = navigator.userAgent.toLowerCase()
  const onBackClick = () => {
    router.go(-1)
  }
  onMounted(() => {
    if(_userAgent.indexOf('oppo') > -1 || _userAgent.indexOf('pbem00') > -1 
    || _userAgent.indexOf('pefm00') > -1 || _userAgent.indexOf('pbct00') > -1
    || _userAgent.indexOf('pbet00') > -1 || _userAgent.indexOf('perm00') > -1) {
      isOppo.value = true
    }
    console.log(isOppo.value, _userAgent)
    url.value = '/chatlink.html?metadata='+metadata
  })
</script>

<style lang="scss" scoped>
  .iframe-page{
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgb(252, 252, 252);
    &-context{
      flex: 1;
      background: rgb(252, 252, 252);
      .external-links{
        padding-bottom: 50px;
      }
    }
    .iframe-page-context.oppo{
      padding-bottom: 68px !important;
    }
  }
</style>