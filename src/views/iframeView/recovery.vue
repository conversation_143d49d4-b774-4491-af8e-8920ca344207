<template>
  <div class="iframe-page">
    <navigation-bar pageName="回收" @onLeftClick="onBackClick"></navigation-bar>
    <div class="iframe-page-context">
      <iframe class="external-links"
        :src="src"
        :key="key"
        scrolling="auto" frameborder="0"
        id="iframe">
    </iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const onBackClick = () => {
    router.go(-1)
  }
  const key = ref(new Date().getTime())
  const route = useRoute();
  const { title, url } = route.query;
  const src = computed(() => {
    key.value = new Date().getTime()
    // setTimeout(() => {
    //   window.location.reload(true); // 如果有必要，也可以手动刷新页面
    // }, 200)
    return decodeURIComponent(url);
    
  });
  // const recoveryPageCallBack = (path, query) => {
  //   router.replace({ path, query })
  //   router.go(-2)
  // }
  // onMounted(() => {
  //   window.recoveryPageCallBack = recoveryPageCallBack
  // })
</script>

<style lang="scss" scoped>
  .iframe-page{
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    &-context{
      height: 100%;
    }
  }
</style>