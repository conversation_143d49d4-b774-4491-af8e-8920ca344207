<template>
  <div class="iframe-page">
    <navigation-bar :pageName="title" @onLeftClick="onBackClick"></navigation-bar>
    <div class="iframe-page-context" :class="{ 'iphonex-bottom': isIphoneX }">
      <iframe class="external-links"
        :src="url"
        scrolling="auto" frameborder="0"
        id="iframe">
    </iframe>
    </div>
  </div>
</template>

<script setup>
  import NavigationBar from '@/components/NavigationBar'
  const router = useRouter()
  const isIphoneX = window.isIphoneX
  const onBackClick = () => {
    router.go(-1)
  }
  const route = useRoute();
  const { title, url } = route.query;
  onMounted(() => {
    console.log('url' + url)
  })
</script>

<style lang="scss" scoped>
  .iframe-page{
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    &-context{
      height: 100%;
      background: #ffffff;
    }
  }
</style>