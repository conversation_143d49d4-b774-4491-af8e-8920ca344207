<template>
  <section class="page">
    <header>
      <navigation-bar :title="title" />
    </header>
    <main v-if="!isLoading">
      <div v-if="showSteps.some((v) => v.name === currentStep)" class="steps">
        <div
          v-for="(step, index) in showSteps"
          :key="step.name"
          class="step"
          :class="{
            finish: index < currentStepIndex,
            active: index === currentStepIndex,
            inactive: index > currentStepIndex,
          }"
        >
          <div class="index">{{ index + 1 }}</div>
          <div class="label">{{ step.label }}</div>
        </div>
      </div>

      <IdCard v-if="currentStep === 'idCard'" @submitted="currentStepIndex++" />
      <CustInfo v-if="currentStep === 'custInfo'" @submitted="currentStepIndex++" />
      <BindBank
        v-if="currentStep === 'bindCard'"
        v-bind="route.query"
        @submitted="currentStepIndex++"
      />
      <Face v-if="currentStep === 'face'" />
      <FaceReturn
        v-if="currentStep === 'faceReturn'"
        @success="end()"
        @fail="currentStep = 'face'"
      />
      <!-- <button :disabled="currentStepIndex === 0" @click="currentStepIndex--">Prev</button>
      <button :disabled="currentStepIndex === 3" @click="currentStepIndex++">Next</button> -->
    </main>
  </section>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useAsyncState } from '@vueuse/core'
import { useRouteQuery } from '@vueuse/router'

import NavigationBar from '@/components/NavigationBar/index2.vue'
import IdCard from './IdCard.vue'
import CustInfo from './CustInfo.vue'
import BindBank from './BindBank.vue'
import Face from './Face.vue'
import FaceReturn from './FaceReturn.vue'

const router = useRouter()
const route = useRoute()

const steps = [
  {
    name: 'idCard',
    label: '实名认证',
    title: '实名认证',
  },
  {
    name: 'custInfo',
    label: '完善信息',
    title: '个人信息',
  },
  {
    name: 'bindCard',
    label: '绑卡激活',
    title: '绑定银行卡',
  },
  {
    name: 'face',
    label: '刷脸认证',
    title: '刷脸认证',
  },
  {
    name: 'faceReturn',
    label: '人脸识别结果',
    title: '人脸识别结果',
  },
]

const showSteps = computed(() => steps.slice(0, 3))

const currentStep = useRouteQuery('step', 'idCard')
const currentStepIndex = computed({
  get() {
    return steps.findIndex((v) => v.name === currentStep.value)
  },
  set(index) {
    currentStep.value = steps[index].name
  },
})
const currentStepObj = computed(() => steps[currentStepIndex.value])
// const currentStep = computed(() => {
//   return steps[currentStepIndex.value]
// })

const title = computed(() => steps[currentStepIndex.value].title)
const store = useStore()
const user = computed(() => store.getters.userInfo)

const {
  execute: init,
  isReady,
  isLoading,
} = useAsyncState(
  async () => {
    await store.dispatch('GetInfo')

    // 扫脸回调
    if (route.query.step === 'faceReturn') {
      return
    }

    // 先绑卡
    if (user.value.flow === 'FACE') {
      currentStep.value = 'bindCard'
      return
    }

    // 已完成
    if (user.value.flow === 'FINISH') {
      // end()

      return
    }

    currentStep.value = {
      IDCARD: 'idCard',
      INFO: 'custInfo',
      // FACE: 'face',
    }[user.value.flow]

    // currentStepIndex.value = 0
    // return
    // if (!user.value.custIdCard?.) {
    //   currentStep.value = 'idCard'
    //   return
    // }
    // if (!user.value.custInfo?.) {
    //   currentStep.value = 'custInfo'
    //   return
    // }
    // if (!user.value.custFace?.) {
    //   currentStep.value = 'face'
    //   return
    // }
  },
  null,
  {
    // immediate: false,
  }
)

function end() {
  if (route.query.back) {
    router.replace(route.query.back)
    return
  }

  // 返回两层，因为人脸识别占用一层
  router.go(-2)
}

onMounted(async () => {
  // currentStep.value = 'faceReturn'
})
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f6f6f6;
  font-size: 14px;
  line-height: 1.2;
  header {
    flex: none;
    background-color: #fff;
  }
  .navigation-bar {
    background-color: #fff;
  }
  main {
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    background-color: #f6f6f6;
    position: relative;
    display: flex;
    flex-direction: column;
  }
}
.steps {
  background-color: #fff;
  padding: 20px 20px 10px;
  display: flex;
  align-items: center;
  // justify-content: space-between;
  justify-content: center;
  gap: 77px;
  position: sticky;
  top: 0;
  z-index: 10;
  .step {
    text-align: center;
    font-size: 15px;
    color: #000;
    font-weight: 400;
    line-height: 1.2;
    width: 60px;
    flex: none;
    position: relative;
    + .step {
      &::after {
        content: '';
        display: block;
        position: absolute;
        width: 88px;
        height: 3px;
        border-radius: 999vw;
        // background: rgba(167, 187, 253, 0.2);
        // background: #f4f4f4;
        top: 14px;
        // left: -60px;
        right: calc(100% - 8px);
      }
    }
    .index {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
      border-radius: 999vw;
      color: #fff;
      border: 3px solid #a7bbfd33;
      box-sizing: border-box;
      background-clip: content-box;
      font-size: 15px;
      font-weight: 600;
      position: relative;
    }
    &.finish {
      color: #000;
      &::after {
        background: rgba(167, 187, 253, 0.2);
      }
    }
    &.active {
      color: #000;
      &::after {
        background: rgba(167, 187, 253, 0.2);
      }
      .index {
        // display: none;
        color: transparent;
        &::after {
          content: '';
          display: block;
          width: 10px;
          height: 10px;
          background: #fff;
          border-radius: 999vw;
          position: absolute;
        }
      }
    }
    &.inactive {
      color: #999999;
      &::after {
        background: #f4f4f4;
      }
      .index {
        color: #cfcfcf;
        background: #f4f4f4;
        background-clip: border-box;
        border-color: transparent;
      }
    }
  }
}
.id-card,
.cust-info,
.bind-bank {
  margin-top: 10px;
}
</style>
