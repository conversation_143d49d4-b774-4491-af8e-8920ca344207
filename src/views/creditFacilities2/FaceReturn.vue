<template>
  <section class="face-return">
    <main class="main">
      <template v-if="!isLoading">
        <Motion
          as-child
          :initial="{
            scale: 0,
          }"
          :animate="{ scale: 1 }"
        >
          <van-icon
            :name="!!error ? 'fail' : 'success'"
            class="status-icon success"
            :class="!!error ? 'fail' : 'success'"
          />
        </Motion>
        <p class="title">
          <span v-if="!!error">{{ error.message }}</span>
          <span v-else>人脸识别成功</span>
        </p>
        <p class="sub-title">
          <span v-if="remaining">{{ remaining }}s</span>
          自动跳转
        </p>
      </template>
    </main>
  </section>
</template>

<script setup>
import { useAsyncState, useCountdown } from '@vueuse/core'
import { queryFaceRecognitionResult, saveCustFace } from '@/api/customer'
import { onMounted } from 'vue'
import { Motion } from 'motion-v'

const props = defineProps({
  // flowId: { type: String, required: true },
})

const emit = defineEmits(['success', 'fail'])
const route = useRoute()

const store = useStore()
const user = computed(() => store.getters.userInfo)

const flowId = localStorage.getItem('faceRecognitionFlowId')

const { state, isLoading, execute, error, isReady } = useAsyncState(
  async () => {
    const { data } = await queryFaceRecognitionResult(
      {
        flowId,
      },
      {
        showError: false,
      }
    )
    if (!data.status === 'SUCCESS') {
      throw new Error('人脸识别失败')
    }
    await saveCustFace({
      flowId,
      custIdCard: user.value.custIdCard,
    })
    await store.dispatch('GetInfo')
    return data
  },
  null,
  {
    onError() {
      start(5)
    },
    onSuccess() {
      start(5)
    },
  }
)
const { remaining, start, stop, pause, resume } = useCountdown(0, {
  onComplete() {
    if (error.value) {
      emit('fail', error)
      return
    }
    emit('success', state)
  },
})

onMounted(async () => {
  await store.dispatch('GetInfo')
})
</script>

<style lang="scss" scoped>
.status-icon {
  width: 62px;
  height: 62px;
  font-size: 54px;
  color: #fff;
  border-radius: 999vw;
  // display: block;
  margin: 80px auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
  &.success {
    background: #52b272;
  }
  &.fail {
    background: #ea5016;
  }
}
.title {
  text-align: center;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-top: 16px;
}
.sub-title {
  text-align: center;
  color: #cfcfcf;
  font-size: 14px;
  font-weight: 600;
  margin-top: 12px;
}
</style>
