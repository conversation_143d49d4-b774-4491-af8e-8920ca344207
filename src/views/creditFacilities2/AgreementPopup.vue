<template>
  <van-popup
    class="agreement-popup"
    :show="show"
    teleport="body"
    position="bottom"
    round
    @click-overlay="cancel('close')"
  >
    <main>
      <van-tabs
        v-model:active="active"
        :ellipsis="false"
        :class="{
          'hidden-link': list.length === 1,
        }"
      >
        <van-tab v-for="(item, index) in list" :key="index" :title="item.title">
          <iframe :src="item.src" frameborder="0" class="iframe"></iframe>
        </van-tab>
      </van-tabs>
    </main>
    <footer>
      <van-button block type="primary" class="confirm-btn" @click="confirm()">同意</van-button>
    </footer>
    <!-- <header></header>
    <main>
      <iframe :src="src" frameborder="0"></iframe>
    </main> -->
    <!-- <footer></footer> -->
  </van-popup>
</template>

<script setup>
import { useConfirmDialog, useToggle } from '@vueuse/core'

const { isRevealed, reveal, confirm, cancel, onReveal, onCancel, onConfirm } = useConfirmDialog()

// const title = defineModel('title', { default: '' })
// const src = defineModel('src', { default: '' })
const show = defineModel({ default: false })
const toggleShow = useToggle(show)
const list = ref([])
const active = ref()

onReveal((data) => {
  if (!data) {
    cancel()
    return
  }
  if (Array.isArray(data)) {
    list.value = data
    active.value = 0
  } else if (Array.isArray(data.list)) {
    list.value = data.list
    active.value = data.active
  } else {
    list.value = [data]
    active.value = 0
  }
  // title.value = data.title
  // src.value = data.src
  toggleShow(true)
})
onCancel(() => {
  toggleShow(false)
  // title.value = ''
  // src.value = ''
})
onConfirm(() => {
  toggleShow(false)
  // title.value = ''
  // src.value = ''
})

defineExpose({
  reveal,
  cancel,
})
</script>

<style lang="scss" scoped>
.agreement-popup {
  font-size: 14px;
  line-height: 1.2;
  height: 60vh;
  display: flex;
  flex-direction: column;
  main {
    flex: 1;
  }
  footer {
    flex: none;
    padding: 12px;
    .confirm-btn {
      width: 326px;
      height: 40px;
      margin: 0 auto;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      background: linear-gradient(180deg, #3f58d6 0%, #a7bbfd 100%);
      border: none;
    }
  }
}
.van-tabs {
  height: 100%;
  // flex: 1;
  display: flex;
  flex-direction: column;
  &.hidden-link {
    :deep() {
      .van-tabs__line {
        display: none;
      }
    }
  }
  :deep() {
    .van-tabs__line {
      height: 5px;
      width: 36px;
      background: #4671eb;
      bottom: 20px;
    }
    .van-tabs__wrap {
      flex: none;
    }
    .van-tabs__content {
      flex: 1;
      .van-tab__panel {
        height: 100%;
      }
    }
  }
  .iframe {
    // margin: 10px;
    // width: calc(100% - 20px);
    // height: calc(100% - 20px);
    padding: 10px;
    width: 100%;
    height: 100%;
    display: block;
    box-sizing: border-box;
  }
}
</style>
