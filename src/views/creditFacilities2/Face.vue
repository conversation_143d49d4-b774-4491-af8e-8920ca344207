<template>
  <van-form ref="formRef" class="face" :show-error-message="true" validate-first @submit="submit">
    <main class="main">
      <p class="title">请确保 <strong>本人</strong> 操作 保障账户安全</p>
      <p class="sub-title">请正对手机屏幕，保证人脸在取景框中</p>
      <img src="@/assets/images/credit/face-bg.png" class="main-img" />
      <p class="psxz">拍摄须知</p>
      <div class="face-tips">
        <div class="face-tip">
          <img src="@/assets/images/credit/face-tip-1.jpg" class="face-tip-img" />
          <div class="face-tip-title">
            <van-icon name="close" />
            <span>遮挡面部</span>
          </div>
        </div>
        <div class="face-tip">
          <img src="@/assets/images/credit/face-tip-2.jpg" class="face-tip-img" />
          <div class="face-tip-title">
            <van-icon name="close" />
            <span>遮挡面部</span>
          </div>
        </div>
        <div class="face-tip">
          <img src="@/assets/images/credit/face-tip-3.jpg" class="face-tip-img" />
          <div class="face-tip-title">
            <van-icon name="close" />
            <span>遮挡面部</span>
          </div>
        </div>
      </div>
    </main>
    <!-- <div class="" style="margin: auto"></div> -->
    <IFooter class="footer" v-model="checked">
      <template #agreement>
        已阅读并同意<strong
          @click.stop="
            iframePopupRef.reveal({
              title: '个人信息查询授权书',
              src: '/agreement/product-service.htm',
            })
          "
          >《个人信息查询授权书》</strong
        >
      </template>
    </IFooter>
    <IframePopup ref="iframePopup"></IframePopup>
  </van-form>
</template>

<script setup>
import { useAsyncState } from '@vueuse/core'
import { getRealnameFaceRecognitionUrl } from '@/api/customer'
import { showFailToast } from 'vant'
import { onMounted, useTemplateRef } from 'vue'
import { useStore } from 'vuex'
import IFooter from './IFooter.vue'
import IframePopup from '@/components/IframePopup.vue'

const iframePopupRef = useTemplateRef('iframePopup')

const store = useStore()
const user = computed(() => store.getters.userInfo)

const checked = ref(true)

const emit = defineEmits(['submitted'])
const { execute: submit } = useAsyncState(
  async () => {
    const returnUrl = new URL(window.location.href)

    returnUrl.searchParams.set('step', 'faceReturn')

    const { data } = await getRealnameFaceRecognitionUrl({
      custId: user.value.id,
      phone: user.value.phone,
      idCard: user.value.custIdCard.idcard,
      name: user.value.custIdCard.name,
      // returnUrl: window.location.origin + '/face-recognition-result',
      returnUrl: returnUrl.href,
    })
    if (!data.flowId) {
      showFailToast('获取人脸识别链接失败')
      return
    }
    localStorage.setItem('faceRecognitionFlowId', data.flowId)
    // window.location.href = data.originalUrl
    window.location.assign(data.originalUrl)
  },
  null,
  {
    immediate: false,
  }
)
onMounted(async () => {
  await store.dispatch('GetInfo')
})
</script>

<style lang="scss" scoped>
.face {
  flex: 1;
  // background: red;
  // min-height: max-content ;
  display: flex;
  flex-direction: column;
  .main {
    flex: 1;
    background-color: #fff;
    color: #333;
  }
  .footer {
    // margin-top: auto;
    margin-top: 10px;
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 10;
  }
}
.title {
  text-align: center;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin-top: 44px;
  strong {
    color: #f00;
  }
}
.sub-title {
  color: #999;
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  margin-top: 8px;
}
.main-img {
  display: block;
  margin: 40px auto 0;
  width: 148px;
  height: 144px;
}
.psxz {
  margin-top: 64px;
  margin-left: 16px;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}
.face-tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 34px 0;
  .face-tip {
    width: 87px;
    text-align: center;
    .face-tip-img {
      width: 87px;
      height: 87px;
      display: block;
    }
    .face-tip-title {
      margin-top: 8px;
      font-size: 13px;
      font-weight: 600;
      // display: flex;
      align-items: center;
      justify-content: center;
      .van-icon {
        vertical-align: middle;
        color: #ea5016;
        font-size: 18px;
        margin-right: 4px;
      }
    }
  }
}
</style>
