<template>
  <section class="page">
    <header></header>
    <main>
      <van-form ref="form" @submit="submit()">
        <van-cell-group inset>
          <van-field
            v-model="formModel.phone"
            name="phone"
            type="text"
            label="手机号"
            placeholder="手机号"
            :rules="[
              { required: true, message: '请填写手机号' },
              { pattern: /^1[3456789]\d{9}$/, message: '请填写正确的手机号码' },
            ]"
          />
          <van-field
            v-model="formModel.code"
            type="text"
            name="code"
            label="验证码"
            placeholder="验证码"
            :rules="[{ required: true, message: '请填写验证码' }]"
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                :disabled="getCodeLoading || remaining > 0"
                class="send-code-btn"
                @click="getCode()"
              >
                <span v-if="remaining > 0"> {{ remaining }}s </span>
                <span v-else>发送验证码</span>
              </van-button>
            </template>
          </van-field>
        </van-cell-group>
        <div style="margin: 16px">
          <van-button round block type="primary" native-type="submit"> 提交 </van-button>
        </div>
      </van-form>
    </main>
    <footer></footer>
  </section>
</template>

<script setup>
import { useAsyncState, useLocalStorage, useCountdown } from '@vueuse/core'
import { ref, onMounted, useTemplateRef } from 'vue'
import { smsCode } from '@/api/base'
import { devMode } from '@/store/devMode'
import { useStore } from 'vuex'
import { showSuccessToast } from 'vant'

const store = useStore()
const formRef = useTemplateRef('form')

const formModel = ref({
  phone: '',
  code: '',
})
const lastLoginPhone = useLocalStorage('lastLoginPhone', '')

onMounted(() => {
  if (devMode.value) {
    formModel.value.phone = lastLoginPhone.value
  }
})
const router = useRouter()

const { execute: submit } = useAsyncState(
  async () => {
    await store.dispatch('Login', {
      phone: formModel.value.phone,
      code: formModel.value.code,
      requestType: 'APP',
    })
    await store.dispatch('GetInfo')
    lastLoginPhone.value = formModel.value.phone
    router.replace({ path: '/' })
  },
  undefined,
  {
    immediate: false,
  }
)
const { remaining, start, stop, pause, resume } = useCountdown(0)

const { execute: getCode, isLoading: getCodeLoading } = useAsyncState(
  async (event) => {
    await formRef.value.validate('phone')
    start(5)
    const res = await smsCode({
      phone: formModel.value.phone,
    })
    showSuccessToast('发送成功')
    if (devMode.value && res.code) {
      formModel.value.code = res.code
      submit()
    }
  },
  undefined,
  {
    immediate: false,
  }
)
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  main {
    // padding: 10px;
    flex: 1;
    overflow-y: auto;
    .van-form {
      margin-top: 20vh;
    }
  }
}
.send-code-btn {
  // color: #fff;
  width: 80px;
}
</style>
