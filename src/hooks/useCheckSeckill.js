import { getSeckillStatus } from "@/api/seckill"

/**
 * 使用检查秒杀
 * @returns 
 */
const useCheckSeckill = () => {
  const checkSeckill = async () => {
    const res = await getSeckillStatus()
    if (res.status !== '1') {
      showDialog({
        title: '提示',
        message: '秒杀活动还未开始或已结束',
      }).then(() => {
        // on close
      });
      throw new Error('wrong_seckill_status')
    }
  }
  return checkSeckill
}

export default useCheckSeckill