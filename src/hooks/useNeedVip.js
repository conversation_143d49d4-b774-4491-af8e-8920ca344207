import { useGetVipInfo } from "./useGetVipInfo"

/**
 * 使用需要vip
 * @returns 
 */
const useNeedVip = () => {
    const openNeedVip = inject('openNeedVip')
    const getVipInfo = useGetVipInfo()

    const needVip = async () => {
        const vipInfo = await getVipInfo()
        if (!vipInfo.vipCust) {
            openNeedVip()
            throw new Error('needVip')
        }
    }
    return needVip
}

export default useNeedVip