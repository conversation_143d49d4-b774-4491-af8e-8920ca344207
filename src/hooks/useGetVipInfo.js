import { getMemberInfo } from "../api/member"

/**
 * 获得会员信息
 * @returns 
 */
export const useGetVipInfo = () => {
    const getVipInfo = async () => {
        const res = await getMemberInfo({ vipType: 'VIP' })
        if (!Array.isArray(res.data) || res.data.length <= 0) {
            return {
                vipCust: false
            }
        }
        const info = res.data[0] // 默认第一条会员
        return info
    }
    return getVipInfo
}