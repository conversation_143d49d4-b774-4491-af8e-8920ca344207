import { computed, onBeforeUnmount, onMounted, ref } from "vue"
import { useSeckillStore } from "../store/seckill"
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)

export const useSeckillTimer = () => {
  const seckillStore = useSeckillStore()
  const currentSession = computed(() => seckillStore.currentSession)
  const currentSessionType = computed(() => seckillStore.currentSessionType)
  const nextSessionStartTime = computed(() => seckillStore.nextSessionStartTime)
  const leftTimeDays = ref('')
  const leftTimeHours = ref('00')
  const leftTimeMinutes = ref('00')
  const leftTimeSeconds = ref('00')
  const timer = ref(null)

  const updateTime = async () => {
    const time = await seckillStore.getLeftTime()
    if (time) {
      const now = dayjs()
      const t = dayjs(time)
      const diff = t.diff(now)
      const dura = dayjs.duration(diff)
      leftTimeDays.value = dura.asDays() >= 1 ? `${parseInt(dura.asDays())}天` : ''
      leftTimeHours.value = dura.format('HH')
      leftTimeMinutes.value = dura.format('mm')
      leftTimeSeconds.value = dura.format('ss')
      // const nowStr = now.format('YYYY-MM-DD HH:mm:ss')
      // const timeStr = time.format('YYYY-MM-DD HH:mm:ss')
      // console.log('diff', timeStr, nowStr, diff, dura, days, hours, minutes, seconds)
    } else {
      leftTimeDays.value = ''
      leftTimeHours.value = '00'
      leftTimeMinutes.value = '00'
      leftTimeSeconds.value = '00'
    }
  }

  onMounted(async () => {
    timer.value = setInterval(() => {
      updateTime()
    }, 1000)
  })

  onBeforeUnmount(() => {
    clearInterval(timer.value)
  })

  return {
    currentSession,
    currentSessionType,
    nextSessionStartTime,
    leftTimeDays,
    leftTimeHours,
    leftTimeMinutes,
    leftTimeSeconds,
  }
}