import { UAParser } from 'ua-parser-js'
const { device, os, browser } = UAParser()

import global from '@/constant/Global'
// const packageName = 'com.yxjshop.app'
const onDownload = () => {

  // let userAgent = navigator.userAgent.toLowerCase(),
  //   url = ''
  if (device.is('Apple')) {
    setTimeout(function () {
      location.href = global.IOS_DOWN_LINK
    }, 500)
    // universal link
  } else if (browser.is('WeChat') || browser.is('QQBrowser') || browser.is('QQBrowserLite')) {
    document.getElementById('mask').style.display = 'block'
  } else {
    // if (userAgent.indexOf('vivo') > -1) {
    //   window.location.href = "vivomarket://details?id=" + packageName  // vivo手机  com.xxxx包名
    // } else if (userAgent.indexOf('oppo') > -1) {
    //   window.location.href = "oppomarket://details?packagename=" + packageName // oppo手机
    // } else if (userAgent.match(/(huawei|honor);?/i)) {
    //   window.location.href = "appmarket://details?id="+packageName // 华为手机
    // } else if (userAgent.indexOf('mi') > -1) {
    //   window.location.href = 'intent://details?id='+ packageName +'#Intent;package=com.xiaomi.market;scheme=market;end;'  // 小米手机  com.xxxx包名
    // } else {
    //   // 应用宝
    //   window.location.href = 'https://play.google.com/store/apps/details?id=' + packageName
    //   // 安卓其他设备
    //   // this.time = setTimeout(function() {
    //   //   location.href = global.ANDRIOD_DOWN_LINK
    //   //   clearTimeout(this.time)
    //   // }, 3000);
    // }
    setTimeout(function () {
      location.href = global.ANDRIOD_DOWN_LINK
    }, 500)
  }
}
export default onDownload
