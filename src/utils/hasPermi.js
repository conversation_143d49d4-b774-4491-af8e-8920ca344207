
import store from '@/store'

export function hasPermi(permi) {
  const all_permission = "*:*:*";
  const permissions = store.getters && store.getters.permissions

  if (permi && permi instanceof Array && permi.length > 0) {
    const permissionFlag = permi

    const hasPermissions = permissions.some(permission => {
      return all_permission === permission || permissionFlag.includes(permission)
    })
    return hasPermissions
  } else {
    return
  }
}
