import router from '@/router'
import { isString } from 'lodash-es'
import ua from '@/utils/ua'

export const eventTarget = new EventTarget()
;[
  'getAppChannel',
  'oneKeyLoginResponse',
  'appVersion',
  'getPhoneInfo',
  'getLocationInfo',
  'payClient',
].forEach((key) => {
  window[key] = (data) => {
    eventTarget.dispatchEvent(new CustomEvent(key, { detail: data }))
  }
})

function getEnv() {
  return new Promise((resolve) => {
    uni.getEnv(resolve)
  })
}

const isLoggingIn = ref(false)

export const env = ref({})
getEnv().then((res) => (env.value = res))

async function postMessage(options) {
  const { action, data, timeout = 0, eventName = action } = options

  const out = new Promise(
    (resolve, reject) => timeout > 0 && setTimeout(reject, timeout, new Error('超时'))
  )
  const res = new Promise((resolve) => {
    eventTarget.addEventListener(
      eventName,
      (event) => {
        console.log(event.detail)
        resolve(event.detail)
      },
      {
        once: true,
      }
    )
    uni.postMessage({
      data: {
        action,
        ...data,
      },
    })
  })
  return Promise.race([out, res])
}

const appJs = {
  getEnv,
  async getAppChannel() {
    const { h5 } = await getEnv()
    if (h5) {
      return 'PROD'
    }
    if (ua.os.is('ios')) {
      return 'PROD'
    }
    return postMessage({ action: 'getAppChannel', timeout: 5000 })
  },
  async appLogin(options) {
    const { h5 } = await getEnv()
    if (h5) {
      router.push({
        path: '/register',
        query: options,
      })
      return
    }

    if (isLoggingIn.value) {
      return
    }

    isLoggingIn.value = true

    setTimeout(() => {
      isLoggingIn.value = false
    }, 1000)

    return postMessage({ action: 'oneKeyLogin', eventName: 'oneKeyLoginResponse' })
  },
  appLogout: () => {
    return postMessage({ action: 'appLogout' })
  },
  async appOtherWebView(url, title, outer = false) {
    const { h5 } = await getEnv()
    if (h5) {
      location.href = isString(url) ? url : url.url
      return
    }

    return postMessage({
      action: 'otherWebView',
      data: {
        data: isString(url)
          ? {
              url,
              title,
              outer,
            }
          : url,
      },
    })
  },
  appCloseWebView: () => {
    return postMessage({ action: 'closeWebView' })
  },
  appCustomerService: (url) => {
    if (typeof window !== 'undefined' && window) {
      if (/iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812) {
        router.push({ path: '/iframe-view/webview', query: { title: '在线客服', url } })
        return
      }
    }
    return postMessage({
      action: 'otherWebView',
      data: { data: { url } },
    })
  },
  appGoBack: () => {
    return postMessage({ action: 'goBack' })
  },
  async appVersion() {
    const { h5 } = await getEnv()
    if (h5) {
      return 'H5'
    }
    return postMessage({ action: 'appVersion', timeout: 5000 })
  },
  appSystemPermission: () => {
    return postMessage({ action: 'systemPermission' })
  },
  appAction: (action) => {
    return postMessage({ action })
  },
  async appGetPhoneInfo() {
    const { h5 } = await getEnv()
    if (h5) {
      return null
    }

    const detail = await postMessage({ action: 'getPhoneInfo', timeout: 5000 })

    try {
      const data = JSON.parse(detail?.replace(/\r|\n/g, ''))

      return data
    } catch (error) {
      return null
    }
  },
  async appGetLocationInfo() {
    const { h5 } = await getEnv()
    if (h5) {
      return null
    }

    const detail = await postMessage({ action: 'getLocationInfo', timeout: 5000 })
    try {
      const data = JSON.parse(detail?.replace(/\r|\n/g, ''))

      return data
    } catch (error) {
      return null
    }
  },
  async appAlipayClient(orderId, cashierId) {
    const { h5 } = await getEnv()
    if (h5) {
      throw new Error('H5环境不支持支付宝支付')
    }
    const detail = await postMessage({
      action: 'alipayClient',
      // timeout: 10000,
      eventName: 'payClient',
      data: {
        data: {
          orderStr: orderId,
          cashierId,
        },
      },
    })
    try {
      const data = JSON.parse(detail?.replace(/\r|\n/g, ''))

      return data
    } catch (error) {
      return null
    }

    // uni.postMessage({
    //   data: {
    //     action: 'alipayClient',
    //     data: { orderStr: orderId, cashierId },
    //   },
    // })
  },
  async appWxPayClient(orderId, cashierId) {
    const { h5 } = await getEnv()
    if (h5) {
      throw new Error('H5环境不支持支付宝支付')
    }
    const detail = await postMessage({
      action: 'wxPayClient',
      // timeout: 10000,
      eventName: 'payClient',
      data: {
        data: {
          orderStr: orderId,
          cashierId,
        },
      },
    })
    try {
      const data = JSON.parse(detail?.replace(/\r|\n/g, ''))

      return data
    } catch (error) {
      return null
    }
    // uni.postMessage({
    //   data: {
    //     action: 'wxPayClient',
    //     data: { orderStr: orderId, cashierId },
    //   },
    // })
  },
  h5JumpPage: (path) => {
    uni.postMessage({
      data: {
        action: 'jumpPage',
        data: { path },
      },
    })
  },
}
window.$appJs = appJs
export default appJs
