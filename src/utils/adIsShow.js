import { computed, toValue } from 'vue'
import { useStore } from 'vuex'
import store from '@/store'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import minMax from 'dayjs/plugin/minMax'
import { until, useAsyncState, useDebounceFn, useThrottleFn, useTimeoutFn } from '@vueuse/core'

import $global from '@/constant/Global.js'
import { creditQuota } from '@/api/customer'
import { listOrder } from '@/api/cashloan'
// import db from '@/utils/db'
import adEventRecord from '@/utils/adEventRecord'

dayjs.extend(isBetween)
dayjs.extend(minMax)

// const {
//   state: quota,
//   isLoading: quotaIsLoading,
//   isReady: quotaIsReady,
//   execute: getQuota,
// } = useAsyncState(
//   async () =>
//     (
//       await creditQuota({
//         requestType: 'query',
//         sceneCode: $global.CREDIT_SCENE_CASH,
//       })
//     ).data,
//   undefined,
//   {
//     immediate: false,
//     throwError: true,
//   }
// )

// const {
//   state: order,
//   isLoading: orderIsLoading,
//   isReady: orderIsReady,
//   execute: getOrder,
// } = useAsyncState(
//   async () =>
//     (
//       await listOrder({
//         pageSize: 1,
//         pageNum: 1,
//         productId: quota.value.productId,
//       })
//     ).data,
//   undefined,
//   { immediate: false, throwError: true }
// )

const getQuotaData = useThrottleFn(async () => {
  const user = computed(() => store.getters.userInfo)
  if (!user.value?.id) {
    return null
  }

  return (
    await creditQuota({
      requestType: 'query',
      sceneCode: $global.CREDIT_SCENE_CONSUME,
    })
  ).data
}, 1000)

const getOrderData = useThrottleFn(async () => {
  const productId = (await getQuotaData())?.productId

  if (!productId) {
    return null
  }

  return (
    await listOrder({
      pageSize: 1,
      pageNum: 1,
      productId,
    })
  ).data
}, 1000)

// 判断广告此刻是否可见
export default async function adIsShow(ad) {
  if (!ad) return false

  const obj = (() => {
    try {
      return JSON.parse(ad.remark)
    } catch (error) {
      return {
        remark: ad.remark,
      }
    }
  })()
  // console.log(ad, obj)

  const { count, isLogin, isVIP, creditIntentionFlag, creditStatus, orderStatus, times } = obj

  if (count) {
    const currentCount = await adEventRecord
      .where(['adId', 'action'])
      .equals([ad.id, 'EXPOSURE'])
      .and((item) =>
        dayjs(item.createTime).isBetween(
          dayjs.max(dayjs().startOf('day'), dayjs(ad.startTime)).format('YYYY-MM-DD HH:mm:ss'),
          dayjs.min(dayjs().endOf('day'), dayjs(ad.endTime)).format('YYYY-MM-DD HH:mm:ss')
        )
      )
      .count()

    if (currentCount >= count) {
      return false
    }
  }
  // XXX 这里使用useStore无法获得store
  // const store = useStore()
  // console.log('store', store)

  const user = computed(() => store.getters.userInfo)
  // console.log('isLogin', isLogin)
  // console.log('user', toValue(user))

  if (isLogin) {
    if (isLogin !== (user.value?.id ? 'Y' : 'N')) {
      return false
    }
  }

  if (isVIP) {
    if (isVIP !== (user.value?.hasVip ? 'Y' : 'N')) {
      return false
    }
  }

  if (creditIntentionFlag) {
    if (creditIntentionFlag !== user.value?.creditIntentionFlag) {
      return false
    }
  }

  if (creditStatus?.length) {
    if (!user.value?.id) {
      return false
    }

    // if (!quotaIsLoading.value && !quotaIsReady.value) {
    //   await getQuota()
    // }

    // await until(quotaIsReady).toBe(true)

    const quota = await getQuotaData()

    if (!creditStatus.includes(quota?.creditStatus)) {
      return false
    }
  }

  if (orderStatus?.length) {
    if (!user.value?.id) {
      return false
    }

    // if (!orderIsLoading.value && !orderIsReady.value) {
    //   await getOrder()
    // }

    // await until(orderIsReady).toBe(true)
    const order = await getOrderData()

    if (!orderStatus.includes(order?.status)) {
      return false
    }
  }

  // if (times?.length) {
  //   const current = dayjs()

  //   const v = times.some((time) => {
  //     if (!time) return false
  //     return current.isBetween(
  //       dayjs(current.format('YYYY-MM-DD') + ' ' + time[0]),
  //       dayjs(current.format('YYYY-MM-DD') + ' ' + time[1])
  //     )
  //   })

  //   if (!v) return false
  // }

  // console.log(ad, obj, 'show')
  return true
}

export async function filter(list = []) {
  // console.log('list', list)
  const list2 = await Promise.all(list.map((item) => adIsShow(item)))
  // console.log('list2', list2)
  return list.filter((item, index) => list2[index])
}
