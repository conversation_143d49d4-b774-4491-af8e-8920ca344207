import global from "../constant/Global"
import * as math from 'mathjs'
import store from '@/store'
// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    b: date.getMilliseconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof (propName) === 'undefined') {
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  } else {
    search.params['begin' + propName] = dateRange[0];
    search.params['end' + propName] = dateRange[1];
  }
  return search;
}

// 回显数据字典 
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  Object.keys(datas).some((key) => {
    if (datas[key].value == ('' + value)) {
      actions.push(datas[key].label);
      return true;
    }
  })
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join('');
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  var currentSeparator = undefined === separator ? "," : separator;
  var temp = value.split(currentSeparator);
  Object.keys(value.split(currentSeparator)).some((val) => {
    var match = false;
    Object.keys(datas).some((key) => {
      if (datas[key].value == ('' + temp[val])) {
        actions.push(datas[key].label + currentSeparator);
        match = true;
      }
    })
    if (!match) {
      actions.push(temp[val] + currentSeparator);
    }
  })
  return actions.join('').substring(0, actions.join('').length - 1);
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments, flag = true, i = 1;
  str = str.replace(/%s/g, function () {
    var arg = args[i++];
    if (typeof arg === 'undefined') {
      flag = false;
      return '';
    }
    return arg;
  });
  return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
};

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
* 参数处理
* @param {*} params  参数
*/
export function tansParams(params) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && typeof (value[key]) !== 'undefined') {
            let params = propName + '[' + key + ']';
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result
}


// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == 'undefined') {
    return p
  };
  let res = p.replace('//', '/')
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1)
  }
  return res;
}
// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
	if (!str || str === 'undefined' || str === 'null') {
			return ''
	}
	return str
}
// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text();
    JSON.parse(text);
    return false;
  } catch (error) {
    return true;
  }
}
/**
 * @description 格式化金额
 * @param number：要格式化的数字
 * @param decimals：保留几位小数 默认2位
 * @param decPoint：小数点符号 默认.
 * @param thousandsSep：千分位符号 默认为,
 */
 export const formatMoney = (number, decimals = 2, decPoint = '.', thousandsSep = ',') => {
  number = (number + '').replace(/[^0-9+-Ee.]/g, '')
  const n = !isFinite(+number) ? 0 : +number
  const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)
  const sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep
  const dec = (typeof decPoint === 'undefined') ? '.' : decPoint
  let s = ''
  const toFixedFix = function(n, prec) {
    const k = Math.pow(10, prec)
    return '' + Math.ceil(parseFloat((n * k).toPrecision(12))) / k
  }
  s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.')
  const re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2')
  }
  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
}
// 手机号码前6后4
export const phoneFormat = (phone) => {
  if(phone) {
    return phone.substr(0,3) + "****" + phone.substr(7)
  }
  return ''
}
export const validPhone = (phone) => {
  const str = /^[1][0-9]{10}$/
  return str.test(phone)
}
// 是否菜单页
export const isMenuPath = (path) => {
  let url = false
  store.getters.menuList.map((item) => {
    if (item.url === path) {
      url = true
    }
  })
  return url
}
// 金额格式化
export const formatValue = (value, type) => {
  if (typeof value === 'string') {
    if (value.match('¥')) {
      var reg = getRegExp('¥', 'g')
      value = value.replace(reg, '')
    }
  }
  var result = value
  switch (type) {
    case 'price': // 普通价格
      // 商品价格：当商品金额小于10000时，保留2位小数显示；大于等于10000元时，则显示为1.0万的格式，即保留1位小数
      result = Number(value)
      if (result < 10000) {
        result = result.toFixed(2)
      } else {
        result = (result / 10000).toFixed(1) + '万'
      }
      break
    case 'sold': // 已售
      // 已售：当虚拟销小于10000件时，显示完全；当虚拟销量大于等于10000件，则显示为已售1.0万件的格式，即保留1位小数
      result = Number(value)
      if (result >= 10000) {
        result = (result / 10000).toFixed(1) + '万'
      }
      break
    case 'commission': // 佣金
      // 佣金：当佣金小于10时，保留2位小数显示；大于等于10元时，保留1位有效数字；大于等于100时，保留为整数；大于等于1000时，显示为：赚¥999+
      result = Number(value)
      if (result < 10) {
        result = result.toFixed(2)
      } else if (result >= 10 && result < 100) {
        result = result.toFixed(1)
      } else if (result >= 100 && result < 1000) {
        result = Math.floor(result)
      } else {
        return '999+'
      }
      break
    case 'coupon': // 优惠券
      result = Number(value / 100)
      break
    case 'split': // 分割
      //result = ('' + result).split('.')
      result = Number(result).toFixed(2).split('.') // 保留两位小数的方案
      // console.log('result=', result, JSON.stringify(result))
      break
  }
  return result
}
/**
 * 只显示姓氏
 * @param {data} 传入数据
 * 格式：张**
 */
export const showFirstName = (name) => {
  let newStr
  if (name.length === 2) {
    newStr = name.substr(0, 1) + '*'
  } else if (name.length > 2) {
    let char = ''
    for (let i = 0, len = name.length - 1; i < len; i++) {
      char += '*'
    }
    newStr = name.substr(0, 1) + char
  } else {
    newStr = name
  }
  return newStr
}
// 多个小数点相加
export const accAdd = (arg1, arg2) => {
  var r1, r2, m
  try {
    r1 = arg1.toString().split('.')[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = arg2.toString().split('.')[1].length
  } catch (e) {
    r2 = 0
  }
  m = Math.pow(10, Math.max(r1, r2))
  return (arg1 * m + arg2 * m) / m
}

// 乘法
export function multiply(a, b) {
	return math.number(math.multiply(math.bignumber(a), math.bignumber(b)))
}
// 除法
export function divide(a, b) {
	let num
	a === 0 || b === 0 ? num = 0 : num = math.number(math.divide(math.bignumber(a), math.bignumber(b)))
	return num
}
// str：字符串，frontLen：前面保留位数，endLen：后面保留位数
export const plusXing = (str,frontLen,endLen) => { 
  var len = str.length-frontLen-endLen;
  var xing = '';
  for (var i=0;i<len;i++) {
  xing+='*';
  }
  return str.substring(0,frontLen)+xing+str.substring(str.length-endLen);
}
// 字符串时间转时间戳
export const getDateFromString = (str) => {
  var reg = /^(\d+)-(\d+)-(\d+) (\d+):(\d+):(\d+)/
  var s  = str.match(reg)
  var result=""
  if(s){
      result = new Date(s[1],s[2] - 1,s[3],s[4],s[5],s[6]);                             
  }
  return result 
}
// 地址参数转对象
export const urlParamsObject = (url) => {
  let urlIndex = url.indexOf("?") // 查看? 位置为多少  22
  // 新对象
  let obj = new Object();
  if(urlIndex > -1) {
    // 截取字符串,urlIndex是从下标开始的,应此加一
    let str = url.substr(urlIndex+1,url.toString().length)
    let strList = str.split("&") // ['id=2', 'type=1', 'age=20']
    for (let i = 0; i < strList.length; i++) {
        obj[strList[i].split("=")[0]] = strList[i].split("=")[1]
    }
  } else {
    obj = {}
  }
  return obj
}
/****银行卡处理 */
export const stringBankAccount = (accountNo) => {
  if(accountNo) {
    return accountNo.slice(accountNo.length-4,accountNo.length)
  }
}
// 格式化富文本
export const formatHtml = content => {
  if (!content) {
      return
  }
  content = content.replace(/\<p/gi,
      '<p style="max-width:100% !important;word-wrap:break-word;word-break:break-word;" ')
  content = content.replace(/\<img/gi,
      '<img style="width:100% !important;height:auto !important;margin:0;display:flex;" ')
  content = content.replace(/style="/gi,
      'style="max-width:100% !important;table-layout:fixed;word-wrap:break-word;word-break:break-word;')
  content = content.replace(/\<table/gi,
      '<table style="table-layout:fixed;word-wrap:break-word;word-break:break-word;" ')
  // content = content.replace(/\<td/gi,'<td cellspacing="0" cellpadding="0" border="0" style="display:block;vertical-align:top;margin: 0px; padding: 0px; border: 0px;outline-width:0px;"');
  content = content.replace(/\<td/gi,
      '<td cellspacing="0" cellpadding="0" style="border-width:1px; border-style:solid; border-color:#666; margin: 0px; padding: 0px;"'
  )
  content = content.replace(/width=/gi, 'sss=')
  content = content.replace(/height=/gi, 'sss=')
  content = content.replace(/ \/\>/gi,
      ' style="max-width:100% !important;height:auto !important;margin:0;display:block;" \/\>')
  return content
}
// style px 转 rem
export const px2rem = (px) => {
  if (/%/.test(px)) {
    return px
  } else {
    return (parseFloat(px) / 37.5) + 'rem'
  }
}
// 随机数
export const getRandomNumber = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}