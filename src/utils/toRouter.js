import store from '@/store'
import router from '@/router'
import { showConfirmDialog } from 'vant'
import { pvuvInside } from '@/utils/pvuv'
import appJS from './appJS'
import { getOutApiUrl } from '@/api/base'
import { isMenuPath, urlParamsObject } from './common'
// 产品跳转
export const productRouter = async (product) => {
  const user = store.getters.userInfo
  if (user) {
    if (product.isCredit === 'Y') {
      if (user.flow === 'FINISH') {
        if (product.type === 'H5') {
          // pv+uv记录
          await pvuvInside(product.id, user.id)
          appJS.appOtherWebView(product.link)
        } else if (product.type === 'FORM') {
          router.push({ path: '/order-apply', params: { productId: product.id } })
        } else if (product.type === 'JOIN') {
          router.push({ path: '/product/detail', query: { productId: product.id } })
        } else if (product.type === 'JOIN_WHOLE') {
          router.push({ path: '/product/whole-detail', query: { productId: product.id } })
        } else if (product.type === 'CASH_LOAN_SXG') {
          router.push({ path: '/cash-loan' })
        } else if (product.type === 'CASH_LOAN_SELF') {
          router.push({ path: '/cash-loan2', query: { productId: product.id } })
        } else if (product.type === 'CREDIT_BILL') {
          // 消费分期产品推荐页先
          //
        }
      } else {
        creditFun(user)
      }
    } else {
      if (product.type === 'H5') {
        // pv+uv记录
        await pvuvInside(product.id, user.id)
        // location.href = product.link
        appJS.appOtherWebView(product.link)
      } else if (product.type === 'FORM') {
        if (user.flow === 'FINISH') {
          router.push({ path: '/order-apply', params: { productId: product.id } })
        } else {
          creditFun(user)
        }
      } else if (product.type === 'JOIN') {
        router.push({ path: '/product/detail', query: { productId: product.id } })
      } else if (product.type === 'JOIN_WHOLE') {
        router.push({ path: '/product/whole-detail', query: { productId: product.id } })
      }
    }
  } else {
    appJS.appLogin()
  }
}
const creditFun = (user) => {
  showConfirmDialog({
    title: '提示',
    message: '该产品需要您完成授信后才可以申请。',
    confirmButtonText: '前往授信',
  })
    .then(() => {
      router.push({ path: user.flow === 'INFO' ? '/cust-info-result' : '/credit-facilities' })
    })
    .catch(() => {
      // on cancel
    })
}
// import { until } from '@vueuse/core'
// import adPosition from '@/utils/adPosition.js'
import { add as addAdEventRecord } from '@/utils/adEventRecord.js'

async function postAdEvent(item) {
  // const user = store.getters.userInfo
  // 上报友盟
  // until(adPosition.isReady)
  //   .toBe(true)
  //   .then(() => {
  //     window._czc.push([
  //       '_trackEvent',
  //       `[广告][点击][${adPosition.state.value?.[item.regionType] ?? item.regionType}][${
  //         item.id
  //       }][${item.name}]`,
  //       '[广告点击]',
  //       JSON.stringify({
  //         userId: user?.id,
  //         partnerId: user?.partnerId,
  //         adId: item.id,
  //         adName: item.name,
  //       }),
  //       1,
  //     ])
  //   })

  // 本地记录
  addAdEventRecord(item, 'CLICK')
}

// 广告跳转
export const adRouter = async (item) => {
  // console.log(item)
  const user = store.getters.userInfo
  if (user) {
    postAdEvent(item)
    if (item.urlType === 'IN_URL') {
      if (item.queryType === 'PRODUCT') {
        router.push({ path: '/product/detail', query: { productId: item.queryTypeValue } })
      } else if (item.queryType === 'GOODS') {
        if (item.queryTypeValue) {
          goodsRouter({ spuId: item.queryTypeValue, categoryGroup: item.categoryGroup })
        } else {
          router.push('/goods-list')
        }
      } else if (item.queryType === 'CUSTOM') {
        const url = isMenuPath(item.url) // 菜单页
        if (url) {
          menuRouter(item.url)
        } else {
          const query = urlParamsObject(item.url)
          router.push({ path: item.url, query })
        }
      } else if (item.queryType === 'ACTIVITYPAGE') {
        router.push({ path: '/activity-page', query: { id: item.queryTypeValue } })
      }
    } else if (item.urlType === 'OUT_URL') {
      if (item.queryType === 'API') {
        // 外部 api
        getOutApiUrl({ sceneCode: item.queryTypeValue }).then((res) => {
          const title = item.queryTypeValue === 'FZS' ? '轻享花商城' : ''
          router.push(
            '/iframe-view/webview?title=' + title + '&url=' + encodeURIComponent(res.data)
          )
          //appJS.appOtherWebView(res.data)
        })
      } else {
        // 等待事件上报
        await new Promise((resolve) => setTimeout(resolve, 300))
        appJS.appOtherWebView(item.url)
      }
    }
  } else {
    appJS.appLogin()
  }
}
// 功能组跳转
export const funcRouter = (item) => {
  const user = store.getters.userInfo
  if (user) {
    // 是否需要登录
    if (item.funcType === 'IN') {
      if (item.linkType === 'PRODUCT') {
        router.push({ path: '/product/detail', query: { productId: item.linkTypeValue } })
      } else if (item.linkType === 'SPU') {
        if (item.funcValues[0].relationId) {
          goodsRouter({
            spuId: item.funcValues[0].relationId,
            categoryGroup: item.funcValues[0].categoryGroup,
          })
        } else {
          router.push('/goods-list')
        }
      } else if (item.linkType === 'CATEGORY') {
        // 跳转分类
        const funcValues = item.funcValues[0].relationId.split(',')
        router.push({
          path: '/goods-list',
          query: { categoryId: funcValues[funcValues.length - 1] },
        })
      } else if (item.linkType === 'CUSTOM') {
        const url = isMenuPath(item.linkTypeValue) // 菜单页
        if (url) {
          menuRouter(item.linkTypeValue)
        } else {
          const query = urlParamsObject(item.linkTypeValue)
          router.push({ path: item.linkTypeValue, query })
        }
      } else if (item.linkType === 'APPVIEW') {
        // app页面
        // JsBridge.callHandler(item.linkTypeValue,
        //   () => {})
      } else if (item.linkType === 'ACTIVITYPAGE') {
        router.push({ path: '/activity-page', query: { id: item.linkTypeValue } })
      } else if (item.linkType === 'VIRTUAL') {
        // 权益购买
        router.push({ path: '/member/equity-buy', query: { id: item.linkTypeValue } })
      } else if (item.linkType === 'TELE') {
        // 权益购买
        router.push({ path: '/telephone' })
      }
    } else if (item.funcType === 'OUT') {
      if (item.linkType === 'API') {
        getOutApiUrl({ sceneCode: item.linkTypeValue }).then((res) => {
          const title = item.linkTypeValue === 'FZS' ? '轻享花商城' : ''
          router.push(
            '/iframe-view/webview?title=' + title + '&url=' + encodeURIComponent(res.data)
          )
          // appJS.appOtherWebView(res.data)
        })
      } else {
        appJS.appOtherWebView(item.linkTypeValue)
      }
    }
  } else {
    appJS.appLogin()
  }
}
// 商品详情页
export const goodsRouter = (item) => {
  if (item.categoryGroup === 'TELE') {
    router.push({ path: '/goods-tele', query: { goodsId: item.spuId } })
  } else {
    router.push({ path: '/goods-detail', query: { goodsId: item.spuId } })
  }
}
// 菜单页
export const menuRouter = (path, replace) => {
  const url = isMenuPath(path) // 菜单页
  const menu = store.getters.menuList.find((item) => item.url === path)
  if (url && menu.type !== '4') {
    if (path === '/save-money') {
      // 特殊的省钱卡
      router.push(path)
      return
    }

    if (localStorage.getItem('currentRouter') !== '/') {
      // 当前路由不是main
      router.push({ name: 'Main', params: { componentName: path } })
    } else {
      // 切换菜单选显卡
      store.commit('MENU_PATH', path)
    }
  } else {
    if (replace) {
      // replace
      router.replace({ name: path })
    } else {
      router.push(path)
    }
  }
}
// 在线客服
export const customerService = () => {
  const user = store.getters.userInfo
  if (user) {
    const query = { tel: user.phone }
    if (user.custIdCard) {
      query.name = user.custIdCard.name
    }
    // query.partner_id = user.partnerId
    query.partner_name = user.partnerName
    router.push('/iframe-view/chatlink?metadata=' + encodeURIComponent(JSON.stringify(query)))
    //appJS.appOtherWebView(import.meta.env.VITE_APP_BASE_API+'/chatlink.html?metadata='+encodeURIComponent(JSON.stringify(query)))
  }
  //router.push('/iframe-view/chatlink?metadata='+encodeURIComponent(JSON.stringify(query)))
}
