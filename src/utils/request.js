// import axios from 'axios'
// import { getToken, setToken, removeToken, getAppVersion, getUserInfo } from '@/utils/auth'
// import { showLoadingToast, showDialog, closeToast } from 'vant'
// import appJS from '@/utils/appJS'
// import store from '@/store'
// import router from '@/router'
// import deviceId from '@/utils/deviceId'
// import ua from '@/utils/ua'
// // 是否显示重新登录
// export let isRelogin = { show: false }

// axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// // 创建axios实例
// const service = axios.create({
//   // axios中请求配置有baseURL选项，表示请求URL公共部分
//   baseURL: import.meta.env.VITE_APP_BASE_API,
//   // 超时
//   timeout: 30000,
//   showError: true,
// })
// service.interceptors.request.use((config) => {
//   config.headers['Device-Id'] = deviceId
//   config.headers['Browser'] = ua.browser
//   config.headers['CPU'] = ua.cpu
//   config.headers['Device'] = ua.device
//   config.headers['Engine'] = ua.engine
//   config.headers['OS'] = ua.os
//   return config
// })
// // request拦截器
// service.interceptors.request.use(
//   (config) => {
//     // console.log(config)
//     // config.data.apiCode = config.url || config.data.apiCode
//     // config.url = ''
//     config.params = config.params ?? {}
//     config.params.apiCode = config?.data?.apiCode
//     // 是否需要设置 token
//     const isToken = (config.headers || {}).isToken === false
//     // 是否需要防止数据重复提交
//     const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
//     if (getToken() && !isToken) {
//       config.headers['appToken'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
//     }
//     var u = navigator.userAgent
//     var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //android终端
//     config.headers['APPOS'] = isAndroid ? 'ANDROID' : 'IOS' // 当前请求系统
//     if (
//       isAndroid &&
//       getAppVersion() === 'VERIFY' &&
//       (!getUserInfo() || (getUserInfo() && getUserInfo().flow !== 'FINISH'))
//     ) {
//       // 安卓审核版
//       config.headers['APPVERSION'] = 'VERIFY'
//     }
//     if (config.loading) {
//       showLoadingToast({
//         duration: 0,
//         message: '加载中...',
//         forbidClick: true,
//       })
//     }
//     return config
//   }
//   // (error) => {
//   //   console.log(error)
//   //   Promise.reject(error)
//   // }
// )

// class RequestError extends Error {
//   constructor(message, config, code, request, response) {
//     super(message)
//     this.config = config
//     this.code = code
//     this.request = request
//     this.response = response
//     this.isRequestError = true
//   }
// }

// let isLoggingIn = false

// // 响应拦截器
// service.interceptors.response.use(
//   async (res) => {
//     if (res.config.loading) {
//       closeToast() // 清除加载中
//     }
//     const code = res.data.rspCode
//     const msg = res.data.rspMsg
//     // 获取错误信息
//     // 二进制数据则直接返回
//     if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
//       return res.data
//     }
//     // throw new RequestError('请求错误', res.config, '9998', res.request, res)
//     if (code === '0000') {
//       return res.data
//     }
//     if (code === '0005' || code === '0004') {
//       if (!isLoggingIn) {
//         isLoggingIn = true
//         res.config.showError = false
//         store.dispatch('FedLogOut').then(() => {
//           appJS.appLogin()
//         })
//       }

//       // return Promise.reject('error')
//       // throw new RequestError(msg, res.config, '9998', res.request, res)
//     }
//     // if (res.config.showError) {
//     //   showDialog({
//     //     message: msg ? msg : '请求失败',
//     //   }).then(() => {
//     //     // on close
//     //   })
//     // }
//     // return Promise.reject(msg)
//     throw new RequestError(msg, res.config, code, res.request, res)
//   }
//   // (error) => {
//   //   closeToast() // 清除加载中
//   //   console.log('error', error)
//   //   let { message } = error
//   //   if (message == 'Network Error') {
//   //     message = '后端接口连接异常'
//   //   } else if (message.includes('timeout')) {
//   //     message = '系统接口请求超时'
//   //   } else if (message.includes('Request failed with status code')) {
//   //     message = '系统接口' + message.substr(message.length - 3) + '异常'
//   //   }
//   //   showDialog({
//   //     message: message ? message : '登录失效，请重新登录',
//   //   }).then(() => {
//   //     // on close
//   //   })
//   //   return Promise.reject(error)
//   // }
// )
// service.interceptors.response.use(
//   (res) => {
//     return res
//   },
//   (error) => {
//     console.dir(error)
//     if (error.config.loading) {
//       closeToast() // 清除加载中
//     }

//     if (error.config.showError) {
//       showDialog({
//         message: error?.message ?? '请求失败',
//       })
//     }

//     throw error
//   }
// )
// export default service

import alovaInstance from '@/plugins/request'
// export default alovaInstance
const request = (config) =>
  alovaInstance.Request({
    ...config,
    meta: {
      showError: config.showError,
      loading: config.loading,
      ...config.meta,
    },
  })
request.Get = (...args) => alovaInstance.Get(...args)
request.Post = (...args) => alovaInstance.Post(...args)
export default request
