import db from './db'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { updateAdClick } from '@/api/base'
import store from '@/store'
import adPosition from '@/utils/adPosition.js'
import { until } from '@vueuse/core'

dayjs.extend(isBetween)

const tableName = 'adEventRecord'

db.version(1).stores({
  // 广告事件记录
  [tableName]: '++id, adId, action, createTime, [adId+action]',
})
db.version(2).stores({
  // 广告事件记录
  [tableName]: '++id, adId, name, action, createTime, [adId+action]',
})

const table = db.table(tableName)

console.log(table)

// 添加 hooks 自动生成 createTime
table.hook('creating', function (primKey, obj) {
  obj.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
})

export async function postAdPV(adInfo, action) {
  if (!adInfo) return
  // const user = store.getters.userInfo

  // console.log('postAdPV', adInfo, action)
  // updateAdClick({
  //   id: adInfo.id,
  // })
}

const postAdUV = postAdPV
// 添加广告记录
export async function add(adInfo, action) {
  // console.log('add', adInfo, action)

  if (!adInfo) return

  if (adInfo.controlEvent === action) {
    if (adInfo.controlType === 'UV') {
      const count = await table
        .where(['adId', 'action'])
        .equals([adInfo.id, action])
        .and((item) =>
          dayjs(item.createTime).isBetween(
            dayjs(adInfo.startTime).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(adInfo.endTime).format('YYYY-MM-DD HH:mm:ss')
          )
        )
        .count()

      if (count === 0) {
        // 没有记录，首次上报UV
        postAdUV(adInfo, action)
      }
    }

    if (adInfo.controlType === 'PV') {
      // 上报PV
      postAdPV(adInfo, action)
    }
  }

  until(adPosition.isReady)
    .toBe(true)
    .then(() => {
      const actionName =
        {
          CLICK: '点击',
          EXPOSURE: '曝光',
        }[action] ?? action
      const user = store.getters.userInfo
      window._czc.push([
        '_trackEvent',
        `[广告][${actionName}][${
          adPosition.state.value?.[adInfo.regionType] ?? adInfo.regionType
        }][${adInfo.id}][${adInfo.name}]`,
        `[广告${actionName}]`,
        JSON.stringify({
          userId: user?.id,
          partnerId: user?.partnerId,
          adId: adInfo.id,
          adName: adInfo.name,
        }),
        1,
      ])
    })

  await table.add({
    adId: adInfo.id,
    name: adInfo.name,
    action,
  })
}

// console.log()

export default table
