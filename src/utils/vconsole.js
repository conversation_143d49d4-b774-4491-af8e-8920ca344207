import VConsole from 'vconsole'

import { dev } from '@/plugins/dev-tools.js'
// import { devMode } from '@/store/devMode'
// import { whenever } from '@vueuse/core'
import { watch, onWatcherCleanup, createApp } from 'vue'
// import DevTools from '@/components/dev-tools.vue'

console.dir(VConsole)

// let vconsole = null

// 延迟初始化 VConsole
// const initVConsole = () => {
//   // if (import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'staging') {
//   vconsole = new VConsole()
//   // }
// }
// class DevToolsPlugin extends VConsole.VConsolePlugin {
//   constructor(...args) {
//     super(...args)
//   }

//   onReady() {
//     // console.log('onReady')
//   }

//   onAddTool() {
//     // console.log('onAddTool')
//   }

//   onRenderTab(callback) {
//     // console.log('onRenderTab')
//     // console.log(this)

//     const $dom = (this.$dom = document.createElement('div'))
//     $dom.classList.add('dev-tools')
//     const app = createApp(DevTools)
//     app.mount($dom)

//     // $dom.innerHTML = '<div><button class="btn" id="clear-indexdb">清空indexDB</button></div>'
//     // console.log($dom)
//     // $dom.querySelector('#clear-indexdb').addEventListener('click', () => {
//     //   console.log('清空indexDB')
//     // })

//     callback($dom)
//   }
//   onInit() {
//     // console.log('onInit')
//   }
// }

watch(
  () => dev.enable,
  (enable) => {
    if (enable) {
      const vconsole = new VConsole()
      window.$vconsole = vconsole
      // const devToolsPlugin = new DevToolsPlugin('devTools', '开发工具')
      // vconsole.addPlugin(devToolsPlugin)
      return
    }
    window.$vconsole?.destroy()
    window.$vconsole = null
  },
  {
    immediate: true,
  }
)

// 在下一个事件循环中初始化
// setTimeout(initVConsole, 0)

// export default vconsole
