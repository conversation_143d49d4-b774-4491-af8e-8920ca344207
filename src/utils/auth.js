import { useLocalStorage } from '@vueuse/core'

const TokenKey = 'qxh-Token'
const UserInfoKey = 'qxh-user-info-token'
const AppVersion = 'qxh-version'

export const appVersion = useLocalStorage(AppVersion, '')

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}
export function getUserInfo() {
  const userInfo = localStorage.getItem(UserInfoKey)
  if (userInfo) {
    return JSON.parse(userInfo)
  }
  return null
}

export function setUserInfo(user) {
  return localStorage.setItem(UserInfoKey, JSON.stringify(user))
}

export function removeUserInfo() {
  return localStorage.removeItem(UserInfoKey)
}

export function setAppVersion(version) {
  // return localStorage.setItem(AppVersion, version)
  appVersion.value = version
}

export function getAppVersion() {
  // return localStorage.getItem(AppVersion)
  return appVersion.value
}
