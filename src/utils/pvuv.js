import { getToday } from '@/utils/date'
const channelKey = 'channel'
const productKey = 'jinf-product'
import { submitPvUv } from '@/api/base'
import localforage from 'localforage'
export const pvuvOutside = async (channel) => {
  const data = {
    objectType: 'PARTNER',
    objectId: channel
  }
  const dateStr = await localforage.getItem(channelKey+channel)
  if(!dateStr || dateStr !== getToday()) {
    await localforage.setItem(channelKey+channel, getToday())
    await submitPvUv({visitType: 'UV', ...data})
  }
  await submitPvUv({visitType: 'PV', ...data})
}

export const pvuvInside = async (productId, userId) => {
  const key = productKey+productId
  const data = {
    objectType: 'PRODUCT',
    objectId: productId,
    userId
  }
  const dateStr = await localforage.getItem(key)
  if(!dateStr || dateStr !== getToday()) {
    await localforage.setItem(key, getToday())
    await submitPvUv({visitType: 'UV', ...data})
  }
  await submitPvUv({visitType: 'PV', ...data})
}
