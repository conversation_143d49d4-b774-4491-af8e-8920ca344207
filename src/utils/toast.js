import { showToast, showFailToast, closeToast, showSuccessToast } from 'vant'
export function onToast(callback, message, duration=2000) {
  showToast({message, duration: duration })
  setTimeout(()=>{
    closeToast()
    callback()
  }, duration)
}
export function onToastSucc(callback, message, duration=2000) {
  showSuccessToast({message, duration: duration, forbidClick: true })
  setTimeout(()=>{
    closeToast()
    callback()
  }, duration)
}
export function onToastFail(callback, message, duration=2000) {
  showFailToast({message, duration: duration })
  setTimeout(()=>{
    closeToast()
    callback()
  }, duration)
}