// 格式化日期yyyy-MM-dd
Date.prototype.format = function (format) {
  var o = {
      "M+": this.getMonth() + 1, //month
      "d+": this.getDate(),    //day
      "h+": this.getHours(),   //hour
      "m+": this.getMinutes(), //minute
      "s+": this.getSeconds(), //second
      "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
      "S": this.getMilliseconds() //millisecond
  }
  if (/(y+)/.test(format)) format = format.replace(RegExp.$1,
  (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o) if (new RegExp("(" + k + ")").test(format))
      format = format.replace(RegExp.$1,
      RegExp.$1.length == 1 ? o[k] :
      ("00" + o[k]).substr(("" + o[k]).length));
  return format;
}
export const formatDate = (date) => {
  let year = date.getFullYear()
  let month = date.getMonth() + 1
  let day = date.getDate()
  if(month < 10) {
    month = '0' + month
  }
  if(day < 10) {
    day = '0' + day
  }
  return (year + '-' + month + '-' + day)
}
// 补0
export const prefilling = (number) => {
  return number < 10 ? '0' + number : number
}
// 获取今日日期yyyy-MM-dd
export const getToday = () => {
  var day = new Date()
  return day.format('yyyy-MM-dd')
}
// 获取昨日日期yyyy-MM-dd
export const getYesterday  = () => {
  return new Date(new Date().setDate(new Date().getDate() - 1)).format('yyyy-MM-dd')
}
/****
 * 获取本周第一天和最后一天
 * return [start, end]
 */
 export const getCurWeekStartEnd = () => {
  const week = []
  var weekday = new Date().getDay() || 7
  week[0] = new Date(new Date().setDate(new Date().getDate() - weekday + 1)).format('yyyy-MM-dd')
  week[1] = new Date(new Date().setDate(new Date().getDate() - weekday + 7)).format('yyyy-MM-dd')
  return week
}
/****
 * 获取本月第一天和最后一天
 * return [start, end]
 */
export const getCurMonthStartEnd = () => {
  const month = []
  month[0] = new Date(new Date().setDate(1)).format('yyyy-MM-dd')
  month[1] = new Date(new Date(new Date().setMonth(new Date().getMonth() + 1)).setDate(0)).format('yyyy-MM-dd')
  return month
}
/****
 * 获取半年第一天和最后一天
 * return [start, end]
 */
export const getLastHalfYear = () => {
  var now = new Date()
  var year = now.getFullYear()
  //0-11表示1-12月
  var month = now.getMonth() + 1
  var day = now.getDate()
  var halfYear = []
  halfYear[1] = year + '-' + month + '-' + day
  //当前月的总天数
  var nowMonthDay = new Date(year, month, 0).getDate()
  //如果是1、2、3,4,5,6月，年数往前推一年    
  if(month - 6 <= 0){ 
      //6个月前所在月的总天数
      var last3MonthDay = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()
      //6个月前所在月的总天数小于现在的天日期    
      if(last3MonthDay < day){    
        halfYear[0] = (year - 1) + '-' + (12 - (6 - month)) + '-' + last3MonthDay
      }else{
        halfYear[0] = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
      }
  }else{
    //6个月前所在月的总天数
    var last3MonthDay = new Date(year, (parseInt(month) - 6), 0).getDate()
    //6个月前所在月的总天数小于现在的天日期
    if(last3MonthDay < day){
        //当前天日期小于当前月总天数,2月份比较特殊的月份
        if(day < nowMonthDay){       
          halfYear[0] = year + '-' + (month - 6) + '-' + (last3MonthDay - (nowMonthDay - day))
        }else{
          halfYear[0] = year + '-' + (month - 6) + '-' + last3MonthDay
        }
    }else{
      halfYear[0] = year + '-' + (month - 6) + '-' + day
    }
  }
  return halfYear
}
// js 根据时间，输出几分钟前，几小时前，几天前，几个月前，几年前。
export const getDateDiff = (dateTimeStamp) => {
  if(dateTimeStamp) {
    // 时间字符串转时间戳
    dateTimeStamp = dateTimeStamp.replace(/-/g, '/')
    var timestamp = new Date(dateTimeStamp).getTime();
    var minute = 1000 * 60;
    var hour = minute * 60;
    var day = hour * 24;
    var halfamonth = day * 15;
    var month = day * 30;
    var year = day * 365;
    var now = new Date().getTime();
    var diffValue = now - timestamp;
    var result;
    if (diffValue < 0) {
        return;
    }
    var yearC = diffValue / year;
    var monthC = diffValue / month;
    var weekC = diffValue / (7 * day);
    var dayC = diffValue / day;
    var hourC = diffValue / hour;
    var minC = diffValue / minute;
    if (yearC >= 1) {
        result = "" + parseInt(yearC) + "年前"
    } else if (monthC >= 1) {
        result = "" + parseInt(monthC) + "月前"
    } else if (weekC >= 1) {
        result = "" + parseInt(weekC) + "周前"
    } else if (dayC >= 1) {
      result = "" + parseInt(dayC) + "天前"
    } else if (hourC >= 1) {
      result = "" + parseInt(hourC) + "小时前"
    } else if (minC >= 1) {
      result = "" + parseInt(minC) + "分钟前"
    } else {
      result = '刚刚'
    }
    return result
  }
}
/** *
 * 日期比较返回天数
 */
 export const compareDate = (starDate, endDate) => {
  let date1
  let date2
  if (typeof starDate === 'object') {
      date1 = starDate.getTime()
  } else {
      date1 = new Date(starDate.replace(/-/g, '/')).getTime()
  }
  if (typeof endDate === 'object') {
      date2 = endDate.getTime()
  } else {
      date2 = new Date(endDate.replace(/-/g, '/')).getTime()
  }
  return parseInt((date2 - date1) / (1000 * 60 * 60 * 24))
}
/** *
 * 日期比较返回毫秒
 */
export const compareMillisecond = (starDate, endDate) => {
  let date1
  let date2
  if (typeof starDate === 'object') {
      date1 = starDate.getTime()
  } else {
      date1 = new Date(starDate.replace(/-/g, '/')).getTime()
  }
  if (typeof endDate === 'object') {
      date2 = endDate.getTime()
  } else {
      date2 = new Date(endDate.replace(/-/g, '/')).getTime()
  }
  return parseInt(date2 - date1)
}


// 秒钟数据转换成天
export function secondsToDays(seconds) {
  if(!seconds) return 0
  const secondsPerDay = 60 * 60 * 24;
  return (seconds / secondsPerDay).toFixed(0);
}

// 秒钟数据转换成年-月-日
export function secondsToDate(seconds) {
  var date = new Date(seconds); // 将秒转换为毫秒
  var year = date.getFullYear();
  var month = date.getMonth() + 1; // 月份是从0开始的，所以需要+1
  var day = date.getDate();
 
  // 格式化月份和日期，确保它们至少有两位数字
  month = ('0' + month).slice(-2);
  day = ('0' + day).slice(-2);
 
  return `${year}-${month}-${day}`; // 使用模板字符串返回格式化的日期
}

// 秒钟数据转换成年.月.日 时：分：秒
export function formatSeconds(seconds) {
  var date = new Date(seconds); // 将秒数转换为毫秒
  var year = date.getFullYear();
  var month = date.getMonth() + 1; // 月份是从0开始的，所以需要+1
  var day = date.getDate();
  var hours = date.getHours();
  var minutes = date.getMinutes();
  var seconds = date.getSeconds();
 
  // 格式化月份和日期，如果需要的话
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  hours = hours < 10 ? '0' + hours : hours;
  minutes = minutes < 10 ? '0' + minutes : minutes;
  seconds = seconds < 10 ? '0' + seconds : seconds;
 
  return `${year}.${month}.${day} ${hours}:${minutes}`;
}