import axios from 'axios'
import router from './router'
import store from './store'
import { getToken, getUserInfo } from '@/utils/auth'
import { showDialog } from 'vant'
import appJS from '@/utils/appJS'
const whiteList = [
  '/download/product',
  '/download/register',
  '/download2/product',
  '/download2/register',
] // 下载页面不在该项目逻辑内
router.beforeEach(async (to, from, next) => {
  await versionCheck()
  if (!to.fullPath.includes('.htm')) localStorage.setItem('currentRouter', to.fullPath)
  appJS.h5JumpPage(to.path) // 通知 app当前跳转页面
  // 授信返回处理
  if (to.path === '/cust-info-result' || to.path === '/credit-facilities') {
    if (from.path === '/cashier') {
      // 返回收银台
      sessionStorage.setItem('backUrl', from.href)
    } else if (from.path === '/cash-loan') {
      // 返回备用金
      sessionStorage.setItem('backUrl', from.path)
    } else if (from.path === '/cash-loan-quota') {
      // 返回备用金
      sessionStorage.setItem('backUrl', '/cash-loan')
    } else {
      if (sessionStorage.getItem('backUrl')) {
        sessionStorage.removeItem('backUrl')
      }
    }
  }
  if (getToken() && whiteList.indexOf(to.path) === -1) {
    if (!store.getters.userInfo) {
      if (getUserInfo()) {
        // 本地缓存获取
        store.commit('SET_USER', getUserInfo())
      }
    }
    next()
  } else {
    next()
  }
})
router.onError((error) => {
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    if (import.meta.env.DEV) return
    window.location.reload()
  }
})
const localVersionKey = 'localVersion'

const versionCheck = async () => {
  console.log('versionCheck')
  if (import.meta.env.DEV) return
  const response = await fetch('/', {
    cache: 'no-cache',
    method: 'HEAD',
  })
  const etag = response.headers.get('etag')
  const localVersion = localStorage.getItem(localVersionKey)
  if (!localVersion) {
    localStorage.setItem(localVersionKey, etag)
    return
  }
  if (localVersion === etag) {
    return
  }
  localStorage.setItem(localVersionKey, etag)
  showDialog({ message: '发现新内容，自动更新中...' }).then(() => {
    window.location.reload()
  })
}

// 初始化时清空，避免访问后立刻弹出更新提示。
localStorage.setItem(localVersionKey, '')
// versionCheck()
