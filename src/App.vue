<script setup>
import { getMenuList } from '@/api/base'
import { setToken, getUserInfo, setAppVersion } from '@/utils/auth'
import NeedVipDialog from '@/components/NeedVipDialog'
import { ref, provide } from 'vue'
import { eventTarget } from '@/utils/appJS'
// import DevTools from '@/components/dev-tools.vue'

// 需要vip的对话框
const needVipDialogRef = ref()
provide('openNeedVip', () => {
  needVipDialogRef.value.open()
})

const router = useRouter()
const transitionName = ref('')
const keepAliveNames = ref([])
const store = useStore()
// ios 的滑动
const isIosMoveBack = computed(() => {
  return store.getters.isIosMoveBack
})
const getAppChannel = (data) => {
  setAppVersion(data)
  // 获取到版本后再查询菜单
  getMenuList().then((res) => {
    store.commit('SET_MENU_LIST', res.data)
  })
  // if (getUserInfo() && getUserInfo().flow === 'FINISH') { // 当前登录用户实名使用后台控制的版本
  //   setAppVersion('')
  // } else {
  //   setAppVersion(data)
  // }
}
onMounted(() => {
  // const ua = navigator.userAgent
  // const isiOS = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios终端
  // if (isiOS) {
  //   document.body.addEventListener(
  //     'touchmove',
  //     () => {
  //       store.commit('SET_ISIOSMOVEBACK', true)
  //     },
  //     false
  //   )
  //   // 获取菜单
  getMenuList().then((res) => {
    store.commit('SET_MENU_LIST', res.data)
  })
  // } else {
  //   // 获取当前版本 安卓
  //   uni.getEnv(({ h5, nvue }) => {
  //     if (h5) {
  //       getMenuList().then((res) => {
  //         store.commit('SET_MENU_LIST', res.data)
  //       })
  //       return
  //     }
  //     window.getAppChannel = getAppChannel
  //     uni.postMessage({
  //       data: {
  //         action: 'getAppChannel',
  //       },
  //     })
  //   })
  // }
  // 注册一键登录获取token方法
  // window.oneKeyLoginResponse = oneKeyLoginResponse
})

eventTarget.addEventListener('oneKeyLoginResponse', (e) => {
  const token = e.detail
  setToken(token)
  store.commit('SET_TOKEN', token)
  store.dispatch('GetInfo').then((res) => {
    // 更新菜单
    getMenuList().then((res) => {
      store.commit('SET_MENU_LIST', res.data)
    })
  })
})
router.beforeEach((to, form) => {
  if (
    (to.path === '/' && (form.path === '/' || form.path === '/register')) ||
    to.path === '/register' ||
    form.path === '/iframe-view' ||
    to.path === '/download/product' ||
    to.path === '/download/register' ||
    to.path === '/download3/product' ||
    to.path === '/download3/register' ||
    to.path === '/download4/product' ||
    form.path.indexOf('/my/privacy') > -1 ||
    form.path.indexOf('/h5/') > -1
  ) {
    // 不需要动画
    transitionName.value = ''
  } else {
    if (to.meta.index > form.meta.index) {
      transitionName.value = 'fold-left'
    } else {
      transitionName.value = 'fold-right'
    }
  }
  // 缓存
  if (to.meta.keepAlive && keepAliveNames.value.indexOf(to.name) === -1) {
    keepAliveNames.value.push(to.name)
  }
  // 返回缓存前置页删除缓存
  if (form.meta.keepAlive && form.meta.keepRemove && form.meta.keepRemove === to.name) {
    keepAliveNames.value = keepAliveNames.value.filter((item) => item != form.name)
  }
})
router.afterEach((to, from, next) => {
  window.scrollTo(0, 0)
})
if (import.meta.env.MODE !== 'development') {
  window.addEventListener('visibilitychange', (e) => {
    if (e.target.visibilityState === 'visible') {
      //进入
      router.replace(localStorage.getItem('currentRouter'))
    }
  })
  // // 扑捉异常防住更新假死
  window.addEventListener('error', (error) => {
    if (error.message.includes("'text/html' is not a valid JavaScript MIME")) {
      if (import.meta.env.DEV) return
      window.location.reload()
    }
  })
}
</script>

<template>
  <div id="app">
    <router-view v-slot="{ Component }">
      <transition :name="transitionName">
        <keep-alive :include="keepAliveNames">
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>
    <need-vip-dialog ref="needVipDialogRef" />
  </div>

  <!-- <DeviceInfoCollect></DeviceInfoCollect> -->
  <!-- <DevTools /> -->
</template>

<style lang="scss">
#app {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  // push页面时：新页面的进入动画
  .fold-left-enter-active {
    animation-name: fold-left-in;
    animation-duration: 0.4s;
  }
  // push页面时：老页面的退出动画
  .fold-left-leave-active {
    animation-name: fold-left-out;
    animation-duration: 0.4s;
  }
  // push页面时：新页面的进入动画
  @keyframes fold-left-in {
    0% {
      transform: translate(100%, 0);
      /* visibility: visible; */
    }
    100% {
      transform: translate(0, 0);
    }
  }
  // push页面时：老页面的退出动画
  @keyframes fold-left-out {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(-100%, 0);
      /* visibility: hidden; */
    }
  }

  // 后退页面时：即将展示的页面动画
  .fold-right-enter-active {
    animation-name: fold-right-in;
    animation-duration: 0.4s;
  }
  // 后退页面时：后退的页面执行的动画
  .fold-right-leave-active {
    animation-name: fold-right-out;
    animation-duration: 0.4s;
  }
  // 后退页面时：即将展示的页面动画
  @keyframes fold-right-in {
    0% {
      width: 100%;
      transform: translate(-100%, 0);
      /* visibility: visible; */
    }
    100% {
      width: 100%;
      transform: translate(0, 0);
    }
  }
  // 后退页面时：后退的页面执行的动画
  @keyframes fold-right-out {
    0% {
      width: 100%;
      transform: translate(0, 0);
    }
    100% {
      width: 100%;
      transform: translate(100%, 0);
      /* visibility: hidden; */
    }
  }
}
</style>
