import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getSeckillInfo } from '@/api/seckill'
import dayjs from 'dayjs'

// 使用秒杀状态
export const useSeckillStore = defineStore('seckill', () => {
  const datetimeFormat = 'YYYY-MM-DD HH:mm:ss'

  // 场次列表
  const sessions = ref([])
  // 当前场次，进行中的场次或下一场
  const currentSession = ref(null)
  // 当前场次类型：进行中 ongoing，即将开始 willstart，已结束 ended
  const currentSessionType = ref('')
  // 进行中时是下一场的开始时间，等待下一场开始时，是下下一场
  const nextSessionStartTime = ref(null)
  // 上一次获取场次列表的时间
  const lastFetchSessionsTime = ref(0)
  // 获取场次列表的时间间隔
  const minFetchSessionsDelta = 3600 // 一小时
  // 是否检查中，避免进入页面的主动重新加载和倒计时加载冲突
  const checking = ref(false)
  // 当前时间
  const now = ref(dayjs())

  // 重新加载
  const reload = async () => {
    // console.log('秒杀reload')
    lastFetchSessionsTime.value = 0
    await checkStatus()
  }

  // 检查场次是否变更
  const checkStatus = async () => {
    if (checking.value) {
      return
    }
    checking.value = true
    await fetchSessions()
    now.value = dayjs()
    const result = getCurrentAndNextFromSessions(sessions.value, now.value)
    // console.log('当前时间：', now.value.format('YYYY-MM-DD HH:mm:ss'), '当前状态：', result.status, result.current ?
    //   dayjs(result.current.startTime).format('YYYY-MM-DD HH:mm:ss') : '当前场次为空',
    //   result.next ? dayjs(result.next.startTime).format('YYYY-MM-DD HH:mm:ss') : '下一场次为空')
    currentSession.value = result.current
    currentSessionType.value = result.status
    nextSessionStartTime.value = result.next ? result.next.startTime : undefined
    checking.value = false
  }

  // 获得场次列表
  const fetchSessions = async () => {
    const timestamp = new Date().getTime() / 1000
    if (timestamp - lastFetchSessionsTime.value >= minFetchSessionsDelta) {
      // 获取场次信息并排序
      const seckillInfoRes = await getSeckillInfo()
      const arr = Array.isArray(seckillInfoRes.data) ? seckillInfoRes.data : []
      const sortedSession = arr.sort((a, b) => {
        return a.startTime < b.startTime ? -1 : (a.start > b.startTime) ? 1 : 0
      })
      // console.log('排序后的场次', sortedSession.map(t => {
      //   return {
      //     ...t,
      //     seckillDate: dayjs(t.seckillDate).format('YYYY-MM-DD HH:mm:ss'),
      //     startTime: dayjs(t.startTime).format('YYYY-MM-DD HH:mm:ss'),
      //     endTime: dayjs(t.endTime).format('YYYY-MM-DD HH:mm:ss'),
      //   }
      // }))
      sessions.value = sortedSession
      // 记录获得时间
      lastFetchSessionsTime.value = timestamp
    }
  }

  // const sessionToString = (session) => {
  //   if (!session) {
  //     return '无'
  //   }
  //   const startTime = dayjs(session.startTime).format(datetimeFormat)
  //   const endTime = dayjs(session.endTime).format(datetimeFormat)
  //   return `场次ID：${session.id}，${startTime} -> ${endTime}`
  // }

  // 获取倒计时，每次获取都会重新检查场次变化
  const getLeftTime = async () => {
    await checkStatus()

    if (currentSessionType.value === 'ongoing') {
      return currentSession.value.endTime
    } else if (currentSessionType.value === 'willstart') {
      return currentSession.value.startTime
    } else {
      return null
    }
  }

  // 从场次列表中获得当前进行中的场次和下一场，或即将开始的场次和下一场
  // session按时间排序好的场次列表
  // now 当前时间 
  const getCurrentAndNextFromSessions = (pSessions, pNow) => {
    const nowDayjs = dayjs(pNow)

    let current = null
    let next = null
    let status = '' // 无效 ''，进行中 ongoing，即将开始 willstart

    // 是否有进行中的场次
    for (let i=0; i < pSessions.length; i++) {
      const session = pSessions[i]
      const startTime = dayjs(session.startTime).format(datetimeFormat)
      const endTime = dayjs(session.endTime).format(datetimeFormat)
      // console.log(`场次 ${session.id} ${startTime} -> ${endTime}`)
      if (nowDayjs.isAfter(startTime) && nowDayjs.isBefore(endTime)) {
        // 秒杀进行中
        status = 'ongoing'
        current = session
        // 找到进行中的场次，寻找下下一场
        const nextSessionIndex = i + 1
        if (nextSessionIndex < pSessions.length) {
          next = pSessions[nextSessionIndex]
        }
        break
      }
    }

    // 没有进行中的场次，是否有下一场
    if (!current) {
      for (let i=0; i < pSessions.length; i++) {
        const session = pSessions[i]
        const startTime = dayjs(session.startTime).format(datetimeFormat)
        // console.log(`场次 ${session.id} ${startTime} -> ${endTime}`)
        if (nowDayjs.isBefore(startTime)) {
          // 下一场秒杀
          current = session
          status = 'willstart'
          // 找到下一场，再找下下一场
          const nextSessionIndex = i + 1
          if (nextSessionIndex < pSessions.length) {
            next = pSessions[nextSessionIndex]
          }
          break
        }
      }
    }

    return {
      current,
      next,
      status,
    }
  }

  return { currentSession, currentSessionType, nextSessionStartTime, now, reload, getLeftTime }
})