import { login, getInfo } from '@/api/login'
import { getToken, setToken, removeToken, setUserInfo, removeUserInfo } from '@/utils/auth'
import { getIntegral } from '@/api/customer'
import { eventTarget } from '@/utils/appJS'
const user = {
  state: {
    token: getToken(),
    info: null,
    integralValue: 0,
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_USER: (state, info) => {
      window._czc.push(['_setUUid', info?.id])
      window._czc.push(['_setCustomVar', 'userId', info?.id, 2])
      window._czc.push(['_setCustomVar', 'partnerId', info?.partnerId, 2])
      state.info = info
    },
    SET_INTEGRAL_VALUE: (state, integralValue) => {
      state.integralValue = integralValue
    },
  },

  actions: {
    // 登录
    Login({ commit }, data) {
      return new Promise((resolve, reject) => {
        login(data)
          .then((res) => {
            setToken(res.data.appToken)
            commit('SET_TOKEN', res.data.appToken)
            resolve()
            eventTarget.dispatchEvent(
              new CustomEvent('loginSuccess', {
                detail: {
                  token: res.data.appToken,
                },
              })
            )
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            commit('SET_USER', res.data)
            setUserInfo(res.data)
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    IntegralData({ commit }) {
      return new Promise((resolve, reject) => {
        getIntegral()
          .then((res) => {
            if (res.data && res.data.avaliableIntegral) {
              commit('SET_INTEGRAL_VALUE', res.data.avaliableIntegral)
            }
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '')
        removeUserInfo()
        removeToken()
        commit('SET_USER', null)
        resolve()
      })
    },
  },
}

export default user
