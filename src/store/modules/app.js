const app = {
  state: {
    isIosMoveBack: false,
    theme: 'flammulated',
    memberPopup: false,
    menuList: [],
    menuPath: '/'
  },
  mutations: {
    SET_ISIOSMOVEBACK: (state, isBack) => {
      state.isIosMoveBack = isBack
    },
    THEME_CHANGE(state, theme) {
      state.theme = theme
    },
    MEMBER_POPUP: (state, show) => {
      state.memberPopup = show
    },
    SET_MENU_LIST: (state, menuList) => {
      state.menuList = menuList
    },
    MENU_PATH: (state, path) => {
      state.menuPath = path
    }
  },
  actions: {
    themeChange({
      commit
    }, theme) {
      commit('THEME_CHANGE', theme)
    }
  }
}
export default app