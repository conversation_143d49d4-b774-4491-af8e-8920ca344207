import request from '@/utils/request'

// 获取短信验证码
export function smsCode(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA002', ...data },
    meta: {
      authRole: null,
    },
  })
}
// 广告
export function getAdList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA006', ...data },
    meta: {
      authRole: null,
    },
  })
}
// 广告点击
export function updateAdClick(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA007', ...data },
    meta: {
      authRole: null,
    },
  })
}
// 文件上传
export function uploadBase64(data) {
  return request({
    url: '/file/base64',
    method: 'post',
    data: { apiCode: 'BA004', ...data },
    loading: true,
  })
}

// 实名次数校验
export function countValid() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA009' },
  })
}

// ocr识别
export function ocrSubmit(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC004', ...data },
    loading: true,
  })
}
export function submitPvUv(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA012', ...data },
  })
}
// 搜索
export function keywordsSearch(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS007', ...data },
  })
}
// 获取配置参数
export function getConfing(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA019', ...data },
  })
}
// 获取菜单
export function getMenuList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA015', ...data },
  })
}
// 获取字典数据
export function getDictData(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA021', ...data },
  })
}
export function getOutApiUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI153', ...data },
  })
}
// 获取热搜词
export async function getHotSearch() {
  const dictType = 'HOT_SEARCH'
  const { data } = await getDictData({ dictType })
  const items = data
  items.forEach((item) => {
    // console.log(item)
    try {
      item.remarkObj = JSON.parse(item.remark)
    } catch (error) {
      item.remarkObj = {
        icon: null,
      }
    }
    item.label = item.dictLabel
    item.value = item.dictValue
    item.icon = item.remarkObj.icon
  })
  return items
}

// getHotSearch()
