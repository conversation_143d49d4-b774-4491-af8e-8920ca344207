import request from '@/utils/request'

export function treasureHuntList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH002', ...data}
  })
}
export function prizeList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH004', ...data}
  })
}
export function prizeDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH003', ...data},
    loading: true
  })
}
export function joinSubmit(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH001', ...data},
    loading: true
  })
}
export function joinList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH005', ...data}
  })
}
export function comcupteInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH006', ...data}
  })
}
export function comcupteList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TH007', ...data},
    loading: true
  })
}
