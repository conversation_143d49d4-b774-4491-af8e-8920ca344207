import request from '@/utils/request'
// 获取支付产品信息
export function payProduct(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS235', ...data},
    loading: true
  })
}

// 支付提交
export function submitPay(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS223', ...data},
    loading: true
  })
}
// 分期预算明细
export function billDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL006', ...data},
    loading: true
  })
}
// 分期短信
export function smsCode(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA013', ...data},
    loading: true
  })
}

//获取分期协议
export function getAgreenment(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL002', ...data}
  })
}
export function chnlSmsSubmit(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS245', ...data},
    loading: true
  })
}
