import request from '@/utils/request'
// 获取默认地址
export function getAddressDefault() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS036'}
  })
}
export function listAddress(data, loading=true) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS031', ...data },
    loading
  })
}
// 更新默认
export function updateDefault(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS035', ...data },
    loading:true
  })
}
// 新增
export function addAddress(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS032', ...data },
    loading:true
  })
}
// 编辑
export function updateAddress(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS033', ...data },
    loading:true
  })
}
// 删除地址
export function deleteAddress(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS034', ...data },
    loading:true
  })
}
