import request from '@/utils/request'
export function savePreOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF201', ...data},
    loading: true
  })
}
// 下单
export function saveOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF202', ...data}
  })
}
export function productOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF203', ...data},
    loading: true
  })
}

export function joinOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF204', ...data},
    loading: true
  })
}
// 获取列表
export function listOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF205', ...data}
  })
}
// 获取详情
export function getOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF206', ...data}
  })
}
