import request from '@/utils/request'

// 获取秒杀信息
export function getSeckillInfo() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SECKILL001' }
  })
}

// 获取秒杀场次商品列表
export function getSeckillGoodsList({ pageNum, pageSize, seckillSessionId, spuTitle }) {
  return request({
    url: '/gateway',
    method: 'post',
    data: {
      apiCode: 'SECKILL002',
      seckillSessionId,
      pageNum,
      pageSize,
      spuTitle: spuTitle || undefined,
    }
  })
}

// 获取秒杀商品详情 data: { spuId }，spuId实际为场次中的商品id
export function getSeckillGoodsDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: {
      apiCode: 'SECKILL003',
      ...data
    }
  })
}

// 校验秒杀时间和状态，seckillSessionDateRelationId 即 session.id
export function getSeckillStatus(seckillSessionDateRelationId) {
  return request({
    url: '/gateway',
    method: 'post',
    data: {
      apiCode: 'SECKILL004',
      seckillSessionDateRelationId,
    }
  })
}

// 查询全部今日抄底的秒杀商品列表
export function getSeckillRecommandGoodsList(seckillSessionId) {
  return request({
    url: '/gateway',
    method: 'post',
    data: {
      apiCode: 'SECKILL006',
      seckillSessionId,
      recommand: '1',
      pageNum: 1,
      pageSize: 100,
    }
  })
}
