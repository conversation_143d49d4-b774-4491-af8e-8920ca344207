// 用于同步加载
import request from '@/utils/request'
// 获取银行卡列表
export function bankCardList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS041', ...data}
  })
}
// 获取支付产品信息
export function payProduct(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS235', ...data}
  })
}
// 会员购买提交
export function submitMemberOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI203', ...data}
  })
}
// 支付提交
export function submitPay(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS223', ...data}
  })
}

// 先享后付是否需要绑卡
export function isNeedBindCard(data, loading=false) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL022', ...data},
    loading
  })
}
