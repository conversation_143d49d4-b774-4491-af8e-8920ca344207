import request from '@/utils/request'
// 商品预下单
export function goodsPreOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS221', ...data},
    loading: true,
    onCloseWhenError: data.onCloseWhenError,
  })
}
export function saveGoodsOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS222', ...data},
    loading: true
  })
}
// 商品订单列表
export function listGoodsOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS101', ...data}
  })
}
// 订单详情
export function getGoodsOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS102', ...data},
    loading: true
  })
}

// 删除订单
export function delOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS230', ...data},
    loading: true
  })
}
// 取消订单
export function cancelOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS225', ...data},
    loading: true
  })
}
// 确认收货
export function submitReceipt(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS228', ...data},
    loading: true
  })
}
// 申请售后
export function applyReturn(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS238', ...data},
    loading: true
  })
}
// 订单售后列表
export function listAfterSales(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS103', ...data}
  })
}
// 申请售后详情
export function applyReturnDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS104', ...data},
    loading: true
  })
}
// 撤销售后
export function cancelReturn(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS240', ...data},
    loading: true
  })
}
// 物流轨迹
export function orderLogistics(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS106', ...data},
    loading: true
  })
}
