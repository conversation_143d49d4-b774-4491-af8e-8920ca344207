import request from '@/utils/request'
// 保存身份证信息
export function saveCustIdCard(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF004', ...data },
    loading: true,
  })
}
// 提交人脸识别信息
export function saveCustFace(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF005', ...data },
    loading: true,
  })
}
// 提交基础信息
export function saveCustInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF006', ...data },
    loading: true,
  })
}

// 账户注销
export function accountCancel(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA010', ...data },
    loading: true,
  })
}
// 平台授信额度
export function creditQuota(data, loading = true) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL101', ...data },
    loading: true,
  })
}
// 待还账单
export function getBillList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL308', ...data },
    loading: true,
  })
}
// 卡券
export function cardPwdList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS008', ...data },
  })
}
// 获取卡密短信
export function smsCardPwd(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA016', ...data },
  })
}
// 获取卡密明文信息
export function pwdPlaintext(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS009', ...data },
  })
}
// 获取积分
export function getIntegral(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF007', ...data },
  })
}
// 获取签到数据
export function getIntegralSign(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF009', ...data },
  })
}
// 获取积分
export function listIntegralDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF008', ...data },
  })
}
// 获取签到数据
export function submitSign(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF010', ...data },
    loading: true,
  })
}
// 提交通讯录讯息
export function saveContacts(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF011', ...data },
  })
}
// 获取产品额度
export function getProductQuota(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF012', ...data },
  })
}
// 获取回收地址
export function getRecoveryUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'EC001', ...data },
    loading: true,
  })
}
// 获取回收地址
export function getsxRecoveryUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS015', ...data },
    loading: true,
  })
}

// 获取实名认证的人脸识别URL
export function getRealnameFaceRecognitionUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC402', ...data },
    loading: true,
  })
}

// 身份证照片识别
export function ocr(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC404', ...data },
    loading: true,
  })
}

// 查询人脸识别结果
export function queryFaceRecognitionResult(data, config) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC401', ...data },
    loading: true,
    ...config,
  })
}

// 获取信用额度记录
export function getCreditQuotaRecord(data, config) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF015', ...data },
    loading: false,
    ...config,
  })
}
