import request from '@/utils/request'

export function getDrawIntegral(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS303', ...data}
  })
}
export function getDrawGoods(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS304', ...data}
  })
}
export function getNewPIntegral(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS301', ...data}
  })
}
export function getNewPreceive(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS302', ...data},
    loading: true
  })
}

export function submitDraw(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS305', ...data}
  })
}
export function getDrawLog(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS309', ...data},
    loading: true
  })
}
export function getIntegralTask(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS310', ...data}
  })
}
// 积分玩法规则
export function getIntegralRule(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS312', ...data}
  })
}
