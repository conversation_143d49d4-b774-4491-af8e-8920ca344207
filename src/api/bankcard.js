import request from '@/utils/request'
// 获取银行卡列表
export function bankCardList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS041', ...data},
    loading: true
  })
}
export function saveBankCard(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS043', ...data},
    loading: true
  })
}
export function smsGet(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS043', ...data},
    loading: true
  })
}
// 绑卡提交
export function saveBindCard(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS048', ...data},
    loading: true
  })
}
// 删除提交
export function delBankCard(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS045', ...data},
    loading: true
  })
}
// 获取银行名称
export function getBankSmsCode(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS050', ...data}
  })
}

// 获取验证码
export function getBankName(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS049', ...data}
  })
}
// 获取银行卡列表
export function getBankListGroup() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS052'}
  })
}
