import request from '@/utils/request'

// 获取会员首页
export function getMemberInfo(data, loading=true) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI201', ...data},
    loading
  })
}

// 会员购买提交
export function submitMemberOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI203', ...data},
    loading: true
  })
}

// 会员购买提交
export function getBuyUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI202', ...data},
    loading: true
  })
}

//会员权益获取
export function getEuqityProduct(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI008', ...data},
    loading: true
  })
}

export function submitEuqityOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI102', ...data},
    loading: true
  })
}

export function euqityOrderList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI001', ...data}
  })
}
export function getEuqityOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI009', ...data},
    loading: true
  })
}
// 查看卡密
export function euqityOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI009', ...data},
    loading: true
  })
}
//提交
export function submitReturn(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI205', ...data},
    loading: true
  })
}

// 是否需要弹框
export function getMemberConfig(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS010', ...data}
  })
}

// 获取会员专区数据
export function getMemberView(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI211', ...data},
    loading: true
  })
}
// 获取加速卡配置
export function acceleratorCardConfig(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI010', ...data}
  })
}
// 获取会员权益
export function memberBenefitList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI214', ...data}
  })
}
// 获取会员权益
export function memberCustInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI217', ...data},
    loading: true
  })
}
// 取消自动续费
export function renewalSwitch(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI216', ...data},
    loading: true
  })
}
// 领取票券
export function receiveSubmit(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI218', ...data},
    loading: true
  })
}

// 订单列表
export function listOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS107', ...data},
    loading: true
  })
}
export function memberBenefitInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI219', ...data}
  })
}
//
export function memberOrderInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'VI220', ...data}
  })
}
export function memberGiftOrder(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS108', ...data}
  })
}
