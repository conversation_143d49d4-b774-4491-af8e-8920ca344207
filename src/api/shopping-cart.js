import request from '@/utils/request'
// 获取购物车数量
export function goodsCartNumber() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS202'}
  })
}
// 加入购物车
export function addShoppingCart(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS203', ...data},
    loading: true
  })
}
// 购物车列表
export function listShoppingCart(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS201', ...data}
  })
}
// 更新购物车数量
export function updateNum(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS204', ...data}
  })
}
export function deleteShppingCart(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS205', ...data}
  })
}
