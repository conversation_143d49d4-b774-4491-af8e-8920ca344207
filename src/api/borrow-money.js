import request from '@/utils/request'

// 查询最新一笔借款
export function getLastOrder() {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL018' }
  })
}
// 试算
export function trial(data, loading=false) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL015', ...data},
    loading
  })
}
// 提交借款
export function saveForm(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL013', ...data},
    loading: true
  })
}
// 获取url
export function getUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BL014', ...data},
    loading: true
  })
}
//
export function smsCode(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'BA018', ...data},
    loading: true
  })
}
