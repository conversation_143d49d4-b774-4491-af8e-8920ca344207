import request from '@/utils/request'
// 分类列表
export function goodsList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS001', ...data}
  })
}
// 分类列表
export function categoryList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS003', ...data}
  })
}
// 品牌列表
export function brandList(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS020', ...data}
  })
}
// 分类列表
export function subCategoryGoods(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MM002', ...data},
    loading: true
  })
}
// 区域商品分页
export function regionGoodsPage(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MM003', ...data}
  })
}
// 区域商品
export function regionGoods(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MM007', ...data }
  })
}
// 商品订单状态
export function orderState(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS105', ...data}
  })
}
// 线下发货
export function cardDelivery(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS014', ...data},
    loading: true
  })
}
export function goodsDetail(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS002', ...data},
    loading: true
  })
}
export function integralGoods(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS307', ...data},
    loading: true
  })
}
export function activityGoods(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MS304', ...data},
    loading: true
  })
}
