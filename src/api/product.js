import request from '@/utils/request'

// 获取列表
export function listProduct(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF051', ...data}
  })
}
// 获取产品协议内容
export function productAgreement(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF052', ...data}
  })
}
// 获取产品图文描述
export function productContent(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF053', ...data}
  })
}
// 全流程
export function getProductQuota(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF207', ...data},
    loading: true
  })
}
export function getProductLoanUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC007', ...data}
  })
}
export function getProductRepayUrl(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'UC008', ...data}
  })
}
// 获取产品信息
export function productInfo(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'SF054', ...data}
  })
}
