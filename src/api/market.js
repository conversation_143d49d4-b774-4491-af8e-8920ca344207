import request from '@/utils/request'
// 活动专区页
export function getActivityPage(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MM008', ...data},
    loading: true
  })
}

// 获取新人专区分类
export function getCategory(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'MM009', ...data},
    loading: true
  })
}

// 获取优惠劵
export function getCoupons(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TV004', ...data},
    loading: true
  })
}

// 获取优惠卷统计数量
export function couponsNumber(data) {
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TV008', ...data}
  })
}

// 获取新人优惠劵
export function getNewCoupons(data){
  return request({
    url: '/gateway',
    method: 'post',
    data: { apiCode: 'TV009', ...data},
    loading: true
  })
}
