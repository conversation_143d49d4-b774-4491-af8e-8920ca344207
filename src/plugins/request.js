import { create<PERSON>lova } from 'alova'
import adapterFetch from 'alova/fetch'

import VueHook from 'alova/vue'
import { createServerTokenAuthentication } from 'alova/client'

import { getToken, setToken, removeToken, getAppVersion, getUserInfo } from '@/utils/auth'
import ua from '@/utils/ua'
import deviceId from '@/utils/deviceId'
import { showFailToast, showDialog, showLoadingToast, closeToast } from 'vant'

import router from '@/router'

import { defaults } from 'lodash-es'

function createError(message, response, method) {
  const error = new Error(message)
  error.isRequestError = true
  error.method = method
  error.response = response
  // if (method) {
  //   method.meta.error = error
  // }
  return error
}

const { onAuthRequired, onResponseRefreshToken } = createServerTokenAuthentication({
  assignToken(method) {
    // console.log('assignToken', method)
    method.config.headers = method.config?.headers ?? {}
    method.config.headers.appToken = getToken() ?? ''
  },
  async login(response, method) {
    const res = await response.clone().json()
    if (+res.rspCode !== 0) return
    setToken(res.data.appToken)
  },
  async logout(response, method) {
    const res = await response.clone().json()
    if (+res.rspCode !== 0) return
    setToken('')
  },
  // logout: {},
  refreshTokenOnSuccess: {
    async isExpired(response, method) {
      // console.log('isExpired', response, method)
      const res = await response.clone().json()
      // console.log(res)
      if (+res.rspCode === 4) {
        // res.rspMsg = '未登录'
        const error = createError('未登录', response, method)
        method.meta.error = error
        return true
      }
      if (+res.rspCode === 5) {
        // res.rspMsg = '登录已失效，请重新登陆'
        const error = createError('登录已失效，请重新登陆', response, method)
        method.meta.error = error
        return true
      }
      return false
    },
    async handler(response, method) {
      // console.log('handler', response, method)
      // const res = await response.clone().json()

      await showDialog({
        message: method.meta.error.message,
      })
      // console.log(res)
      setToken('')
      router.replace('/register')
      // throw new Error(res.rspMsg)
      throw method.meta.error
    },
  },
})

const alovaInstance = createAlova({
  id: 'app',
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 30000,
  // cacheFor: {},
  statesHook: VueHook,
  requestAdapter: adapterFetch(),
  // 当你使用alova/fetch请求适配器时，由于window.fetch的特点，只有在连接超时或连接中断时才会触发onError拦截器，其他情况均会触发onSuccess拦截器
  beforeRequest(method) {
    method.meta = defaults(method?.meta ?? {}, {
      showError: true,
      loading: false,
    })
    // console.log(method.meta)
    if (method.meta.loading) {
      showLoadingToast({
        duration: 0,
        message: '加载中...',
        forbidClick: true,
      })
    }

    Object.keys(method.meta).forEach((key) => {
      // 删除undefined，避免assignToken判断错误
      if (method.meta[key] === undefined) {
        delete method.meta[key]
      }
    })
    return onAuthRequired((method) => {
      // console.log('beforeRequest', method)
      method.config.headers = method.config?.headers ?? {}
      method.config.headers['Device-Id'] = deviceId.toString()
      method.config.headers['Browser'] = ua.browser.toString()
      method.config.headers['CPU'] = ua.cpu.toString()
      method.config.headers['Device'] = ua.device.toString()
      method.config.headers['Engine'] = ua.engine.toString()
      method.config.headers['OS'] = ua.os.toString()
      // method.config.headers.appToken = getToken() ?? ''
      method.config.params = method.config?.params ?? {}
      method.config.params.apiCode = method?.data?.apiCode
    })(method)
  },
  responded: onResponseRefreshToken({
    async onSuccess(response, method) {
      console.log(response, method)
      const res = await response.clone().json()

      if (method.meta.loading) {
        closeToast() // 清除加载中
      }

      if (+res.rspCode === 0) {
        return res
      }

      const error = createError(res.rspMsg, response, method)

      if (method.meta?.showError) {
        showDialog({
          message: error.message,
        })
      }

      throw error
    },
    async onError(err, method) {
      // console.log({ err, method })

      if (method.meta.loading) {
        closeToast() // 清除加载中
      }

      let error

      if (err.message.includes('Failed')) {
        error = createError('请求失败', null, method)
      } else if (err.message.includes('timeout')) {
        error = createError('请求超时', null, method)
      } else {
        error = createError(err.message, null, method)
      }

      method.meta.error = error
      if (method.meta?.showError) {
        showDialog({
          message: error.message,
        })
      }

      throw error
    },
  }),
})

export default alovaInstance
