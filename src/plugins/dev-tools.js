import { reactive, ref, watch } from 'vue'
import { useLocalStorage, useToggle } from '@vueuse/core'

export const dev = (() => {
  // const enable = ref(import.meta.env.DEV || import.meta.env.MODE !== 'production')
  const [enable, toggleEnable] = useToggle(
    import.meta.env.DEV || import.meta.env.MODE !== 'production'
  )

  // if (import.meta.env.MODE === 'production') {
  //   toggleEnable(false)
  // }

  return reactive({
    enable,
    toggleEnable,
  })
})()

// 灰度测试
export const uat = (() => {
  const enable = useLocalStorage('uat-enable', false)
  const toggle = useToggle(enable)
  const enableTime = useLocalStorage('uat-enable-time', '')
  const url = import.meta.env?.VITE_APP_DEV_TOOLS_UAT_URL
  const watermark = useLocalStorage('uat-watermark', true)
  const toggleWatermark = useToggle(watermark)

  // const defaultUrl = import.meta.env?.VITE_APP_DEV_TOOLS_DEFAULT_REDIRECT_URL || location.origin
  // const url = useLocalStorage('uat-url', defaultUrl)
  if (enableTime.value) {
    const time = Date.now() - parseInt(enableTime.value)
    if (time > 1000 * 60 * 10) {
      enable.value = false
      // enableTime.value = ''
    }
  }

  if (enable.value && url) {
    const u = new URL(url)
    if (u.origin !== location.origin) {
      window.location.href = url
    }
  }

  // function resetUrl() {
  //   url.value = defaultUrl
  // }

  watch(
    enable,
    (newVal) => {
      if (newVal) {
        enableTime.value = Date.now().toString()
      }
    },
    {}
  )
  return reactive({
    enable,
    toggle,
    url,
    watermark,
    toggleWatermark,
    // resetUrl,
  })
})()

// export const pre = (() => {
//   const enable = useLocalStorage('pre-enable', false)
//   const toggleEnable = useToggle(enable)
//   const url = useLocalStorage('pre-url', location.origin)
//   const enableTime = useLocalStorage('pre-enable-time', '')

//   if (enableTime.value) {
//     const time = Date.now() - parseInt(enableTime.value)
//     if (time > 1000 * 60 * 30) {
//       enable.value = false
//       // enableTime.value = ''
//     }
//   }

//   if (enable.value && url.value) {
//     // window.location.href = url.value
//   }

//   watch(
//     enable,
//     (newVal) => {
//       if (newVal) {
//         enableTime.value = Date.now().toString()
//       }
//     },
//     {}
//   )
//   return reactive({
//     enable,
//     toggleEnable,
//     url,
//   })
// })()
