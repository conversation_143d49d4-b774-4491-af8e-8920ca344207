<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=utf-8">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="6、第三方共享及SDK清单-202310.files/filelist.xml">
<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>60449</o:Author>
  <o:LastAuthor>caizj</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>1</o:TotalTime>
  <o:Created>2023-10-16T07:59:00Z</o:Created>
  <o:LastSaved>2023-10-16T07:59:00Z</o:LastSaved>
  <o:Pages>1</o:Pages>
  <o:Words>340</o:Words>
  <o:Characters>1940</o:Characters>
  <o:Lines>16</o:Lines>
  <o:Paragraphs>4</o:Paragraphs>
  <o:CharactersWithSpaces>2276</o:CharactersWithSpaces>
  <o:Version>16.00</o:Version>
 </o:DocumentProperties>
 <o:CustomDocumentProperties>
  <o:KSOProductBuildVer dt:dt="string">2052-11.1.0.12155</o:KSOProductBuildVer>
  <o:ICV dt:dt="string">283A36AAA4D044D082AA4BDE5BF168FB</o:ICV>
 </o:CustomDocumentProperties>
</xml><![endif]-->
<link rel=themeData href="6、第三方共享及SDK清单-202310.files/themedata.thmx">
<link rel=colorSchemeMapping
href="6、第三方共享及SDK清单-202310.files/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>EN-US</w:LidThemeOther>
  <w:LidThemeAsian>ZH-CN</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:SpaceForUL/>
   <w:BalanceSingleByteDoubleByteWidth/>
   <w:DoNotLeaveBackslashAlone/>
   <w:ULTrailSpace/>
   <w:DoNotExpandShiftReturn/>
   <w:AdjustLineHeightInTable/>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:UseWord2010TableStyleRules/>
   <w:DontGrowAutofit/>
   <w:DontUseIndentAsNumberingTabStop/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
   <w:UseFELayout/>
  </w:Compatibility>
  <w:DocumentVariables>
   <w:commondata>eyJoZGlkIjoiYWM2ZWYzZGY2YzZmNmE5NDkwOWE2YjIwNTM3OWQ4ZTgifQ==</w:commondata>
  </w:DocumentVariables>
  <w:DoNotOptimizeForBrowser/>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" LatentStyleCount="376">
  <w:LsdException Locked="false" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="true"
   Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="HTML Top of Form"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="HTML Bottom of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Table"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="No List"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Outline List 1"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Outline List 2"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Outline List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Contemporary"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Elegant"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Professional"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Theme"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="99" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="99" Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="99" Name="Quote"/>
  <w:LsdException Locked="false" Priority="99" Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Mention"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Hashtag"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Unresolved Mention"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:宋体;
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-alt:SimSun;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:515 680460288 22 0 262145 0;}
@font-face
	{font-family:黑体;
	panose-1:2 1 6 9 6 1 1 1 1 1;
	mso-font-alt:SimHei;
	mso-font-charset:134;
	mso-generic-font-family:modern;
	mso-font-pitch:fixed;
	mso-font-signature:-2147482945 953122042 22 0 262145 0;}
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
@font-face
	{font-family:等线;
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-alt:DengXian;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:-1610612033 953122042 22 0 262159 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-469750017 -1073732485 9 0 511 0;}
@font-face
	{font-family:"\@黑体";
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-charset:134;
	mso-generic-font-family:modern;
	mso-font-pitch:fixed;
	mso-font-signature:-2147482945 953122042 22 0 262145 0;}
@font-face
	{font-family:"\@宋体";
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:515 680460288 22 0 262145 0;}
@font-face
	{font-family:"\@等线";
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:-1610612033 953122042 22 0 262159 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin:0cm;
	text-align:justify;
	text-justify:inter-ideograph;
	mso-pagination:none;
	font-size:10.5pt;
	mso-bidi-font-size:12.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:宋体;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;
	mso-font-kerning:1.0pt;}
h3
	{mso-style-qformat:yes;
	mso-style-next:正文;
	mso-margin-top-alt:auto;
	margin-right:0cm;
	mso-margin-bottom-alt:auto;
	margin-left:0cm;
	mso-pagination:none;
	mso-outline-level:3;
	font-size:13.5pt;
	font-family:宋体;}
span.grame
	{mso-style-name:grame;
	mso-style-unhide:no;}
span.spelle
	{mso-style-name:spelle;
	mso-style-unhide:no;}
span.SpellE
	{mso-style-name:"";
	mso-spl-e:yes;}
span.GramE
	{mso-style-name:"";
	mso-gram-e:yes;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-size:10.0pt;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;
	mso-ascii-font-family:"Times New Roman";
	mso-fareast-font-family:宋体;
	mso-hansi-font-family:"Times New Roman";
	mso-font-kerning:0pt;
	mso-ligatures:none;}
 /* Page Definitions */
 @page
	{mso-page-border-surround-header:no;
	mso-page-border-surround-footer:no;}
@page WordSection1
	{size:595.3pt 841.9pt;
	margin:72.0pt 90.0pt 72.0pt 90.0pt;
	mso-header-margin:42.55pt;
	mso-footer-margin:49.6pt;
	mso-paper-source:0;
	layout-grid:15.6pt;}
div.WordSection1
	{page:WordSection1;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:普通表格;
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman",serif;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="1026"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=ZH-CN style='tab-interval:21.0pt;word-wrap:break-word;text-justify-trim:
punctuation'>

<div class=WordSection1 style='layout-grid:15.6pt'>

<h3 align=center style='text-align:center'><span style='font-family:黑体;
color:black'>产品服务及第三方</span><span lang=EN-US style='font-family:"Times New Roman",serif;
color:black'>SDK</span><span style='font-family:黑体;color:black'>清单</span><span
lang=EN-US style='font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></h3>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>本《第三方<span lang=EN-US>SDK</span>清单》列明我们主要对外提供个人信息的情况及接入的由第三方提供的软件工具开发包（<span
lang=EN-US>SDK</span>）。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><b><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>1.</span></b><b><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b><b><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>实现我们的产品与<span
lang=EN-US>/</span>或服务</span></b><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(1)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>实名及活体识别服务</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：用户实名认证，证件识别及活体识别场景等。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：满足相关法律规定及监管要求、确保用户身份真实性、实现反欺诈等风险控制、保障系统和服务安全等。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息类型：实名相关信息（需要提供姓名、手机号码、身份证信息、银行卡，活体识别及个人基本信息等）</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：提供活体检测服务的合作方，如商汤科技等。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：我们采用加密算法对数据做加密处理，并将加密后的数据采用专用传输通道提供给合作方，合作方根据算法进行解密后在您的授权范围内使用您的个人信息。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(2)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>支付服务</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：商品购买、会员权益等产品支付、其他商品<span
lang=EN-US>/</span>服务支付等。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：保障交易安全、完成商品<span
lang=EN-US>/</span>服务的支付。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息类型：订单支付相关信息（采用银行卡支付的，需要提供姓名、手机号码、身份证信息、银行卡信息）</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：提供支付服务的合作方，<span
class=GramE><span class=grame>如财付通支付</span></span>科技有限公司、支付宝（中国）网络技术有限公司、中国银联股份有限公司等支付公司。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：我们采用加密算法对数据做加密处理，并将加密后的数据采用专用传输通道提供给合作方，合作方根据算法进行解密后在您的授权范围内使用您的个人信息。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(3)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>商品<span
lang=EN-US>/</span>服务下单、订单管理与交付</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：自营、第三方商品<span
lang=EN-US>/</span>服务购买与交付等。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：实现购买商品<span
lang=EN-US>/</span>服务的需求（包括商品<span lang=EN-US>/</span>服务配送、享受售后服务等）</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息类型：订单相关信息、配送相关信息及处理所涉问题所必要的个人信息，以实现具体服务目在“最小必要”范围提供您的个人信息。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：提供相关服务的合作方，如北京京东世纪信息技术有限公司、<span
class=GramE><span class=grame>优友电子商务</span></span>（深圳）有限公司。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：我们采用加密算法对数据做加密处理，并将加密后的数据采用专用传输通道提供给合作方，合作方根据算法进行解密后在您的授权范围内使用您的个人信息。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(4)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>市场推广服务</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：广告投放<span
lang=EN-US>/</span>推广，如用户拉新、用户召回等</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：向您提供我们各类精彩内容及商业性信息的展示与通知、推广我们的产品与<span
lang=EN-US>/</span>或服务、优化我们的服务。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息范围：设备信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：为我们提供推广服务的合作方。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：由我们提供无法直接识别用户的去标识化的数据，推广服务合作方向相应设备投放推广宣传我们的产品与<span
lang=EN-US>/</span>或服务的广告内容。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><b><span lang=EN-US style='font-size:13.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>2.</span></b><b><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b><b><span
style='font-size:13.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>广告合作</span></b><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(1)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：广告投放</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：实现广告投放<span
lang=EN-US>/</span>推送</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息范围：设备信息、群体标签、您主动填写的并授权我们提供给合作方的相关个人信息。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：广告主与<span
lang=EN-US>/</span>或其委托的投放代理商。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：由我们提供无法直接识别用户的去标识化的数据（您主动填写的数据将采用加密算法对数据做加密处理），接收方通过自动化方式自行决策是否下发广告。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(2)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用场景：广告统计分析与优化</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：分析、衡量广告和相关服务的有效性、进行广告或决策建议、提高广告<span
class=GramE><span class=grame>有效触达率</span></span>。</span><span lang=EN-US
style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>个人信息范围：设备信息、群体标签、广告曝光、浏览、点击、播放以及广告转化数据等广告相关数据。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>接收方类型：提供广告<span
class=GramE><span class=grame>统分析</span></span>与优化服务的合作方，<span class=GramE><span
class=grame>如友盟科技</span></span>。</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式： 由我们提供无法直接识别用户的去标识化的数据，由接收方自主进行统计分析与优化，包括与其合法获取的其他数据相结合进行统计分析与优化。</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><b><span lang=EN-US style='font-size:13.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>3.</span></b><b><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b><b><span
style='font-size:13.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>第三方<span
lang=EN-US>SDK</span>清单</span></b><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>特别提示：</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>第三方<span lang=EN-US>SDK</span>可能因为其版本升级、策略调整等原因导致其处理的信息类型发生一定变化，请以其公示的官方说明为准。以下是我们接入的主要的第三方<span
lang=EN-US>SDK</span>的信息：</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(1)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：商汤科技<span class=SpellE><span class=spelle><span
lang=EN-US>sdk</span></span></span></span><span lang=EN-US style='font-size:
12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：商汤科技（深圳）有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：用于拍摄用户人脸信息并采集视频照片进行活体检测和人脸比对</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息，网络信息摄像头权限</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(2)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：<span lang=EN-US>QQ SDK</span></span><span lang=EN-US
style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：<span
class=GramE><span class=grame>腾讯科技</span></span>（深圳）有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：用于帮助用户分享内容至<span
lang=EN-US>QQ</span>客户端</span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(3)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：<span class=grame>微信</span><span lang=EN-US>&nbsp;SDK</span></span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：<span
class=GramE><span class=grame>深圳市腾讯计算机系统</span></span>有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：用于帮助用户分享内容<span
class=GramE><span class=grame>至微信客户端</span></span></span><span lang=EN-US
style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(4)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：百度<span lang=EN-US>&nbsp;SDK</span></span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：北京百度网讯科技有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：用于帮助用户分享内容至百度客户端</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(5)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：<span class=GramE><span class=grame>微信支付</span></span><span
lang=EN-US>&nbsp;SDK</span></span><span lang=EN-US style='font-size:12.5pt;
mso-ascii-font-family:Calibri;mso-hansi-font-family:Calibri;mso-bidi-font-family:
Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：<span
class=GramE><span class=grame>财付通支付</span></span>科技有限公司</span><span lang=EN-US
style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：帮助用户在应用内<span
class=GramE><span class=grame>使用微信支付</span></span></span><span lang=EN-US
style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(6)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：<span lang=EN-US>Alipay</span>（支付宝）</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：支付宝（中国）网络技术有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：帮助用户在应用内使用支付宝支付</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息、位置信息</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用去标识化、加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='margin-left:21.25pt;text-indent:-21.25pt;line-height:
15.0pt'><span lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>(7)</span><span lang=EN-US style='font-size:7.0pt;
font-family:"Times New Roman",serif;mso-fareast-font-family:等线;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;
color:black'>SDK</span><span style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:
Calibri;color:black'>名称：移动<span lang=EN-US>/</span>联通<span lang=EN-US>/</span>电信一键登录</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>服务商名称：深圳市<span
class=GramE><span class=grame>和讯华谷</span></span>信息技术有限公司</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>使用目的：手机号码一键登录</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>信息类型：设备信息、电话号码</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span style='font-size:12.5pt;
font-family:等线;mso-bidi-font-family:Calibri;color:black'>处理方式：采用加密传输等安全处理方式</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span lang=EN-US
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='line-height:15.0pt'><span lang=EN-US
style='font-size:12.5pt;font-family:等线;mso-bidi-font-family:Calibri;color:black'>&nbsp;</span><span
lang=EN-US style='font-size:12.5pt;mso-ascii-font-family:Calibri;mso-hansi-font-family:
Calibri;mso-bidi-font-family:Calibri;color:black'><o:p></o:p></span></p>

<p class=MsoNormal><span lang=EN-US><o:p>&nbsp;</o:p></span></p>

</div>

</body>

</html>
