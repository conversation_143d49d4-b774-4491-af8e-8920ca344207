const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputDir = path.join(__dirname, '../src/assets/images/member');

// 递归处理目录
function processDirectory(inputPath) {
  // 读取目录内容
  const items = fs.readdirSync(inputPath);

  items.forEach(item => {
    const currentPath = path.join(inputPath, item);
    const stat = fs.statSync(currentPath);

    if (stat.isDirectory()) {
      // 如果是目录，递归处理
      processDirectory(currentPath);
    } else if (stat.isFile() && /\.(jpg|jpeg|png|gif)$/i.test(item)) {
      // 如果是图片文件，进行压缩
      const ext = path.extname(currentPath).toLowerCase();
      let sharpInstance = sharp(currentPath);

      // 根据文件扩展名选择不同的压缩方式
      if (ext === '.png') {
        sharpInstance = sharpInstance.png({ quality: 80 });
      } else if (ext === '.jpg' || ext === '.jpeg') {
        sharpInstance = sharpInstance.jpeg({ quality: 80 });
      } else if (ext === '.gif') {
        // 对于 GIF，我们只复制文件，不进行压缩
        fs.copyFileSync(currentPath, currentPath + '.temp');
        fs.unlinkSync(currentPath);
        fs.renameSync(currentPath + '.temp', currentPath);
        console.log(`Copied ${currentPath} successfully`);
        return;
      }

      sharpInstance
        .toFile(currentPath + '.temp')
        .then(() => {
          // 删除原文件
          fs.unlinkSync(currentPath);
          // 重命名临时文件为原文件名
          fs.renameSync(currentPath + '.temp', currentPath);
          console.log(`Compressed ${currentPath} successfully`);
        })
        .catch(err => {
          console.error(`Error processing ${currentPath}:`, err);
          // 如果处理失败，删除临时文件
          if (fs.existsSync(currentPath + '.temp')) {
            fs.unlinkSync(currentPath + '.temp');
          }
        });
    }
  });
}

// 开始处理
processDirectory(inputDir);
