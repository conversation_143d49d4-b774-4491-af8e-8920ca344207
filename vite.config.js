import { defineConfig, loadEnv } from 'vite'
import path from 'path'
// import createVitePlugins from './vite/plugins'

import vue from '@vitejs/plugin-vue'
import autoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import vueDevTools from 'vite-plugin-vue-devtools'
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const now = new Date().getTime()
  return {
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
          api: 'modern-compiler', // or "modern", "legacy"
        },
        sass: {
          silenceDeprecations: ['legacy-js-api'],
          api: 'modern-compiler', // or "modern", "legacy"
        },
      },
    },
    plugins: [
      vue(),
      autoImport({
        resolvers: [VantResolver()],
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            vuex: ['useStore'],
            '@/plugins/request.js': [['default', 'request']],
            'alova/client': ['useRequest', 'useWatcher', 'useFetcher', 'usePagination', 'useForm'],
          },
        ],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
      vueDevTools({}),
    ],
    define: {
      // 定义全局变量
      // __APP_VERSION__: now,
    },
    resolve: {
      // 解析
      alias: {
        // 重命名路劲
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
      },
      extensions: ['.js', '.ts', '.jsx', '.tsx', '.json', '.vue', '.mjs'], // 类型： string[] 导入时想要省略的扩展名列表。
    },
    server: {
      port: 4202, // 服务端口
      open: false, // 是否自动打开浏览器
      host: '0.0.0.0',
      proxy: {
        // 代理
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_PROXY_TARGET,
          ws: false,
          changeOrigin: true,
          rewrite: (p) => p.replace(new RegExp(`^${env.VITE_APP_BASE_API}`), ''),
        },
      },
      cors: true,
    },
  }
})
