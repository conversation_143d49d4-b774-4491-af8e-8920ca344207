import vue from '@vitejs/plugin-vue'
import versionUpdatePlugin from '../../build/versionUpdatePlugin'
import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'
import styleImport, { VantResolve } from 'vite-plugin-style-import'
export default function createVitePlugins(viteEnv, isBuild = false, now) {
  const vitePlugins = [vue()]
  vitePlugins.push(createAutoImport())
  vitePlugins.push(createSetupExtend())
  vitePlugins.push(createSvgIcon(isBuild))
  vitePlugins.push(
    versionUpdatePlugin({
      version: now,
    })
  )
  vitePlugins.push(styleImport({ esolves: [VantResolve()] }))
  isBuild && vitePlugins.push(...createCompression(viteEnv))
  return vitePlugins
}
